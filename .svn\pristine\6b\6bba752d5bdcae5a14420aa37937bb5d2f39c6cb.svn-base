# Copyright (c) Microsoft Corporation.
# Licensed under the MIT License.

# coding=utf-8
import abc
import bisect
import logging
import warnings
from inspect import getfullargspec
from typing import Callable, Dict, Union, Tuple, List, Iterator, Optional

import pandas as pd
import numpy as np
import json
from sklearn.model_selection import KFold

from qlib.log import get_module_logger, TimeInspector
# from qlib.data import D
# from qlib.config import C
# from qlib.utils import parse_config, transform_end_date, init_instance_by_config
# from qlib.utils.serial import Serializable
# from qlib.utils import fetch_df_by_index, fetch_df_by_col
# from qlib.utils import lazy_sort_index
from pathlib import Path
from qlib.data.dataset.loader import DataLoader
from qlib.data.dataset.handler import DataHandler
from .loader import AFDataLoader

# from . import processor as processor_module
from . import loader as data_loader_module

class DataHandlerAF(DataHandler):
    """
    加载raw data进行加工处理的地方
    """
    def __init__(
        self,
        instruments=None,
        start_time=None,
        end_time=None,
        data_loader: Union[dict, str, DataLoader]=None,
        valid_fold = 5,
        is_normal = True,
        init_data=True,
        fetch_orig=True
    ):
        self._valid_fold = valid_fold
        self._is_normal = is_normal
        self.train_ft_df = pd.DataFrame()
        self.train_lb_df = pd.DataFrame()
        self.valid_ft_df = pd.DataFrame()
        self.valid_lb_df = pd.DataFrame()

        if data_loader is None:
            raise ValueError("DataHandlerAF init parameter data_loader is None")
        self.logger = get_module_logger("DataHandlerAF")

        super().__init__(
            instruments=instruments,
            start_time=start_time,
            end_time=end_time,
            data_loader=data_loader,
        )

    def config(self, **kwargs):
        super().config(**kwargs)

    def setup_data(self, enable_cache: bool = False):
        """
        Base class DataHandler->load()->raw data(4个 DataFrame)
        """

        if not isinstance(self.data_loader, AFDataLoader):
            return


        with TimeInspector.logt("Loading data"):
            # 加载原始数据
            lb_df, lf_df, sf_df, ct_df = self.data_loader.load(self.instruments, self.start_time, self.end_time)
            self.logger.info(f"lb{lb_df.shape} sf{sf_df.shape} lf{lf_df.shape} ct{ct_df.shape}")

            # todo： 当前未做col_sel选择，直接合并特征数据
            ft_df = pd.merge(lf_df, sf_df, how='inner', left_index=True, right_index=True)
            ft_df = pd.merge(ft_df, ct_df, how='inner', left_index=True, right_index=True)

            # self.codes = sorted(lb_df.CODE.unique().tolist())
            # self.ft_lens["lf_len"] = lf_df.shape[1]
            # self.ft_lens["sf_len"] = sf_df.shape[1]
            # self.ft_lens["ct_len"] = ct_df.shape[1]
            self.ft_mean = ft_df.mean()
            self.ft_std = ft_df.std()

            # 划分测试集与验证集
            cv = KFold(self._valid_fold, shuffle=True, random_state=1)
            train_ids, valid_ids = next(cv.split(lb_df.index))
            self.train_lb_df = lb_df.iloc[train_ids,:]
            self.valid_lb_df = lb_df.iloc[valid_ids,:]
            # print(self.train_lb_df, self.valid_lb_df)

            self.train_ft_df = ft_df[ft_df.index.isin(self.train_lb_df.index)]
            self.train_lb_df.sort_index(inplace=True)
            self.train_ft_df.sort_index(inplace=True)

            self.valid_ft_df = ft_df[ft_df.index.isin(self.valid_lb_df.index)]
            self.valid_lb_df.sort_index(inplace=True)
            self.valid_ft_df.sort_index(inplace=True)

            assert len(self.train_lb_df) == len(self.train_ft_df) and self.train_lb_df.index[-1] == self.train_ft_df.index[-1],\
                f'dataset processing error: {len(self.train_lb_df)} == {len(self.train_ft_df)} and {self.train_lb_df.index[-1]} == {self.train_ft_df.index[-1]}'
            assert len(self.valid_lb_df) == len(self.valid_ft_df) and self.valid_lb_df.index[-1] == self.valid_ft_df.index[-1],\
                f'dataset processing error: {len(self.valid_lb_df)} == {len(self.valid_ft_df)} and {self.valid_lb_df.index[-1]} == {self.valid_ft_df.index[-1]}'

            # 归一化
            # data_val = self.train_ft_df.values
            # self.train_ft_df = pd.DataFrame((data_val - data_val.mean(axis=0)) /data_val.std(axis=0))
            # data_val = self.valid_ft_df.values
            # self.valid_ft_df = pd.DataFrame((data_val - data_val.mean(axis=0)) /data_val.std(axis=0))
            if self._is_normal:
                self.train_ft_df = (self.train_ft_df - self.train_ft_df.mean()) / self.train_ft_df.std()
                self.valid_ft_df = (self.valid_ft_df - self.valid_ft_df.mean()) / self.valid_ft_df.std()

            # self._dump_input_param_json(self.data_loader.direct, self.data_loader.model_name, self.data_loader.model_path)

        # super().setup_data(enable_cache=enable_cache)
        self.logger.info(f"train shape{self.train_ft_df.shape} valid shape{self.valid_ft_df.shape}")

    def fetch(
        self,
        selector: Union[pd.Timestamp, slice, str] = slice(None, None),
        level: Union[str, int] = "datetime",
        col_set: Union[str, List[str]] = DataHandler.CS_ALL,
        squeeze: bool = False,
        proc_func: Callable = None
    ) -> pd.DataFrame:
        """
        
        Returns
        -------
        pd.DataFrame
        """

        if isinstance(selector, str):
            if selector == "train":
                if col_set == DataHandler.CS_ALL:
                    return self.train_ft_df, self.train_lb_df
                elif isinstance(col_set, list):
                    if "feature" in col_set and "label" in col_set:
                        if "encoded" in col_set:
                            return pd.concat({"feature": self.train_ft_df, "label": self.train_lb_df[["label"]], "encoded": self.train_lb_df[["code_encoded"]]}, axis=1)
                        else:
                            return pd.concat({"feature": self.train_ft_df, "label": self.train_lb_df[["label"]]}, axis=1)
                    else:
                        raise KeyError(f"{col_set} type not supported.")            
            elif selector == "valid":
                if col_set == DataHandler.CS_ALL:
                    return self.valid_ft_df, self.valid_lb_df
                elif isinstance(col_set, list):
                    if "feature" in col_set and "label" in col_set:
                        if "encoded" in col_set:
                            return pd.concat({"feature": self.valid_ft_df, "label": self.valid_lb_df[["label"]], "encoded": self.valid_lb_df[["code_encoded"]]}, axis=1)
                        else:
                            return pd.concat({"feature": self.valid_ft_df, "label": self.valid_lb_df[["label"]]}, axis=1)
                    else:
                        raise KeyError(f"{col_set} type not supported.")            
            else:
                raise NotImplementedError(f"{selector} type not supported.")            
        else:
            raise NotImplementedError(f"{selector} type not supported.")

