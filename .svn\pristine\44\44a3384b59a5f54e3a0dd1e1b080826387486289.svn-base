{"cells": [{"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["import lmdb\n", "import os, sys\n", "\n", "def initialize():\n", "    env = lmdb.open(\"students\")\n", "    return env\n", "\n", "def insert(env, sid, name):\n", "    txn = env.begin(write = True)\n", "    txn.put(str(sid).encode('utf-8'), name)\n", "    txn.commit()\n", "\n", "def delete(env, sid):\n", "    txn = env.begin(write = True)\n", "    txn.delete(str(sid).encode('utf-8'))\n", "    txn.commit()\n", "\n", "def update(env, sid, name):\n", "    txn = env.begin(write = True)\n", "    txn.put(str(sid).encode('utf-8'), name.encode('utf-8'))\n", "    txn.commit()\n", "\n", "def search(env, sid):\n", "    txn = env.begin()\n", "    name = txn.get(str(sid).encode('utf-8'))\n", "    return name\n", "\n", "def display(env):\n", "    txn = env.begin()\n", "    cur = txn.cursor()\n", "    for key, value in cur:\n", "        print (key, value)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Insert 3 records.\n", "b'1' b'<PERSON>'\n", "b'2' b'Bob'\n", "b'3' b'<PERSON>'\n", "Delete the record where sid = 1.\n", "b'2' b'Bob'\n", "b'3' b'<PERSON>'\n", "Update the record where sid = 3.\n", "b'2' b'Bob'\n", "b'3' b'<PERSON>'\n", "Get the name of student whose sid = 3.\n", "b'<PERSON>'\n"]}, {"data": {"text/plain": ["1"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["env = initialize()\n", "\n", "print(\"Insert 3 records.\")\n", "insert(env, 1, \"<PERSON>\".encode('utf-8'))\n", "insert(env, 2, \"<PERSON>\".encode('utf-8'))\n", "insert(env, 3, \"<PERSON>\".encode('utf-8'))\n", "display(env)\n", "\n", "print(\"Delete the record where sid = 1.\")\n", "delete(env, 1)\n", "display(env)\n", "\n", "print(\"Update the record where sid = 3.\")\n", "update(env, 3, \"<PERSON>\")\n", "display(env)\n", "\n", "print(\"Get the name of student whose sid = 3.\")\n", "name = search(env, 3)\n", "print(name)\n", "\n", "env.close()\n", "\n", "os.system(\"rm -r students\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 2}