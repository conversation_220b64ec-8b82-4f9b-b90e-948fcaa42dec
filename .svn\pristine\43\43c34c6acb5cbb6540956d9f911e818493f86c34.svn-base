{"cells": [{"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["rscript = \"\"\"\n", "require(fBasics)\n", "\n", "da = read.table(\"data/d-caus.txt\", header = T)\n", "db = read.table(\"data/d-useu.txt\", header = T)\n", "dc = read.table(\"data/d-jpus.txt\", header = T)\n", "dd = read.table(\"data/d-usuk.txt\", header = T)\n", "# (a)\n", "caus = diff(log(da$rate))\n", "useu = diff(log(db$Value))\n", "jpus = diff(log(dc$value))\n", "usuk = diff(log(dd$value))\n", "# (b)\n", "basicStats(caus)\n", "basicStats(useu)\n", "basicStats(jpus)\n", "basicStats(usuk)\n", "# (d)\n", "d = density(useu)\n", "plot(d$x, d$y, type = 'l', xlab = 'log-rtn', ylab = 'density')\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["require(fBasics)\n", "da = read_table(\"data/d-caus.txt\", header=T)\n", "db = read_table(\"data/d-useu.txt\", header=T)\n", "dc = read_table(\"data/d-jpus.txt\", header=T)\n", "dd = read_table(\"data/d-usuk.txt\", header=T)\n", "# (a)\n", "caus = diff(log(da . rate))\n", "useu = diff(log(db . Value))\n", "jpus = diff(log(dc . value))\n", "usuk = diff(log(dd . value))\n", "# (b)\n", "basicStats(caus)\n", "basicStats(useu)\n", "basicStats(jpus)\n", "basicStats(usuk)\n", "# (d)\n", "d = density(useu)\n", "plot(d . x, d . y, type='l', xlab='log-rtn', ylab='density')\n", "\n"]}], "source": ["from pyensae.languages import r2python\n", "print(r2python(rscript, pep8=True))"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'require' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[1;32m<ipython-input-10-8b2b2c18779e>\u001b[0m in \u001b[0;36m<module>\u001b[1;34m()\u001b[0m\n\u001b[1;32m----> 1\u001b[1;33m \u001b[0mrequire\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mfBasics\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      2\u001b[0m \u001b[0mda\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mread_table\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;34m\"data/d-caus.txt\"\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mheader\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mT\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      3\u001b[0m \u001b[0mdb\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mread_table\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;34m\"data/d-useu.txt\"\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mheader\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mT\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      4\u001b[0m \u001b[0mdc\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mread_table\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;34m\"data/d-jpus.txt\"\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mheader\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mT\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      5\u001b[0m \u001b[0mdd\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mread_table\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;34m\"data/d-usuk.txt\"\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mheader\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mT\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;31mNameError\u001b[0m: name 'require' is not defined"]}], "source": ["require(fBasics)\n", "da = read_table(\"data/d-caus.txt\", header=T)\n", "db = read_table(\"data/d-useu.txt\", header=T)\n", "dc = read_table(\"data/d-jpus.txt\", header=T)\n", "dd = read_table(\"data/d-usuk.txt\", header=T)\n", "# (a)\n", "caus = diff(log(da . rate))\n", "useu = diff(log(db . Value))\n", "jpus = diff(log(dc . value))\n", "usuk = diff(log(dd . value))\n", "# (b)\n", "basicStats(caus)\n", "basicStats(useu)\n", "basicStats(jpus)\n", "basicStats(usuk)\n", "# (d)\n", "d = density(useu)\n", "plot(d . x, d . y, type='l', xlab='log-rtn', ylab='density')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 2}