# MIT License
# Copyright (c) 2018 CMU Locus Lab
"""
GitHub Copilot: GRU和TCN都是可用于时序数据预测的神经网络模型，
它们各自有着不同的优缺点。

GRU是一种循环神经网络（RNN）的变体，它通过引入重置门和更新门来控制信息的
流动，从而减少了LSTM中的门数量，简化了模型结构。相比于LSTM，GRU的优点在于
模型结构更简单，参数更少，训练速度更快。但是，GRU的表现并不一定比LSTM更好，
具体效果还需要根据具体任务和数据集来进行评估。

TCN是一种卷积神经网络（CNN）的变体，它通过使用一系列卷积层和池化层来捕捉
时序数据中的长期依赖关系。相比于传统的RNN模型，TCN的优点在于可以并行计算，
训练速度更快，同时也可以处理更长的时间序列。此外，TCN还可以通过调整卷积核
大小和层数来适应不同的时间序列数据。

总的来说，GRU和TCN都是可用于时序数据预测的有效模型，具体选择哪种模型还需要
根据具体任务和数据集来进行评估。如果数据集中存在长期依赖关系，可以考虑使用
GRU；如果需要处理更长的时间序列或者需要更快的训练速度，可以考虑使用TCN。
"""
import torch.nn as nn
from torch.nn.utils import weight_norm


class Chomp1d(nn.Module):
    def __init__(self, chomp_size):
        super(Chomp1d, self).__init__()
        self.chomp_size = chomp_size

    def forward(self, x):
        return x[:, :, : -self.chomp_size].contiguous()


class TemporalBlock(nn.Module):
    def __init__(self, n_inputs, n_outputs, kernel_size, stride, dilation, padding, dropout=0.2):
        super(TemporalBlock, self).__init__()
        self.conv1 = nn.Conv1d(n_inputs, n_outputs, kernel_size, stride=stride, padding=padding, dilation=dilation)
        self.normed_conv1 = weight_norm(self.conv1)

        self.chomp1 = Chomp1d(padding)
        self.relu1 = nn.ReLU()
        self.dropout1 = nn.Dropout(dropout)

        self.conv2 = nn.Conv1d(n_outputs, n_outputs, kernel_size, stride=stride, padding=padding, dilation=dilation)
        self.normed_conv2 = weight_norm(self.conv2)

        self.chomp2 = Chomp1d(padding)
        self.relu2 = nn.ReLU()
        self.dropout2 = nn.Dropout(dropout)

        self.net = nn.Sequential(
            self.normed_conv1, self.chomp1, self.relu1, self.dropout1, self.normed_conv2, self.chomp2, self.relu2, self.dropout2
        )
        self.downsample = nn.Conv1d(n_inputs, n_outputs, 1) if n_inputs != n_outputs else None
        self.relu = nn.ReLU()
        self.init_weights()

    def init_weights(self):
        self.normed_conv1.weight.data.normal_(0, 0.01)
        self.normed_conv2.weight.data.normal_(0, 0.01)
        if self.downsample is not None:
            self.downsample.weight.data.normal_(0, 0.01)

    def forward(self, x):
        out = self.net(x)
        res = x if self.downsample is None else self.downsample(x)
        return self.relu(out + res)


class TemporalConvNet(nn.Module):
    def __init__(self, num_inputs, num_channels, kernel_size=2, dropout=0.2):
        super(TemporalConvNet, self).__init__()
        layers = []
        num_levels = len(num_channels)
        for i in range(num_levels):
            dilation_size = 2**i
            in_channels = num_inputs if i == 0 else num_channels[i - 1]
            out_channels = num_channels[i]
            layers += [
                TemporalBlock(
                    in_channels,
                    out_channels,
                    kernel_size,
                    stride=1,
                    dilation=dilation_size,
                    padding=(kernel_size - 1) * dilation_size,
                    dropout=dropout,
                )
            ]

        self.network = nn.Sequential(*layers)

    def forward(self, x):
        return self.network(x)
