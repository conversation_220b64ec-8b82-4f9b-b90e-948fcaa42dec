import sys
import numpy as np
import pandas as pd
import pytz
from datetime import datetime
import talib as ta
from qtunnel import DataSource, BarData, BarSize, DoRight, RunMode

def zscore(series):
    nonans = series[~np.isnan(series)]
    zscore = (series - np.mean(nonans))/np.std(nonans)
    return zscore
    
def trend_level(series, win=15, ma_win=5):
    ma=ta.EMA(series, ma_win)
    ls=ta.LINEARREG_SLOPE(ma, win)
    nonans=ls[~np.isnan(ls)]
    std=np.std(nonans)
    direct=[]
    for i in range(ls.shape[0]):
        if np.isnan(ls[i]):
            direct.append(ls[i])
        else:
            drt=0
            if ls[i]>0 and ls[i]<=0.5*std:
                drt=1
            elif ls[i]>0.5*std and ls[i]<=std:
                drt=2
            elif ls[i]>std:
                if ls[i-1]>=ls[i]:
                    drt=3
                else:
                    drt=4
            elif ls[i]<0 and ls[i]>=-0.5*std:
                drt=-1
            elif ls[i]<-0.5*std and ls[i]>=-1*std:
                drt=-2
            elif ls[i]<-1*std:
                if ls[i-1]<=ls[i]:
                    drt=-3
                else:
                    drt=-4
            direct.append(drt)
    return direct

def get_history_data(symbol, begindate, enddate, barsize) -> pd.DataFrame:
    # 历史行情
    hist=ds.get_history_data2(symbol, begindate, enddate, [BarData.datetime,BarData.open,BarData.high,BarData.low,BarData.close,BarData.volume,BarData.amount], barsize)
    df=pd.DataFrame(hist, columns=["date", "open", "high", "low", "close", "volume","amount"])
    df["date"] = df["date"].apply(datetime.fromtimestamp, args=(pytz.timezone('Asia/Shanghai'),))#.apply(lambda x: x.strftime("%Y-%m-%d"))
    df["pctchange"] = (df["close"]-df["close"].shift())/df["close"]*100
    df["amount"] = df["amount"].apply(lambda x: x * 1e4 if x/10e6 < 1.0 else x)
    df["vwap"]=df["amount"]/df["volume"]
    df.set_index(["date"], inplace=True, drop=True)
    return df

def draw_level_chart(symbol, barsize=BarSize.day, length=120, win=15, ma_win=5):
    import mplfinance as mpf
    ds=DataSource(RunMode.passive)

    hist=ds.get_history_data(symbol, length, [BarData.datetime,BarData.open,BarData.high,BarData.low,BarData.close,BarData.volume], barsize)
    natr = ta.NATR(hist[:,2], hist[:,3], hist[:,4], win)
    # zscore=zscore(natr)
    tl=trend_level(natr, win, ma_win)

    df=pd.DataFrame(hist, columns=["date", "open", "high", "low", "close", "volume"])
    df["date"] = df["date"].apply(datetime.fromtimestamp) #.apply(lambda x: x.strftime("%Y-%m-%d"))
    df.set_index(["date"], inplace=True, drop=True) 

    add_plot = [
        mpf.make_addplot(natr, panel=1),
        mpf.make_addplot(tl, panel=2),
    ]
    mpf.plot(df, type='candle',
        mav=(5, 10, 20, 60),
        # volume=True,
        style='nightclouds',
        addplot=add_plot,
        datetime_format='%Y-%m-%d',
        figscale=1.2, #设置图像的缩小或放大,1.5就是放大50%
        figratio=(6.5, 3.5), # 设置图形纵横比
    )   