{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Round Trip Tear Sheet Example"]}, {"cell_type": "markdown", "metadata": {}, "source": ["When evaluating the performance of an investing strategy, it is helpful to quantify the frequency, duration, and profitability of its independent bets, or \"round trip\" trades. A round trip trade is started when a new long or short position is opened and then later completely or partially closed out.\n", "\n", "The intent of the round trip tearsheet is to help differentiate strategies that profited off a few lucky trades from strategies that profited repeatedly from genuine alpha. Breaking down round trip profitability by traded name and sector can also help inform universe selection and identify exposure risks. For example, even if your equity curve looks robust, if only two securities in your universe of fifteen names contributed to overall profitability, you may have reason to question the logic of your strategy.\n", "\n", "To identify round trips, pyfolio reconstructs the complete portfolio based on the transactions that you pass in. When you make a trade, pyfolio checks if shares are already present in your portfolio purchased at a certain price. If there are, we compute the PnL, returns and duration of that round trip trade. In calculating round trips, pyfolio will also append position closing transactions at the last timestamp in the positions data. This closing transaction will cause the PnL from any open positions to realized as completed round trips."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import pyfolio as pf\n", "%matplotlib inline\n", "import gzip\n", "import os\n", "import pandas as pd\n", "\n", "# silence warnings\n", "import warnings\n", "warnings.filterwarnings('ignore')"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["transactions = pd.read_csv(gzip.open('../tests/test_data/test_txn.csv.gz'),\n", "                    index_col=0, parse_dates=True)\n", "positions = pd.read_csv(gzip.open('../tests/test_data/test_pos.csv.gz'),\n", "                    index_col=0, parse_dates=True)\n", "returns = pd.read_csv(gzip.open('../tests/test_data/test_returns.csv.gz'),\n", "                      index_col=0, parse_dates=True, header=None)[1]"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Optional: Sector mappings may be passed in as a dict or pd.Series. If a mapping is\n", "# provided, PnL from symbols with mappings will be summed to display profitability by sector.\n", "sect_map = {'COST': 'Consumer Goods', 'INTC':'Technology', 'CERN':'Healthcare', 'GPS':'Technology',\n", "            'MMM': 'Construction', 'DELL': 'Technology', 'AMD':'Technology'}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The easiest way to run the analysis is to call `pyfolio.create_round_trip_tear_sheet()`. Passing in a sector map is optional. You can also pass `round_trips=True` to `pyfolio.create_full_tear_sheet()` to have this be created along all the other analyses."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"scrolled": false}, "outputs": [{"data": {"text/html": ["<div>\n", "<style>\n", "    .dataframe thead tr:only-child th {\n", "        text-align: right;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>Summary stats</th>\n", "      <th>All trades</th>\n", "      <th>Short trades</th>\n", "      <th>Long trades</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Total number of round_trips</th>\n", "      <td>5822.00</td>\n", "      <td>1155.00</td>\n", "      <td>4667.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Percent profitable</th>\n", "      <td>0.50</td>\n", "      <td>0.52</td>\n", "      <td>0.49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Winning round_trips</th>\n", "      <td>2887.00</td>\n", "      <td>596.00</td>\n", "      <td>2291.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Losing round_trips</th>\n", "      <td>2917.00</td>\n", "      <td>553.00</td>\n", "      <td>2364.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Even round_trips</th>\n", "      <td>18.00</td>\n", "      <td>6.00</td>\n", "      <td>12.00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Summary stats                All trades  Short trades  Long trades\n", "Total number of round_trips     5822.00       1155.00      4667.00\n", "Percent profitable                 0.50          0.52         0.49\n", "Winning round_trips             2887.00        596.00      2291.00\n", "Losing round_trips              2917.00        553.00      2364.00\n", "Even round_trips                  18.00          6.00        12.00"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style>\n", "    .dataframe thead tr:only-child th {\n", "        text-align: right;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>PnL stats</th>\n", "      <th>All trades</th>\n", "      <th>Short trades</th>\n", "      <th>Long trades</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Total profit</th>\n", "      <td>$65404.25</td>\n", "      <td>$3560.10</td>\n", "      <td>$61844.15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Gross profit</th>\n", "      <td>$448803.34</td>\n", "      <td>$20608.45</td>\n", "      <td>$428194.89</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Gross loss</th>\n", "      <td>$-383399.09</td>\n", "      <td>$-17048.35</td>\n", "      <td>$-366350.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Profit factor</th>\n", "      <td>$1.17</td>\n", "      <td>$1.21</td>\n", "      <td>$1.17</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Avg. trade net profit</th>\n", "      <td>$11.23</td>\n", "      <td>$3.08</td>\n", "      <td>$13.25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Avg. winning trade</th>\n", "      <td>$155.46</td>\n", "      <td>$34.58</td>\n", "      <td>$186.90</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Avg. losing trade</th>\n", "      <td>$-131.44</td>\n", "      <td>$-30.83</td>\n", "      <td>$-154.97</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Ratio Avg. Win:Avg. Loss</th>\n", "      <td>$1.18</td>\n", "      <td>$1.12</td>\n", "      <td>$1.21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Largest winning trade</th>\n", "      <td>$9500.14</td>\n", "      <td>$1623.24</td>\n", "      <td>$9500.14</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Largest losing trade</th>\n", "      <td>$-22902.83</td>\n", "      <td>$-661.29</td>\n", "      <td>$-22902.83</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["PnL stats                 All trades  Short trades  Long trades\n", "Total profit               $65404.25      $3560.10    $61844.15\n", "Gross profit              $448803.34     $20608.45   $428194.89\n", "Gross loss               $-383399.09    $-17048.35  $-366350.75\n", "Profit factor                  $1.17         $1.21        $1.17\n", "Avg. trade net profit         $11.23         $3.08       $13.25\n", "Avg. winning trade           $155.46        $34.58      $186.90\n", "Avg. losing trade           $-131.44       $-30.83     $-154.97\n", "Ratio Avg. Win:Avg. Loss       $1.18         $1.12        $1.21\n", "Largest winning trade       $9500.14      $1623.24     $9500.14\n", "Largest losing trade      $-22902.83      $-661.29   $-22902.83"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style>\n", "    .dataframe thead tr:only-child th {\n", "        text-align: right;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>Duration stats</th>\n", "      <th>All trades</th>\n", "      <th>Short trades</th>\n", "      <th>Long trades</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Avg duration</th>\n", "      <td>13 days 03:21:49.653555</td>\n", "      <td>2 days 10:39:35.064935</td>\n", "      <td>15 days 18:53:36.628026</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Median duration</th>\n", "      <td>8 days 00:00:00</td>\n", "      <td>2 days 00:00:00</td>\n", "      <td>12 days 00:00:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Longest duration</th>\n", "      <td>84 days 00:00:00</td>\n", "      <td>13 days 00:00:00</td>\n", "      <td>84 days 00:00:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Shortest duration</th>\n", "      <td>0 days 00:00:01</td>\n", "      <td>1 days 00:00:00</td>\n", "      <td>0 days 00:00:01</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Duration stats                 All trades           Short trades  \\\n", "Avg duration      13 days 03:21:49.653555 2 days 10:39:35.064935   \n", "Median duration           8 days 00:00:00        2 days 00:00:00   \n", "Longest duration         84 days 00:00:00       13 days 00:00:00   \n", "Shortest duration         0 days 00:00:01        1 days 00:00:00   \n", "\n", "Duration stats                Long trades  \n", "Avg duration      15 days 18:53:36.628026  \n", "Median duration          12 days 00:00:00  \n", "Longest duration         84 days 00:00:00  \n", "Shortest duration         0 days 00:00:01  "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style>\n", "    .dataframe thead tr:only-child th {\n", "        text-align: right;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>Return stats</th>\n", "      <th>All trades</th>\n", "      <th>Short trades</th>\n", "      <th>Long trades</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Avg returns all round_trips</th>\n", "      <td>0.01%</td>\n", "      <td>0.00%</td>\n", "      <td>0.01%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Avg returns winning</th>\n", "      <td>0.13%</td>\n", "      <td>0.03%</td>\n", "      <td>0.15%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Avg returns losing</th>\n", "      <td>-0.11%</td>\n", "      <td>-0.03%</td>\n", "      <td>-0.13%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Median returns all round_trips</th>\n", "      <td>-0.00%</td>\n", "      <td>0.00%</td>\n", "      <td>-0.00%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Median returns winning</th>\n", "      <td>0.02%</td>\n", "      <td>0.01%</td>\n", "      <td>0.03%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Median returns losing</th>\n", "      <td>-0.01%</td>\n", "      <td>-0.00%</td>\n", "      <td>-0.02%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Largest winning trade</th>\n", "      <td>6.78%</td>\n", "      <td>1.37%</td>\n", "      <td>6.78%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Largest losing trade</th>\n", "      <td>-17.23%</td>\n", "      <td>-0.72%</td>\n", "      <td>-17.23%</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Return stats                    All trades  Short trades  Long trades\n", "Avg returns all round_trips          0.01%         0.00%        0.01%\n", "Avg returns winning                  0.13%         0.03%        0.15%\n", "Avg returns losing                  -0.11%        -0.03%       -0.13%\n", "Median returns all round_trips      -0.00%         0.00%       -0.00%\n", "Median returns winning               0.02%         0.01%        0.03%\n", "Median returns losing               -0.01%        -0.00%       -0.02%\n", "Largest winning trade                6.78%         1.37%        6.78%\n", "Largest losing trade               -17.23%        -0.72%      -17.23%"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style>\n", "    .dataframe thead tr:only-child th {\n", "        text-align: right;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>Symbol stats</th>\n", "      <th>AMD</th>\n", "      <th>CERN</th>\n", "      <th>COST</th>\n", "      <th>DELL</th>\n", "      <th>GPS</th>\n", "      <th>INTC</th>\n", "      <th>MMM</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Avg returns all round_trips</th>\n", "      <td>-0.00%</td>\n", "      <td>0.02%</td>\n", "      <td>0.02%</td>\n", "      <td>-0.03%</td>\n", "      <td>0.00%</td>\n", "      <td>0.02%</td>\n", "      <td>0.01%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Avg returns winning</th>\n", "      <td>0.20%</td>\n", "      <td>0.15%</td>\n", "      <td>0.10%</td>\n", "      <td>0.11%</td>\n", "      <td>0.10%</td>\n", "      <td>0.11%</td>\n", "      <td>0.10%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Avg returns losing</th>\n", "      <td>-0.19%</td>\n", "      <td>-0.13%</td>\n", "      <td>-0.07%</td>\n", "      <td>-0.15%</td>\n", "      <td>-0.09%</td>\n", "      <td>-0.06%</td>\n", "      <td>-0.09%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Median returns all round_trips</th>\n", "      <td>-0.00%</td>\n", "      <td>0.00%</td>\n", "      <td>0.00%</td>\n", "      <td>-0.00%</td>\n", "      <td>-0.00%</td>\n", "      <td>-0.00%</td>\n", "      <td>0.00%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Median returns winning</th>\n", "      <td>0.03%</td>\n", "      <td>0.02%</td>\n", "      <td>0.02%</td>\n", "      <td>0.02%</td>\n", "      <td>0.02%</td>\n", "      <td>0.01%</td>\n", "      <td>0.02%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Median returns losing</th>\n", "      <td>-0.02%</td>\n", "      <td>-0.01%</td>\n", "      <td>-0.01%</td>\n", "      <td>-0.02%</td>\n", "      <td>-0.01%</td>\n", "      <td>-0.01%</td>\n", "      <td>-0.01%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Largest winning trade</th>\n", "      <td>6.78%</td>\n", "      <td>6.14%</td>\n", "      <td>3.96%</td>\n", "      <td>2.78%</td>\n", "      <td>1.80%</td>\n", "      <td>2.40%</td>\n", "      <td>2.45%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Largest losing trade</th>\n", "      <td>-17.23%</td>\n", "      <td>-3.92%</td>\n", "      <td>-2.32%</td>\n", "      <td>-6.39%</td>\n", "      <td>-6.86%</td>\n", "      <td>-4.45%</td>\n", "      <td>-1.79%</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Symbol stats                       AMD   CERN   COST   DELL    GPS   INTC  \\\n", "Avg returns all round_trips     -0.00%  0.02%  0.02% -0.03%  0.00%  0.02%   \n", "Avg returns winning              0.20%  0.15%  0.10%  0.11%  0.10%  0.11%   \n", "Avg returns losing              -0.19% -0.13% -0.07% -0.15% -0.09% -0.06%   \n", "Median returns all round_trips  -0.00%  0.00%  0.00% -0.00% -0.00% -0.00%   \n", "Median returns winning           0.03%  0.02%  0.02%  0.02%  0.02%  0.01%   \n", "Median returns losing           -0.02% -0.01% -0.01% -0.02% -0.01% -0.01%   \n", "Largest winning trade            6.78%  6.14%  3.96%  2.78%  1.80%  2.40%   \n", "Largest losing trade           -17.23% -3.92% -2.32% -6.39% -6.86% -4.45%   \n", "\n", "Symbol stats                      MMM  \n", "Avg returns all round_trips     0.01%  \n", "Avg returns winning             0.10%  \n", "Avg returns losing             -0.09%  \n", "Median returns all round_trips  0.00%  \n", "Median returns winning          0.02%  \n", "Median returns losing          -0.01%  \n", "Largest winning trade           2.45%  \n", "Largest losing trade           -1.79%  "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style>\n", "    .dataframe thead tr:only-child th {\n", "        text-align: right;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>Profitability (PnL / PnL total) per name</th>\n", "      <th></th>\n", "    </tr>\n", "    <tr>\n", "      <th>symbol</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>COST</th>\n", "      <td>39.90%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>INTC</th>\n", "      <td>38.27%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>CERN</th>\n", "      <td>32.31%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MMM</th>\n", "      <td>22.15%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GPS</th>\n", "      <td>4.94%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AMD</th>\n", "      <td>-6.41%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>DELL</th>\n", "      <td>-31.15%</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Profitability (PnL / PnL total) per name        \n", "symbol                                          \n", "COST                                      39.90%\n", "INTC                                      38.27%\n", "CERN                                      32.31%\n", "MMM                                       22.15%\n", "GPS                                        4.94%\n", "AMD                                       -6.41%\n", "DELL                                     -31.15%"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style>\n", "    .dataframe thead tr:only-child th {\n", "        text-align: right;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>Profitability (PnL / PnL total) per name</th>\n", "      <th></th>\n", "    </tr>\n", "    <tr>\n", "      <th>symbol</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Consumer Goods</th>\n", "      <td>39.90%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Healthcare</th>\n", "      <td>32.31%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Construction</th>\n", "      <td>22.15%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Technology</th>\n", "      <td>5.65%</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Profitability (PnL / PnL total) per name       \n", "symbol                                         \n", "Consumer Goods                           39.90%\n", "Healthcare                               32.31%\n", "Construction                             22.15%\n", "Technology                                5.65%"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA9cAAAT3CAYAAADjfIORAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz\nAAALEgAACxIB0t1+/AAAIABJREFUeJzs3Xl0FFXi9vGnCSEQCEs2wABBtgTBhLAN2ygIhoDLgAsq\nMCgahYEZ1B/jgooKyCCOCsoiiKCDyEhUNiMIiIqDQgTZHVk1LA6YSFhCAgmGev/gTZtOupNObneH\nwPdzTs6hq27durfqVnU/VHeVzbIsSwAAAAAAoMwqlXcDAAAAAACo6AjXAAAAAAAYIlwDAAAAAGCI\ncA0AAAAAgCHCNQAAAAAAhgjXAAAAAAAYqlzeDfCF9PTM8m4CAAAAAKCCCwsLcjmPK9cAAAAAABgi\nXAMAAAAAYIhwDQAAAACAIcI1AAAAAACGCNcAAAAAABgiXAMAAAAAYIhwDQAAAACAIcI1AAAAAACG\nCNcAAAAAABgiXAMAAAAAYIhwDQAAAACAIcI1AAAAAACGCNcAAAAAABiqXN4NwO/Cw4N8vk5LNtlk\nObyW5DCtNHUVlp52WlLJfStuvYXb6Gy59LTTXt1+xbUhf35+XyWzfVlwW5jsj5LqL1yvs+lpaZlF\nlne3b4X7UVyd7ggPD3JZZ+H1ujO/YBsL7rvi1u9Nxe0XV2OhpHFZ2nKmbS0833SdJa3HneULjoXS\n1ufuGDM5TkwVNz4Kzi9NfQUVXNZVP71xnirYnsL7rrj3FXf76+o85Mv95uy84431u3O8luWcWFjh\ntrs6Z7mqv7i+O/usUlIdzt5LCyq4fFh4TYdt4OmxXFydxb0nlnU8lOaYdLb/y9p/Z9s5X+FzZX7f\nCq/T3c8IzpYvOC7K4zN1YYWPPXc+s5b0GeZS5ayvZW1/WT8nXiq4cg0AAAAAgCHCNQAAAAAAhgjX\nAAAAAAAYIlwDAAAAAGCIcA0AAAAAgCHCNQAAAAAAhowfxfXkk0/q2LFjeuedd/TnP/9Ze/bsUXJy\nssLDw12We/LJJ7VkyRKXdUZEROjzzz+XJFmWpQ8++EAfffSR9u/fr8qVKys6OlrDhg1Tly5dTJsP\nAAAAAIAxjz/n+tSpUxo3bpxmzJjhsszTTz+t0aNHS5KOHj2qO++8UzNnzlRMTIwkyc/PT5J04cIF\njRw5Utu2bdOoUaP0hz/8QXl5eVqyZIkeeOABvfTSS7rllls83QUAAAAAAErF4+G6QYMG+uyzz/TJ\nJ5/opptuclomKChIQUEXH56ek5MjSapVq5bCwsIcyi1YsEBfffWVPvroI0VHR9unP/7448rOztbE\niRPVs2dPBQYGerobAAAAAAC4zePhulOnTjp37pwmTJigzp07Kzg4uMx1LVq0SD179nQI1vn++te/\n6rbbblNAQIBJcwEAAAAAMOaVG5o988wzqlSpkiZMmFDmOnJycrR//37FxsY6nR8aGqqYmBj7V8gB\nAAAAACgvXgnXderU0dixY7VixQp99tlnZarj1KlTkqSaNWt6smkAAAAAAHic1x7F1adPH8XHx+v5\n55+3B+XSqF27tmw2m06ePOmF1gEAAAAA4Dlefc71c889p/Pnz2vSpEmlXrZKlSq65pprtH37dqfz\nU1NTdf/992vfvn2mzQQAAAAAwIhXw3VoaKjGjBmjJUuWaPPmzaVe/o477tAXX3yh3bt3F5k3d+5c\n7dixQxEREZ5oKgAAAAAAZebVcC1J/fr1U/fu3XX48OFSL3vXXXepU6dOuu+++5SUlKSDBw/q+++/\n17PPPqsPP/xQ48eP5zFcAAAAAIBy5/VwLUnjx4+3P9e6NPz8/DRr1iwlJiZqwYIF6tevn+6//34d\nOXJE//rXv9S3b18vtBYAAAAAgNKxWZZllXcjvC09PbO8mwAAAAAAqODCwlxfNPbJlWsAAAAAAC5n\nhGsAAAAAAAwRrgEAAAAAMES4BgAAAADAEOEaAAAAAABDhGsAAAAAAAwRrgEAAAAAMES4BgAAAADA\nEOEaAAAAAABDhGsAAAAAAAwRrgEAAAAAMES4BgAAAADAEOEaAAAAAABDhGsAAAAAAAwRrgEAAAAA\nMES4BgAAAADAEOEaAAAAAABDhGsAAAAAAAwRrgEAAAAAMES4BgAAAADAEOEaAAAAAABDhGsAAAAA\nAAwRrgEAAAAAMES4BgAAAADAEOEaAAAAAABDhGsAAAAAAAwRrgEAAAAAMES4BgAAAADAEOEaAAAA\nAABDhGsAAAAAAAwRrgEAAAAAMES4BgAAAADAEOEaAAAAAABDlcu7AfhdeHhQeTfBzpJNNlmyZFN6\n2ukytS2/DlzcFvkKbldnPDUO8teZvz72xeXrUtu/3mxPccdOQc6Oo8LHYXHzJblcT1h4Tad15C9f\n0jmz4LHpan5x7Ss4z1vbuvC6XG33wv0s+N5RuK3urLPw+SotLbPY9RVcriyK29blfUx5o1/FlS/I\nJqvIti9OeHiQ8TYr6/LFLedsXv600vav4PKS6/ODs2WKW3d5fPbzxL7KV9bjvbw5O8cVPrfmzy94\n/svfX2lpmUXGRcFtIRU/RtzZ76V5r3DW/uL648l95enxVJpj81LElWsAAAAAAAwRrgEAAAAAMES4\nBgAAAADAEOEaAAAAAABDhGsAAAAAAAwRrgEAAAAAMFTmcH3DDTcoKirK/te6dWv17NlTkydP1pkz\nZyRJR44ccShT+G/8+PGSpJSUFEVFRenYsWMu1xcVFaVly5aVtbkAAAAAAHiN0XOuH3zwQd17772S\npLNnz2rXrl168cUXtXXrVs2fP99ebubMmYqJiSmyfLVq1UxWDwAAAADAJcEoXAcGBiosLMz+ulGj\nRoqMjNTtt9+ujz76SH/84x8lSbVq1XIoBwAAAADA5cTjv7lu1aqV2rVrpxUrVni6agAAAAAALkle\nuaFZixYttHfvXm9UDQAAAADAJcfoa+Gu1KxZ035TM0l64IEHVKlS0Rz/4YcfqmnTpt5oAgAAAAAA\nPuOVcJ2VlaWgoCD760mTJqlVq1ZFytWvX98bqwcAAAAAwKe8Eq6///57XXPNNfbX4eHhioyM9Maq\nAAAAAAAodx7/zfXu3bu1detW3XLLLZ6uGgAAAACAS5LRlevs7Gylp6dLks6dO6cdO3bo5ZdfVocO\nHXTrrbfq6NGjkqRTp07ZyzmsvHJl1alTx/46JSXF4bV08eZo9erVkyTt2bNHX331lcP88PBwRUdH\nm3QDAAAAAAAjRuF6zpw5mjNnjiSpevXqioiI0IABA3TffffJz8/PXm7EiBFOl2/evLmSk5Ptrx9/\n/PEiZV544QXdeeedkqS5c+dq7ty5DvNvueUWvfzyyybdAAAAAADAiM2yLKu8G+Ft6emZ5d0EAAAA\nAEAFFxYW5HKeV55zDQAAAADAlYRwDQAAAACAIcI1AAAAAACGCNcAAAAAABgiXAMAAAAAYIhwDQAA\nAACAIcI1AAAAAACGCNcAAAAAABgiXAMAAAAAYIhwDQAAAACAIcI1AAAAAACGCNcAAAAAABgiXAMA\nAAAAYIhwDQAAAACAIcI1AAAAAACGCNcAAAAAABgiXAMAAAAAYIhwDQAAAACAIcI1AAAAAACGCNcA\nAAAAABgiXAMAAAAAYIhwDQAAAACAIcI1AAAAAACGCNcAAAAAABgiXAMAAAAAYIhwDQAAAACAIcI1\nAAAAAACGCNcAAAAAABgiXAMAAAAAYIhwDQAAAACAIcI1AAAAAACGCNcAAAAAABgiXAMAAAAAYIhw\nDQAAAACAocrl3QD8Ljw8yK1ylmyyyXJrfv6/i1umcJm0tMwibXG2fFpaptO2W7JJkkP5/LKu+uhO\n+0o7ryQmy/q6fmfb1JPlCy5XcOykp522z8vfd2UZU+6u01RZ+30pKM02Q8VSeJ8WPNdKRc+f7p7f\nfaHgMVXwnJ+vNG0v6/rdOS4qwrFfmu3ji/engvK3ccFzfkHufj4pvI6C7yf565Hk1ucMb733e0rB\nbViwX9LF7VVwvqvtWrBs/j4oWD48PMjptiovBfejq31QuC/5Cm8Ddz8PFq6v8LmotJ8rC7crv07J\n+efawn121Z/CTPaZu2Pf2TGWnnbaY+PF2Rh3Nr8sx2JxdTt7r6lIuHINAAAAAIAhwjUAAAAAAIYI\n1wAAAAAAGCJcAwAAAABgiHANAAAAAIAhr9wt/MKFC1q0aJGWLl2qH3/8UTk5OYqMjNRNN92koUOH\nKiAgQCkpKRoyZIjDcv7+/goLC1N8fLweffRRVa1aVZJ06tQpzZgxQ2vWrFF6erpq1aqlTp06adSo\nUYqMjPRGFwAAAAAAcJvHw/Vvv/2mYcOG6b///a9Gjhypzp07KyAgQFu3btXUqVO1ceNGvf322/by\nS5YsUVhYmH3Zbdu2acyYMTp37pzGjRsnSRo2bJhsNpsmT56siIgIHTt2TNOnT9c999yj5ORkBQcH\ne7obAAAAAAC4zePhet68eUpJSdHixYvVokUL+/QGDRooNjZWffr00bp161StWjVJUnBwsD1cS1L9\n+vW1YcMGrVixQuPGjdOePXu0detWLV++XFFRUZKkiIgITZ8+XV27dlVycnKRK+AAAAAAAPiSR8O1\nZVl677331K9fP4dgna9Ro0ZasWKFGjVqpG+//dZlPX5+fqpSpYr935L01VdfqUWLFrLZLj50vHr1\n6lq6dKnq1KnjyS4AAAAAAFBqHg3XR44c0bFjx9SpUyeXZYr7jfT58+e1YcMGLVu2TP369ZMkNWvW\nTD169NDLL7+sf//73+ratavat2+vbt26qXHjxp5sPgAAAAAAZeLRcP3rr79KUpGrybfeeqsOHz5s\nf33LLbfopptukiQlJCTYr0afPXtWVapUUZ8+fTR69Gh7+enTp2vRokVatmyZPvzwQyUlJcnPz093\n3nmnnnnmGfn7+3uyGwAAAAAAlIpHw3Xt2rUlXby7d0GzZs3S+fPnJUlPPPGEcnNz7fPeeusthYWF\nyWazqUqVKgoNDVXlyo7Nqly5sgYNGqRBgwbp9OnT+vbbb7V8+XK9//77qlGjhh577DFPdgMAAAAA\ngFLx6HOuGzVqpNDQUG3evNlh+lVXXaXIyEhFRkbaH6+Vr0GDBoqMjFSjRo1Ur169IsF69erVevPN\nN+2va9asqV69eun111/XzTffrHXr1nmyCwAAAAAAlJpHw7Wfn58GDRqkxYsX68CBA0Xm5+bmKiMj\no1R15j9265dffikyLygoSCEhIWVuLwAAAAAAnuDRcC1JDz30kDp37qx77rlHb7/9tvbt26fDhw/r\n448/1u23364ff/xR7dq1c7u+2267TRERERoyZIhWrFihI0eO6Pvvv9dbb72lJUuWaPjw4Z7uAgAA\nAAAApeLx51xXrlxZM2fO1LJly7R48WLNmjVL2dnZuuqqq9StWzdNmzZNjRs3VkpKilv11ahRQwsX\nLtQbb7yhqVOn6ujRo/L391dsbKzmzJmjjh07eroLAAAAAACUisfDtSTZbDb169fP/jgtZ/7whz9o\nz549btVXp04dPfXUU3rqqac81UQAAAAAADzGZlmWVd6N8Lb09MzybgIAAAAAoIILCwtyOc/jv7kG\nAAAAAOBKQ7gGAAAAAMAQ4RoAAAAAAEOEawAAAAAADBGuAQAAAAAwRLgGAAAAAMAQ4RoAAAAAAEOE\nawAAAAAADBGuAQAAAAAwRLgGAAAAAMAQ4RoAAAAAAEOEawAAAAAADBGuAQAAAAAwRLgGAAAAAMAQ\n4RoAAAAAAEOEawAAAAAADBGuAQAAAAAwRLgGAAAAAMAQ4RoAAAAAAEOEawAAAAAADBGuAQAAAAAw\nRLgGAAAAAMAQ4RoAAAAAAEOEawAAAAAADBGuAQAAAAAwRLgGAAAAAMAQ4RoAAAAAAEOEawAAAAAA\nDBGuAQAAAAAwRLgGAAAAAMAQ4RoAAAAAAEOEawAAAAAADBGuAQAAAAAwVLm8G4DfhYcHlaq8JZts\nskpdPi0t0622WLJJkmyyHP5d2jYWXC7/tbO6nPWntH10tWzhPhfc1oXXUbBsWHjNIm0tafu52o+F\nt2fB7ZqedrpUdZXEZLtdCgqPk4L9KbwdXW27wvLHtDvbvfByztrkrM2Fx1tJ+69wv9w51lwdJyX1\npeAxXZZ+F94PJkralu4s64vx7ewcmK/w9khLy3TYZs7GqzvrKTjNG30sXK+rPlQUrraTyXtWSceh\ns/OTdPG4Krz93HnPK64PruYXdy67VPahq2OmuH/nvy7tWCzN/nb3XOus/oLlnU1z99xfUv1l+Wzg\nzvtbSZ9PCrbB3TZ76xzu7rnT3c+1+Zy9tzlbb/68wsdh/rrCwmsWef8uDW++l7lqT0nHlbP3Lnfa\nWHjsOVuu8Lgs/Dm8YPnS7M9LEVeuAQAAAAAwRLgGAAAAAMAQ4RoAAAAAAEOEawAAAAAADBGuAQAA\nAAAw5PFwnZ6erlatWqlv375F5t1www2KiorSwoULnS6bmJioqKgoLVu2TJK0ePFiRUVF2f9atmyp\n9u3ba8iQIfr666893XQAAAAAAMrE4+F6+fLlatCggQ4cOKDNmzcXme/v769Vq1YVmX7y5Elt3Lix\nyHQ/Pz+tX79e69ev1xdffKF33nlHDRs2VGJiotauXevp5gMAAAAAUGoeD9dLly5V3759dc0112jR\nokVF5nfq1EmbNm1SRkaGw/Q1a9YoNjbWaZ1hYWEKCwtTvXr11Lp1a02cOFHdu3fXhAkTdP78eU93\nAQAAAACAUvFouN65c6f27t2rLl26KD4+XqtWrdKpU6ccysTFxSk0NFSfffaZw/SVK1c6/Sq5K0OG\nDNHRo0e1ZcsWj7QdAAAAAICy8mi4XrJkiUJDQ9WuXTv16dNHOTk5Wrp0qUMZm81mD975MjIytGnT\nJvXu3dvtdUVFRUmS9u7d65nGAwAAAABQRh4L17m5ufrkk08UHx+vSpUqqXHjxmrVqpWSkpKKlE1I\nSFBKSor9qvbq1avVtm1bhYaGur2+mjVrSpLOnDnjmQ4AAAAAAFBGHgvXn3/+uU6ePKmEhAT7tD59\n+mj//v1FbmzWrl071alTx35DstJ+JVz6PVTnh2wAAAAAAMpLZU9VtGTJEknS0KFD7dMsy5IkJSUl\nqX379vbpNptNvXv31qpVq9S9e3dt2bJFU6ZMKdX6/vvf/0qSWrZsadp0AAAAAACMeCRcp6ena/36\n9Ro4cKDuueceh3mTJ0/WqlWr9PTTTztMT0hI0NChQ7V06VJ17NhRwcHBpVrnwoUL1bBhQ8XFxRm3\nHwAAAAAAEx4J18uXL9eFCxeUmJioiIgIh3mJiYlav369li1b5jC9bdu2qlWrlqZPn14keBeWnp4u\nSbpw4YLS0tL0wQcf6PPPP9fs2bNls9k80QUAAAAAAMrMI+F66dKl6t69e5FgLUmdO3dWdHR0kRub\nVapUSb1799aiRYt04403uqw7Ly9P3bp1kyT5+fkpJCREMTExWrBggdq2beuJ5gMAAAAAYMQj4frj\njz8udn7hq9b5xo4dq7FjxzpM27Nnj/3ft912m2677TbzBgIAAAAA4EU2K/+uY5ex9PTM8m4CAAAA\nAKCCCwsLcjnPY4/iAgAAAADgSkW4BgAAAADAEOEaAAAAAABDhGsAAAAAAAwRrgEAAAAAMES4BgAA\nAADAEOEaAAAAAABDhGsAAAAAAAwRrgEAAAAAMES4BgAAAADAEOEaAAAAAABDhGsAAAAAAAwRrgEA\nAAAAMES4BgAAAADAEOEaAAAAAABDhGsAAAAAAAwRrgEAAAAAMES4BgAAAADAEOEaAAAAAABDhGsA\nAAAAAAwRrgEAAAAAMES4BgAAAADAEOEaAAAAAABDhGsAAAAAAAwRrgEAAAAAMES4BgAAAADAEOEa\nAAAAAABDhGsAAAAAAAwRrgEAAAAAMES4BgAAAADAEOEaAAAAAABDhGsAAAAAAAwRrgEAAAAAMFS5\nvBuA34WHB5WqvCWbbLKM1+upepzVm88mq8zr8UT78tviqp78dRReV+E+uNNOV3U5K5eedlqS833v\nrf3ibD353FlfwXY5W7Zgv0oSHh7ksG9K2k8ltcedaaVpnzsK7ruC28OZ9LTTpTrOnY3Hsm4bd5Z1\nd/u7KuerMeusPa6OW0nFHmelrVuS0tIy3Vo2f3wXHNuS+XYry3HiTSbbqCRl2WcFFXe+SkvLNDoe\nvcHdY76s72lXIlfHniePo8Lj3XTcloUn+1P4vFWa90xP993kfc8T63bF2Tbx1HtMWbn7Pu/snOip\n/pRW4XHr6c9o5YEr1wAAAAAAGCJcAwAAAABgiHANAAAAAIAhwjUAAAAAAIYI1wAAAAAAGCJcAwAA\nAABgyO1HceXm5mr+/PlKTk7WwYMHVa1aNcXExGjkyJG69tprJUlRUVEul+/evbtmz56tI0eOqGfP\nng7zbDabAgMD1axZMz388MPq2rWrJCklJUVDhgzRgw8+qL///e8Oy+TX895776l9+/ZudxgAAAAA\nAE9zK1yfPXtWQ4YM0YkTJzRq1CjFxsYqKytL8+fP16BBg/Tmm2+qU6dOkqRnn31W8fHxReoICAhw\neD1z5kzFxMRIkizL0v/+9z+9+uqr+stf/qKVK1cqIiLCXnbevHlKSEhQ69aty9xRAAAAAAC8xa1w\nPXXqVKWmpio5OVl169a1T3/xxRd1/PhxTZgwQcnJyZKkGjVqKCwsrMQ6a9Wq5VAuPDxckydPVvfu\n3bV27VoNGTLEPu+qq67SmDFjtHjxYvn7+7vdOQAAAAAAfKHE31zn5uZq8eLFuuOOOxyCdb5nn31W\nr7zyimw2m3FjqlSpIkny8/NzmP7cc8/pp59+0qxZs4zXAQAAAACAp5UYrg8fPqzTp08rNjbW6fyG\nDRsqOjrauCH5V8ADAwPVq1cvh3nNmzfXX/7yF82ePVt79uwxXhcAAAAAAJ5U4tfCT58+LUmqWbOm\nWxU+88wzev7554tMf+2113TdddfZXz/wwAOqVOlits/Ly5NlWWrXrp0WLFjg9Ar5Qw89pNWrV+up\np55SUlKSW20BAAAAAMAXSgzXderUkSSdPHnSrQofffTRIncDly7+prqgSZMmqVWrVjp79qzmzZun\nDRs2aMSIEWrVqpXTev39/TVp0iTdeeedmjt3rvr27etWewAAAAAA8LYSvxbeqFEjhYSEaPv27U7n\np6SkaPjw4UpLS5MkhYSEKDIysshftWrVHJYLDw9XZGSkoqOjNXnyZLVq1UrDhw9Xamqqy7Zcc801\nSkxM1PTp0/XTTz+VopsAAAAAAHhPieG6UqVK6t+/vz766CP98ssvDvMsy9Kbb76pn376ya07hLti\ns9n0wgsvyN/fX08++aQsy3JZduTIkWrYsKHGjRtX5vUBAAAAAOBJJYZrSRoxYoQaNGiggQMHKjk5\nWYcPH9bWrVs1atQobdq0SRMnTrTfLfzMmTNKT08v8nf8+PFi1xEaGqrHH39cW7du1cKFC12Wq1Kl\niv7xj3/o559/LkU3AQAAAADwHreec129enUtWLBAc+bM0fTp03X06FEFBQUpNjZWixYtUsuWLe1l\nx48fr/HjxxepIzAwUFu3bi12PbfffruWL1+uV199tcgdwwuKjY3Vfffdp3nz5rnTfAAAAAAAvMpm\nFfcd7MtEenpmeTcBAAAAAFDBhYUFuZzn1tfCAQAAAACAa4RrAAAAAAAMEa4BAAAAADBEuAYAAAAA\nwBDhGgAAAAAAQ4RrAAAAAAAMEa4BAAAAADBEuAYAAAAAwBDhGgAAAAAAQ4RrAAAAAAAMEa4BAAAA\nADBEuAYAAAAAwBDhGgAAAAAAQ4RrAAAAAAAMEa4BAAAAADBEuAYAAAAAwBDhGgAAAAAAQ4RrAAAA\nAAAMEa4BAAAAADBEuAYAAAAAwBDhGgAAAAAAQ4RrAAAAAAAMEa4BAAAAADBEuAYAAAAAwBDhGgAA\nAAAAQ4RrAAAAAAAMEa4BAAAAADBEuAYAAAAAwBDhGgAAAAAAQ4RrAAAAAAAMEa4BAAAAADBEuAYA\nAAAAwBDhGgAAAAAAQ5XLuwH4XXh4kEfrs2STTZZH6ywrV21JS8uU5Pm+u7PustQjyaGu/PY74439\nmb9+Z33y9f4u2J7C26G4vucvJ0npaadLLH+5y99vBbfhpb498tvsbF9Knm1/wXVcKuez0ip87ih8\nrBY+tgtuy3y+HhMFt7tUdNsXPnebjAVn5y5fvDc4W3fBse3q3OaLc21x63D2XlTc8oWPodKcry9V\nrsZbWTnbBu5sZ3cUPKYLr6fwudSdfe7sc4erfehqHLtSEceCOwqfzyTP7FdX5/RLjavznDtly4O7\n4/VSxZVrAAAAAAAMEa4BAAAAADBEuAYAAAAAwBDhGgAAAAAAQ4RrAAAAAAAMEa4BAAAAADBUqkdx\n5ebmav78+UpOTtbBgwdVrVo1xcTEaOTIkbr22mvt5c6cOaO5c+dq5cqV+vnnn1WzZk3FxcXpgQce\nUFxcnEOdx44d0+uvv66vvvpKJ0+eVGhoqK677jr97W9/U1hYmI4cOaKePXsW265JkybptttuK01X\nAAAAAADwGLfD9dmzZzVkyBCdOHFCo0aNUmxsrLKysjR//nwNGjRIb775pjp16qSTJ09q8ODBOn/+\nvB5++GHFxMQoIyNDSUlJGjx4sMaPH6/bb79dkpSTk6PBgwerefPmmjFjhkJDQ3Xw4EG9/PLL+vOf\n/6zly5erfv36Wr9+vb0dEydOVHp6uqZOnWqfFhR0eT6XDwAAAABQMbgdrqdOnarU1FQlJyerbt26\n9ukvvviijh8/rgkTJig5OVkTJkxQVlaWlixZotq1a0uSGjRooJiYGIWGhmrcuHFq166dGjdurK+/\n/lqHDx/W0qVLVaNGDUlSRESEXnvtNfXq1Uv/+c9/1LNnT4WFhdnXV7VqVfn7+ztMAwAAAACgPLn1\nm+vc3FwtXrxYd9xxh0Owzvfss8/qlVde0YkTJ7Ry5Urde++99mBd0IgRI+Tv76+kpCRJkp+fnyRp\n3bp1DuUaNmyoFStWqFOnTqXuEAAAAAAAvubWlevDhw/r9OnTio2NdTq/YcOGki6G5Ly8PLVt29Zp\nuSpVqqjiE9uoAAAgAElEQVRNmzbaunWrJKlz585q1aqV/u///k8zZsxQly5d1KFDB3Xp0kVNmzYt\nS38AAAAAAPA5t65cnz59WpJUs2ZNt8o5u2qdr3bt2srIyJB0MWy/9957evTRR1W5cmW9++67GjVq\nlLp27app06a51QEAAAAAAMqbW1eu69SpI0k6efJkseXyQ/WZM2dclsnMzFRwcLD9dbVq1TR8+HAN\nHz5cx48f14YNG/TBBx9o+vTpCgkJ0cCBA91pIgAAAAAA5catK9eNGjVSSEiItm/f7nR+SkqKhg8f\nrqioKPn7++u7775zWi43N1fbt29XmzZtJElJSUlatGiRfX5ISIhuvvlmvfPOO4qLiyvyW2wAAAAA\nAC5FboXrSpUqqX///vroo4/0yy+/OMyzLEtvvvmmfvrpJ4WFhal///6aO3euTpw4UaSeuXPnKjs7\nWwMGDJAkHThwQNOmTVN2drZDOZvNpqCgIIWEhJS1XwAAAAAA+Ixb4Vq6eKfvBg0aaODAgUpOTtbh\nw4e1detWjRo1Sps2bdLEiRNls9n0xBNPKDw8XHfffbdWrlypn3/+WT/88INeeOEFzZgxQ88995yu\nvvpqSdLQoUNlWZaGDBmiL7/8Uj///LO2b9+uV155RZs3b9bQoUO91nEAAAAAADzF7edcV69eXQsW\nLNCcOXM0ffp0HT16VEFBQYqNjdWiRYvUsmVLSVKNGjX07rvvav78+Zo5c6YOHTqk6tWrq3379lqw\nYIH9K+GSVK9ePfvvq8eNG6f09HQFBgaqffv2+ve//63mzZt7vscAAAAAAHiYzbIsq7wb4W3p6Znl\n3QQAAAAAQAUXFhbkcp7bXwsHAAAAAADOEa4BAAAAADBEuAYAAAAAwBDhGgAAAAAAQ4RrAAAAAAAM\nEa4BAAAAADBEuAYAAAAAwBDhGgAAAAAAQ4RrAAAAAAAMEa4BAAAAADBEuAYAAAAAwBDhGgAAAAAA\nQ4RrAAAAAAAMEa4BAAAAADBEuAYAAAAAwFDl8m7ApcCyLGVnZysn51x5N+WyEhBQVYGBgbLZbOXd\nFAAAAADwqiv+yvW5c+e0a9cOnTx5orybctk5efKEdu3aoXPn+E8LAAAAAJe3K/rKtWVZ2rdvj1q3\njuHqqpdcdVWEdu3awTYGAAAAcFm7oq9cZ2dnKzg4hNDnRTabTcHBIcrOzi7vpgAAAACA11zR4Ton\n55yqVatW3s247FWtWo3fswMAAAC4rF3R4Rq+wRcDAAAAAFzuCNcAAAAAABi6om9o5kpenpSaWj6X\nWxs3tuTnVy6rBgAAAACUEeHaidRUmzp3rlEu696w4YyaNrVKtcwdd9yim2/+k+67L9FLrQIAAAAA\nFIevhQMAAAAAYIhwDQAAAACAIb4Wfpn77bfftGjRe/r446VKS/tFDRo01L33JqpnzxslSXPnztb3\n3+9STEysliz5QJmZZ9SuXXs98cQzCg0NkyQdOnRQU6a8pJ07t6t27TpKTByuSZPGa8qUGWrbtn15\ndg8AAAAALgmE68vc9OlT9NlnqzR69JNq2rS5vvxyrZ5//in5+VVS9+49JUlbt25WYGA1TZkyU5mZ\nmXr22Sf11luz9OSTY3X27Fk98sgINWvWQm+++Y6OH/9VL730D+Xl5ZVzzwAAAADg0sHXwi9jWVln\ntGTJh3rwwRHq0aOXGjWK1JAh96tHj15asOBf9nIXLlzQU089pyZNmio2to169rxRu3btlCR9/vka\nnTlzRs8+O0FNmjRThw6d9Mgjj5VXlwAAAADgkkS4vowdPJiqvLw8XXttjMP02Ng4/fjjAfvrkJBQ\nBQZWt7+uXr2GfvvtvCRp797dioxsrBo1fr97ekxMGy+3HAAAAAAqFsL1ZSwgoKrT6RcuXFDlyr//\nIsDf379IGcu6+DgwPz8/+78BAAAAAM4Rri9jDRo0kL+/v3bs2O4wfceObWrc+Gq36mjatLkOHkzV\nmTNn7NP++99dHm0nAAAAAFR03NDsMnHkyGFt3PiNw7SgoJq6665BeuutN1SrVi01a9ZCX375udat\n+1zPP/8Pt+rt1au35s6drYkTn9ODD/5FJ0+e1JQpL0mSbDabx/sBAAAAABWRzboCvvObnp7pdHpG\nxnFJUnBwiMP0vDwpNdX3wfGtt6qodm1LlUr5fYLVqxN09uz/ikwPDo5T165vac+eWTp0aJlyc08q\nKKiJmjdPVEREvCRp9+6ZOnLkE/Xq9Yl9ucLTTp/ep1M7hmnXiQxVrlpP9913h2bOfF2zZs3TypUl\nP4rr7Nnjmj49QFKIyzKWbLLpsh+KPlURtmlFaKOvWbp47qnI26Xgfs3vT0HpaafLVG94eJBRu+Bd\nvj6eK+L5oyK1uTzamn++KHiOCA8PUlqa4+c4zgUVU/6YsmRTetppp/s2X8F9XPB9pPDY8HZbva3w\ne37B1wW3V8G2uPqc4Is2u1pHSet2tZ+lovu6rJ8RfCkszPXY48q1E35+UtOmvn/zCw4u2zrj4z8t\ndn7Lln9Ty5Z/czovOnqEoqNHuJyWnf0/5eQc11vdrpckjdNzion5TpIUFhZepvYCAAAAwOWG31yj\nWHl55/TNN8OV9NMB/S87SydO7NT06VPUpk1b1a1br7ybBwAAAACXBK5co1hBQU3Uvv2L+mjvRE3Z\ntUNW5RT16tVNI0Y8XN5NAwAAAIBLBuEaJYqISNCbESmSLn4t/LHHcsu5RQAAAABwaeFr4QAAAAAA\nGCJcAwAAAABgqEzh+oYbblBUVJQWLlzodH5iYqKioqK0bNkyLV68WFFRUerWrZsuXLhQpOz27dsV\nFRWlG2+8sUz1AwAAAABQ3sp85drf31+rVq0qMv3kyZPauHGjwzSbzaYTJ05oy5YtRcqvWLFCNlvR\n56CWpn4AAAAAAMpTmcN1p06dtGnTJmVkZDhMX7NmjWJjYx1XUqmSOnbsqE8/dXwes2VZWrVqldq1\na2dUPwAAAAAA5anMdwuPi4vT3r179dlnn2nAgAH26StXrlTfvn21efNmh/IJCQmaOXOmnn76afuV\n6q1bt+r8+fPq0KGDPvnkE6P6PSovT36pP3qvfhdCM6roeO0msir5+XzdAAAAAICyK3O4ttlsio+P\n16pVq+zhNyMjQ5s2bdJLL72k8ePHO5S/8cYbNW7cOG3fvl1t2rSRdDEo9+7dW35+RcNkaev3JL/U\nHxXcuejVdG97XNJLD+zUr8HNy7T8oUPL9dNP7yszc7+kSqpVq4WaNBmoiIgESdKyZTFq2/Yfatjw\nZo+12bIsHT78serW7aqAgBCP1QsAAAAAFYnR3cITEhKUkpKiU6dOSZJWr16ttm3bKjQ0tEjZ4OBg\ndezY0f47asuy9Omnn6pv374eqf9Kl5r6oXbunKSrr75L3bt/qOuue0/h4d20efMTOnTIezd+y8jY\npq1bn1Fe3jmvrQMAAAAALnVG4bpdu3aqU6eO1q5dK+n3r2y7kpCQYA/X3333nWw2m9PfW5e1/itZ\nauoHioy8XY0a/Uk1ajRSzZpNFRX1kBo2vFk//vieF9dsebFuAAAAAKgYyvy1cOniV7d79+6tVatW\nqXv37tqyZYumTJnisnx8fLzGjx+vnTt36tNPP1VCQoLTO4WXtf4rmc3mp4yMrTp/PlP+/kH26a1a\njVZe3ln768zMA1q//n6dOLFDAQGhiooapsjI/vb5Bw8u0YED85WVdUTVqoWrSZPBatLkHknS8kOp\n+mzvLbLZOmjNmpXq1u16rV+/QpK0Zk0fRUUNV3T0CB/1GAAAAAAuHUZXrqWLV6O/+eYbLV26VB07\ndlRwcLDLssHBwerQoYNWrVql1atXu3UVujT1X8maNbtPJ07s0qpVPZWS8jft3/+OTp3arYCAYAUG\nRtjL/fTTIl199V264Yalqlevu7ZtG6esrCOSpP3752vnzklq2nSwevT4UM2a3afvv39V+/f/y758\nVtZBZWdnad689zRo0L3q2PE1SdJ11y1Us2b3+bTPAAAAAHCpMLpyLUlt27ZVrVq1NH36dD399NMl\nlk9ISNA///lP1a5d235jM0/Wf6WKiIhXtWrhOnBggdLSNujYsXWSpFq1otW27T9Us2YzSdLVV9+t\niIjekqTo6BH66aeFOnVqtwIDI7R//9tq0mSQIiNvlyTVqBGprKwj2r//bVlNu9vXdd99DygiooEk\nqUqVi1fFAwLqqHLlQF91FwAAAAAuKcZXritVqqTevXsrNzdXN954Y4nl4+Pjde7cOfXu3dsr9V/J\ngoPbqEOHl9W371e67rr31KLFQ8rK+lkbN47QhQvnJV0MzPmqVKkpScrLy1FuboZyco4rONjxPzxC\nQtopJydDGTk5/3+KTfXrRwgAAAAA8DubZVmX/R2p0tMznU7PyDguSQoOdnyElN+BfeXyKC5Jytjw\nnfKalu5RXL/8ckzvvvuOhg5NVEiI453Uv/12o/7v//6qt96ar8TEIRo7drx69/796/jdurXX2LHj\n9cc/Xq/4+Os1efIUde36R/v89evX6cknR2vFirVav/4rTZ78gtatS7HP3759m0aOTNQHHyxX/fpX\nOe+Ti+0MAAAAABVJWFiQy3nGV65R/gICqio5eanWrPm0yLwaNWrIZrOpdu3if6seGFhd4eF1tXPn\ndofpO3ZsU0hIiIKCajpdrpj70QEAAADAFcP4N9cof7Vr19bAgUM0e/YMZWVlqXv3ngoICNCBA/s1\nZ84b6tPnZtWrV6/EeoYMuV/Tpr2qiIgGiotrpy1bNuvDD5OUmDjM5V3dAwOrS5L27t2joKCaqlGj\nhkf7BgAAAAAVAeH6MvHQQyPUoEFDffzxEiUlLVRubq4iIhqoT5+bddddg9yqo1+/25Wbm6N3331b\nr746WVddFaG//e1R9e9/h8tlGje+Wt2736Dnn39K/frdoYcfHu2pLgEAAABAhcFvruXkt8B5efJL\n/dHbzXIqr3ETyc+vXNbtLfzmGgAAAMDloLjfXHPl2hk/v1LfVAwAAAAAcOXihmYAAAAAABgiXAMA\nAAAAYIhwDQAAAACAIcI1vO7yv2UeAAAAgCvdFR2uAwKq6uzZs+XdjMveuXNnFRBQtbybAQAAAABe\nc0WH68DAQGVkHNcV8DSycmNZljIyjiswMLC8mwIAAAAAXnNFP4rLZrOpefMo7dq1Q8HBIapatZps\ntvJu1eXBsi5esc7IOK7mzaNkY8MCAAAAuIzZrCvgsm16emax8y3LUnZ2tnJyzvmoRVeGgICqCgwM\nJFgDAAAAuCyEhQW5nHdFX7nOZ7PZVL16dVWvXr28mwIAAAAAqICu6N9cAwAAAADgCYRrAAAAAAAM\nEa4BAAAAADBEuAYAAAAAwBDhGgAAAAAAQ1fEo7gAAAAAAPAmrlwDAAAAAGCIcA0AAAAAgCHCNQAA\nAAAAhgjXAAAAAAAYIlwDAAAAAGCIcA0AAAAAgCHCNQAAAAAAhgjXhn799Vc98cQT6tatm9q3b68H\nHnhAe/futc9fv369/vSnPykmJka33HKL1q1b57D88ePH9fDDD6t9+/bq3Lmz/vnPf+q3335zuq7D\nhw+rbdu2Wrx4sVf7BO/x9njJyspSdHS0oqKiHP6WLVvmsz7Cc3xxflm7dq1uvfVWxcTEqE+fPlqx\nYoVP+gbP8+Z4OXLkSJHzSv5fz549fdpPeIa3zy+//fabpk2bph49eiguLk4DBw7Uli1bfNY/eJa3\nx0tubq5effVV3XDDDWrbtq2GDx+ugwcP+qx/8CzT8ZIvNzdXt956q9PPse+884569Oih2NhYDR06\nVKmpqd7qTulYKLO8vDzrrrvusgYMGGBt377d2rdvnzVq1Circ+fOVkZGhrVv3z6rdevW1syZM639\n+/dbU6ZMsVq1amXt3bvXXsc999xjDRw40Prhhx+sL7/80urUqZP16quvOl3X3XffbbVo0cL66KOP\nfNlNeIgvxsu2bdusqKgo69ChQ1ZaWpr979y5c+XRZRjwxXj55ptvrJYtW1qzZ8+2Dh48aM2ZM8dq\n2bKltW3btvLoMgx4e7z89ttvDueUtLQ06+uvv7auueYaKykpqby6jTLyxfllxowZVteuXa3//Oc/\nVmpqqjV27Firbdu21i+//FIeXYYBX4yXsWPHWh06dLA++eQTa//+/dbYsWOtLl26WMePHy+PLsOA\nJ8aLZVlWZmamlZiYaLVo0cJaunSpw7ykpCQrLi7OWrlypbV7925r2LBhVs+ePa2cnBxfdtUpwrWB\n77//3mrRooW1f/9++7ScnBwrNjbWWrJkiTV27Fhr8ODBDssMHjzYeuaZZyzLsqwtW7ZYLVq0sA4d\nOmSfv3jxYisuLq7I4Jg1a5Y1cOBAq2XLloTrCsoX4yUpKcnq3r27D3oDb/PFeBk0aJD12GOPOdSR\nmJhozZkzx1vdgpf48v3Isizr/Pnz1s0332w98sgjXuoRvMkX46V///7WpEmT7POzs7OtFi1aWKtW\nrfJm1+AF3h4vJ0+etKKiohz+oy4vL8+Kj4+3pk2b5uXewdNMx4tlWdbXX39t9ezZ0+rfv7/TcB0f\nH2+9/vrr9tdnzpyx2rRpYy1fvtxLvXIfXws3UL9+fc2ePVtXX321fZrNZpMknTp1Sps3b1bHjh0d\nlvnDH/6gzZs3S5I2b96siIgINWzY0D6/Y8eOysrK0g8//GCf9sMPP2jevHl68cUXvdkdeJkvxsu+\nffvUpEkTb3cFPuDt8ZKdna3vvvtOffv2dahjzpw5SkxM9Fa34CW+ej/K9/777+t///ufxowZ443u\nwMt8MV6Cg4P1xRdf6PDhw8rLy9OiRYvk7++vqKgob3cPHubt8XLw4EFZlqV27drZ51eqVEnR0dH6\n9ttvvdk1eIHpeJGkzz//XP369dP7779fpP7jx48rNTXVoY7q1aurdevWDnWUF8K1gTp16qh79+6q\nVOn3zfjuu+/q3Llz6tatm44dO6a6des6LBMeHq5jx45Jkn755ReFh4cXmS9JR48elXTxtwaPPfaY\nHnnkEYeTEioeX4yXffv2KTs7W3/+85/VpUsX3X333S5/x4JLm7fHy6FDh3ThwgVZlqXhw4erc+fO\nuv3227V27Vov9wze4IvzS77c3Fy98cYbuvfee4ssg4rBF+NlzJgxqly5snr16qVrr71WL7/8sqZO\nnarIyEhvdg1e4O3xkr9sfvl8P//8szIyMjzeH3iX6XiRpGeeeUZ//etfVaVKlSL155crqY7yQrj2\noLVr1+rVV1/V0KFD1bRpU507d67IoKhSpYpycnIkSWfPnlVAQIDDfH9/f9lsNnuZV155RXXr1tU9\n99zjm07AZ7wxXvbt26cTJ05o2LBhmjNnjuLi4jRs2DBt2LDBN52C13h6vJw5c0aSNHbsWF1//fWa\nN2+eevTooZEjRzJeLgPeOL/k++STT+z/iYfLgzfGy5EjR1SlShVNmTJFSUlJGjBggJ544gnt3r3b\nN52C13h6vNStW1edOnXS5MmTlZqaqvPnz2v+/Pn64YcfdP78eZ/1C95R2vFSkrNnz0pSkTFVmjq8\nqXJ5N+BysXjxYo0dO1Z9+/bVY489JuniTi98UsjNzVW1atUkSVWrVlVubq7D/PPnz8uyLAUGBmrj\nxo1aunSpli9f7ptOwGe8MV4kac2aNZJkX6ZVq1bat2+f/vWvf6lz585e7RO8xxvjxd/fX5I0YMAA\n+3/etWzZUjt37mS8VHDeOr/kW7ZsmeLj41WnTh0v9gK+4o3xkpWVpdGjR2vcuHH2n560bt1ae/fu\n1YwZMzRt2jQf9Aze4K3zy0svvaQnn3xSCQkJ8vPz03XXXaf+/fs7/VkKKo6yjJeSVK1a1b5MWevw\nJq5ce8Abb7yhMWPG6O6779ZLL71k/xpE/fr1lZaW5lA2LS3N/jWGevXqKT09vch86eJXHZYtW6bM\nzEwlJCQoLi5OcXFxysvL03PPPcdvIiswb40X6WKoLnxiadGiRZGvdaLi8NZ4yS/XokULhzJNmzbV\nzz//7JW+wPu8eX6RpNOnT2vTpk1FfquPislb4+XAgQPKzMxU69atHcpce+21PF6pAvPm+aVu3bp6\n++23tWnTJn3zzTd64403dPLkSTVq1Mjb3YKXlHW8lKR+/fqS5HRMuVuHNxGuDc2ZM0dTp07VqFGj\nNHbsWPsP9iWpXbt22rRpk0P5lJQUtW/f3j7/8OHDDsEnJSVF1atXV3R0tP7+979r5cqVWrp0qf3P\nz89Po0aN0sSJE33TQXiUN8fLr7/+qvbt22v16tUOdezatUvNmjXzYq/gLd4cL/Xq1VNERIR27tzp\nUMe+ffu4v0MF5c3xkm/btm2yLKvIzWhQ8Xj7/CJJe/bscahj3759aty4sZd6BG/y5nixLEsPPvig\nvv76awUFBalWrVo6c+aMNm7cqK5du/qmg/Aok/FSkpCQEDVu3NjhZndZWVnatWuXOnTo4JkOmCiv\n25RfDn744QerZcuW1pgxY4o8/zMrK8vavXu31apVK+u1116z9u/fb02dOtW69tpr7bemv3DhgjVg\nwADrrrvusnbt2mV/7l/BW8sXxqO4Ki5fjJfExETrhhtusL755hvrwIED1uTJk63WrVsXeXYgLn2+\nGC9JSUlW69atraSkJOvgwYPW7NmzrejoaGvjxo3l1W2Uka/ej2bNmmXFx8eXRxfhQb4YL6NGjbKu\nv/56a926dVZqaqo1bdo0q2XLltb27dvLq9soI1+Ml9GjR1u33nqrtWvXLmv37t3W4MGDrZtuuumS\neG4xSsd0vBTm7FFcCxcutNq0aWMlJydbe/bssYYNG2bFx8dfEuOFcG3glVdesVq0aOH0b8aMGZZl\nWdYXX3xh9e3b12rdurV16623Wl9//bVDHWlpadaIESOs2NhYq0uXLtYrr7xi5eXluVwn4bri8sV4\nOXXqlPXcc89Z3bp1s1q3bm3ddddd1qZNm3zaT3iGr84vSUlJVnx8vNWqVSvr5ptvtlavXu2zPsJz\nfDVeJkyYYN15550+6xe8wxfjJSsry5o8ebLVo0cPq02bNtaAAQOsDRs2+LSf8AxffX55/PHHrY4d\nO1odO3a0Ro8ebaWnp/u0n/AMT4yXgpyFa8u6+J+9Xbt2tdq0aWPdf//9Ds9RL082y7Ks8r56DgAA\nAABARcZvrgEAAAAAMES4BgAAAADAEOEaAAAAAABDhGsAAAAAAAwRrgEAAAAAMES4BgAAAADAEOEa\nAAAAAABDhGsAAAAAAAwRrgEAAAAAMES4BgAAAADAEOEaAAAAAABDhGsAAAAAAAwRrgEAAAAAMES4\nBgAAAADAEOEaAAAAAABDhGsAAAAAAAwRrgEAAAAAMES4BgAAAADAEOEaAAD8P/buPDzK8tD//2eS\nWTJJyD5Z2MnOFrIJCqip9rToORw5Vu0CX6m1VpGq6PFQu3hsL6nlFKuiWOlify1YPdYW6GJ79LQe\nUUSQCRDWrOyQZCYhgYRsk2R+f6BpI8iSTOaZmbxf18WlfWaeeT73xKR88txz3wAAYJAo1wAAAAAA\nDBLlGgAAAACAQaJcAwAAAAAwSJRrAAAAAAAGiXINAAAAAMAgUa4BAAAAABgkyjUAAAAAAINEuQYA\nAAAAYJAo1wAAAAAADBLlGgAAAACAQaJcAwAAAAAwSJRrAAAAAAAGiXINAAAAAMAgUa4BAAAAABgk\nyjUAAAAAAINEuQYAAAAAYJAo1wAAAAAADBLlGgAAAACAQaJcAwAAAAAwSJRrAAAAAAAGiXINAAAA\nAMAgmY0O4A9ud4vRES5bfHykmpraDLn2hg2/kSTNm3ebT1/XyDENhVAbjxR6Y2I8/jGYnxmBOqaB\nutzxOBwjhjBNcPPl/3eH2n9ngY732794v/2L99u/AvH9vtD/d3PnOkCZzeFGR/C5UBtTqI1HCr0x\nMZ7AF2pjCrXxhAq+Lv7F++1fvN/+xfvtX8H2flOuAQAAAAAYpIAr16+99po++9nPKi8vTzfffLPe\nf//9vsc2bdqkm266SXl5eZo7d642btxoYFIAAAAAAM4KqHK9fv16fe9739Ndd92lP/7xj7riiit0\n77336tixY6qurtaiRYs0Z84crV+/Xtdff70WL16sqqoqo2MDIaG7p1der9foGAAAAEBQCpgFzbxe\nr5577jnddddduuWWWyRJ3/jGN7Rlyxbt2LFD27ZtU35+vhYtWiRJWrJkiUpLS7VmzRo9/vjjRkYH\ngt6umkb9eMNu9fZKMVEWxUZZFRNpVWy0VTFRVsVG2T7859k/MVFWRVjDZTKZjI4OAAAABISAKdcH\nDhzQ8ePHdeONN/YdCwsL0+9//3tJ0gsvvKAbbrih3zkzZszQ66+/7tecQKg53dalX/x5v5Ji7bpq\naprqGlp16kyXmlo6dai+RS1nPOo9zx1tqzmsr3Cf+0+bYqOsSoyNUPwImwGjAgAAAPwrYMr1oUOH\nJEmnT5/W7bffrqqqKqWnp+vf//3fVVhYqLq6OqWkpPQ7Jzk5WXV1dQakBUKD1+vVmv+pUFuHR//+\n+XwVTk47Z/ubXq9Xre0enW7t0qkzXTp95uw/T53p7Pt3d3O7qo+fUmubR/9Yw8NMJn39c1OVn5nk\n34EBAAAAfhYw5bq1tVWS9Mgjj+j+++9Xenq6XnvtNS1cuFAbNmxQR0eHrFZrv3OsVqs6Ozsv+trx\n8ZFBt4y7ZNz+pxZL+JBdP9T2dA328fxt2xFtr3Trjn+ZpMLJaZLOP6aUc46cX09P79m73qc71NTS\nqV/+aa9e/muVZheOkd1mzI+bYP8afVwgjmewPzMCcUyDEWrjAQAAlyZgyrXFYpEk3XPPPZo7d64k\nadKkSSotLdUrr7wim80mj8fT75yuri7Z7faLvnagbTx+KRyOEefcQfQXj6dHknx+fSPHNBSCfTwN\np9q1et0uZY+O1axJKXK7W3w2phhbuGJskZr/6Ww98VKpfr5+l75wfZYPUl+eYP8afVygjmcwPzMC\nde7dV7MAACAASURBVEwDdbnjoYgDABA6Ama18OTkZElSdnZ23zGTyaT09HQdO3ZMaWlpcrlc/c5x\nuVznTBUHcHG9Xq9e/NN+eSXd+S+TFBY2NAuTZY6OVUn+SP3VeUyH60KnQAEAAAAfFzDlevLkyYqM\njNTu3bv7jnm9XtXU1GjMmDEqKirStm3b+p2zdetWFRcX+zsqEPTe/OCoKo4260ufzpIj7uKzPwbj\ncyUZio60aM0b5ertZasvAAAAhKaAKdd2u10LFy7UM888ozfffFOHDh3SD37wAx05ckRf/OIXtWDB\nAjmdTj377LOqqanRypUrVVZWpoULFxodHQgqx9ytWvdOjQqykjR7atqQXy8qwqIvXJ+pg7Ut+r8d\nx4f8egAAAIARAuYz15L0wAMPyG6364knnlBjY6MmTpyoX/ziF0pPT5ckrVq1SitWrNDPfvYzpaen\na/Xq1crIyDA4NRA8unt69bM/7lOkzayFc3L9tk/1jIkpem93nX63sUaF2Q625wIAAEDICahybTKZ\ndPfdd+vuu+8+7+MlJSUqKSnxbygghPx+00EddbXqvs9NVUyU9eIn+IjJZNL/+0y2Hn3xA73y10rd\n+29T/XZtAAAAwB8CZlo4gKFVdaxZf95yWFfnpakgy+H36yfHR2ruzPFyVri1s7rB79cHAAAAhhLl\nGhgG2ju79fM/7VNiTIQhW2J9ZM6MsRqZFKVfv1mhzq4ew3IAAAAAvhZQ08IBDI1X36pWQ3OHvjG/\nUHabcd/25vAw3f7ZHC3/9Xb9/r2Duu1TmYZlATB8/M/7h9TS2nHB55Tkj/JPGABAyOLONRDidlY1\n6J2yE5pz5Vhlj4kzOo6yx8TpmmlpevODozpSz97XAAAACA2UayCEnW7r0i//sl+jHdGaNzvd6Dh9\nbinJVJTdrDVvVLD3NQAAAEIC5RoIUV6vV2v+p0Jtnd362txJspgD59s92m7RF67P0oETp7VxJ3tf\nAwAAIPgFzt+2AfjU5j112l7p1s3XZGh0crTRcc5x5aQUTRofr99urFFza6fRcQAAAIBBoVwDIaih\nuV2//t9KZY+J02euGGN0nPM6u/d1jjzdXr3y1yqj4wAAAACDQrkGQkyv16sXX98vSfrqP09UWJjJ\n4ESfLCUhUnNnjtO2cpd21TQaHQcAAAAYMMo1EGLe/OCoKo4260ufzlZSnN3oOBc1Z8Y4pSVG6qU3\nK9TpYe9rAAAABCfKNRBCjrlate6dGhVkJWnW1FSj41wSi/ns3tcNpzr0h/cOGh0HAAAAGBDKNRAi\nPN29+tmf9inSZtbCG3JlMgXudPCPyxkbr9l5Z/e+PuZqNToOAAAAcNko10CI+P2mgzrqatWXb5io\nmEir0XEu222fypTdZtav3ihXr5e9rwEAABBcKNdACKg82qy/bDmsa6alKT8ryeg4AxJtt+jz12Wq\n5vhpvbPzhNFxAAAAgMtCuQaCXHtnt37+p31KjI3Q56/LMjrOoMyckqrcsXH67ds1OsXe1wAAAAgi\nlGsgyL36VpUaT3forrmTZLeZjY4zKCaTSf/vsznq6u7Rf79VbXQcAAAA4JJRroEgtrOqQe+U1eqG\nGeOUNTrO6Dg+kZYYpX++ary27qvXngPsfQ0AAIDgQLkGgtTpti798i/7NSY5WvOunmB0HJ+68cpx\nSkmI1No3K9TF3tcAAAAIApRrIAh5vV796i/lauvs1l1zJ8kcHlrfyh/tfe1u7tAfNx8yOg4AAABw\nUaH1N3JgmHhvd512VDXo5msyNNoRbXScITFxXLxmTUnV/2w9ouNu9r4GAABAYKNcA0GmqaVTL/+1\nUjlj4vSZ6WOMjjOkbrvuo72vK9j7GgAAAAGNcg0EmY07j6uzq0dfvjFXYSaT0XGG1IhIq279VIaq\nj53Spl21RscBAAAAPhHlGggivV6v3ttdq0kTEpQSH2l0HL+YPTVN2WPi9Ju3qnXqTJfRcQAAAIDz\nolwDQWT/oSY1nu7U1XlpRkfxG5PJpIVzctTp6dGrb1UZHQcAAAA4L8o1EETe3XVCURFmFWQ5jI7i\nV2mJUbrxynHasrdeew+dNDoOAAAAcA7KNRAkWts92l7ZoCsnp8piHn7fuv8yc5yS4+1a+0aFPN3s\nfQ0AAIDAMvz+hg4Eqa376tXd0zuspoT/I4s5XAv+KVuupnZt3ecyOg4AAADQD+UaCBLv7jqhcSkj\nNDZlhNFRDDN5QoJS4u3avIeVwwEAABBYKNdAEDhc16Ij9a2aPUzvWn/EZDJp5pRUlR9pVkNzu9Fx\nAAAAgD6UayAIbNpVK3N4mK6cnGJ0FMNdNTlVkvT+3jqDkwAAAAB/R7kGApynu0db9tWpKMehqAiL\n0XEMlxRnV+7YOG3eUyev12t0HAAAAEAS5RoIeDuqGnSmo3vYTwn/RzOnpKm+qV01x08bHQUAAACQ\nRLkGAt67ZSeUGBOhiePijY4SMIpyHLJawvQeC5sBAAAgQFCugQDWcKpd+w41aXZemsJMJqPjBAy7\nzayi7GR9sN+lLg97XgMAAMB4lGsggG3efXbRrllTUw1OEnhmTU1Ve2e3dlY3GB0FAAAAoFwDgarX\n69Wm3bWaOD5eSbF2o+MEnNyx8YofYdPmPawaDgAAAONRroEAVX64SQ2nOljI7BOEhZ3d83rPgZM6\n1dppdBwAAAAMc5RrIEBt2lWrSJtZRdkOo6MErJlTUtXr9er9vfVGRwEAAMAwR7kGAtCZDo+cFW5d\nOTlFFnO40XECVlpilNJHxmjznlr2vAYAAIChKNdAANq6r17dPb26Om+k0VEC3qwpqTrmPqMj9a1G\nRwFCUltbmx5//HHNnj1bxcXF+upXv6rq6uq+xzdt2qSbbrpJeXl5mjt3rjZu3Njv/MbGRj3wwAMq\nLi7WVVddpRUrVqi7u9vfwwAAYMhRroEA9O6uWo1Njta41BFGRwl4V0xMkTncxJ7XwBD5/ve/r82b\nN2vlypV69dVXZbPZ9NWvflWdnZ2qrq7WokWLNGfOHK1fv17XX3+9Fi9erKqqqr7z77vvPjU0NOil\nl17S8uXLtW7dOj333HMGjggAgKFBuQYCzJH6Fh2ua2Ehs0sUbbdoWmZS391+AL7117/+VV/60pdU\nVFSkjIwMPfjgg6qtrVV1dbXWrFmj/Px8LVq0SBkZGVqyZIkKCgq0Zs0aSdKOHTtUWlqq5cuXKzc3\nV9dee62WLl2qtWvXqqury+CRAQDgW5RrIMBs2lUrc7hJV05mb+tLNWtKmlraPNpz4KTRUYCQk5CQ\noD//+c9qbGxUV1eXfvvb3yo2NlZjxoyR0+nU9OnT+z1/xowZcjqdkiSn06lRo0ZpzJgxfY9Pnz5d\nZ86c0f79+/06DgAAhhrlGgggnu5evb+3ToXZDkXbLUbHCRpT0hM0ItLC1HBgCDz++OOqq6vTzJkz\nlZ+fr9/85jf66U9/qpiYGNXV1SklJaXf85OTk1VXd3b/+fr6eiUnJ5/zuCTV1vL9CgAILWajAwD4\nux1Vbp3p6GZK+GUyh4fpykmp+r8dx9Ta7uEXE4APHT58WElJSfrud7+ruLg4vfjii7r//vv1m9/8\nRh0dHbJarf2eb7Va1dl5du/59vZ22Wy2fo9bLBaZTKa+53yS+PhImX21W0J1o0ZER1zwKQ4Ha1z4\nEu+nf/F++xfvt38F0/tNuQYCyKZdtUqIsWnSuASjowSdWVNT9b/Oo/pgf72uKxxtdBwgJBw9elSP\nPvqoXn75ZeXn50uSfvSjH+nGG2/UL3/5S9lsNnk8nn7ndHV1yW63S5IiIiLO+Wy1x+OR1+tVZGTk\nBa/d1NTmw5FILa0dF3zc7W7x6fWGM4djBO+nH/F++xfvt38F4vt9obLPtHAgQJw83aG9B09q9tQ0\nhYWZjI4TdMYkR2u0I0qb99QZHQUIGXv27FFPT4+mTJnSd8xisWjixIk6fPiw0tLS5HK5+p3jcrn6\npoqnpqbK7Xaf87ikc6aTAwAQ7AKqXFdXVysnJ+ecPx8tjHKxvTSBYLZpd628kmZNZUr4QJhMJs2c\nkqYDJ06rtvGM0XGAkJCaenZhxYqKir5jXq9XNTU1Gj9+vIqKirRt27Z+52zdulXFxcWSpKKiIh09\nerTf56u3bt2qqKgo5ebm+mEEAAD4T0CV68rKSsXHx2vTpk39/kybNu2S9tIEglWv16tNu2o1cVy8\nHHF2o+MErasmpyjMZOLuNeAjeXl5ys/P1yOPPCKn06mamho99thjOnHihBYsWKAFCxbI6XTq2Wef\nVU1NjVauXKmysjItXLhQklRQUKD8/Hw9+OCD2rt3rzZu3KgVK1bojjvuOOez2gAABLuAK9eZmZly\nOBz9/lgslovupQkEs4ojzWo41cFCZoMUG23TlPQEbd5Tp95er9FxgKAXHh6uF154QdOmTdNDDz2k\nz3/+8zpy5IhefvlljRo1Sjk5OVq1apXeeOMNzZs3T2+99ZZWr16tjIwMSWdnlKxatUqJiYmaP3++\nvvWtb+nWW2/V4sWLDR4ZAAC+F1ALmlVVVSk9Pf28jzmdTt1www39js2YMUOvv/66P6IBQ+rdXSdk\nt5lVlO0wOkrQmzklVat/v1f7jzQpJSXG6DhA0EtISNCyZcs+8fGSkhKVlJR84uMOh0PPP//8ECQD\nACCwBNSd66qqKp04cUK33XabZs2apS9/+cvatWuXJF10L00gWLV1eFRa4daVk1Jktfho25lhrCAr\nSXabWZt3s4cuAAAA/Cdg7lx3dHTo6NGjSkhI0NKlS2W1WvXSSy9pwYIFWr9+/UX30rwQn+6V6UdG\n7elm+bDgDcX1g2mfukvhi/H8ZfNBebp7NffajIB4fwIhw2BdUzBKb28/prYOT0iM5x8F4ngG+zMj\nEMc0GKE2HgAAcGkCplxHRERo27ZtslqtfSV6+fLl2rt3r15++eWL7qV5Ib7eK9MfjNzTzePpkeT7\nPT8DcZ+6wfDVeP783kGNdkQr1hZu+PsTKl+joswkvbHlsDbvqtW0CfFGx/GZQP36DOZnRqCOaaAu\ndzwUcQAAQkdATQuPjo7ud3c6LCxMmZmZqq2tvehemkAwOupq1aG6Fl2dlyaTib2tfSVjVIxS4u16\ny3nU6CgAAAAYJgKmXO/Zs0eFhYXas2dP37Genh6Vl5crKyvrontpAsFo065ahYeZdOVkfknkS2f3\nvE7V7poGNTS3Gx0HAAAAw0DAlOvc3FyNGjVK//mf/6mysjJVVVXpm9/8ppqamnT77bdfdC9NINh4\nunv1/t46FWQ7NCKS/V597aopqZKkzXtZ9BAAAABDL2DKtdls1s9//nNNmDBB99xzj2699VY1NDTo\npZdeUmJi4kX30gSCTVl1g1rbPbqava2HRFKsXXmZSdq8p05eL3teAwAAYGgFzIJmkpSSkqIf/ehH\nn/j4xfbSBILJu7tqFT/CpsnjE4yOErI+VTRGK1/doZrjp5U5OtboOAAAAAhhAXPnGhhOTp7u0J6D\njZo1NU1hYSxkNlRm5qXJagnTe3vY8xoAAABDi3INGOC9PXXyeqXZU1ONjhLSIiMsKspO1gf7Xer6\ncLsoAAAAYChQrgE/6/V6tWnXCeWOjVNyfKTRcULerKmpau/s1s7qBqOjAAAAIIRRrgE/qzraLHdz\nh67OG2l0lGEhd1y8EmJsem83q4YDAABg6FCuAT97p6xWdlu4CnMcRkcZFsJMJl01OVV7DjaqubXT\n6DgAAAAIUZRrwI/aOrpVWuHSjIkpslnCjY4zbMyckiqvV9qyt97oKAAAAAhRAbUVFxDqPiivV1d3\nr2YzJdyv0hKjlD4yRpv31Oqz08fIZGKFdgD9vb3z+AUfL8kf5ackAIBgxZ1rwI/eLavVKEeUJqSN\nMDrKsDNrSqqOuc/oSH2r0VEAAAAQgijXgJ8cc7fqYO1pXT01jTunBrhiYorM4Sb2vAYAAMCQoFwD\nfrJpV63Cw0y6cgp7Wxsh2m5RfmaStu6rV3dPr9FxAAAAEGIo14AfdPf0avOeOuVnJSkm0mp0nGFr\n5tQ0tbR5tOfASaOjAAAAIMRQrgE/KKtuUGu7R1fnpRkdZVibMiFBIyItTA0HAACAz1GuAT94d1et\n4qKtmjwhwegow5o5PExXTkrt+2UHAAAA4CuUa2CINbV0aveBRs2amqbwML7ljDZraqq6e7z6YD97\nXgMAAMB3+Js+MMQ276mV1yvNZkp4QBibMkKjHdF6b3ed0VEAAAAQQijXwBDyer16d1etssfEKSU+\n0ug4+NCsqak6WHtatY1njI4CAACAEEG5BoZQ9fFTcjW1s5BZgLlyUorCTCZt3sPdawAAAPgG5RoY\nQqUVbpnDTSrMdhgdBf8gNtqmKekJ2rynTr29XqPjAAAAIARQroEh4vV6VVrh1qTxCbLbzEbHwcfM\nnJKqppZO7T/SZHQUAAAAhADKNTBEjtS3qvF0B3etA1RBVpIibWZt3s2e1wAAABg8yjUwREor3TKZ\npPysJKOj4Dws5nBNn5is0kq32ju7jY4DAACAIEe5BobIjkq3skfHKSbSanQUfIKZU9PU5emVs8Jl\ndBQAAAAEOco1MATqTrbpeMMZpoQHuIyRMUqJt+t9Vg0HAADAIFGugSGwvdItSZTrAGcymTRzSqrK\njzSrobnd6DgAAAAIYpRrYAhsr3RrXOoIJcZGGB0FF3Hl5FRJkrPCbXASAAAABDPKNeBjTS2dOnDi\ntIq4ax0UHHF2jUmO1vYqyjUAAAAGjnIN+BhTwoNPYbZDNcdO6dSZLqOjAAAAIEhRrgEf217pVmpC\npEYmRRkdBZeoMNshr6Sd3L0GAADAAFGuAR9qbfeo4kizinK4ax1MRjui5IiL0PbKBqOjAAAAIEhR\nrgEfKqtuUK/Xy5TwIGMymVSY7dD+wyfV3tltdBwAAAAEIco14EOlFW7Fj7BpfOoIo6PgMhVmO9Td\n49WumkajowAAACAIUa4BH+ns6tHeQydVmO2QyWQyOg4uU8aoWMVEWfsWpAMAAAAuB+Ua8JHdBxrl\n6e5lC64gFWYyqSArSbsONMrT3WN0HAAAAAQZyjXgI9sr3Yq2W5Q1JtboKBigwmyHOrt6tO9Qk9FR\nAAAAEGQo14APdPf0qqymUfmZSQoP49sqWE0cFy+7LZyp4QAAALhstADAB8oPN6m9s1uFbMEV1Mzh\nYcrLSNLO6gb19nqNjgMAAIAgQrkGfKC00i2bNVyTx8cbHQWDVJCVpJY2j6qONRsdBQAAAEGEcg0M\nUm+vVzuqGpSXniiLOdzoOBikqemJMoeHaXtlg9FRAAAAEEQo18AgVR8/pdNnulTIKuEhwW4za9L4\neO2ocsvrZWo4AAAALg3lGhik7ZVumcNNystINDoKfKQw26GGUx066mo1OgoAAACCBOUaGASv16vt\nlW5NGp8gu81sdBz4SH5WkkwmsWo4AAAALhnlGhiEo65WNZzqYEp4iImJtCprdBzlGgAAAJeMcg0M\nwvZKt0wmKT8zyego8LHCbIeOuc/I1dRmdBQAAAAEAco1MAillW5ljY5TTJTV6CjwscKss78wYdVw\nAAAAXArKNTBAJxpaddx9hinhISopzq6xKdFMDQcAAMAloVwDA7Rld60kqTCbKeGhqjDboZrjp3Sq\ntdPoKAAAAAhwlGtggDbvrtW4lBFKirUbHQVDpDDbIa+kHVVMDQcAAMCFUa6BAWhq6VTF4SYV5jAl\nPJSNSopScpydqeEAAAC4qIAs1zt37tSkSZO0devWvmObNm3STTfdpLy8PM2dO1cbN240MCGGux1V\nZ8sWn7cObSaTSYXZDu0/3KS2jm6j4wAAACCABVy5bmtr09KlS9XT09N3rLq6WosWLdKcOXO0fv16\nXX/99Vq8eLGqqqoMTIrhbHulW6Mc0RqZGGl0FAyxwmyHenq92lXD1HAAAAB8MrPRAT5u+fLlSklJ\n0eHDh/uOrVmzRvn5+Vq0aJEkacmSJSotLdWaNWv0+OOPGxUVw1Rru0cVR5r1byWZMplMRsfBEEsf\nFaPYKKu2VzXoysmpRscBYJC3dx6/6HNK8kf5IQkAIFAF1J3rjRs36u2339Z3vvOdfsedTqemT5/e\n79iMGTPkdDr9GQ+QJJVVN6in16urpqYZHQV+EGYyqSArSbsPNMrT3XPxEwAAADAsBUy5PnnypL79\n7W9r2bJlio2N7fdYXV2dUlJS+h1LTk5WXV2dPyMCks5OCY8fYVPm6Dijo8BPCrMd6uzq0d5DTUZH\nAQAAQIAKmGnhjz32mK677jpdc80155Tmjo4OWa3WfsesVqs6Oy9t79n4+EiZzeE+y+ovDscIQ65r\nsYQP2fWNGpOvdHR2a+/Bk/rMjHEKCzMF/XjOJ9TG5IvxzI6P0k/+sFf7jzTrn66a4INUAxeIX5/B\n/swIxDENRqiNBwAAXJqAKNfr16/Xvn379Ic//OG8j9tsNnk8nn7Hurq6ZLdf2v7CTU1tg87obw7H\nCLndLYZc2+M5O/XV19c3cky+UlrhUld3ryaOPXvXOtjH83Gh8DX6R74cz9T0RL2/u1a3laQrPMyY\nST+B+vUZzM+MQB3TQF3ueCjiAACEjoAo1+vWrVN9fb1mz54tSfJ6vZKku+66S/PmzVNaWppcLle/\nc1wu1zlTxYGhtr3SrWi7RdljYi/+ZISUwmyHtuyrV/WxU8oZG290HAAAAASYgPjM9ZNPPqnXX39d\nGzZs0IYNG/Tzn/9ckrRs2TI98MADKioq0rZt2/qds3XrVhUXFxsRF8NUd0+vdlY3alpmomF3LmGc\nKekJMoeHqbTSbXQUwO9ee+01ffazn1VeXp5uvvlmvf/++32Pbdq0STfddJPy8vI0d+5cbdy4sd+5\njY2NeuCBB1RcXKyrrrpKK1asUHc3+8YDAEJPQDSElJQUjRs3ru/P6NGj+44nJiZqwYIFcjqdevbZ\nZ1VTU6OVK1eqrKxMCxcuNDg5hpPyI01q7+xWUXay0VFggAirWVMmJGhHpbtvdg0wHKxfv17f+973\ndNddd+mPf/yjrrjiCt177706duyYqqurtWjRIs2ZM0fr16/X9ddfr8WLF6uqqqrv/Pvuu08NDQ16\n6aWXtHz5cq1bt07PPfecgSMCAGBoBES5vpicnBytWrVKb7zxhubNm6e33npLq1evVkZGhtHRMIxs\nr3DLZgnX5AlMCR6uCrKS1Hi6U0fqW42OAviF1+vVc889p7vuuku33HKLxo0bp2984xsaO3asduzY\noTVr1ig/P1+LFi1SRkaGlixZooKCAq1Zs0aStGPHDpWWlmr58uXKzc3Vtddeq6VLl2rt2rXq6uoy\neHQAAPhWQHzm+uNSU1NVUVHR71hJSYlKSkqMCYRhr9fr1Y6qBk3NSJQlCFeeh29My0qS6X+k0kq3\nxqWyEBVC34EDB3T8+HHdeOONfcfCwsL0+9//XpL0wgsv6IYbbuh3zowZM/T6669LkpxOp0aNGqUx\nY8b0PT59+nSdOXNG+/fv17Rp0/wwCgAA/CMo7lwDRjtw/LROnelSYXaS0VFgoJhIq7JHx2kHn7vG\nMHHo0CFJ0unTp3X77bfrqquu0vz587V9+3ZJUl1d3TmLiyYnJ/dtqVlfX6/k5ORzHpek2traIU4P\nAIB/BeSdayDQlFa6FB5mUl465Xq4K8x26JW/Van+ZJtSEiKNjgMMqdbWsx+BeOSRR3T//fcrPT1d\nr732mhYuXKgNGzaoo6NDVqu13zlWq1WdnZ2SpPb2dtlstn6PWywWmUymvud8kvj4SJl9NVOoulEj\noiN881oXwNZqf8d74V+83/7F++1fwfR+U66Bi/B6vdpe6dak8QmKjOBbZrgryE7SK3+r0vYqt26Y\nMc7oOMCQslgskqR77rlHc+fOlSRNmjRJpaWleuWVV2Sz2eTxePqd09XVJbvdLkmKiIg457PVHo9H\nXq9XkZEX/uVUU1Obr4YhSWpp7fDp651PKO3ZPhihtn99oOP99i/eb/8KxPf7QmWfaeHARRx1tcrd\n3MGUcEiSkmLtGpcyQtuZGo5h4KMp3NnZ2X3HTCaT0tPTdezYMaWlpcnlcvU7x+Vy9U0VT01Nldvt\nPudxSedMJwcAINhRroGL2F7plklSQZbD6CgIEIXZSao5flrNrRee1goEu8mTJysyMlK7d+/uO+b1\nelVTU6MxY8aoqKhI27Zt63fO1q1bVVxcLEkqKirS0aNH+32+euvWrYqKilJubq5/BgEAgJ9QroGL\n2F7ZoKzRsYqJsl78yRgWCrPP/qJlR1WDwUmAoWW327Vw4UI988wzevPNN3Xo0CH94Ac/0JEjR/TF\nL35RCxYskNPp1LPPPquamhqtXLlSZWVlWrhwoSSpoKBA+fn5evDBB7V3715t3LhRK1as0B133HHO\nZ7UBAAh2fIAUuABXU5uOuVv1heuzjI6CADIyKUop8XZtr3TrUwWjjI4DDKkHHnhAdrtdTzzxhBob\nGzVx4kT94he/UHp6uiRp1apVWrFihX72s58pPT1dq1evVkZGhqSzU8hXrVql7373u5o/f76ioqJ0\n6623avHixUYOCQCAIUG5Bi5ge+XZO5OFWXzeGn9nMplUmO3Qm9uOqq3Do8gIi9GRgCFjMpl09913\n6+677z7v4yUlJSopKfnE8x0Oh55//vkhSgcAQOBgWjhwAaWVLo1NiVZSnN3oKAgwhdkO9fR6VVbT\naHQUAAAABADKNfAJmls7VXP8tIqyWcgM55owMkax0VZWDQcAAIAkpoUDn+ijxaoKKdc4jzCTSQVZ\nDm3eU6suT4+slnCjIwEw2Ns7j1/0OSX5rNMAAKGKO9fAJ9he4VJKQqRGJkUZHQUBqjA7SV2eXu09\ndNLoKAAAADAY5Ro4jzMdHpUfaVZhdpJMJpPRcRCgcsfGy24zMzUcAAAAlGvgfMqqG9TT62VKOC7I\nHB6maZmJKqtuVE9vr9FxAAAAYCDKNXAe2ysbFD/CpglpMUZHQYArzHKotd2jqqOnjI4CAAAATWzF\nHgAAIABJREFUA1GugY/p9PRoz4FGFWQlKYwp4biIqemJspjDmBoOAAAwzFGugY/Zc+Ckurp72YIL\nl8RmDdfk8QnaUeWW1+s1Og4AAAAMQrkGPmZ7pVtREWZlj40zOgqCRGG2Q42nO3W4vsXoKAAAADAI\n5Rr4B909vSqrblB+ZpLCw/j2wKXJ//AjBEwNBwAAGL5oD8A/qDjSrLbObhXmMCUcly7ablH2mFht\nr2wwOgoAAAAMQrkG/sH2SrdslrOfoQUuR0G2QycazqjuZJvRUQAAAGAAyjXwoV6vV9ur3JqaniCr\nJdzoOAgyhVlnZzswNRwAAGB4olwDHzpw4rROtXapkFXCMQCJsREalzqCcg0AADBMUa6BDznLXTKH\nm5SXkWR0FASpwmyHDpw4raaWTqOjAAAAwM8o14Akr9er0gq3Jo1PUGSE2eg4CFIfzXrYUcXdawAA\ngOGGcg1IOlTXosbTHSrOSTY6CoLYyMRIpSREagdTwwEAAIYdyjUgyVnhUniYSflZTAnHwJlMJhVm\nJ6n8SLPOdHiMjgMAAAA/olxj2PtoSnjuuHhF2y1Gx0GQK8x2qKfXq13VjUZHAQAAgB9RrjHsHXW1\nytXUruIcVgnH4E1Ii1FctJVVwwEAAIYZyjWGvdIKt0wmqYAtuOADYSaTCrId2n2wUV2eHqPjAAAA\nwE8o1xj2nBUu5YyJU0yk1egoCBGF2Q51eXq15+BJo6MAAADATwZcrm+66Sbt27dPkrRhwwY1NTX5\nLBTgL8cbzqi2sU3FuawSDt/JGROnaLtFznKX0VEAAADgJwMu1wcOHFBj49kFe775zW/q6NGjPgsF\n+EtphUsm/X1/YsAXzOFhKshK0s7qBnm6mRoOAAAwHJgHemJWVpYefvhhZWdny+v16rvf/a6io6PP\n+1yTyaRf/epXAw4JDBVnuVuZo2MVF20zOgpCzBW5yXp3V632HDypgix+eQMAABDqBnzn+oc//KFm\nzZoli8Uik8mk8PDwT/wTFsZHuxF46k+26Zi7VUU5TAmH7+WOi1dUhJmp4QAAAMPEgO9cZ2Zm6qmn\nnpIk5ebm6tFHH1VeXp7PggFDzVlxtvQUMSUcQ8AcHqaCbIdKK1zydPfKYuaXjAAAAKHMJ3/bKy8v\n7yvW3d3dcrvd6u7u9sVLA0PGWeHWhLQYJcZGGB0FIeqK3GS1d/ZoL6uGAwAAhDyf3UrZs2eP7rzz\nThUWFuraa69VRUWFHnnkET3//PO+ugTgMw3N7Tpc16LiXO5aY+hM/HBq+DamhgMAAIQ8n5Tr7du3\n60tf+pKam5t11113yev1SpJSU1O1atUqvfzyy764DOAzzgq3JPF5awwpc3iY8vtWDe81Og4AAACG\nkE/K9ZNPPqmZM2fqd7/7nRYtWtRXrpcsWaKFCxfqlVde8cVlAJ8prXRpbEq0kuPsRkdBiDs7Nbxb\n+w4xNRwAACCU+aRc7927V1/84hclnd126x996lOfYg9sBJSTpztUc/y0irlrDT+YND5BkTZWDQcA\nAAh1PinXUVFRamxsPO9j9fX1ioqK8sVlAJ8orfxoSjift8bQM4eHqSArSTuqGtTdw9RwAACAUOWT\ncn3dddfpmWee0b59+/qOmUwmud1u/eQnP9G1117ri8sAPlFa4dYoR5TSEvmlD/yjODdZbUwNBwAA\nCGk+KdcPP/yw4uPjdcstt+jTn/60JGnp0qX6zGc+o+7ubj388MO+uAwwaKdaO1V1tJkp4fCrSeMT\nZLeFy1nuNjoKAAAAhojZFy8SFxen1157TRs2bNCWLVs0YcIERUdH6wtf+IJuvvlmRUZG+uIywKBt\nr3TLK6aEw78s5jDlZzq0o8qt7p4cmcN9tgsiAAAAAoRPyrUkWa1W3Xbbbbrtttt89ZKAzzkr3EpN\niNSoJKaEw7+uyE3W+3vrtP9wk6amJxodBwAAAD424HK9evVq3XzzzUpOTtbq1asv+FyTyaS77757\noJcCfKKlrUsVR5p1w5Vjz1nVHhhqkyecnRq+rdxFuQYAAAhBAy7XzzzzjGbOnKnk5GQ988wzF3zu\npZbruro6PfHEE9qyZYt6e3t19dVX65FHHlFKSookadOmTVqxYoUOHjyocePG6eGHH2axNFyyHVUN\n6vV6+bw1DHF2aniSdlS61f1ZpoYDAACEmgGX6/Ly8vP++0B5vV597WtfU0JCgtasWSNJWrZsmRYt\nWqR169apurpaixYt0r333qvPfOYz+uMf/6jFixdr/fr1ysrKGvT1EfqcFS454iI0NiXa6CgYpopz\nkvX+3nqVH2nSlAncvQYAAAglAXPrpKGhQRkZGVq2bJlyc3OVm5urL3/5y9q7d69OnTqlNWvWKD8/\nX4sWLVJGRoaWLFmigoKCviIOXMiZDo/2H2pSUU4yU8JhmCnpCYqwhstZ7jI6CgAAAHxswHeuv/KV\nr1zyc00mk1588cULPsfhcOjpp5/u+991dXV69dVXNXXqVMXGxsrpdOqGG27od86MGTP0+uuvX15w\nDEs7qxrU08uUcBjLYg5XfmaStlc2aMFnepkaDgAAEEIGXK49Ho8vc/Rz77336m9/+5tiY2P77kzX\n1dX1ffb6I8nJyaqrqxuyHAgdpRVuJcTYNCFthNFRMMwV5yZry756VRxp1uQJCUbHAQAAgI8MuFyv\nXbvWlzn6eeCBB3TPPffoxz/+se644w5t2LBBHR0dslqt/Z5ntVrV2dl50deLj4+U2Rw+VHGHjMNh\nTBG0WMKH7PpGjKmtw6O9h07qhpnjlZwc49PXNuprNJRCbUyBNp6SuEi9+Po+7TncpJLp4y77/EAb\njzT4nxmBOKbBCLXxAACAS+Ozfa6ls3eXt2zZIpfLpX/7t3+T2+1WZmbmOaX4YnJyciRJTz/9tEpK\nSrR+/XrZbLZz7pZ3dXXJbrdf9PWamtou6/qBwOEYIbe7xZBrezw9kuTz6xs1pi376uTp7tXksXE+\nvb6RX6OhEmpjCtTxTE1P1HtlJ3TLNRMUHnbpU8MDdTyD+ZkRqGMaqMsdD0UcAIDQ4bNy/V//9V9a\nu3aturu7ZTKZNGvWLD311FOqr6/Xr371KyUmXnhl3IaGBm3dulX//M//3HfMbrdrzJgxqq+vV1pa\nmlyu/osAuVyuc6aKAx9XWu5WbLRVGaNijY4CSJKuyE3WB/tdqjjSrEnjmRoOAAAQCnyyms5Pf/pT\nrV27VkuXLtX//u//yuv1SpK+/vWv69SpU/0WKvskJ06c0EMPPaTdu3f3HWtpadHBgweVmZmpoqIi\nbdu2rd85W7duVXFxsS+GgBDV2dWj3QcaVZTtUBirhCNATE1PlM3CquEAAAChxCfl+tVXX9V9992n\n22+/XSNHjuw7XlBQoCVLluidd9656GtMmTJFxcXF+s53vqNdu3Zp3759WrJkiRISEjRv3jwtWLBA\nTqdTzz77rGpqarRy5UqVlZVp4cKFvhgCQtTuA43q6u5VEauEI4BYLeGalpmo0kq3enp7jY4DAAAA\nH/BJuXa5XJo6dep5Hxs1apSam5svHiQsTM8995wmTpyou+++WwsWLFBUVJReeuklRUVFKScnR6tW\nrdIbb7yhefPm6a233tLq1auVkZHhiyEgRDkrXBoRaVH2GKaEI7AU5ySrpc2jyiMX//kIAACAwOeT\nz1yPHTtW7777rmbOnHnOY06nU2PGjLmk10lISNDy5cs/8fGSkhKVlJQMNCaGmS5Pj8pqGnXlpJTL\nWjQK8IepGYmyWsLkrHBrIp+7BgAACHo+KdcLFy7UY489pu7ubl133XUymUw6evSoSktL9eKLL+rh\nhx/2xWWAy7L34El1dvWoKMdhdBTgHDZLuPIyklRa6db8f8pWWBhrAgAAAAQzn5Tr2267TU1NTXrh\nhRf00ksvyev1asmSJbJYLPrKV76i+fPn++IywGVxVrgVFWFW7th4o6MA53VFbrKc5S5VHm1W7jj+\nOwUAAAhmPtuK6+6779b8+fO1Y8eOvs9Yz5o1SwkJTHeE/3m6e7WzukGF2UkyhzMlHIEpLz1RVnOY\ntlW4KNcAAABBblDluqamRuvWrZPJZNItt9yi8ePH6/Dhw1q5cqVaW1sVExOjO++8U1/72td8lRe4\nJPsPn1R7Z7eKWSUcAcxmDVdeRqJKK9ya/2mmhgMAAASzAZfrbdu26c4771R4eLjsdrt+/etf6777\n7tMPf/hDzZw5UxMnTtSuXbv09NNPKzo6Wl/60pd8mRu4IGeFW3ZbuCaxUBQCXHFuspwVblUda1YO\nH2EAAAAIWgMu16tWrdKVV16p5557TjabTU899ZRWrFihz33uc/r+97/f97xvf/vb+u1vf0u5ht90\n9/RqR6Vb0zKTZDEzJRyBLS8jURZzmJzlbso1AABAEBtw89i3b58+//nPy2azSTq7YrjX69WcOXP6\nPe9f//VfdfDgwcGlBC5DxdFmnelgSjiCQ4TVrLz0RDkrXer1eo2OAwAAgAEacLluaWnpt1hZbGys\nJCkuLq7f8yIiItTR0THQywCXrbTcJZslXFMmMCUcwaE4N1mnWrtUfeyU0VEAAAAwQIOaMxseHt73\n7ybT2YV4wsKYhgvj9PZ6tb3SrbyMRFkt4Rc/AQgA0zLPTg3fVu4yOgoAAAAGyOdN+KOSDRih6liz\nTrd5VJzLlHAEjwirWVPTE1VawdRwAACAYDWorbiWLVum6OhoSZL3w78Qfu9731NUVFTfc1pbWwdz\nCeCyOMvdspjDNDWdKeEILsU5Dm2vdKvm+ClljY67+AkAAAAIKAMu11dccYUkyePxXPCYzWZTcXHx\nQC8DXLJer1ellS5NTU9UhHVQvzcC/G5aZpLM4WenhlOuAQAAgs+AG8jatWt9mQMYtAPHT6u5tUtF\nOQ6jowCXzW4za2p6gkor3PrC9VkK4yM2AAAAQYXVxxAynBUumcNNmpaRZHQUYECKc5PV1NKpA8dP\nGx0FAAAAl4lyjZDg9XpVWuHS5PEJioxgSjiCU35mkszhJjkrWDUcgWfnzp2aNGmStm7d2nds06ZN\nuummm5SXl6e5c+dq48aN/c5pbGzUAw88oOLiYl111VVasWKFuru7/R0dAAC/oFwjJByqa1Hj6U4V\n5bBKOIKX3WbWlAmJcrJqOAJMW1ubli5dqp6enr5j1dXVWrRokebMmaP169fr+uuv1+LFi1VVVdX3\nnPvuu08NDQ166aWXtHz5cq1bt07PPfecEUMAAGDIUa4REpwVLoWHmZSfxZRwBLfiXIdOnu7UwRNM\nDUfgWL58uVJSUvodW7NmjfLz87Vo0SJlZGRoyZIlKigo0Jo1ayRJO3bsUGlpqZYvX67c3Fxde+21\nWrp0qdauXauuri4jhgEAwJCiXCPoeb1elZa7lTsuXtF2i9FxgEHJz3TIHG7StnKmhiMwbNy4UW+/\n/ba+853v9DvudDo1ffr0fsdmzJghp9PZ9/ioUaM0ZsyYvsenT5+uM2fOaP/+/UMfHAAAP6NcI+gd\ndbXK1dyuYlYJRwiIjDBr8vgElVa45GVqOAx28uRJffvb39ayZcsUGxvb77G6urpz7mYnJyerrq5O\nklRfX6/k5ORzHpek2traIUwNAIAxWPkJQc9Z4ZbJJBVkU64RGopzk1VW06iDtS1KHxljdBwMY489\n9piuu+46XXPNNX2l+SMdHR2yWq39jlmtVnV2dkqS2tvbZbPZ+j1usVhkMpn6nnMh8fGRMpvDBzmC\nD1U3akR0hG9ea5AcjhFGR/CL4TLOQMH77V+83/4VTO835RpBzev1ylnuUs6YOMVEWi9+AhAECrKS\nFB5mkrPcRbmGYdavX699+/bpD3/4w3kft9ls8ng8/Y51dXXJbrdLkiIiIs75bLXH45HX61VkZORF\nr9/U1DbA5OfX0trh09cbKLe7xegIQ87hGDEsxhkoeL/9i/fbvwLx/b5Q2adcI6idaDijupNt+nTx\naKOjAD4TGWHR5AkJ2lbu0q2fypDJZDI6EoahdevWqb6+XrNnz5akvo8p3HXXXZo3b57S0tLkcvVf\nG8DlcvVNFU9NTT1na66Pnv/x6eQAAIQCyjWCWmmFWyZJhUwJR4gpzknWrpr9OlTXoglp3L2G/z35\n5JPq6Pj73V6326358+dr2bJlmjVrlp555hlt27at3zlbt25VcXGxJKmoqEhPPvmkamtrlZaW1vd4\nVFSUcnNz/TcQAAD8hAXNENScFS5ljo5VXLTt4k8GgkhB9tmp4awaDqOkpKRo3LhxfX9Gjx7ddzwx\nMVELFiyQ0+nUs88+q5qaGq1cuVJlZWVauHChJKmgoED5+fl68MEHtXfvXm3cuFErVqzQHXfccc5n\ntQEACAWUawStupNtOuY+o+Kc5Is/GQgyUREWTRqfIGc5q4YjMOXk5GjVqlV64403NG/ePL311lta\nvXq1MjIyJEkmk0mrVq1SYmKi5s+fr29961u69dZbtXjxYoOTAwAwNJgWjqBVWnH2jl4RW3AhRBXn\nOPT//aVRh+tbND6VqeEwVmpqqioqKvodKykpUUlJySee43A49Pzzzw9xMgAAAgN3rhG0nOVupY+M\nUUJMYGyvAvhaQbaDqeEAAABBgnKNoORubtfh+hbuWiOkRdstmjgunqnhAAAAQYByjaD03u5amSRd\nweetEeKKc5Plbu7QkfpWo6MAAADgAijXCDq9vV5t2l2rSRMSlBRnNzoOMKQKsx0KM5nkrGBqOAAA\nQCCjXCPo7Dl4UidPd+qaaSONjgIMuWi7RRPHx2sbU8MBAAACGuUaQeedshMaEWlRQVaS0VEAvyjO\nccjV1K6DJ04bHQUAAACfgHKNoHKqtVNl1Q2aNSVN5nD+88Xw8NHU8E1lx42OAgAAgE9AO0FQeW9P\nnXp6vbp6WprRUQC/GRFpVe64OL1XdoKp4QAAAAGKco2g4fV69U7ZCWWPjlVaYpTRcQC/Ks5N1omG\nMzrqYtVwAACAQES5RtAoP9IsV1O7rslnITMMP8U5ybKYw/RO2QmjowAAAOA8KNcIGu+UnZDdZlYx\ne1tjGIq2WzR72kht3lOnjq5uo+MAAADgY8xGBwAuRWu7R6UVLl0zbaSslnCj4wCGuOGqCfq/0mP6\nYL+LreiAIPX2zgsvTFiSP8pPSQAAvsadawSF9/fUqbvHS6HAsJY7Pl6jHVH6vx2sGg4AABBoKNcI\neB8tZDYhbYTGpowwOg5gGJPJpJKCUTpc16KDtex5DQAAEEgo1wh4B06c1vGGM9y1BiRdNTlVNku4\nNl5kaikAAAD8i3KNgLex7IRslnBNn5hidBTAcHabWTMmJWvLvnq1dbCwGQAAQKCgXCOgtXd264P9\n9Zo+MVl2G+vvAZJUUjBKXZ5evb+3zugoAAAA+BDlGgFt6/56dXl62dsa+AfjU2M0PnWE3t55XF6v\n1+g4AAAAEOUaAe6dnSc0yhGl9LQYo6MAAaWkYJSOu8+o+vgpo6MAAABAlGsEsCP1LTpU16Jrpo2U\nyWQyOg4QUGZMTJHdFq632ZYLAAAgIFCuEbA2lp2QOTxMV01ONToKEHBs1nDNnJymbeVutbZ7jI4D\nAAAw7FGuEZA6PT3asrdexbkORdstRscBAtK1BSPV3dOr93bXGh0FAABg2AuYct3Q0KBvfOMbmj17\ntoqLi3XnnXeqsrKy7/FNmzbppptuUl5enubOnauNGzcamBZDzVnuUntnt67JYyEz4JOMdkQrc3Ss\n3t7BwmYAAABGC4hy3dvbq69//es6dOiQfvzjH+u///u/FR0drS9/+ctqampSdXW1Fi1apDlz5mj9\n+vW6/vrrtXjxYlVVVRkdHUPknbITSom3K2dsnNFRgID2qfxRqm9qV/nhJqOjAAAADGsBUa7Ly8u1\nY8cOPfHEE8rLy1NmZqZWrFihtrY2bdy4UWvWrFF+fr4WLVqkjIwMLVmyRAUFBVqzZo3R0TEEahvP\nqOrYKRYyAy5Bca5DURFm/d/OE0ZHAQAAGNYColynpaXpJz/5iSZMmNB37KNSderUKTmdTk2fPr3f\nOTNmzJDT6fRrTvjHO2UnFB5m0sypaUZHAQKexRyu2Xlp2lHp1qnWTqPjAAAADFsBUa7j4+NVUlKi\nsLC/x1m7dq06Ojo0e/Zs1dXVKSUlpd85ycnJqqur83dUDDFPd6/e212n/MwkxUZZjY4DBIVr80ep\np9erd3exsBkAAIBRzEYHOJ+//e1veuqpp3THHXcoIyNDHR0dslr7Fy2r1arOzku7SxMfHymzOXwo\nog4ph2OEIde1WMKH7PoXe81NZcfV2u7R3GszDBv/5QiGjJcr1MY0HMbjcIzQtKwkbdpTp9vnTlF4\nmH8/TjHYnxnD4WsEAABCX8CV63Xr1unRRx/VjTfeqP/4j/+QJNlsNnk8/fdx7erqkt1uv6TXbGpq\n83nOoeZwjJDb3WLItT2eHkny+fUvZUx/eqdGiTE2jY63Gzb+S2Xk12iohNqYhtN4Zk5O1Qsb9ujt\nDw4rLyPRr7kG8zNjOH2NPun5AAAgNATEtPCPvPDCC/rmN7+pL3zhC/rhD3/YN008LS1NLper33Nd\nLtc5U8UR3NzN7dp7qEmz80YqzM933oBgV5CVpJgoq97ecdzoKAAAAMNSwJTrn/3sZ3rmmWd0//33\n69FHH+23SnRRUZG2bdvW7/lbt25VcXGxv2NiCL2764RMJunqPBYyAy6XOTxMV+elqaymQSdPdxgd\nBwAAYNgJiHJdXl6up59+Wp/73Od02223ye129/1pa2vTggUL5HQ69eyzz6qmpkYrV65UWVmZFi5c\naHR0+EhPb6827arV1PREJcREGB0HCErXThspec+uuA8AAAD/Cohy/ec//1k9PT363e9+p9mzZ/f7\n88tf/lI5OTlatWqV3njjDc2bN09vvfWWVq9erYyMDKOjw0d215xUc2uXrs4baXQUIGglxdk1NSNR\nG8tOqLun1+g4AAAAw0pALGj20EMP6aGHHrrgc0pKSlRSUuKfQPC7d8pOKCbKqmmZ/l2ICQg1Jfmj\n9OzvdqmsulFFOQ6j4wAAAAwbAXHnGsNbU0unymoaNHtqmszh/Cf5/7N332FRXG0bwO9dYKkivYgo\nigLSm9gQATVRYy/RRI01zZqYxJKIJSZ5NVbsRk2MfIkxFtQkmqKJJBJFiqKoqICKhSag9Lrz/cHL\nvq6giALDwv27Lq7Lnfqcc9aZfWbOnCF6ES62RjBsoYmw8xzYjIiIiKghMZMh0Z26mAJBAHq6cSAz\nohelJpWil1srxN3IQvqDQrHDISIiImo2mFyTqOSCgH9i78GhjQHMDXXEDoeoSejp1gpSiYR3r4mI\niIgaUKN45pqarys3s3H/YRGG92ovdihETYZhC024dzTBqQspGOrbHhrqvI5K1JScrOHCmb+7VQNF\nQkREj+IvLhLV37H3oKulDi87DrxEVJf8PVoht6AUMdcyxA6FiIiIqFlgck2iySkoQcy1DHRztoCG\nuprY4RA1KY42RjA10MLJc+waTkRERNQQmFyTaP69mIpyuYBebny3NVFdk0ok6OVuhau3H+De/Xyx\nwyEiIiJq8phckygEQcA/F+7B1kofVqZ6YodD1CT5ulhCTSpB2Pl7YodCRERE1OQxuSZRXL/zECmZ\nBfBz5V1rovqiryuDl70pwi+moKS0XOxwiIiIiJo0jhZOovg79h60ZGrw6WQudihETVqAhxXOXklH\nZHw6erjwXfJEjV1NI4ETEVHjxTvX1OAKikoRFZ+Oro7m0JRxIDOi+mRnbQBLYx0ObEZERERUz5hc\nU4M7czkNJWVy+LmzSzhRfZNIJPB3t0LivRwkp+WKHQ4RERFRk8XkmhqUIAj4+/w9tDHTQ1vzFmKH\nQ9QsdHexgIa6FCc5sBkRERFRvWFyTQ3qZmouktPz4OfeChKJROxwiJoFXS0N+DiY4fSlVBQWl4kd\nDhEREVGTxOSaGtQ/sfcgU5eiqyMHMiNqSP4eViguKUfElTSxQyEiIiJqkphcU4MpLC7Dmctp8HYw\ng46WhtjhEDUr7Vvpw9pMDyfP3YUgCGKHQ0RERNTkMLmmBnPq/F0UlZTDz40DmRE1NIlEAn8PKySn\n5eFGCgc2IyIiIqprTK6pwfwecQuWxjro2Lql2KEQNUuVr7/ja7mIiIiI6h6Ta2oQdzPyEH8rG35u\nHMiMSCzamuro5miOs1fSkF9UKnY4RERERE0Kk2tqEGGx96CuJkE3ZwuxQyFq1nq5W6GkTI5/41LF\nDoWIiIioSWFyTfWutKwcp+NS0dXZEvo6MrHDIWrW2lq0QDtLfQ5sRkRERFTHmFxTvQu/mIr8ojL0\n62ojdihEBMDfoxVSMgtw/c5DsUMhIiIiajKYXFO9Ki4px+FTN9CxdUu4djQROxwiAuDTyRzamuoc\n2IyIiIioDjG5pnr1W2QyHuaXYJR/Bw5kRtRIaGqooYezBaKupiOnoETscIiIiIiaBCbXVG9y8ktw\nLCIZnnam6MDXbxE1Kr08rFBWLiD8YorYoRARERE1CUyuqd78FH4TpaVyjOjVXuxQiOgxVia6sGvd\nEmHn7kHOgc2IiIiIXhiTa6oX6dkFOHn+LvzcLGFprCt2OERUDX8PK6Q/KMT56/fFDoWIiIhI5TG5\npnpx8O8kqKlJMNi3ndihENETeDuYwdJYB/v+SkBZuVzscIiIiIhUGpNrqnM3UnJw9ko6XurcBgZ6\nmmKHQ0RPoK4mxasBHZCWXYg/YzhyOBEREdGLYHJNdUoQBOz7KwF62hro36WN2OEQUQ1cbY3h1M4I\nR07dQF5hqdjhEBEREaksJtdUpy4mZSE++QEG97CBtqa62OEQUQ0kEglGB3ZAYUkZDp+6IXY4RERE\nRCqLyTXVGblcwP6TCTA10IK/h5XY4RDRM2ptqode7lb4K+YuUjLzxQ6HGpH79+9j3rx58PX1hbe3\nN6ZMmYJr164p5p86dQpDhgyBq6srBg0ahLCwMKX1MzMzMXv2bHh7e6Nbt25YuXIlysqQq0MOAAAg\nAElEQVTKGroYREREDYLJNdWZ05dScScjH8P9bKGuxq8WkSoZ2rMdNGVS7P0zQexQqJGQy+WYMWMG\nbt68ic2bN+OHH36Anp4eJk6ciOzsbCQkJODdd99Fv379EBoait69e2P69Om4fv26YhszZ87E/fv3\n8X//939Yvnw5Dh48iA0bNohYKiIiovrDDIjqRGlZOQ79k4S2Fi3QuZOZ2OEQUS3p68gwsLsNLiRm\nIu5GptjhUCMQHx+Pc+fO4YsvvoCrqys6dOiAlStXoqCgAGFhYdi9ezfc3d3x7rvvwtbWFu+99x48\nPDywe/duAMC5c+cQHR2N5cuXw8HBAb169cLcuXMREhKCkpISkUtHRERU95hcU504EX0XmTnFeNXf\nFlKJROxwiOg59PGyhqmBFvaeSEC5nK/mau4sLS2xbds2tGv3v1cqSv57fH/48CGioqLg4+OjtE6X\nLl0QFRUFAIiKioKVlRWsra0V8318fJCfn48rV640QAmIiIgaFpNremH5RaX45fRNOLc3QicbI7HD\nIaLnpKFe8Wquu/fz8XdsitjhkMgMDQ3h7+8PqfR/PxVCQkJQVFQEX19fpKamwtzcXGkdMzMzpKam\nAgDS0tJgZmZWZT4ApKTw+0VERE0Ph3OmF/bL6VsoKCrDyF62YodCRC/I084U9tYGOPRPErp0MoeO\nFk8TVOHEiRNYs2YNJk2aBFtbWxQVFUEmkyktI5PJUFxcDAAoLCyEpqam0nwNDQ1IJBLFMk9jaKgD\ndXW1ugk+IRMt9LTqZlsqwNS0hdghNIoYmhPWd8NifTcsVapv/mqiF5KVU4TjUXfQ1ckCbcxV54tP\nRNWTSCQY07sjPt0ViZ9P38SrAR3EDokagYMHDyIoKAgDBgzARx99BADQ1NREaanyu9FLSkqgra0N\nANDS0qrybHVpaSkEQYCOjk6N+8zOLqij6Cvk5hXV6fYas4yMXFH3b2raQhHDyfN3n7qsvzvfLvKi\nHq1vqn+s74bVGOv7ack+u4XTCwn9JwmAgGF+7WpclohUQ1uLFujuYoHjUbeRXscJDqmeLVu2YMGC\nBRgzZgy+/PJLRTdxS0tLpKenKy2bnp6u6CpuYWGBjIyMKvMBVOlOTkRE1BQwuabndic9D/9eTEVv\nr9YwaaktdjhEVIeG+9lCTSrFvr8SxQ6FRLR9+3asW7cOs2bNQlBQkGJAMwDw8vJCZGSk0vIRERHw\n9vZWzL99+7bS89URERHQ1dWFg4NDwxSAiIioATG5pue2PywRWprqeKWbjdihEFEdM2yhif5d2yD6\nWgauJmeLHQ6JID4+HmvXrsWIESPw6quvIiMjQ/FXUFCAcePGISoqCuvXr0diYiKCg4MRGxuLCRMm\nAAA8PDzg7u6O999/H5cuXUJYWBhWrlyJSZMmVXlWm4iIqCngM9f0XOJvZeNCYiZG+ttCT1tD7HCI\nqB687NMGYefv4YcTCQia6M3X7DUzR48eRXl5OQ4cOIADBw4ozZs9ezamTZuGjRs3YuXKldi+fTva\nt2+PrVu3wta2YnBLiUSCjRs3YsmSJRg7dix0dXUxatQoTJ8+XYziUD140vPULfS0mtUz7kRElZhc\nU60JgoB9JxNg2EITfbxaix0OEdUTTQ01jPK3xVc/Xca/F1Ph62opdkjUgObMmYM5c+Y8dRl/f3/4\n+/s/cb6pqSk2bdpUx5ERERE1TuwWTrUWdTUDN1JyMbRnO8g06ug1KUTUKHVxNEf7Vvo48HciikrK\nxA6HiIiIqNHinWuqlbJyOQ6EJcLKRBc9nHkXi6ipq3w11xch0Th2JhnD/NqLHRIR1QG+IouIqO4x\nuaZaCTt/D+nZhZg10hVSKZ+/JGoOOli1hE8nM/x6Nhl+bq1g3FJL7JCIqJ7VlHwTEVFV7BZOz6yw\nuAxHwm/AztoAbrbGYodDRA1opH/FIFUHwvhqLiIiIqLqMLmmZ/bb2WTkFpRiVICt0rtOiajpM2mp\njZc6W+PM5TQk3nsodjhEREREjU6jTa4XLVqETz75RGnaqVOnMGTIELi6umLQoEEICwsTKbrm52Fe\nMX47exve9qawbdVS7HCISAQDurZFS10ZfjhxHYIgiB0OERERUaPS6JJrQRAQHByMvXv3Kk1PSEjA\nu+++i379+iE0NBS9e/fG9OnTcf36dZEibV6OhN9EaZkcI3rZih0KEYlEW1Mdw/3aI/FuDs5eSRc7\nHCIiIqJGpVEl17dv38Ybb7yBPXv2oFWrVkrzdu/eDXd3d7z77ruwtbXFe++9Bw8PD+zevVukaJuP\n1KwChJ2/h17urWBupCN2OEQkoh4ulmhjpod9JxNQUloudjhEREREjUajSq5jYmJgaWmJn376Ca1b\nt1aaFxUVBR8fH6VpXbp0QVRUVEOG2CwdCEuEhroUg33biR0KEYlMKq14NVdWTjF+i7wtdjhERERE\njUajehXXkCFDMGTIkGrnpaamwtzcXGmamZkZUlNTGyK0Zivx7kNEX83A4B42aKkrEzscImoEHNoa\nwqOjCY6evoWernzfPRERERHQyJLrpykqKoJMppzcyWQyFBcX17iuoaEO1NXV6iu0emNq2kKU/Wpo\nVNSViYkeVv8YCwM9TYwd4AgdLY0X3rZYZaovTa08QNMrE8tTP94Z6YbpX/6JY2dvQ/+/x4znja2x\nlKmuNLXyEBER0bNRmeRaU1MTpaWlStNKSkqgra1d47rZ2QX1FVa9MTVtgYyMXFH2Xfrf5yiPn7mJ\nS0mZGNvXDvm5RcjPLXqh7YpZpvrQ1MoDNL0ysTz1RwNAb6/W+P1sMvq2L4GWTO25YmtMZaoLtS0P\nE3GqDyfP3xU7BCKiZqlRPXP9NJaWlkhPVx6dNj09vUpXcaobggAcOJkIM0Nt9HJvVfMKRNTsDOpu\nA11tDaSr4AVMIiIiorqmMsm1l5cXIiMjlaZFRETA29tbpIiatpz8Yty9n48RvWyhrqYyXxMiakA6\nWhoY2rMdCovLkFtQWvMKRERERE2YymRN48aNQ1RUFNavX4/ExEQEBwcjNjYWEyZMEDu0JkcuCLj/\nsAjtLPXhbW8qdjhE1Ij1cm8FmYYaMh4UorRMLnY4RERERKJRmeTa3t4eGzduxG+//YahQ4fizz//\nxNatW2Frayt2aE1Odm4xysrleDXAFhKJROxwiKgRU5NKYWagjdKycpyIviN2OERERESiabQDmoWE\nhFSZ5u/vD39//4YPphnJKShBVk4RdLU1YN/GUOxwiEgF6GprQFdLAz/9exPdXSygr8PX9hEREVHz\n02iTa2p4ZeVybA6Ng1QATA1qHoWdiKiSqaE2zmeX4/CpGxj/kr3Y4RBRE1LT6Of+7lYNsg0iopqo\nTLdwql+CIGD3r1dx7fYDWBjpQFND9d4LTkTi0dRQg79HK4Sdu4e7GXlih0NERETU4JhcEwDg14hk\nnLqYgsE9bKCvyy6dRFR7Q3zbQVOmhr1/JogdChEREVGDY3JNiL6agf0nE+HTyQxDfNuJHQ4RqagW\nOjIM7mGDuBtZ+Dv2ntjhEBERETUoJtfN3K3UXGz/+RJsLPUxeUAnjg5ORC+kt1drOLUzwu5fr+JC\n4n2xwyEiIiJqMEyum7Hs3GIE74+FnrYGZo1wgYzPWRPRC1JXk2LaUGdYm+lh86E4JN3LETskIiIi\nogbB5LqZKi4px/r9F1BYUo7ZI93QUk9T7JCIqInQ1lTHe6Ncoa8jw7p9sUjLKhA7JCIiIqJ6x+S6\nGZILAnb8fBnJ6bl4Z7ATrM30xA6JiJqYlnqa+GC0OwBg9d7zeJhfInJERERERPWL77luhkL/TkL0\ntQyM6d0Rbh1MxA6HiJoocyMdvDfKDV/uicG6H2Mx93UPaGvytENE/1PT+6eJiFQJ71w3M+EXU/DL\n6Vvwd2+Fvt6txQ6HiJq49q30MW2oM26n52HzoTiUlcvFDomIiIioXjC5bkau3X6AXcfi0amtIV7v\na8eRwYmoQbjammBCf3tcupGFb47GQxAEsUMiIiIiqnPsn9dMpGcXYOPBizA10Ma0Yc5QV+N1FSJq\nOD1dW+FBbjFC/7kBgxYyjPLvIHZIRERERHWKyXUzUFBUiuD9FyAIAmaPcoWulobYIRFRMzSwuw2y\n80pw7EwyDPU00cfbWuyQiIiIiOoMk+smrqxcji2H4pCeXYgPx7jD3FBH7JCIqJmSSCQY19cOD/OK\nsef4dbTU08QA0xZih0VERERUJ9g3uAkTBAHfH7+OSzez8UY/e9i3MRQ7JCJq5qRSCd4e7ATb1i2x\n/adLuJh4X+yQiIiIiOoEk+sm7Hj0HZw8dxf9u7ZBT9dWYodDRAQAkGmoYdYIV5gaaOPzryNwJz1P\n7JCIiIiIXhiT6yYqNuE+fjhxHR4dTTCil63Y4RARKdHT1sCcV92hKVPH2n2xyMopEjskIiIiohfC\n5LoJupOeh61HLsHaTA9vDXKClK/cIqJGyLilFpa+1Q1FJWVY82Ms8gpLxQ6JiIiI6LlxQLMm5mF+\nCYL3x0JbpobZI92gKVMTOyQioieysdTHzOGuWPPjeWw4cAEfjHaHTIPHLSJqfE6ev1vjMv7uVg0Q\nCRE1Vkyum5CS0nJsPHABuQWlmD/OE4YtNMUOiYioRg5tDTF1oCO2Hb6Er366jGlDnSGVsscNEame\nmhJwJt9ETRu7hTcRgiDg66NXkHgvB28OcoSNhb7YIRERPTOfTuYY07sjYq5l4Lvj1yAIgtghERER\nEdUK71w3EUfCb+LslXSM6NUeXvZmYodDRFRrfTtbIzuvGL9GJMNQTxMDu9uIHRIRERHRM2Ny3QSc\nuZyKw6duoIezBQZ0bSt2OEREz22kvy0e5BXj4N9JMNDThK+rpdghERERET0TJtcqLvHuQ3z9Szzs\nWrfEG/0cIOHI4ESkwqQSCSYP6ITc/BLsOhYPfV0ZXG2NxQ6LiJq4ZxmsjIioJnzmWoXdSs3FhgMX\nYNRCE9OHu0BDnc1JRKpPXU2KacNc0NpMF5sPXcSNlByxQyIiIiKqEbMxFVRWLsehf5Lw2e4oSKUS\nzB7lihY6MrHDIiKqM9qa6nh/lBv0dWRYty8WadkFYodERERE9FRMrlXMnYw8fL47GkfCb8KnkxmW\nTe0CS2NdscMiIqpzLfU0MWe0OwQBWLP3PFIy88UOiYiIiOiJ+My1ipDLBfx2Nhmh/yRBW1Md04e5\nwMveVOywiIjqlYWRDmaPcsW6H2Ox+OuzGNjNBv27tuVjMESkwOeliaixYHKtAtKyCrDjl8tIvJsD\nLztTjO9nD312AyeiZsK2VUt89mZX7Dl+DYdO3UDElTRM6OcAO2sDsUMjIiIiUmBy3YjJBQF/Rt/B\n/pOJUFeT4q1BjujiaM4RwYmo2WmpK8M7Q5zR3fk+Qn67huXfxcDfvRVG+ttCR0tD7PCIiIiImFw3\nVulZBVi15xzikx/Apb0xJvZ3gGELTbHDIiISlautCZZNNcChf27gj6jbOHf9Psb2tYOXvSkvPBJR\ns/B4N/gWelrIzStSmubvbtWQIRHRfzG5bmQEQcA/F1Kw988EyAUBE/s7oKerJX80EhH9l5ZMHWN6\nd0RXJ3PsOhaPzYfi4N7BBONesoORvpbY4REREVEzxeS6EcnOLca3v8bjQmImXDuYYFyfjjAx0BY7\nLCKiRsnGQh9BE7zxR+QdHDqVhE92RGC4X3v09mwNqZQXJImIiKhhMbluBARBQMTlNHz3xzWUlsnx\nep+OGP1yJ2Rm5okdGhFRo6YmlaJflzbwsjdFyG9Xsef4dZy5lIaJ/R1gbaYndnhERETUjDC5FllO\nQQlCfruK6KsZsG2ljykDHWFhpMO7LkREtWBqoI33X3VDxJU07Dl+HUu/icTLXawxpEc7yDTUxA6P\niOiZ8dViRKqLybWIYq5l4Ntf41FYXIaR/rbo59OGSTUR0XOSSCTo6mgB53bG+PGvBBw7k4zo+AyM\n72cPJxsjscMjImowNSXoHPCMqH4wuRZBflEpvv/jOk5fSkUbcz18NMYDrdl9kYioTuhpa2DygE7o\n5mSB3b/GY/UP59HNyQJjendACx2Z2OERUTPWWO5KP0scTMCJao/JdQOLS8rEN8fi8TCvBIN72GBg\ndxuoq0nFDouIqMnp1NYQn07xwU//3sKxM7dwMSkTowM7oLuzBd/AQERERHWOyXUDyXhQiKNnbiHs\n/D20MtHFjOEuaGepL3ZYRERNmoa6Gob7tYdPJzN8eyweO3+5gtOXUvHGy/YwM9QROzwiIiJqQphc\n16OHecU4G5+Os5fTkHgvBxIA/bq0wbCe7aChzgF2iIgaSmtTPSwY74WT5+5i/8lEBO08i8E9bBDg\nYQUdLQ2xwyMianTYdZyo9phc17GColJEX81AxJU0XLmVDUEArM30MNLfFj6dzGDSku+tJiISg1Qi\nQaBna3h0NMV3f1zDgbAkHPrnBjq1NYSXvSk8OppCX5fPZBMREdHzYXJdB4pLyxGbcB8Rl9NwMSkT\nZeUCzAy08Uo3G3RxNIeVia7YIRIR0X8ZttDEjOEuSLqXg6ir6Yi+mo5vf72K3b9dRcfWBvCyN4WX\nnSmM9LXEDpWIqFGriwHaePebmhIm18+prFyOyzezEXE5FTHX76O4pBwt9WQI8GiNLo7maGfZggPm\nEBE1Yu1b6aN9K32M8rfFnYx8RF9NR/S1DOw5fh17jl9HO8sW8LI3g5edKcyN+Hw2ERERPR2T61qQ\nCwIS7jzEmctpiIpPR15hKXQ01dGlkxm6dDKHfRtDvqeaiEjFSCQSWJvpwdpMD0N7tkdqVgFirmUg\n+mo69p9MxP6TibAy1YWXnSm87M3Q2lSXF0+JiIioCibXNRAEAclpeYi4nIaz8WnIyimGTF0K944m\n6OJoDud2xtBQ56u0iIiaCgsjHQzo2hYDurZF5sMixFzPQPTVDPwUfhNHwm/CzFAbXnam8LQ3RTtL\nfUiZaBMRNWocnI0aCpPragiCgNSsAkReSceZy2lIzSqAmlQC53ZGGNnLFu4dTaAlY9URETV1xi21\n0NfbGn29rfEwvwTn/5to/x55G8cikmHYQhOedhXPaHe0bil2uEREKofPbVNT0qwzRLlcwP2HhbiX\nWYCU+/m4l5mPlMwCpGTmo7C4HBIAdtYGeMnHGt72ZtDT5utaiIiaq5a6MvRyt0IvdysUFJUiNiET\nUVfT8XfsPZyIvgM9bQ10dbaEWUtNWBjpwMxIByb6WnxciIiIqJlQqeS6vLwc69atQ2hoKPLz89Gz\nZ08sWrQIJiYmT12vrFyOtKwCpGQW4N4jSXRqVgFKy+SK5VrqytDKRBfdnCxgZaoH9w4mMGyhWd/F\nIiIiFaOjpYFuzhbo5myB4pJyXEzKRMy1DJyOS0F+YaliOXU1CUwNtGFuqAMLIx2YG1X829xIBwZ6\nzeO1X8977iYiUjXsfk4qlVxv2LABoaGhWLFiBQwMDLB06VLMnDkTe/bseep676wKg1wQFJ9NWmqh\nlYkuHG0MYWmsi1YmumhlrAMdLd6ZJiKi2tGUqcHbwQzeDmYwMdFD4s1MpGUXIjWrAGlZBUjLLkRa\nVgHibmShrPx/F3Q1NdSwf/lAESNvGM977iYielZ10bX8WffTQk8LuXlFDbI/Uj0qk1yXlJRg9+7d\nWLhwIXr06AEAWLNmDXr37o2YmBh4eno+cd0B3dpUJNHGurAw1oGmhlpDhU1ERM2IRCJBSz1NtNTT\nhJ21gdI8uVxAVm4R0rIKkZZd0XuqqXuRczcRUUNqqASdmjaVSa7j4+ORn58PHx8fxbTWrVvDysoK\nUVFRTz1BD/ezbYgQiYiInkgqlcCkpTZMWmrDqZ2R2OE0iBc5dxMRNUU1JfHP0m28IbbxrNshZSqT\nXKempgIAzM3NlaabmZkp5hEREVHjwXM3EVHt1MUd9Lq6C98Qd/ObWgKvMsl1YWEhpFIpNDSUn4uW\nyWQoLi5+6rqmpi3qM7R6I1bcb745pd62rapt8SRNrTxA0ysTy1P/XvSY0RjL9CKaWnleRGM5d/dj\nmxARqSxVOq9KxQ7gWWlpaUEul6OsrExpeklJCbS1tUWKioiIiJ6E524iImpOVCa5trS0BABkZGQo\nTU9PT6/S3YyIiIjEx3M3ERE1JyqTXDs4OEBXVxdnz55VTLtz5w7u3r2Lzp07ixgZERERVYfnbiIi\nak5U5plrmUyG119/HV9++SUMDQ1hbGyMpUuXwsfHB+7u7mKHR0RERI/huZuIiJoTiSAIgthBPKuy\nsjKsWrUKoaGhKCsrQ8+ePbFo0SIYGTWPV5oQERGpGp67iYiouVCp5JqIiIiIiIioMVKZZ66JiIiI\niIiIGism1/WgvLwcq1evhq+vLzw8PDBr1izcv3//mdZ9++23MX78eKVpWVlZ+Oijj9C1a1d06dIF\ns2fPRmpqqtIyR44cwcsvvwxXV1e8+uqruHDhQp2VBxCnTN26dYO9vb3S3+bNmxtleTIyMvD++++j\na9eu6N69O5YtW4aCggKlZeqzjcQoT322z/OUafbs2VXimThxomJ+YWEhgoKC0KVLF3h7e2PhwoXI\nz89X2kZjaqO6KI+qtVElQRAwderUamNVpTZ6lvLUdxs1dy9ybKSnu3//PubNmwdfX194e3tjypQp\nuHbtmmL+qVOnMGTIELi6umLQoEEICwsTMdqm5fz583B0dERERIRiGuu77u3bt09xvhk+fDhOnz6t\nmMf6rlsFBQVYtmyZ4ngydepUJCQkKOarVH0LVOfWrl0r9OjRQzh16pQQFxcnjBo1ShgzZkyN6+3Z\ns0ews7MTxo0bpzR9woQJwpgxY4RLly4Jly9fFsaMGSMMHz5cMT88PFxwcnISfvjhByEhIUH45JNP\nBG9vbyEzM1Nly5SRkSHY2dkJkZGRQnp6uuIvPz+/0ZWnpKREGDhwoDBo0CAhKipKiIuLE0aPHi1M\nmDBBsUx9t1FDl6e+2+d5ytSvXz9h27ZtSvE8ePBAMf/DDz8U+vfvL5w7d06IjIwU+vbtK8yZM0cx\nv7G10YuWRxXbSBAEobi4WFiwYIFgZ2cnbNq0SWmeqrVRTeVpiDZq7p732EhPV15eLowePVp49dVX\nhdjYWOH69evCrFmzhG7duglZWVnC9evXBWdnZ2Hz5s1CQkKCsHbtWsHJyUm4du2a2KGrvPz8fKFv\n376CnZ2dcObMGUEQBNZ3PTh48KDg5OQk7Nu3T7h586bwxRdfCO7u7sLt27dZ3/Xg448/Fvr16ydE\nRUUJCQkJwrRp04RevXoJRUVFKlffTK7rWHFxseDh4SEcOHBAMe327duCnZ2dEB0d/cT1bt68Kfj4\n+AijR49WSnRyc3MFe3t74cSJE4ppJ0+eFOzs7ITs7GxBEARh8uTJwrx58xTzy8vLhd69ewtbtmxR\n2TL9+++/gqOjo1BSUlInZajP8vzxxx+CnZ2dcOPGDcW0lJQUwd7eXoiIiBAEoX7bSIzy1Gf7CELt\ny1RcXCw4OjoKp0+frnZ7KSkpgoODg+KHiCAIQkREhGBvby+kpqYKgtC42qguyqNqbSQIghAXFycM\nGTJECAwMFLy9vasko6rURs9Snvpuo+bueY+NVLNLly4JdnZ2QkJCgmJacXGx4ObmJoSGhgpBQUFV\nLqqPGzdOWLhwYUOH2uRU1u2jyTXru27J5XIhICBAWLdunWJaeXm5MHjwYOHIkSOs73rg4+Mj7N69\nW/H5+vXrgp2dnRAXF6dy9c1u4XUsPj4e+fn58PHxUUxr3bo1rKysEBUVVe065eXlmDdvHqZOnQpb\nW1uleZqamtDR0cGhQ4eQl5eH/Px8HDp0CG3btoW+vj7kcjliYmKU9ieVStG5c+cn7q+xlwkArl27\nBmtra2hoaNRJGeqzPDdv3oSpqSlsbGwU0ywsLGBoaIizZ8/Wexs1dHmA+m0foPZlSkpKQllZWZWy\nVIqJiYFUKoWnp6dimqenJ9TU1BAdHd3o2uhFywOoXhsBQHh4OLy9vXH48GG0aNFCaZ6qtVFN5QHq\nv42au+c5NtKzsbS0xLZt29CuXTvFNIlEAgB4+PAhoqKilOodALp06cJ6f0FhYWE4efIkFi5cqDSd\n9V23kpKScPfuXQwYMEAxTSqV4vDhwxg0aBDrux4YGRnh6NGjyMzMRElJCfbv34+WLVvC2tpa5eqb\nyXUdq3xu2NzcXGm6mZlZlWeKK23btg0AMGXKlCrzNDQ0sHz5ckRERMDb2xve3t6IjIzE9u3bIZVK\nkZOTg4KCglrtr7YaukwAcP36dairq+Ptt99Gjx49MHz4cBw6dKhRlsfMzAwPHjxQeiY5Ly8PDx8+\nRFZWVr23UUOXB6jf9gFqX6Zr165BQ0MDGzZsgL+/P15++WWsXbsWxcXFAIC0tDQYGRkpJTHq6uow\nMjJCSkpKo2ujFy0PoHptBABvvfUWFi5cCD09vSrrq1ob1VQeoP7bqLl7nmMjPRtDQ0P4+/srztkA\nEBISgqKiIvj6+iI1NZX1XseysrLwySef4LPPPkPLli2V5rG+69bNmzcBVJx33njjDXTr1g1jx45F\nTEwMANZ3fVi2bBlSU1PRvXt3uLu748cff8RXX30FfX19latvJtd1rLCwEFKptMqdCJlMpvSjq1Jc\nXBy++eYbrFixQukk9aikpCTY2dlh9+7dCAkJQbt27TBjxgzk5eWhqKgIQMXd4EdpaGhUuz9VKBMA\nJCQk4MGDBxg5ciR27tyJfv364eOPP8aBAwcaXXn8/Pygp6eHoKAg5OTkIDc3F4sXL4ZEIkFpaWm9\nt1FDlweo3/Z5njJVDnrRvn17bNu2DTNmzMD+/fuxaNEixfYer/9Ht9fY2uhFy1O5DVVqo5qoWhs9\ni/puo+autm1Kz+/EiRNYs2YNJk2aBFtbWxQVFUEmkyktw3p/MYsXL0ZgYCD8/PyqzGN9163K36Lz\n58/HqFGjsGPHDnTs2BETJkxAYmIi67se3Lp1CyYmJvjqq6+wZ88e+Pr6YtasWQRIRvwAACAASURB\nVEhNTVW5+lYXO4CmRktLC3K5HGVlZVBX/1/1lpSUQFtbW2nZ4uJizJ07F++99x7atm1b7faioqIQ\nHByMkydPKq7abNq0CQEBAQgNDcXAgQMV239UaWlplf2pSpnGjx+P3bt3o6SkRHHHx8HBAXfv3sWu\nXbswYsSIRlUeAwMDbNmyBfPnz4ePjw+0tLQwbtw4ODg4QE9PT5EM1FcbNXR5ANRr+9S2TADw3nvv\nYfLkyTAwMAAA2NvbQ01NDe+//z7mz58PLS2tKvVfuT0dHZ1G1UZ1UR5A9drI0NDwqftTtTaqqTxA\n/bdRc1fbNqXnc/DgQQQFBWHAgAH46KOPAFT8f628GFuJ9f78QkNDcfnyZRw5cqTa+azvulV5Qe6d\nd97BoEGDAACOjo6Ijo7Gnj17WN917Pbt2wgKCsL3338Pd3d3AMDq1asxYMAA7Nq1S+Xqm8l1HbO0\ntARQ8Sqjyn8DQHp6epUuDbGxsUhMTMSqVauwatUqABVfFrlcDg8PD/zyyy84f/48TE1NldbV19eH\njY0Nbt26BQMDA+jo6CA9PV1p29XtT1XKBFRckXr8KpWdnR1++eWXRleeVq1awcPDA7/99hsyMzOh\nq6sLTU1NdO3aFSNHjqz3Nmro8gD12z61LRNQ8SxUZZLzaDxARfctCwsLZGVloby8HGpqagCAsrIy\nZGVlwczMrFG1UV2UB1C9NqopGVW1NnqW5Lq+26i5q22bUu1t2bIF69atw7hx47Bw4ULFc9eWlpb1\n+rukuTl48CDS0tLg6+sLoOL1fgDw5ptvYujQoazvOlZ5Hq08pgMVYwq0b98ed+7cYX3Xsbi4OJSX\nl8PZ2VkxTUNDA506dcKtW7dUrr7ZLbyOOTg4QFdXVzHwEwDcuXMHd+/eRefOnZWWdXV1xe+//45D\nhw4p/vr06QNnZ2ccOnQIZmZmsLCwQGZmJjIzMxXrFRYW4s6dO7CxsYFEIoGHhwciIyMV8+VyOSIj\nI6vsT1XKVFZWhl69euGbb75R2nZcXBw6dOjQ6Mpz8+ZNvPbaa3jw4AGMjY2hpaWFqKgo5OTkoHv3\n7vXeRg1dnvpun9qWCah43/D06dOrxCOTydCmTRt4eXmhrKwM586dU8yvHMjMy8urUbVRXZRHFduo\nJqrWRjVpiDZq7mrbplQ727dvx7p16zBr1iwEBQUpEmsA8PLyUvq/CkAxzgrV3qpVq/DLL78ozts7\nduwAAHz22WeYPXs267uOOTk5QUdHBxcvXlRMEwQBiYmJsLa2Zn3XMQsLCwDA1atXFdMq69vGxkbl\n6lttyZIlS8QOoilRU1NDbm4udu7ciY4dOyIvLw8ff/wx2rZti2nTpqGkpARZWVnQ0NCApqYmDAwM\nlP5OnTqF/Px8TJo0CVKpFFZWVvjpp58QHh4OOzs7ZGdnY9myZcjMzMSyZcsgk8lgaGiI1atXw8DA\nALq6uli7di2uXLmCL774ok66TDR0mbS0tHDr1i388MMPaN++PdTU1HDgwAHs2rULy5Yte6Yfrg1Z\nHl1dXWzduhXXrl2Dg4MD4uPj8dFHH2HAgAEYNmwYANRrGzV0eaRSab22T23LpKamBkEQsHXrVujq\n6sLY2BinT5/G559/jnHjximeIU9MTMTevXvh6OiIe/fuYeHChQgICMDQoUMbVRvVRXlUsY0e9+23\n38LR0VEpCVKlNqqpPA3RRs1dTW1Kzy8+Ph7vv/8+hg8fjqlTp6KgoEDxJ5FIYGNjg3Xr1qGsrAwm\nJiYICQnBsWPH8J///AdGRkZih69y9PT0lM7bUqkUu3btwvjx49GxY0dYWVmxvuuQhoYGioqKsH37\ndrRt2xZqamrYsmULwsPD8fnnn8PZ2Zn1XYfMzMwQHh6OY8eOwc7ODoWFhVi3bh2io6OxfPlyODg4\nqFZ9i/gasCartLRU+M9//iP4+PgInp6ewuzZs4XMzExBEAThzJkzSu8mfNzHH39c5V1ud+7cEWbN\nmiV069ZN8PHxEd59910hOTlZaZn9+/cLgYGBgouLizB69GghLi5OpctUXFwsrFmzRggICBCcnJyE\nQYMGCb///nujLc+1a9eE8ePHC+7u7oKfn5+wZs0aobS0VGmZ+myjhi5PfbfP85QpNDRUGDhwoODi\n4iL4+/sLmzdvFsrLyxXz8/LyhPnz5wuenp6Cj4+PEBQUJBQWFirtszG10YuWRxXb6FEBAQFV3gst\nCKrVRjWVpyHaqLl7WpvS81u9erVgZ2dX7V/l9/yvv/4SBgwYIDg7OwuDBw8WwsPDRY666UhJSaly\nPGJ91y25XC5s3bpV6NWrl+Ds7CyMGjVKiIyMVMxnfdetzMxM4ZNPPhF69uwpeHl5CRMmTBAuX76s\nmK9K9S0RhP8+uEFEREREREREz4XPXBMRERERERG9ICbXRERERERERC+IyTURERERERHRC2JyTURE\nRERERPSCmFwTERERERERvSAm19RkcSB8qg1+X4iISFXwnEXUODG5boLGjx8Pe3t7pT9nZ2f06dMH\nX375JYqLi194H3fu3IG9vT0OHz5cJ/FOnDjxqcvMnz8fffv2VXy2t7fH5s2bAQARERGwt7dHVFQU\nACAtLQ1vv/027t69+8Kx1SQtLQ3jx4+Hi4sLunfvjpKSknrf56Mer5fHPV43zVFeXh6mTZsGNzc3\ndO7cGbdv31b67ubm5mL+/Pm1rqMNGzbA0dHxhZd5XgcPHoS9vT1SU1PrbJu1/X9dl8cBIqLnUdN5\nMDAwEJ988kmttvks6zy+3+fZz/Patm0bdu7cqfhcn+eamtT3eeDR33tEqkBd7ACofri4uGDhwoWK\nz8XFxYiMjMSmTZuQkpKCtWvXihhd7U2bNg35+fnVznNycsLevXvRoUMHAMCZM2dw8uRJBAUF1Xtc\nISEhiImJwcqVK2FhYQGZTFbv+6yNx+umOfrpp59w4sQJLFq0CB07doS5uTn27t2LNm3aAACuXr2K\n0NBQDBs2TORIxWdmZqZUN3W9PBFRU7Vx40a0aNGiQfYVHByMd999V/F51KhR8PPza5B9P47nASJl\nTK6bKD09Pbi7uytN69KlC1JTU7F//34sWLAAZmZmIkVXe087aFdX1oby4MEDmJmZYcCAAaLsvyZi\n1k1j8eDBAwDA66+/DolEAgDNvk6eRCaT1apuars8EVFTJdadYwCwsLCAhYWFKPvmeYBIGbuFNzOO\njo4QBAEpKSkAKroxLV++HOPHj4erqyuWLl0KAEhNTcXcuXPRs2dPuLm5YezYsTh79myV7aWmpmLK\nlClwdXVF79698c033yjNz8rKwuLFixEQEABnZ2f4+Phg5syZVbpsC4KA4OBgdO3aFZ6envjggw+Q\nkZGhmP+0bl+Pdn0+ePAg5s6dCwDo3bs35s+fjxUrVsDd3b3Kne/Vq1fD19cXZWVl1W63qKgIwcHB\nePnll+Hi4oIBAwZg7969ivmBgYHYt28f7t27B3t7e2zYsKHa7cyfPx9vv/02vvvuOwQEBMDNzQ1T\npkxBRkYG9u/fjz59+sDDwwMTJ07EnTt3FOsVFBRg5cqVeOmll+Ds7AxPT09MmTIF8fHx1e4HAM6f\nPw8PDw/Mnj0b5eXlVbqFb9iwAf369cOJEycwaNAgODs74+WXX67SnevatWuYPHkyPDw84Ofnh127\ndmHixImYP3/+E/cNAL/99htee+01eHh4wNnZGf3798f333//1HXmz5+PiRMn4rvvvkPPnj3h4eGB\nt956C8nJyYplKuNev349OnfuDD8/P+Tn56OsrAy7du3CK6+8ovgObtmyBeXl5QAqHjlYt24dAMDB\nwQHz589X6sIWERGBsWPHAgDeeOMNjB8/HgBQXl6Obdu2YeDAgXB1dYW7uztee+01REREVIn/119/\nRd++feHq6opx48bhwoULTy3vH3/8geHDh8PFxQW+vr5YsWJFjY8TyOVybN68Gf7+/nBzc8O0adPw\n8OHDKstFRkZi7NixcHNzQ5cuXbBw4ULk5OQoLZOUlITp06ejc+fO8PHxwbRp0xR1/Xj3PrlcjrVr\n1yIwMBDOzs4IDAzEmjVrUFpaWu3yAJCYmIhp06ahW7du8PDwwNSpU5W+s5XfyTNnzmDixIlwc3ND\njx49sGrVKkW7ERHVl5rOG9V5+PAhFixYAB8fH3Tu3BkrV66EXC5XWubRbuGVx8bff/8dM2bMgIeH\nB3x8fBAUFITCwkLFOiUlJVi+fDl8fX3h7u6O2bNnY9euXbC3t39iLPb29igvL8fGjRsVyz3eLXz8\n+PFYsmQJNmzYgB49esDDwwPvv/8+8vLy8NVXX6Fnz57w8vLCzJkzkZ2drVhPLpdj69at6NOnD5yd\nndGvXz/s27fvqfX5+Hng4MGDcHFxQUxMDEaNGgUXFxcEBATg66+/fup2AODs2bMYPXo03Nzc8PLL\nL+Pff/+tssyVK1cwffp0dO3aFU5OTvDz88Pnn3+ueORx1qxZCAwMrPJM+pw5czBixAgAQHJyMt55\n5x106dIFbm5uGD16NMLCwmqMj+hZMLluZm7evAkAsLa2VkwLCQmBp6cnNm3ahGHDhiE9PR0jR45E\nbGws5s6di7Vr10JLSwuTJk3C6dOnlbYXHBwMKysrbNq0CX369MHy5cuxfft2ABUJ89SpU3HmzBl8\n+OGH2LlzJ2bMmIHw8HAsWbJEaTuRkZE4fvw4li1bhqCgIJw+fRpTpkx5YuL7JP7+/pg5cyaAii5a\n06ZNw4gRI1BYWIg//vhDsZxcLseRI0cwaNAgqKtX7cAhCALefPNNfPvtt3jttdewZcsWdO/eHYsX\nL8amTZsU2w8MDISpqSn27t2LUaNGPTGuyMhIHDhwAIsWLcKiRYtw9uxZjB8/HiEhIZg/fz6WLVuG\n2NhYfPbZZ4p15s6di0OHDuHtt9/G119/jQULFuDq1av48MMPqx3IJD4+Hm+99Ra6d++O1atXQ01N\nrdpY0tLS8Pnnn2PChAn46quv0Lp1a8ybN0/x3cjKysL48eORlZWFlStXYs6cOdi5cyeio6OfWvcn\nTpzArFmz4Orqis2bN2PDhg1o3bo1li5dWmPCGRcXh61bt+LDDz/E559/jsTERLzxxhsoKChQLHP7\n9m2Eh4dj3bp1WLBgAXR1dfHJJ59g1apV6N+/P7Zs2YKhQ4di06ZNikcCFi9ejNGjRwMA9u7di2nT\npint18nJCZ9++ikAYNGiRVi8eDEA4Msvv8TWrVvx2muvYceOHVi2bBmys7Mxe/ZspR9G5eXlWLx4\nMaZMmYK1a9eiuLgYEyZMwO3bt6st508//YQZM2agY8eO2LRpE9555x3s3bsXH3zwwVPrZ+XKldi0\naRNGjhyJjRs3wsDAAKtXr1ZaJjIyEpMmTYKuri6Cg4Mxd+5cnDx5Uun/UVpaGkaPHo3bt2/j008/\nxfLly3Hnzh1MnDhRqa4rbd++HXv27MGMGTPw9ddfK+pj27Zt1cZ59epVjBw5EhkZGVi6dClWrFiB\n7OxsvPbaa0hISFBa9oMPPoCPj4/iIsb27dtx8ODBp9YDEdGTlJWVVfv3uJrOG4+Ty+WYOnUqwsLC\nMG/ePCxfvhwxMTE4evRojTEtXLgQ1tbW2Lx5M6ZMmYJ9+/YpHT+DgoKwZ88eTJkyBcHBwSgpKaly\nbH/c3r17oaamhpEjRypd8H/ckSNHcO7cOaxYsQKzZs3C0aNHMXLkSJw6dQqfffYZ5syZgxMnTmDj\nxo2KdZYsWYKNGzdi2LBh2Lp1KwICAhAUFISQkJAay/qosrIyzJkzB4MGDcL27dvh6emJFStWVPkN\n+ahLly5h8uTJaNGiBdavX4833ngDc+bMUVomLS0NY8eORXFxMVasWIHt27djwIAB2L17N3bv3g0A\nGDFiBO7evas0jkpeXh6OHz+O4cOHQy6X4+2330ZhYSG+/PJLbN68GQYGBnj33XeVLuoTPS92C2+i\nBEFQOqlkZ2fj77//xg8//IB+/frByMhIMc/a2hrvv/++4vOKFSuQk5ODffv2wdLSEkBF0jpkyBCs\nWrUKBw4cUCzbq1cvRXLSs2dPpKenY8eOHZg8eTIyMjKgq6uLhQsXwtPTE0BF1/Tk5GTs379fKV51\ndXXs2LED5ubmAABjY2O8+eabOHnyJPr06fPM5TYyMlJcOOjUqRNat24NAHBzc8Phw4cxdOhQABXP\nZaempmL48OHVbicsLAxnz55FcHAw+vXrBwCKu9xbt27F66+/DkdHRxgZGT1Tl6j8/HwEBwcrYvvj\njz/w119/4fjx44pp586dw88//wyg4hn5wsJCBAUFKfbv4+ODvLw8LF++HNnZ2UptePPmTUyZMgUe\nHh5Yu3ZttRcMKhUUFGDLli3o2rUrAMDGxgYBAQEICwuDjY0NQkJCUFxcjJ07d8LY2BgA0L59+6de\nPAAq7lgOHz4cCxYsUEzz8PBAly5dcPbsWbi6uj5x3dzcXOzcuRNubm4AAFtbWwwZMgShoaGKO8tl\nZWVYsGCBoq6vX7+OQ4cOYe7cuZgyZQoAoEePHtDS0sKqVaswceJE2NnZKbrKVa73aO8APT092Nra\nAgA6dOigeDY9PT0dc+bMUewbADQ1NTFz5kxcv35dqSyfffaZoleFp6cnAgMDsXv37ioD2wiCgFWr\nViEgIAArVqxQTLewsMD06dMRHR0NLy+vKnWTk5ODkJAQTJ48GTNmzADwv/9r//zzj2K51atXw9bW\nFlu3boVUWnHd1NHREcOGDcPRo0cxePBg7Nq1C2VlZfjmm2+U2nbSpEm4fPlylW6FZ8+ehbOzs+L/\niY+PD7S1tZ/4XOGmTZugra2Nb7/9Fjo6Ooo26du3L9avX4/169crlh09erTiYkfXrl1x/PhxnDx5\nssbvGRHR45KTk+Hk5FTjcs963njU33//jQsXLmDHjh3o2bMnAKBbt24IDAyscX8BAQGYN2+eYp3w\n8HCcPHkS7733HpKTk3H48GEEBQUpzjU9e/bE4MGDcf369Sdus/JcZmFh8dTfHoIgYP369dDT04Ov\nry8OHjyIu3fvYt++fYpj+D///INz584BAG7cuIEff/wRc+fOxeTJkwFU/O4pLy9HcHAwRo4cCW1t\n7RrLDFRckJg5c6biTrGnp6fid0+3bt2qXWfbtm0wNTXFli1boKGhAQAwNDRU+n169epVODk5ITg4\nGLq6ugCA7t27Izw8HJGRkXjzzTfh6+sLc3NzHD58GJ07dwYAHDt2DIIg4JVXXkFmZiaSkpIwbdo0\n9OrVCwDg6uqKjRs31smAv0RMrpuoM2fOVDnRqKmpoU+fPlXuGnfq1Enpc1RUFLy8vBSJNQBIpVIM\nGDAAwcHByMvLU0yvTPwq9e7dG0ePHkViYiLs7OwQEhICQRBw584d3Lp1C0lJSYiJiVF0K63k6emp\nSKwBwM/PDzKZDNHR0bVKrp9kxIgRWLJkCdLS0mBubo7Q0FA4OzujY8eO1S4fGRkJDQ0NvPTSS0rT\nBw0ahD179iA2Nhb+/v7PvH9jY2Ol3gLGxsYwNDRUmmZgYIDc3FwAFYlc5UigaWlpuHHjBm7evIm/\n/voLAJTqLzc3F5MmTUJWVhaWLl36TIOqVV7sAKBIqCrvyJ45cwZeXl6K5AuoOPFYWVk9dZtvvfUW\ngIoLCTdu3EBycjIuXrxYJd7qWFtbKxJroKLbW9u2bREVFaWU4D76XY2MjAQADBw4UGlbgwcPxqpV\nqxAZGVnlR9KzqhzwLysrC0lJSbh161a1da+hoYHevXsrPhsaGsLT07Pau/xJSUlITU3F9OnTlS58\n9ezZExoaGvj333+rTa7Pnz+P0tJSpf0AQP/+/RXJdWFhIWJjY/HWW29BLpcruit27NgRrVq1wr//\n/ovBgwcjOjoanp6eSm1rY2OjKNujFx6Aiothq1evxuuvv47AwED4+/tj3LhxT6y3qKgoBAYGKhJr\nANDV1UVgYCCOHz+utOyj30Gg4nv4aK8AIqJnZWFhoXQH9lGPDvz1POeNqKgoaGpqKhJrANDR0UGv\nXr0QExPz1LiqO86lpaUBqHhERhAEpd8ZUqkU/fr1e2py/aw6dOgAPT09xWdjY2PIZDKli6MGBgZI\nTEwEUHHuFwQBAQEBSueowMBAfPvtt7hw4QK6dOnyzPt/tOwymQxGRkZPPcZHR0ejd+/eisQaAF56\n6SWlXnh+fn7w8/NDaWkpEhIScOvWLVy7dg1ZWVkwMTEBUPFbd+jQodizZw8WLVoEmUyG0NBQBAQE\nwMDAAIIgoEOHDggKCsKpU6fg6+sLPz8/pRsDRC+CyXUT5erqikWLFgEAJBIJtLS0YGVlVe1Vx0d/\nCAMVzxbZ2NhUWc7ExASCICg9u1x5MKtU+aO9Mkk8cuQI1qxZg5SUFBgYGKBTp07Q0tKq0q350R/7\nlYyMjBTbeVGvvPIKvvjiC/z8888YM2YMjh8/jg8//PCJyz98+BDGxsaKO4CVKstb27gqr7A+6vF6\nf9w///yDL774AklJSdDV1YWDg4NinUfrLzs7G7a2tsjJycHq1auxcuXKp25XTU1NKQGvLGNlQpaV\nlaW44/8oU1PTp2638vn648ePQyKRoG3btvD29q4Sb3WqG1zP2NhY6XlhNTU1aGpqKj5XPnP8+Hfn\n8e/g87h48SKWLl2KixcvQltbGx06dECrVq0AKJfF0NCwynfEyMio2m7hlQOrBQUFVdv9MD09vdpY\nKsv5aE8FQLk9cnJyFM/Kbd269YnbfvDgAdq2bVvtfqozdepU6Orq4sCBA1i1ahVWrlyJjh07YuHC\nhYqeD4/H+vgxAahok0cvygGAlpaW0mepVFrlGUYiomchk8ng4uLyxHmVnue88fDhQxgaGlaZXtM5\nEXj6cS4rKwtA1WN7dcfQ51Hb3x2V56jHb5pUetI56kke/71Z0zH+4cOHVepCXV1dqe7lcjnWrFmD\n7777DgUFBbC0tISrqys0NTWVzs3Dhw/Htm3b8Oeff8LJyQkxMTGK7vgSiQRff/01tmzZgj/++AOH\nDh2ChoYG+vTpg6VLl6Jly5a1KifR45hcN1G6urpPPNHURF9fH/fv368yvfLAamhoqPj344MqVQ5C\nZmxsjKioKMybNw8TJkzApEmTFHemv/zyS5w/f15pvccHXRIEAZmZmdUm3c9DT08PL730Eo4dOwZL\nS0uUlZVVuXL9KH19fWRmZkIulyslT5Xlq+5EW5eSk5Mxffp09O3bV/FctEQiwXfffafUFRioqOsd\nO3Zg3759+PzzzzFkyBD4+vo+977Nzc0VJ/1HZWZmol27dk9c78MPP8SNGzewa9cueHh4QCaTobCw\nED/++GON+6w8qT++v6d1JdfX11cs92ivhxdto7y8PEydOhWdOnXCL7/8gvbt20MqlSIsLAy//fab\n0rK5ubkQBEExCjkA3L9/v8oPBACKuwULFiyo9g71k+KtnH7//n2lUfMfrTNdXV1IJBJMnjwZ/fv3\nr7KNyh9Zenp61bbtqVOnFN3jHyWVSjF27FiMHTsWmZmZCAsLw9atWzFr1iyEh4dXWf5Jx46MjAwY\nGBhUWz4ioobyPOcNQ0NDZGVlVTnWV3feqo3K/WdmZipdYM7MzHyh7T6vynPU//3f/1W5KACg2ovu\ndcnAwKBK2QVBUPqd+dVXX2HXrl349NNP0bdvX0XMI0eOVFrPxsYGXl5eOHbsGJKTk2FiYqL0u8jc\n3BxLlizB4sWLER8fj19//RXbt2+HsbFxg7zGlZo2DmhGVXTu3BnR0dFITU1VTJPL5fj111/h4uKi\ndBX48UTv119/hbm5Odq2bYtz584pnrupPImUl5fj33//rXL1MiYmRunO1okTJ1BaWgofH59ax/+k\ngbxGjBiBuLg4fP/99wgMDHzq1UkfHx+Ulpbi999/V5r+888/Q0ND46lJX12Ii4tDcXEx3nnnHVhb\nWytO6JX1/Wj96erqQltbG2PHjoWjoyMWL178Qt1rO3fujJiYGKURROPj46t0Gf5/9u4/qKr7zv/4\n6xqEKxe+VgkXqXFjxBTSGgRFWBsnYl07NK0h06w2Vlu5WxMkFEHTQvOrsZM0Uq/4A9GSQicGaJvo\nBLpJ7dbuZiutuzPoJcRUY+qPLV2b8jtWzJVfJnz/cDz1ipItH34I9/mYcQbO55wPn/fhJofXvZ/z\nOdeqra1VSkqKkpKSrNfIb3/72z7jvZ76+nprQbUrP+9Pf/rTdT8dvXqckqz71K+48v31Auz1XPt6\n+Z//+R/99a9/VVpammbOnGm9uXKllqvfHe/o6PBZNKW5uVm1tbXXnToXFRWlyZMn67333tPdd99t\n/Zs0aZK2bNliTc27Vnx8vOx2u371q1/5bL8ylVu6HJo//elPq76+3qfvO+64Q9u3b9fRo0etc1JX\nV+fzR+F7772nNWvWXHcl9K9+9avWInthYWH68pe/rJUrV+r8+fPXfY3NmzdPv/nNb3wWR7t48aJ+\n85vf/J9/HwAwVAZy3Zg/f766u7v1xhtvWNu6u7uv+wbj32POnDm65ZZbfPqV1Of767l2xtRguDLT\n7Pz58z7XkYaGBhUWFg75bTvz58/Xb37zG3V2dlrbfve73/ncilVbW6vo6Gh9+ctftoJ1U1OTTp48\n2efvjAcffFC//e1v9atf/Ur333+/da1/++239dnPflZvv/22bDab7rrrLq1fv16f+tSnrCfpACb4\n5Bp9uFwu/eu//qtWr16trKwsORwO/fSnP9WZM2f0ox/9yGfff/u3f9OUKVOUmJioAwcO6I033lB+\nfr5sNpsVQJ999lk98MADOn/+vCoqKvTuu++qt7dXnZ2d1rujly5dUkZGhh555BE1NDSooKBA//iP\n/6jPfvazf/f4r/wP99///d917733Wp/IJSUlaerUqTpy5EifOq517733at68eXryySfV2NioO++8\nU9XV1Xr55Ze1du1a693vofKZz3xGAQEBcrvdSktLU1dXlyorK3Xw4EFJdYR3JgAAIABJREFUuu5F\n7pZbbtH3vvc9feUrX1FhYaG1iMrf62tf+5oqKiq0Zs0aZWRkqKurS9u2bZPNZvN51/5asbGxeu21\n13TXXXcpIiJCb775pn70ox/JZrN97EX5o48+UkZGhnJycnTp0iUVFBRo5syZWrp06Q2P+dSnPqX7\n779f27ZtU0dHh+Lj41VXV6fi4mLdf//91uJkH+fK7/LgwYOaOHGi7rjjDoWEhGj37t2y2WwaN26c\nDhw4YC3kd3VwHD9+vPLy8vStb31LgYGBKiwsVGhoqL7+9a/3+Tm33HKLcnJy9L3vfU/jxo3Tvffe\nq7/+9a/auXOnLly4cMNnpDocDj366KPavn277Ha7EhMTdfDgQZ9wLUnZ2dlau3atvvOd7+i+++5T\nd3e3SkpKdPLkSeu1cOW/7W984xtau3atbDabioqKNGPGDH3+85/v86l2YmKiSkpKdOuttyo+Pl5N\nTU168cUXNX/+fP2///f/+sw4yczM1PLly5WWlqY1a9ZIkkpLS3Xx4sU+K7UDwHAbyHVj/vz5WrBg\ngZ544gm1trYqMjJSZWVlev/99697S9P/1T/8wz8oNTVVmzdvVldXl6KiolRVVaUTJ070e62VLl+3\n3nzzTR05csQKxaZiYmL0pS99SU888YTOnj2ru+66S6dOndK2bdv0mc98xro1aqhkZmbqP/7jP/Tw\nww/rX/7lX9Ta2qodO3b43IN95WkkJSUlmj17tv70pz/phRdeUHd3d5+/M1JSUvTcc8/p+PHj2rx5\ns0+dwcHBys3NVVZWlm699Vb993//t06cOCGXyzWkNcI/EK7Rh9Pp1M9+9jNt2bJFzzzzjD766CPN\nmjVLL774Yp9P5B5//HH94he/UGlpqSIjI/WDH/zAWpE7KSlJ3/3ud/Xiiy9q//79uvXWW5WUlKTV\nq1crMzNTHo/HmqbzT//0T3I6ndqwYYN6e3v1hS98QXl5eR97gbmexMRE3XPPPSooKFBNTY11D6rN\nZtPChQv161//+mOnTY8bN04vvPCCtm/frtLSUus+9CvhdajdfvvtKigoUFFRkdauXauJEycqLi5O\n5eXl+trXviaPx3PdabyxsbFavny5XnrppX6nvffnE5/4hF566SV9//vf14YNGzRp0iSlp6eruLj4\nuvdwXZGfn69nn33WWj3+yvl67bXXPvYxXtOmTdNXv/pVfe9731N3d7eSk5P1xBNPfOzibJs2bdLt\nt9+uyspKFRcX65Of/KSysrKsYPd/cccdd+hLX/qSfvKTn+jQoUN6/fXXtXv3bm3evFnr1q2Tw+HQ\nXXfdpYqKCj388MOqra21VhidPHmysrOz5Xa71dbWpnnz5mnHjh03vJ3hK1/5ikJCQlRaWqqf/vSn\nCgkJ0bx587Rhw4Z+799LT09XcHCwXnrpJb344ouKj49XXl6ez+KECxcuVGlpqYqKipSVlaWgoCDd\nfffdKisrsxbo+eQnP6mf/OQncrvdys3NVVBQkD772c8qNzdXwcHBfcJ1VlaWAgIC9Oqrr2rXrl0K\nDQ3V4sWLb/josOjoaP3kJz/R1q1blZeXp3HjxikhIUGvvPJKv89tBYDhMpDrRlFRkbZs2aLt27er\nq6tL9913n5YvX2694T1QzzzzjIKDg1VcXKzOzk4tXrxYDz30kPXM6Bt55JFHtGvXLj388MN9ZjWZ\nyM/PV3FxsSoqKtTU1KRbb71V//zP/6x169YN2s+4kenTp6uiokL5+fnKyclRWFiY9eizK9LT03Xu\n3Dm99NJLunDhgiIjI5WamiqbzaYf/ehH+uCDD6xF3BwOhxITE/X+++/7vGkSGBioH//4xyooKND3\nv/99tbe3a/r06Xr22WeVmpo65HVi7LP1ftxKQ8AY8dFHHyklJUVf+MIXfB7tAF9Hjx7VBx98oHvu\nucfa1t7eboWw630qa+I73/mOamtrfZ5DDgDAWPbXv/5Vv/vd77Rw4UKf2XDZ2dn63//9X1VVVY3g\n6EY/r9ere++9V3l5eVq+fPlIDwd+hE+uMeZduHBBL730kurq6tTU1KSvfvWrIz2km9qf//xnfetb\n31JOTo7i4+PV3t6uPXv2KDQ0dMCfhgMAgL+x2+169tln9dprr2nVqlUKCgrSf/3Xf+nXv/61vv/9\n74/08EatP//5z/r5z3+u3/3ud5owYUK/t5cBQ4FwjTHPbrfr5ZdfVm9vrzZt2uSzQij6+uIXv6j3\n339fr7zyinbv3m3d5/uzn/3suqtgAwCAv4/dbtePf/xjbd++Xbm5uers7FRUVJR+8IMf6P777x/p\n4Y1a48aNU3l5uRwOhwoKCq77CFpgKDEtHAAAAAAAQzyKCwAAAAAAQ4RrAAAAAAAMEa4BAAAAADBE\nuAYAAAAAwBDhGgAAAAAAQ4RrAAAAAAAMEa4BAAAAADBEuAYAAAAAwBDhGgAAAAAAQ4RrAAAAAAAM\nEa4BAAAAADBEuAYAAAAAwBDhGgAAAAAAQ4RrAAAAAAAMEa4BAAAAADBEuAYAAAAAwBDhGgAAAAAA\nQ4RrAAAAAAAMBYz0AIZDS8uFkR7CkJg0KVjnzl0c6WEMC3+qVfKvev2pVsm/6vWnWqWB1RseHjpE\noxn9xuq1W/K//zaGAufQDOfPDOfPzGg/f/1du/nkehQLCLhlpIcwbPypVsm/6vWnWiX/qtefapX8\nr14MHK8Vc5xDM5w/M5w/M2P5/BGuAQAAAAAwRLgGAAAAAMAQ4RoAAAAAAEOEawAAAAAADBGuAQAA\nAAAwRLgGAAAAAMAQ4RoAAAAAAEOEawAAAAAADBGuAQAAAAAwRLgGAAAAAMAQ4RoAAAAAAEOEawAA\nAAAADBGuAQAAAAAwRLgGAAAAAMAQ4RoAAAAAAEMBIz0AABjrDr71nvV1aIhdFz7o9GlPjps63EMC\nAAD9uPrafa3QELvmzgwbxtFgtOCTawAAAAAADBGuAQAAAAAwRLgGAAAAAMAQ4RoAAAAAAEOEawAA\nAAAADBGuAQAAAAAwRLgGAAAAAMAQ4RoAAAAAAEOEawAAAAAADBGuAQAAAAAwRLgGAAAAAMDQoIXr\n06dPKzo6us8/j8cjSTp06JBSU1MVGxurpUuXqrq62uf4trY2ZWdnKyEhQfPnz5fb7dalS5d89tmz\nZ48WLVqk2bNny+Vyqb6+frCGDwAAAADAgAUMVkcnT57UpEmT9Prrr/ts/8QnPqHTp08rIyNDjz76\nqD7/+c/r9ddfV2ZmpqqqqnTnnXdKkrKysmSz2VRRUaGmpiZ95zvfUUBAgNavXy9J2rdvnwoLC/X8\n88/rjjvu0LZt27RmzRr98pe/VGBg4GCVAQAAAADA323QPrk+efKkZs6cqfDwcJ9/48ePV1lZmeLi\n4pSRkaGoqCjl5OQoPj5eZWVlkqS6ujrV1tYqPz9fMTExWrhwoXJzc1VeXq7u7m5JUmlpqVwul1JS\nUhQdHa2CggK1tbXpwIEDg1UCAAAAAAADMmjh+tSpU5oxY8Z12zwejxITE322JSUlWVPGPR6Ppk6d\nqmnTplntiYmJ8nq9OnHihNra2lRfX+/Th8Ph0KxZs6w+AAAAAAAYKYM2LfzUqVPq6urS8uXL9d57\n7+nOO+/Uhg0bFBsbq8bGRkVERPjs73Q61djYKElqamqS0+ns0y5JDQ0NCgi4PMz++gAAAAAAYKQM\nSrju7OzU2bNnNXnyZOXm5iowMFAVFRVatWqVqqqq1NnZ2ee+6MDAQHV1dUmSOjo6FBQU5NM+fvx4\n2Ww2dXV1qaOjQ5L67HN1H/2ZNClYAQG3mJR40woPDx3pIQwbf6pV8q96x3qtoSH2fr8fy/WP5dqu\nx9/qBQAAfzMo4dput+vIkSMKDAy0QnR+fr6OHz+un/70pwoKClJPT4/PMd3d3ZowYYJ1/JV7q6/o\n6elRb2+vgoODZbfbrWNu1Ed/zp27OODabmbh4aFqabkw0sMYFv5Uq+Rf9fpDrRc+6LS+Dg2x+3wv\naczW7w+/26sNpF7COAAAY8eg3XMdEhLi8+n0uHHjNHPmTDU0NCgyMlLNzc0++zc3N1vTvKdMmaKW\nlpY+7dLlqeCRkZGSdN19rp0qDgAAAADAcBuUcH3s2DHNmTNHx44ds7Z9+OGHevfdd3XnnXdq7ty5\nOnLkiM8xNTU1SkhIkCTNnTtXZ8+eVUNDg0+7w+FQTEyMwsLCNH36dB0+fNhq93q9OnbsmObNmzcY\nJQAAAAAAMGCDEq5jYmI0depUffe739XRo0d16tQpPf744zp37py+/vWva9WqVfJ4PCosLNSZM2e0\nY8cOHT16VKtXr5YkxcfHKy4uTuvXr9fx48dVXV0tt9stl8tlfRqelpamkpIS7d+/XydPntRjjz0m\np9OpJUuWDEYJAAAAAAAM2KDccx0QEKDS0lJt3rxZa9euVUdHh+bMmaOKigqFhYUpLCxMRUVFcrvd\nKikp0YwZM1RcXKyoqChJks1mU1FRkTZu3KiVK1fK4XBo2bJlyszMtH7GihUr1N7erk2bNsnr9WrO\nnDkqLS3ts1AaAAAAAADDbdAexRUREaGCgoIbticnJys5OfmG7eHh4dq1a1e/PyM9PV3p6ekDHSIA\nAAAAAENi0BY0AwAAAADAXxGuAQAAAAAwRLgGAAAAAMAQ4RoAAAAAAEOEawAAAAAADBGuAQAAAAAw\nRLgGAAAAAMAQ4RoAAAAAAEOEawAAAAAADBGuAQAAAAAwRLgGAAAAAMAQ4RoAAAAAAEOEawAAAAAA\nDBGuAQAAAAAwRLgGAAAAAMAQ4RoAAAAAAEOEawAAAAAADBGuAQAAAAAwRLgGAAAAAMAQ4RoAAAAA\nAEOEawAAAAAADBGuAQAAAAAwRLgGAAB666239OlPf1o1NTXWtkOHDik1NVWxsbFaunSpqqurfY5p\na2tTdna2EhISNH/+fLndbl26dMlnnz179mjRokWaPXu2XC6X6uvrh6McAACGHeEaAAA/d/HiReXm\n5urDDz+0tp0+fVoZGRlKSUlRVVWVFi9erMzMTJ06dcraJysrS62traqoqFB+fr4qKyu1c+dOq33f\nvn0qLCxUXl6e9u7dq6CgIK1Zs0bd3d3DWh8AAMOBcA0AgJ/Lz89XRESEz7aysjLFxcUpIyNDUVFR\nysnJUXx8vMrKyiRJdXV1qq2tVX5+vmJiYrRw4ULl5uaqvLzcCs+lpaVyuVxKSUlRdHS0CgoK1NbW\npgMHDgx7jQAADDXCNQAAfqy6uloHDx7UU0895bPd4/EoMTHRZ1tSUpI8Ho/VPnXqVE2bNs1qT0xM\nlNfr1YkTJ9TW1qb6+nqfPhwOh2bNmmX1AQDAWBIw0gMAAAAj4/3339eTTz6p559/XhMnTvRpa2xs\n7PNpttPpVGNjoySpqalJTqezT7skNTQ0KCDg8p8Y/fUBAMBYQrgGAMBPPfPMM/rc5z6ne++9t0/g\n7ezsVGBgoM+2wMBAdXV1SZI6OjoUFBTk0z5+/HjZbDZ1dXWpo6NDkvrsc3Uf/Zk0KVgBAbf83TWN\nFuHhoSM9hFGPc2iG89e/0BB7v+2cPzNj9fwRrgEA8ENVVVV655139Nprr123PSgoSD09PT7buru7\nNWHCBEmS3W7vszBZT0+Pent7FRwcLLvdbh1zoz76c+7cxf9zLaNNeHioWloujPQwRjXOoRnO38e7\n8EHnDdtCQ+ycPwOj/fXX3xsDhGsAAPxQZWWlmpqatGDBAklSb2+vJOnhhx/WAw88oMjISDU3N/sc\n09zcbE3znjJlSp9Hc13ZPyIiQpGRkZKklpYW3X777T77REVFDU1RAACMIMI1AAB+aMuWLers/Nsn\nMy0tLVq5cqWee+453XPPPdq+fbuOHDnic0xNTY0SEhIkSXPnztWWLVvU0NBgBemamho5HA7FxMQo\nMDBQ06dP1+HDh61jvF6vjh07poceemiYqgQAYPgQrgEA8EPXLjR25d7oiIgIhYWFadWqVXrwwQdV\nWFioL37xi/rFL36ho0ePauPGjZKk+Ph4xcXFaf369Xr66afV2toqt9stl8tl3audlpamzZs36/bb\nb9edd96prVu3yul0asmSJcNaKwAAw4FwDQAA+oiOjlZRUZHcbrdKSko0Y8YMFRcXW1O6bTabioqK\ntHHjRq1cuVIOh0PLli1TZmam1ceKFSvU3t6uTZs2yev1as6cOSotLe2zUBoAAGMB4RoAAGjKlCn6\nwx/+4LMtOTlZycnJNzwmPDxcu3bt6rff9PR0paenD8YQAQC4qY0b6QEAAAAAADDaEa4BAAAAADBE\nuAYAAAAAwBDhGgAAAAAAQ4RrAAAAAAAMEa4BAAAAADBEuAYAAAAAwBDhGgAAAAAAQ4RrAAAAAAAM\nEa4BAAAAADBEuAYAAAAAwBDhGgAAAAAAQ4RrAAAAAAAMEa4BAAAAADBEuAYAAAAAwBDhGgAAAAAA\nQ4RrAAAAAAAMEa4BAAAAADBEuAYAAAAAwBDhGgAAAAAAQ4Mert966y19+tOfVk1NjbXt0KFDSk1N\nVWxsrJYuXarq6mqfY9ra2pSdna2EhATNnz9fbrdbly5d8tlnz549WrRokWbPni2Xy6X6+vrBHjoA\nAAAAAAMyqOH64sWLys3N1YcffmhtO336tDIyMpSSkqKqqiotXrxYmZmZOnXqlLVPVlaWWltbVVFR\nofz8fFVWVmrnzp1W+759+1RYWKi8vDzt3btXQUFBWrNmjbq7uwdz+AAAAAAADMighuv8/HxFRET4\nbCsrK1NcXJwyMjIUFRWlnJwcxcfHq6ysTJJUV1en2tpa5efnKyYmRgsXLlRubq7Ky8ut8FxaWiqX\ny6WUlBRFR0eroKBAbW1tOnDgwGAOHwAAAACAARm0cF1dXa2DBw/qqaee8tnu8XiUmJjosy0pKUke\nj8dqnzp1qqZNm2a1JyYmyuv16sSJE2pra1N9fb1PHw6HQ7NmzbL6AAAAAABgJAUMRifvv/++nnzy\nST3//POaOHGiT1tjY2OfT7OdTqcaGxslSU1NTXI6nX3aJamhoUEBAZeH2F8fAAAAAACMpEEJ1888\n84w+97nP6d577+0TeDs7OxUYGOizLTAwUF1dXZKkjo4OBQUF+bSPHz9eNptNXV1d6ujokKQ++1zd\nx8eZNClYAQG3/F01jRbh4aEjPYRh40+1Sv5V71ivNTTE3u/3Y7n+sVzb9fhbvQAA4G+Mw3VVVZXe\neecdvfbaa9dtDwoKUk9Pj8+27u5uTZgwQZJkt9v7LEzW09Oj3t5eBQcHy263W8fcqI+Pc+7cxf/T\nfqNNeHioWloujPQwhoU/1Sr5V73+UOuFDzqtr0ND7D7fSxqz9fvD7/ZqA6mXMA4AwNhhHK4rKyvV\n1NSkBQsWSJJ6e3slSQ8//LAeeOABRUZGqrm52eeY5uZma5r3lClT+jya68r+ERERioyMlCS1tLTo\n9ttv99knKirKdPgAAAAAABgzDtdbtmxRZ+ffPoVpaWnRypUr9dxzz+mee+7R9u3bdeTIEZ9jampq\nlJCQIEmaO3eutmzZooaGBitI19TUyOFwKCYmRoGBgZo+fboOHz5sHeP1enXs2DE99NBDpsMHAAAA\nAMCYcbi+dqGxK/dGR0REKCwsTKtWrdKDDz6owsJCffGLX9QvfvELHT16VBs3bpQkxcfHKy4uTuvX\nr9fTTz+t1tZWud1uuVwu617ttLQ0bd68WbfffrvuvPNObd26VU6nU0uWLDEdPgAAAAAAxgZlQbP+\nREdHq6ioSG63WyUlJZoxY4aKi4utKd02m01FRUXauHGjVq5cKYfDoWXLlikzM9PqY8WKFWpvb9em\nTZvk9Xo1Z84clZaW9lkoDQAAAACAkTDo4XrKlCn6wx/+4LMtOTlZycnJNzwmPDxcu3bt6rff9PR0\npaenD8YQAQAAAAAYVONGegAAAAAAAIx2hGsAAAAAAAwRrgEAAAAAMES4BgAAAADAEOEaAAAAAABD\nhGsAAAAAAAwRrgEAAAAAMES4BgAAAADAEOEaAAAAAABDhGsAAAAAAAwRrgEAAAAAMES4BgAAAADA\nEOEaAAAAAABDhGsAAAAAAAwRrgEAAAAAMES4BgAAAADAEOEaAAAAAABDhGsAAAAAAAwRrgEAAAAA\nMES4BgAAAADAEOEaAAAAAABDhGsAAAAAAAwRrgEAAAAAMES4BgAAAADAEOEaAAAAAABDhGsAAAAA\nAAwRrgEAAAAAMES4BgAAAADAEOEaAAAAAABDhGsAAAAAAAwRrgEA8FONjY1at26dEhMTlZCQoPXr\n16upqclqP3TokFJTUxUbG6ulS5equrra5/i2tjZlZ2crISFB8+fPl9vt1qVLl3z22bNnjxYtWqTZ\ns2fL5XKpvr5+OEoDAGDYEa4BAPBDvb29euSRR9Te3q6ysjJVVFSopaVFGRkZkqTTp08rIyNDKSkp\nqqqq0uLFi5WZmalTp05ZfWRlZam1tVUVFRXKz89XZWWldu7cabXv27dPhYWFysvL0969exUUFKQ1\na9aou7t72OsFAGCoEa4BAPBDra2tioqK0nPPPaeYmBjFxMQoLS1Nx48f1/nz51VWVqa4uDhlZGQo\nKipKOTk5io+PV1lZmSSprq5OtbW1ys/PV0xMjBYuXKjc3FyVl5db4bm0tFQul0spKSmKjo5WQUGB\n2tradODAgZEsHQCAIUG4BgDAD4WHh2vbtm267bbbJF2eIv7KK6/o7rvv1sSJE+XxeJSYmOhzTFJS\nkjwejyTJ4/Fo6tSpmjZtmtWemJgor9erEydOqK2tTfX19T59OBwOzZo1y+oDAICxJGCkBwAAAEbW\no48+qjfeeEMTJ060PplubGxURESEz35Op1ONjY2SpKamJjmdzj7tktTQ0KCAgMt/YvTXBwAAYwnh\nGgAAP5edna21a9dq9+7dcrlc+vnPf67Ozk4FBgb67BcYGKiuri5JUkdHh4KCgnzax48fL5vNpq6u\nLnV0dEhSn32u7qM/kyYFKyDgFpOybmrh4aEjPYRRj3NohvPXv9AQe7/tnD8zY/X8Ea4BAPBz0dHR\nkqRt27YpOTlZVVVVCgoKUk9Pj89+3d3dmjBhgiTJbrf3WZisp6dHvb29Cg4Olt1ut465UR/9OXfu\n4oDrudmFh4eqpeXCSA9jVOMcmuH8fbwLH3TesC00xM75MzDaX3/9vTHAPdcAAPih1tZW7d+/32fb\nhAkTNG3aNDU1NSkyMlLNzc0+7c3NzdY07ylTpqilpaVPu3R5KnhkZKQkXXefa6eKAwAwFhCuAQDw\nQ3/5y1+0YcMG/f73v7e2XbhwQX/84x81c+ZMzZ07V0eOHPE5pqamRgkJCZKkuXPn6uzZs2poaPBp\ndzgciomJUVhYmKZPn67Dhw9b7V6vV8eOHdO8efOGuDoAAIYf4RoAAD80a9YsJSQk6KmnntLbb7+t\nd955Rzk5OZo8ebIeeOABrVq1Sh6PR4WFhTpz5ox27Niho0ePavXq1ZKk+Ph4xcXFaf369Tp+/Liq\nq6vldrvlcrmse7XT0tJUUlKi/fv36+TJk3rsscfkdDq1ZMmSkSwdAIAhwT3XAAD4oXHjxmnnzp3a\nvHmz0tPT1dXVpQULFqiiokIOh0PR0dEqKiqS2+1WSUmJZsyYoeLiYkVFRUmSbDabioqKtHHjRq1c\nuVIOh0PLli1TZmam9TNWrFih9vZ2bdq0SV6vV3PmzFFpaWmfhdIAABgLCNcAAPipyZMnKz8//4bt\nycnJSk5OvmF7eHi4du3a1e/PSE9PV3p6+kCHCADAqMG0cAAAAAAADBGuAQAAAAAwRLgGAAAAAMAQ\n4RoAAAAAAEOEawAAAAAADBGuAQAAAAAwRLgGAAAAAMAQ4RoAAAAAAEOEawAAAAAADBGuAQAAAAAw\nRLgGAAAAAMAQ4RoAAAAAAEODFq4bGxu1bt06JSYmKiEhQevXr1dTU5PVfujQIaWmpio2NlZLly5V\ndXW1z/FtbW3Kzs5WQkKC5s+fL7fbrUuXLvnss2fPHi1atEizZ8+Wy+VSfX39YA0fAAAAAIABG5Rw\n3dvbq0ceeUTt7e0qKytTRUWFWlpalJGRIUk6ffq0MjIylJKSoqqqKi1evFiZmZk6deqU1UdWVpZa\nW1tVUVGh/Px8VVZWaufOnVb7vn37VFhYqLy8PO3du1dBQUFas2aNuru7B6MEAAAAAAAGbFDCdWtr\nq6KiovTcc88pJiZGMTExSktL0/Hjx3X+/HmVlZUpLi5OGRkZioqKUk5OjuLj41VWViZJqqurU21t\nrfLz8xUTE6OFCxcqNzdX5eXlVnguLS2Vy+VSSkqKoqOjVVBQoLa2Nh04cGAwSgAAAAAAYMAGJVyH\nh4dr27Ztuu222yRdniL+yiuv6O6779bEiRPl8XiUmJjoc0xSUpI8Ho8kyePxaOrUqZo2bZrVnpiY\nKK/XqxMnTqitrU319fU+fTgcDs2aNcvqAwAAAACAkRIw2B0++uijeuONNzRx4kTrk+nGxkZFRET4\n7Od0OtXY2ChJampqktPp7NMuSQ0NDQoIuDzM/voAAAAAAGCkDHq4zs7O1tq1a7V79265XC79/Oc/\nV2dnpwIDA332CwwMVFdXlySpo6NDQUFBPu3jx4+XzWZTV1eXOjo6JKnPPlf30Z9Jk4IVEHCLSVk3\nrfDw0JEewrDxp1ol/6p3rNcaGmLv9/uxXP9Yru16/K1eAADwN4MerqOjoyVJ27ZtU3JysqqqqhQU\nFKSenh6f/bq7uzVhwgRJkt1u77MwWU9Pj3p7exUcHCy73W4dc6M++nPu3MUB13MzCw8PVUvLhZEe\nxrDwp1ol/6rXH2q98EGn9XVoiN3ne0ljtn5/+N1ebSD1EsYBABg7Bm1Bs/379/tsmzBhgqZNm6am\npiZFRkaqubnZp725udma5j1lyhS1tLT0aZcuTwWPjIyUpOvuc+1UcQAAAAAAhtughOu//OUv2rBh\ng37/+99b2y5cuKA//vGPmjlzpubOnasjR474HFNTU6OEhARJ0txhCDTwAAAgAElEQVS5c3X27Fk1\nNDT4tDscDsXExCgsLEzTp0/X4cOHrXav16tjx45p3rx5g1ECAAAAAAADNijhetasWUpISNBTTz2l\nt99+W++8845ycnI0efJkPfDAA1q1apU8Ho8KCwt15swZ7dixQ0ePHtXq1aslSfHx8YqLi9P69et1\n/PhxVVdXy+12y+VyWfdqp6WlqaSkRPv379fJkyf12GOPyel0asmSJYNRAgAAAAAAAzYo91yPGzdO\nO3fu1ObNm5Wenq6uri4tWLBAFRUVcjgcio6OVlFRkdxut0pKSjRjxgwVFxcrKipKkmSz2VRUVKSN\nGzdq5cqVcjgcWrZsmTIzM62fsWLFCrW3t2vTpk3yer2aM2eOSktL+yyUBgAAAADAcBu0Bc0mT56s\n/Pz8G7YnJycrOTn5hu3h4eHatWtXvz8jPT1d6enpAx0iAAAAAABDYlCmhQMAAAAA4M8I1wAAAAAA\nGCJcAwAAAABgiHANAAAAAIAhwjUAAAAAAIYI1wAAAAAAGCJcAwAAAABgiHANAAAAAIAhwjUAAAAA\nAIYI1wAAAAAAGCJcAwAAAABgiHANAAAAAIAhwjUAAAAAAIYI1wAAAAAAGCJcAwAAAABgiHANAAAA\nAIAhwjUAAAAAAIYI1wAAAAAAGCJcAwAAAABgiHANAAAAAIAhwjUAAAAAAIYI1wAAAAAAGCJcAwAA\nAABgiHANAAAAAIAhwjUAAAAAAIYI1wAAAAAAGCJcAwAAAABgiHANAAAAAIAhwjUAAAAAAIYI1wAA\nAAAAGCJcAwAAAABgiHANAAAAAIAhwjUAAAAAAIYI1wAAAAAAGCJcAwAAAABgiHANAAAAAIAhwjUA\nAAAAAIYI1wAAAAAAGCJcAwAAAABgiHANAICfam1tVV5enhYsWKCEhAR94xvf0MmTJ632Q4cOKTU1\nVbGxsVq6dKmqq6t9jm9ra1N2drYSEhI0f/58ud1uXbp0yWefPXv2aNGiRZo9e7ZcLpfq6+uHozQA\nAIYd4RoAAD/00Ucf6Zvf/Kbq6+u1e/duvfzyywoJCVFaWprOnTun06dPKyMjQykpKaqqqtLixYuV\nmZmpU6dOWX1kZWWptbVVFRUVys/PV2VlpXbu3Gm179u3T4WFhcrLy9PevXsVFBSkNWvWqLu7eyRK\nBgBgSBGuAQDwQ++++67q6ur0/PPPKzY2VjNnzpTb7dbFixdVXV2tsrIyxcXFKSMjQ1FRUcrJyVF8\nfLzKysokSXV1daqtrVV+fr5iYmK0cOFC5ebmqry83ArPpaWlcrlcSklJUXR0tAoKCtTW1qYDBw6M\nZOkAAAwJwjUAAH4oMjJSL7zwgu644w5rm81mkySdP39eHo9HiYmJPsckJSXJ4/FIkjwej6ZOnapp\n06ZZ7YmJifJ6vTpx4oTa2tpUX1/v04fD4dCsWbOsPgAAGEsI1wAA+KFJkyYpOTlZ48b97U+B8vJy\ndXZ2asGCBWpsbFRERITPMU6nU42NjZKkpqYmOZ3OPu2S1NDQYO3XXx8AAIwlASM9AAAAMPLeeOMN\nbd26VS6XS1FRUers7FRgYKDPPoGBgerq6pIkdXR0KCgoyKd9/Pjxstls6urqUkdHhyT12efqPvoz\naVKwAgJuMSnpphYeHjrSQxj1OIdmOH/9Cw2x99vO+TMzVs8f4RoAAD9XWVmpp59+Wvfdd5++/e1v\nS7ocint6enz26+7u1oQJEyRJdru9z8JkPT096u3tVXBwsOx2u3XMjfroz7lzFwdcz80uPDxULS0X\nRnoYoxrn0Azn7+Nd+KDzhm2hIXbOn4HR/vrr740BpoUDAODHfvjDH+rxxx/XQw89pM2bN1vTxCMj\nI9Xc3Oyzb3NzszXNe8qUKWppaenTLl2eCh4ZGSlJ193n2qniAACMBYRrAAD8VElJibZv365169bp\n6aefthY0k6S5c+fqyJEjPvvX1NQoISHBaj979qwaGhp82h0Oh2JiYhQWFqbp06fr8OHDVrvX69Wx\nY8c0b968Ia4MAIDhR7gGAMAPvfvuu9q2bZsefPBBLV++XC0tLda/ixcvatWqVfJ4PCosLNSZM2e0\nY8cOHT16VKtXr5YkxcfHKy4uTuvXr9fx48dVXV0tt9stl8tl3audlpamkpIS7d+/XydPntRjjz0m\np9OpJUuWjGTpAAAMCe65BgDAD/3yl7/Uhx9+qFdffVWvvvqqT1t2drYeffRRFRUVye12q6SkRDNm\nzFBxcbGioqIkXX5sV1FRkTZu3KiVK1fK4XBo2bJlyszMtPpZsWKF2tvbtWnTJnm9Xs2ZM0elpaV9\nFkoDAGAssPX29vaO9CCG2mi+Yb4/o30xgL+HP9Uq+Ve9/lDrwbfes74ODbH3WSQlOW7qcA9pWPjD\n7/ZqA6l3rK6WOhjG8mvH3/7bGAqcQzOcv4939bX7WqEhds2dGTaMoxlbRvvrjwXNAAAAAAAYQoRr\nAAAAAAAMEa4BAAAAADBEuAYAAAAAwNCghevW1lbl5eVpwYIFSkhI0De+8Q2dPHnSaj906JBSU1MV\nGxurpUuXqrq62uf4trY2ZWdnKyEhQfPnz5fb7dalS5d89tmzZ48WLVqk2bNny+Vyqb6+frCGDwAA\nAADAgA1KuP7oo4/0zW9+U/X19dq9e7defvllhYSEKC0tTefOndPp06eVkZGhlJQUVVVVafHixcrM\nzNSpU6esPrKystTa2qqKigrl5+ersrJSO3futNr37dunwsJC5eXlae/evQoKCtKaNWvU3d09GCUA\nAAAAADBggxKu3333XdXV1en5559XbGysZs6cKbfbrYsXL6q6ulplZWWKi4tTRkaGoqKilJOTo/j4\neJWVlUmS6urqVFtbq/z8fMXExGjhwoXKzc1VeXm5FZ5LS0vlcrmUkpKi6OhoFRQUqK2tTQcOHBiM\nEgAAAAAAGLBBCdeRkZF64YUXdMcdd1jbbDabJOn8+fPyeDxKTEz0OSYpKUkej0eS5PF4NHXqVE2b\nNs1qT0xMlNfr1YkTJ9TW1qb6+nqfPhwOh2bNmmX1AQAAAADASBmUcD1p0iQlJydr3Li/dVdeXq7O\nzk4tWLBAjY2NioiI8DnG6XSqsbFRktTU1CSn09mnXZIaGhqs/frrAwAAAACAkRIwFJ2+8cYb2rp1\nq1wul6KiotTZ2anAwECffQIDA9XV1SVJ6ujoUFBQkE/7+PHjZbPZ1NXVpY6ODknqs8/VffRn0qRg\nBQTcYlLSTSs8PHSkhzBs/KlWyb/qHeu1hobY+/1+LNc/lmu7Hn+rFwAA/M2gh+vKyko9/fTTuu++\n+/Ttb39b0uVQ3NPT47Nfd3e3JkyYIEmy2+19Fibr6elRb2+vgoODZbfbrWNu1Ed/zp27OOB6bmbh\n4aFqabkw0sMYFv5Uq+Rf9fpDrRc+6LS+Dg2x+3wvaczW7w+/26sNpF7COAAAY8egPuf6hz/8oR5/\n/HE99NBD2rx5szVNPDIyUs3NzT77Njc3W9O8p0yZopaWlj7t0uWp4JGRkZJ03X2unSoOAAAAAMBw\nG7RwXVJSou3bt2vdunV6+umnrQXNJGnu3Lk6cuSIz/41NTVKSEiw2s+ePauGhgafdofDoZiYGIWF\nhWn69Ok6fPiw1e71enXs2DHNmzdvsEoAAAAAAGBABu1RXNu2bdODDz6o5cuXq6Wlxfp38eJFrVq1\nSh6PR4WFhTpz5ox27Niho0ePavXq1ZKk+Ph4xcXFaf369Tp+/Liqq6vldrvlcrmse7XT0tJUUlKi\n/fv36+TJk3rsscfkdDq1ZMmSwSgBAAAAAIABG5R7rn/5y1/qww8/1KuvvqpXX33Vpy07O1uPPvqo\nioqK5Ha7VVJSohkzZqi4uFhRUVGSLj+2q6ioSBs3btTKlSvlcDi0bNkyZWZmWv2sWLFC7e3t2rRp\nk7xer+bMmaPS0tI+C6UBAAAAADDcbL29vb0jPYihNlYX1PGnxYL8qVbJv+r1h1oPvvWe9fX1FjRL\njps63EMaFv7wu70aC5oNrrH82vG3/zaGAufQDOfv41197b5WaIhdc2eGDeNoxpbR/vrr79o9qAua\nAQAAAADgjwjXAAAAAAAYIlwDAAAAAGCIcA0AAAAAgCHCNQAAAAAAhgjXAAAAAAAYIlwDAAAAAGCI\ncA0AAAAAgCHCNQAAAAAAhgjXAAAAAAAYIlwDAAAAAGCIcA0AAAAAgCHCNQAAAAAAhgjXAAAAAAAY\nIlwDAAAAAGCIcA0AAAAAgCHCNQAAAAAAhgjXAAAAAAAYIlwDAAAAAGCIcA0AAAAAgCHCNQAAAAAA\nhgjXAAAAAAAYIlwDAAAAAGCIcA0AAAAAgCHCNQAAAAAAhgjXAAAAAAAYIlwDAAAAAGCIcA0AAAAA\ngCHCNQAAAAAAhgjXAAAAAAAYIlwDAAAAAGCIcA0AAAAAgCHCNQAAAAAAhgjXAAAAAAAYIlwDAAAA\nAGCIcA0AAAAAgCHCNQAAAAAAhgjXAAAAAAAYIlwDAAAAAGCIcA0AAAAAgCHCNQAAAAAAhgjXAAAA\nAAAYIlwDAAAAAGCIcA0AAAAAgCHCNQAAAAAAhgjXAAAAAAAYIlwDAAB997vf1ZNPPumz7dChQ0pN\nTVVsbKyWLl2q6upqn/a2tjZlZ2crISFB8+fPl9vt1qVLl3z22bNnjxYtWqTZs2fL5XKpvr5+qEsB\nAGBEEK4BAPBjvb292rFjh1555RWf7adPn1ZGRoZSUlJUVVWlxYsXKzMzU6dOnbL2ycrKUmtrqyoq\nKpSfn6/Kykrt3LnTat+3b58KCwuVl5envXv3KigoSGvWrFF3d/ew1QcAwHAhXAMA4KfOnj2rr3/9\n6/rZz36mT37ykz5tZWVliouLU0ZGhqKiopSTk6P4+HiVlZVJkurq6lRbW6v8/HzFxMRo4cKFys3N\nVXl5uRWeS0tL5XK5lJKSoujoaBUUFKitrU0HDhwY9loBABhqhGsAAPzUm2++qcjISL3++uu67bbb\nfNo8Ho8SExN9tiUlJcnj8VjtU6dO1bRp06z2xMREeb1enThxQm1tbaqvr/fpw+FwaNasWVYfAACM\nJQEjPQAAADAyUlNTlZqaet22xsZGRURE+GxzOp1qbGyUJDU1NcnpdPZpl6SGhgYFBFz+E6O/PgAA\nGEsI1wAAoI/Ozk4FBgb6bAsMDFRXV5ckqaOjQ0FBQT7t48ePl81mU1dXlzo6OiSpzz5X99GfSZOC\nFRBwi0kJN7Xw8NCRHsKoxzk0w/nrX2iIvd92zp+ZsXr+CNcAAKCPoKAg9fT0+Gzr7u7WhAkTJEl2\nu73PwmQ9PT3q7e1VcHCw7Ha7dcyN+ujPuXMXTYZ/UwsPD1VLy4WRHsaoxjk0w/n7eBc+6LxhW2iI\nnfNnYLS//vp7Y4B7rgEAQB+RkZFqbm722dbc3GxN854yZYpaWlr6tEuXp4JHRkZK0nX3uXaqOAAA\nY8GQhGuelQkAwOg2d+5cHTlyxGdbTU2NEhISrPazZ8+qoaHBp93hcCgmJkZhYWGaPn26Dh8+bLV7\nvV4dO3ZM8+bNG54iAAAYRoMarnlWJgAAY8OqVavk8XhUWFioM2fOaMeOHTp69KhWr14tSYqPj1dc\nXJzWr1+v48ePq7q6Wm63Wy6Xy7pXOy0tTSUlJdq/f79Onjypxx57TE6nU0uWLBnJ0gAAGBKDFq55\nViYAAGNHdHS0ioqKdODAAT3wwAP6z//8TxUXFysqKkqSZLPZVFRUpLCwMK1cuVJPPPGEli1bpszM\nTKuPFStWaO3atdq0aZO+8pWvqKenR6WlpX0WSgMAYCwYtAXNrjwrc+vWrdqwYYNPm8fj0Re+8AWf\nbUlJSdq/f7/V3t+zMm+77bZ+n5W5dOnSwSoDAAC/VF5e3mdbcnKykpOTb3hMeHi4du3a1W+/6enp\nSk9PNx0eAAA3vUEL1zwrEwAAAADgr4blUVw8K3PojNVnxF2PP9Uq+Ve9Y73Wa5+Vee33Y7n+sVzb\n9fhbvQAA4G+GJVzzrMyhMdqfEff38KdaJf+q1x9qvfpZmaEh9j7Pzhyr9fvD7/ZqA6mXMA4AwNgx\nLM+55lmZAAAAAICxbFjCNc/KBAAAAACMZcMSrnlWJgAAAABgLBuWe66vPCvT7XarpKREM2bMuO6z\nMjdu3KiVK1fK4XBc91mZ7e3t2rRpk7xer+bMmcOzMgEAAAAAN4UhCdc8KxMAAADAWHXwrff6bU+O\nmzpMI8HNZFimhQMAAAAAMJYRrgEAAAAAMES4BgAAAADAEOEaAAAAAABDhGsAAAAAAAwRrgEAAAAA\nMES4BgAAAADAEOEaAAAAAABDhGsAAAAAAAwRrgEAAAAAMES4BgAAAADAEOEaAAAAAABDhGsAAAAA\nAAwRrgEAAAAAMES4BgAAAADAEOEaAAAAAABDhGsAAAAAAAwRrgEAAAAAMES4BgAAAADAEOEaAAAA\nAABDhGsAAAAAAAwRrgEAAAAAMES4BgAAAADAEOEaAAAAAABDhGsAAAAAAAwRrgEAAAAAMES4BgAA\nAADAEOEaAAAAAABDhGsAAAAAAAwRrgEAAAAAMES4BgAAAADAEOEaAAAAAABDhGsAAAAAAAwRrgEA\nAAAAMES4BgAAAADAEOEaAAAAAABDhGsAAAAAAAwRrgEAAAAAMES4BgAAAADAEOEaAAAAAABDhGsA\nAAAAAAwRrgEAAAAAMES4BgAAAADAEOEaAAAAAABDASM9AAAY7Q6+9d5IDwEAAAAjjE+uAQAAAAAw\nRLgGAAAAAMAQ4RoAAAAAAEOEawAAAAAADLGgGQCMsP4WREuOmzqMIwEAAMBA8ck1AAAAAACGCNcA\nAAAAABhiWjgAAAAADCJu+fJPhGsAAAAAfqe/AAwMxKiaFv7hhx+qoKBACxYsUHx8vNatW6fW1taR\nHhYAALgBrt0AAH8xqsL1zp07VVVVpR/84AeqqKhQY2OjsrKyRnpYAADgBrh2AwD8xaiZFt7d3a2y\nsjI99dRTuueeeyRJW7du1eLFi/Xmm29qzpw5IzxCAGPZSE0d+7ify31buJlx7QYA+JNRE67fffdd\neb1eJSYmWttuu+02TZ06VR6Phws0AAA3Ga7dAEbSzXpPNW+cj12jJlw3NjZKkiIiIny2O51Oqw0A\nBupmvQB/HC7QuJlx7QYwlEbrtfvjcG0fvUZNuO7o6NC4ceM0fvx4n+2BgYHq6urq99jw8NChHNqI\nGsu1XcufapX8q96bodZlS2JGeghj0s3wux1O/lbvx+HafWNjvb7hwDk0MxbOH9fu0WssvP6uZ9Qs\naGa32/XRRx/p0qVLPtu7u7s1YcKEERoVAAC4Ea7dAAB/MmrC9f9v776jorjaP4B/qRrUKGgor0aN\nCouydFhAUBEUDa+9YcFEiR39SbDRLcSChaaIGrvGgFGISoyaRESDDcQk4mvDWAlIU1Skc39/cHbC\nwMIurtJ8PufsOTJ3Zu59dsZ95s7cmdHR0QEAZGdn86ZnZWXVGG5GCCGEkMZHuZsQQsiHpNl0rvX1\n9dGmTRtcvXqVm/b06VOkp6fD0tKyEVtGCCGEEEkodxNCCPmQNJt7rlVVVTF58mSsX78e6urq6Nix\nI1auXAmRSAQTE5PGbh4hhBBCqqHcTQgh5EOiwBhjjd0IWZWVlWHjxo2IjY1FWVkZ+vXrh4CAAGho\naDR20wghhBAiAeVuQgghH4pm1bkmhBBCCCGEEEKaomZzzzUhhBBCCCGEENJUUee6Ed28eRPTpk2D\nhYUF7Ozs4OvrixcvXvDm2bt3LwYOHAhjY2NMnz4dDx8+5JXfuHEDEydOhLGxMZycnPDjjz/yygsL\nC+Hv7w8rKytYWFjAz88PBQUFvHmOHz+OIUOGwMjICBMmTMBff/31XuIVKykpwYgRI3Ds2DHe9IKC\nAujr60MgEPA+VedrbvHWFivQMret2HfffVdjO/bp04c3T0PE31SUl5dj06ZNsLOzg6mpKf7v//4P\nOTk5jd0smaSlpdXYlgKBAMnJyQCA33//HSNHjoSRkRGGDx+OhIQE3vK5ublYuHAhLCwsYGNjgw0b\nNtR4LZO0faEhBAQEwNfXlzetIWKTtp+TlkeeHEgqyZNbyb9kydWErznn86ZA2jFFi8BIo8jMzGSW\nlpbM19eXpaWlseTkZDZs2DD25ZdfcvMcPnyYmZqasp9//pndvn2bzZ49mzk6OrLi4mLGGGO5ublM\nJBKxVatWsbS0NLZ//37Wp08fduHCBW4dixcvZp9//jm7fv06S0pKYoMHD2aenp5ceWJiIjMwMGBR\nUVEsLS2N+fr6MgsLC5abm/te4n716hWbMWMG09PTYz/++COv7I8//mACgYA9fvyYZWVlcZ+ioqJm\nGW9dsbbEbVtVQEAAmzNnDm87ZmdnN2j8TUlISAiztbVlv//+O0tNTWXjx49nEydObOxmyeSnn35i\nVlZWvG2ZlZXFSkpK2L1795hQKGRbt25laWlpLCQkhBkYGLC7d+9yy0+aNIlNnjyZ3bp1i507d45Z\nW1uz4OBgrlzavvC+VVRUsNDQUKanp8d8fHy46Q0Rmyz7OWlZ5MmBpJI8uZXwScvVpKbmnM+bgrqO\nKVoK6lw3kj179jBbW1tWVlbGTUtKSmJ6enosPT2dMcaYk5MTCw8P58pfv37NTExM2PHjxxljjG3b\nto05ODiw8vJybh4vLy82ffp0xhhjGRkZTF9fn12+fJkrv3LlChMIBCwzM5MxxpibmxtbtmwZV15e\nXs4cHR1ZZGTkO485MTGROTo6stGjR9eaFO3t7WtdvjnFKy3WlrZtq5s0aRILCwurtbwh4m8qiouL\nmampKTt69Cg37cmTJ0xPT49du3atEVsmm5CQEDZlyhSJZf7+/szV1ZU3zdXVlfn5+THGGEtJSWF6\nenrs8ePHXHlMTAwzNTXlDnal7Qvv0+PHj5mrqyuzsrJi9vb2vM51Q8QmbT8nLYu8OZDIn1sJn7Rc\nTfiaez5vCuo6pmgpaFh4I3FwcEBoaCiUlJS4aQoKCgCAly9fIjc3Fw8fPoRIJOLK27RpA6FQyA2d\nSE5OhqWlJRQV/92MIpEIKSkpYIwhJSUFioqKMDMz48rNzMygpKSEa9euoaKiAikpKbw6FBUVYWlp\n+V6GZ5w9exajRo1CVFSUxPJ79+6hR48etS7fnOKtK9aWuG2rS0tLQ8+ePSWWNUT8Tcnt27dRUFDA\ni7dLly7o3LlzsxgGVdf/y+TkZF5cAGBlZcXbjp07d8ann37KlYtEIhQUFODWrVsy7QvvU0pKCnR0\ndHDixAl06dKFV9YQsUnbz0nLIm8OJPLnVsJXV64mNTX3fN4UfAi/c83mPdctTdeuXdG1a1fetG+/\n/RZaWlrQ1dXF7du3AQBaWlq8eTQ1NZGZmQkAyMzMrHFvjKamJgoLC/H8+XM8e/YMGhoaUFFR4cqV\nlZWhoaGBjIwMvHz5Em/evJFYx40bN95ZrGJ+fn51lt+7dw9FRUWYOnUq7t+/j65du2Lu3LkYMGAA\ngOYVb12xirdfS9q2VT179gz5+fk4f/48Nm/ejMLCQlhaWmLJkiXQ0tJqkPibElnibcru3buH4uJi\nTJgwAenp6dDV1YWnpyeMjIyQmZlZZ1zPnj2DpqZmjXIAyMjIgLJyZQpqrO9m5MiRGDlypMSyhohN\n2n5Or6pqWeTNgUT+3Er+JS1Xk5poH5NfXccULQV1rt+Tp0+fwtHRUWKZqqpqjQ7Oxo0bce7cOURE\nREBJSQmFhYUAgFatWtVYtri4GABQVFQEVVXVGuVA5cM+CgsLayxfdR1FRUUS61BRUeHqkFV945Xk\n3r17aNu2Lfz8/KCuro64uDjMnj0be/bsgY2NTZOJV95Ym9u2rU5a/JGRkQAqO7shISF4/vw5goOD\nMW3aNMTGxjZI/E1JYWEhFBUVeScCgKbZ1uqKiorw5MkTaGhoYOnSpVBVVcXBgwfh6uqK2NjYWreT\nOC5J20lFRQUKCgooLi6WaV9oLA0Rm7T9nDQfDZEDW7qGyK0fEnlzdevWrRuyuc1Cc87nTYG0Y4qW\nMoqCOtfviZaWFk6ePCmxrOoQwPLycqxatQrR0dFYsWIF90Mo/lGrfoBVUlKCjz76iJtHUjkAfPTR\nRxLLxfOoqalxCaj6PKWlpVwdspI13rr88ssvAMDVbWBggHv37mHfvn2wsbFpMvHKG2tz27bVSYv/\ns88+w6VLl3hX3Xr16oX+/fsjISEBnTt3lti2dxl/U9K6dWtUVFSgrKyMu5oJ8ONtqlq3bo2kpCSo\nqqpynb5169bh5s2bOHToEFq1aoXS0lLeMtK2Y2lpKRhjUFNTk+n/QmNpiNik7eek+WiIHNjSNURu\n/ZDIm6uHDBnSUE1tNppzPm8KpB1T+Pv7N3IL3w3qXL8nKioqUs/AFBcXY+HChfj999+xYcMGDB8+\nnCvT0dEBAGRnZ6Nbt27c9KysLG692trayM7O5q0zKysLampqaNeuHbS1tZGXl4fy8nLu3u6ysjLk\n5eVBU1MTHTp0gJqaGrKysmqso75DgmSJVxpJP0x6enpITEwE0HTilTfW5rZtq5Ml/urDWTU1NaGu\nro6MjAxYWFgAeL/xNyVVt7f438C72RYNoW3btry/FRUV0atXL2RkZEBHR6fOfUxbW7vG66vE82tp\nacn0f6GxNERs0vZz0nw0RA5s6Roit35I5M3VpKbmns+bgrqOKVoKeqBZI6moqMDChQtx+fJlREZG\n8jrWANCxY0d0794dV69e5aYVFBQgNTUVlpaWAABzc3MkJyfzHnxz5coVmJmZQVFREebm5igrK8P1\n69e5cvHDrszNzaGgoABTU1MkJSXx2pWUlMTV0VBycnJgYWGBM2fO8KanpqaiV69eAFpOvC192+7f\nvx92dna8q37p6enIy8uDrq5ug8TflOjr66NNmza8eJ8+fYr09PQG/39WX6mpqTAzM0Nqaio3rby8\nHLdv34auri7Mzc15+xhQuZ3EJ1DMzc3x5MkTXtK8cuUK2o0J6/EAABmRSURBVLRpA319fZn2hcbS\nELFJ28/Jh0OWHEjq1pR/T5oiabma1NSc83lTIO2YosVonIeUk4MHDzI9PT12+PDhWt/1dujQIWZi\nYsLi4uLYnTt32OzZs5mTkxP3mpfs7Gxmbm7O/P39uXekGhgYsIsXL3L1eHh4MCcnJ5acnMy9C7jq\n65kSEhJYnz592MGDB7l3IYtEovf+LmRJr9CYMWMGc3BwYBcvXmT3799nQUFBTCgUcu+Vba7xSoq1\nJW/bR48eMRMTE7ZkyRLuHe6jR49mkyZNatD4m5INGzawvn37soSEBO69mNVf89QUlZaWsmHDhrHR\no0ezP/74g929e5ctWbKEWVpaspycHHb79m1mYGDAwsLCWFpaGgsNDWWGhoYsLS2NMVb5DukJEyYw\nFxcXlpqayr0LuuqrcqTtCw3F1dWV9yquhohNlv2ctExvkwMJ39vkVvIvWXI1qam55vOmQNoxRUtB\nnetG4uLiwvT09CR+kpKSuPm2bdvGbG1tmYmJCXNzc+O9U5Uxxq5fv87Gjh3LhEIhc3JyYnFxcbzy\n169fMy8vL2ZmZsZEIhHz9/dnhYWFvHmOHDnCHBwcmKGhIXeg+L5JSor5+fls+fLlzM7OjgmFQubi\n4sL7LhhrnvFKipWxlrttxW13dXVlpqamTCQSMS8vL/bixQvePA0Rf1NRWlrK1q5dy0QiETMzM2ML\nFy587yc53pXMzEzm6enJrK2tmbGxMZs+fTq7c+cOVx4fH8+cnZ2ZUChkI0aMYImJibzls7Ky2Lx5\n85ixsTHr27cv27RpE++9zoxJ3xcaQvXONWMNE5u0/Zy0TG+bA8m/3ja3kn/JkqsJX3PO502BtGOK\nlkCBMXqZJiGEEEIIIYQQIg+6qYsQQgghhBBCCJETda4JIYQQQgghhBA5UeeaEEIIIYQQQgiRE3Wu\nCSGEEEIIIYQQOVHnmhBCCCGEEEIIkRN1rgkhpBm6desWrly50tjNIIQQUk/N6UU9zamthDQF1Lkm\nhLxzDZmMP7TEn5+fj7CwMCxduhS+vr4ICgrCs2fPGrtZhBDyTk2dOhUCgYD30dfXh5mZGcaMGYNj\nx441dhNrEAgE2Lp1a53zXL9+HbNnz5a6rs2bN6NPnz7vqmlvZfv27di1a1ejtkEaLy8vDB48WO71\n3L17F2PGjIG5uTm+/vprvH79mld+4MABjB8/Xu56SMtHnWvSJElKqkKhEIMGDcL69etRXFxcr/U1\nhSTV3MmSwF69egUvLy8kJyfXOd/Tp08hEAjkPji6f/8+Jk2aJNc6pHlXiVuWgy5pXr16hXHjxuHw\n4cPQ1NREx44dERcXh4kTJyIvL0/uNhJCSFNiaGiI6Oho7vPdd98hMDAQSkpKWLp0KRISEhq7ifV2\n5MgRpKWlSZ1v/Pjx+P777xugRbULCwtDYWFho7ZBmnnz5iE8PFzu9Xh5eUFHRwchISG4desWIiIi\nuLLXr18jMjISS5Yskbse0vIpN3YDCKmNoaEh/Pz8uL+Li4uRlJSEiIgIZGRkICQkpBFbRyS5c+cO\nYmNjMXr06Drn09TURHR0NLp27SpXfadPn8b169flWkdDiY6Oho6OjlzrOHLkCJ4+fYq4uDj8+eef\nSE9Px/Dhw/H5558jOjoac+fOfUetJYSQxte2bVuYmJjUmN6/f3/Y2NggJiYGAwYMaISWvX/a2trQ\n1tZu7GY0efIeRwCVJ65v3ryJwMBAGBgY4O+//8bx48e58t27d0MoFEIkEsldF2n56Mo1abLESVX8\nsbKywvz58zF27Fj8/PPPyMrKauwmkrekqqoKExMTaGhoNHZTGoyJiQm0tLTkWseDBw/QqVMn9OzZ\nk5vWvXt3BAcHY+DAgfI2kRBCmoVWrVpBVVUVCgoK3LSioiKEhYVhyJAhMDQ0hLOzM6Kjo3nLSRpB\nVH1k29SpUxEQEIBt27ZhwIABMDQ0xMSJE3Hjxg3eclevXoWLiwuMjY0xZMgQXLx4UWq7vby8cOTI\nEaSnp0MgECAmJoYbybV3714MGTIEJiYmOHHihMR2+fr6IiwsDFZWVrCwsICnp6fUUUsCgQAREREY\nM2YMjIyMsGPHDgBAeno6PDw8YGlpCRMTE3z11Ve8K+oCgQDl5eXYsmULBAIB1/7qI7mqj0SLiYmB\noaEhoqKi0LdvX1hZWeHx48cyfa9FRUVYsWIF+vfvD6FQiKFDh0odll69TQ4ODtiyZQvWrVuHvn37\nwtjYGF999RUePXpU53oAoHXr1gAAFRUVVFRUAABycnKwb98+LFq0SOryhADUuSbNUJ8+fcAYQ0ZG\nBgD5fkilcXBwQFhYGFasWAEzMzNYW1tjxYoVNYZJ/fLLLxgzZgwMDQ1hZ2eHoKAglJSUcOWbN2/G\n0KFDER4eDktLS/Tv3x8FBQU16rty5QoEAgGio6Nhb28Pc3NzJCUlAQASEhIwceJEmJqawsbGBn5+\nfnj+/Dm3bH2SXkpKCsaPHw9DQ0MMHDgQu3fv5i2Xn58Pb29viEQiWFpaYsOGDVyiqc2VK1cwZcoU\nAMAXX3yBqVOnAqg8IFi2bBnc3d1hbGyMOXPmSGxXnz59kJycjGHDhsHIyAhjxoxBYmJirfVt3rwZ\nYWFhACoPAjZv3sz9W9KBxJUrV+Dm5gZLS0sIhUI4Ojpiy5YtvLhkjfvw4cNwdnaGUCiEg4MDduzY\nIfXe76oHdeLtfPnyZUybNg3GxsawtbXFxo0bUV5eXus6tLS0kJubi7///ps3/fPPP4e+vn6d9RNC\nSHPDGENZWRn3KS4uxv379+Ht7Y2CggKMHDmSm2/mzJnYt28fJk2ahMjISPTt2xfLly/nDe+V1cmT\nJ3Hu3Dn4+/sjODgYOTk5WLhwIZcPbt68CTc3N7Rr1w7h4eH44osv4OnpKXW98+bNg4ODAz755BMu\nz4tt2bIFc+bMwdq1a2FjYyNx+dOnT+PUqVMIDAyEt7c3EhMTMWPGDKn5edu2bRgxYgRCQ0Ph6OiI\nvLw8TJo0Cbdv38aKFSuwceNGFBQUYPLkyUhPTwdQOdpKSUkJ48aNq3GSQprS0lLs378fa9euhbe3\nN3d1Wdr3umbNGpw/fx5eXl7YtWsXHB0dsX79esTGxtar/r179+LBgwdYu3YtAgMDkZqaCm9v71rn\nb9euHXr27Iljx47hxYsXOH36NMzNzQEAERERcHJy4k4wECINDQsnzc7Dhw8BAJ9++ik3be/evTA3\nN8fatWuRn5+P1atXw9vbG4cOHZK7vgMHDkBXVxcbN27EkydPEBISgpycHGzZsgUAcOLECSxevBij\nRo2Ch4cHHj9+jODgYDx9+pTr8AHAkydPkJiYiNDQULx8+RJt2rSptc6IiAj4+fmhoKAAxsbGOHr0\nKHx8fDBy5EjMmzcPmZmZCAsLw59//onDhw/jo48+kjmesrIyeHp6ws3NDYsWLcIPP/yAoKAg9O7d\nGzY2NqioqMCMGTOQnp6OZcuWoUOHDti5cydu3LhR57BmAwMDrFq1CgEBAQgICICVlRVXFhcXh1Gj\nRmHbtm21Ll9RUYH58+fDzc0NAoEA+/btw+zZsxEVFQWhUFhj/vHjxyM7O5u7F6/q8Llt27Zh0aJF\n6Nq1K7p168YdCDk7OyM0NBQVFRXclYEePXrA2dlZ5ri3b9+OkJAQfPnll+jXrx9u3LiB8PBw5OXl\nwcvLS+btAACLFi3ClClTMGfOHMTHx+Pbb79Ft27dan1oyrhx47B//364urrCwMAAXbt2RUVFBRQV\n6TwpIaTluXz5MgwMDHjTFBQUIBAIEBYWxo3YSUhIwNWrVxEWFoahQ4cCAOzs7FBWVoZt27Zh8uTJ\nUFdXl7ne8vJy7Ny5E23btgUAFBQUYNmyZbh79y709fWxfft2fPLJJ4iMjISKigoAQF1dHV9//XWd\n6+3atSs0NDS40VsA8ObNGwCAs7Oz1FuqioqKsHv3bi4naWhoYM6cOTh//jyvo16dSCTCtGnTuL9D\nQkKQn5+Pw4cPc7nTzs4OgwcPRmRkJL755huufdra2hKH5teFMQZ3d/caQ/alfa9Xr16Fra0tnJ2d\nAQBWVlZQU1Or17YDgA4dOmDr1q1QUlICADx+/BibN2/Gq1ev0K5dO4nLrF69Gl9//TW2b98OkUgE\nd3d3PHr0CMeOHcNPP/2ECxcuIDw8HOXl5Zg1axa3nxFSHXWuSZMlPmMt9vz5c5w/fx5RUVEYOnQo\nb0jx2/yQykpZWRk7d+7kOsNKSkoIDAzEvXv30KtXL2zcuBEDBw5EUFAQt4y2tjbc3d1x7do17uxn\nWVkZvL29ZUpSrq6ucHJyAlDZ6QwODoa9vT3Wr1/PzSMQCDBhwgTExMRwV4xlUVFRgQULFmDs2LEA\nADMzM/zyyy+Ij4+HjY0Nzp8/j7/++gs7d+5Ev379AAA2NjZwcHCoc71t27blhiv36tULvXr14spa\ntWqF5cuXQ1VVFUDlFfXqGGNwc3PDrFmzuDoHDRqEHTt2SHxYSdX70ap/p9UPJGJjY2FnZ4f169dz\nwwhtbW1x9uxZJCUlwdnZWaa4X716ha1bt2LKlCncWXA7OzuoqakhKCgIX3zxBf7zn//U+T1V5eLi\ngnnz5gEArK2t8euvv+LcuXO1dq61tLRw5MgRhIaG4rfffsP58+dx8uRJzJgxA25ubrwhkoQQ0twZ\nGRkhICAAAPDs2TOEhYWhrKwMISEh6NGjBzdfUlISVFRUuLwpNnz4cHz//ff4888/6+x8VicQCLgO\nIADulh5xR/jatWtwdHTkOtYA4OTkxB2DAJUdyaojmhQVFes8Edq7d2+p7TI3N+ed7LW3t4eqqiqS\nk5PrjK/6ui9dugQDAwN06tSJO85SVlaGra2tTMPbZSFpNJW079XKygpRUVHIzMzEgAEDMGDAALi7\nu9e7bmNjY962EB8rvHnzptZjQlNTU5w7dw5v3ryBmpoaAGDVqlWYOHEiWrVqhfnz52PNmjXQ0NDA\n3Llzoaury7tFixAx6lyTJkvSGWslJSUMGjQIK1as4E1/mx9SWTk4OPCuMjs5OSEwMBDJyclQVFRE\nZmYm3N3deScC+vXrBxUVFVy8eJHrXAOyJU+An5QePHiAnJwc/Pe//+XNY2xsjG7duvGGY8vKzMyM\n+7eqqio0NDS4oe7Jyclo1aoV18EEADU1NQwYMAApKSkAKjvo1YehKSvX/nPSq1cvrmNdl+HDh/Pa\nZW9vj99++022oKqo/j2PHj0ao0ePRnFxMR48eIDHjx/jf//7H8rLy1FaWgpAtrivX7+OoqIiODg4\n8La3g4MD1qxZg8uXL2PMmDEyt7PqdgAq91tpT2b99NNPsWnTJsTExODcuXMoKSnB+vXr0bp163rv\nB4QQ0pS1adMGhoaGACofcmpiYoIRI0bAzc0NMTEx3En2/Px8dOzYsUbntVOnTgAqT4zWh/jeWzHx\nesV5Lz8/v8YzQ5SVlXlXWAcPHswNsQYq89C6detqrVPcoauLpqYm728FBQVoaGjg5cuXdS5Xfd0v\nXrzAo0ePahxjAeCdMJCHpNF50r5XX19faGtr4/jx4wgMDERgYCBMTU2xYsWKet36VFs9sry6U/xd\n3bhxA5cuXcKZM2fw66+/QkdHhzsOs7CwwM8//4z58+fL3Cby4aDONWmyqp6xVlBQQOvWrdG5c2eJ\nQ6Dl+SGVpnoyEyfUly9f4sWLFwAAf39/+Pv711i26kPXlJSU0KpVK5nqrJoIxXV88sknNebr2LFj\njXcxyqL6d6ioqMg7aJA0BKtq/REREdyweLE7d+7UWp8sBw3V6wAqv+v8/HyZlq2rvqKiIgQGBuLY\nsWMoKytDly5dYGpqCmVlZW4fkSVu8bZwc3OTWG99H7Inab+Vdu9cVbq6uliwYAHc3Nxw6NAh6lwT\nQlq0Tp06ISAgAAsXLsTq1auxadMmAMDHH3+M3NzcGrfJZGdnAwDvt736b6z4qml9dOjQAbm5ubxp\njDFevoqMjOQ9e6W+Q5slEeegqnXm5ubW++Ggbdu2hbW1NRYvXlyv5RQUFGo8F+Rtvr/aqKqqYu7c\nuZg7dy7++ecfxMfHY+vWrViyZAlOnDjxzuqRxaZNmzBz5ky0b98eubm56NChA1fWvn17bt8ipDrq\nXJMmq+oZ68ZUPZmJE6qGhgZ3Vdzb25t3hVrsXSTT9u3bA4DEH/Ls7GwYGxsDeHdJT11dHXl5eWCM\n8YYZV/0eJkyYUK8hdrISX30Qy83N5f39tlavXo0zZ84gLCwMNjY2XOe76kNjZIlbvL1DQkJ49/yL\nVT8R866tWbMGxcXFWLlyJW+6vr5+jSfZEkJISzR06FD069cPcXFxcHFxgUgkgkgkwq5du3DmzBne\nvbBxcXFQUVGBkZERgMpOpfhhqGLikUn1YWNjg/j4eBQVFXEnSS9cuMCNhAJQ6wOwqo6yq6+UlBS8\nfPkSH3/8MQDg7NmzKC0thbW1db3WIxKJcPLkSfTs2ZN3Mtrf35937FV9JECbNm2Ql5eHkpISbjTa\ntWvX3jqeqoqLizFixAi4uLjAzc0N//nPfzBlyhQ8evQIMTEx76QOWV24cAEPHz7E9u3bAVReyMjJ\nyeHKs7Ky0L179wZtE2k+6Ck4hEhx4cIF3hDg06dPQ0FBAdbW1ujZsyc0NDSQnp4OQ0ND7qOuro6N\nGzfi/v37ctffo0cPdOrUCT/99BNv+l9//YUnT55wQ4urJj2xt0l6NjY2KCkp4Q3HLikp4T25W0tL\nixevOBHLc9AAAPHx8dy/i4uLER8fX+dBg6z1Xbt2DTY2NnB0dOQOJFJTU5GXl8ddxZAlbmNjY6io\nqCArK4sXu/gewPd9Jjs7OxsnTpyocTU/JSWF7v0ihHwwfHx8oKKigm+++Qbl5eXo378/LC0t4evr\ni7179yIxMRFr1qxBVFQUZsyYwXVG7e3tceLECURFReHSpUtYsmTJW71ZxN3dHQUFBZg5cybi4+Px\nww8/cG2Spl27dsjJyUFCQkK9RzsVFBRg1qxZvDptbW3r3bmePn06SkpK4ObmhlOnTuHixYtYunQp\nDh8+DD09PW6+jz/+GCkpKUhKSgJjDAMHDkRhYSF8fHxw+fJl7N+/Hzt27JA79wOVz2YxMjLCli1b\ncODAAVy9ehXR0dGIjY3FkCFD5F6/rBhj2LRpExYuXMiNNrSzs8OzZ8+wY8cO/PDDD0hJSYGjo2OD\ntYk0L3TlmnwwGGPYu3dvjekaGhoYMWJErculp6dj/vz5mDx5Mu7fv4/Q0FCMGzeOu3Lp4eGBlStX\nQlFREf3798eLFy+4h6lVfUfl21JUVISHhwf8/PywdOlSDBs2jHuwy2effcY9XXTgwIE4cOAAfHx8\nMG7cONy9exd79uypd9KzsbGBnZ0dfHx8kJOTAx0dHezfvx95eXlSr8yKD2DOnTuH9u3b1/v1UEFB\nQSguLkaXLl2wZ88eFBQUYM6cObXOL76SHBcXBxMTE3Tp0kXifEZGRjh16hSio6Px2Wef4fbt24iM\njISCggJ3j7MscWtoaMDNzQ0hISF4/fo1zM3N8c8//yAkJATt2rWDrq5uveKtr+nTp+PMmTOYOXMm\nhEIh8vPzsXjxYly/fp07w04IIS1djx49MHXqVOzevRvff/89XF1dsX37doSGhmLnzp3Iz89H9+7d\nsXLlSri4uHDLeXt7o6ysDEFBQVBWVoazszMWLVqE5cuX16v+7t274+DBg1i3bh08PDzQsWNHLFu2\nrM57qsVGjx6Ns2fPwt3dHR4eHvV66rRIJIKpqSmWLFkCZWVlDBs2rN5Du4HKE+RRUVEIDg6Gv78/\nSktL0aNHDwQHB/Oe7zJr1ixERERg5syZOHXqFGxtbbFs2TIcOHAAp0+fhoGBAbZs2YKJEyfWuw2S\nrFy5Eurq6ti9ezeys7PRsWNHjBs3Dh4eHu9k/bI4ceIEysvLude8AZXfV1BQEDZs2ICysjL4+/u/\nk+M70kIxQpogV1dX9uWXX8o078CBA5mPjw9v2tGjR5menh7LyMhgjDEWHh7O9PT0JH5GjBhR57oX\nL17M/Pz8mImJCbOzs2NhYWGsrKyMN19cXBwbNWoUEwqFzNrami1YsIA9ePCAKw8PD2e9e/eWGsvl\ny5eZnp4eS0pKqlEWFxfHRo4cyQwMDFjfvn2Zv78/y8vL482za9cuZm9vz4RCIXNxcWGpqalMKBSy\nH3/8UeL3Utt3+ObNG7Zq1SpmZWXFTExMmI+PD/vmm2/YoEGD6mx/eXk58/T0ZIaGhmzYsGGMMcnb\n8smTJ0xPT69Gu44dO8aGDBnCjIyMmKurK7tx40ad9T179oyNHTuWGRgYsJUrVzLGGNPT02MRERG8\n+Z4/f848PT2ZSCRiJiYmbNiwYWzfvn3M39+f9e/fn5WXl8scd0VFBdu/fz8bOnQoMzAwYLa2tszL\ny4tlZWXV2daq7aptO8uy358/f56NHz+e9e7dm+nr6zMnJycWGxtb5zKEEEKat/ocFxFCGo8CY+/g\niU+EtFAODg6wsbHB6tWrG7spLVpMTAy8vb2RkJDAe181qd3Ro0fxzz//YMGCBY3dFEIIIe/Z1KlT\noaSkJHEEHiGk6aBh4YQQ0gz16dOn1mHwhBBCCCGk4VHnmhBCmiFZ35lOCCGk+Ttw4EBjN4EQIgMa\nFk4IIYQQQgghhMiJXsVFCCGEEEIIIYTIiTrXhBBCCCGEEEKInKhzTQghhBBCCCGEyIk614QQQggh\nhBBCiJyoc00IIYQQQgghhMiJOteEEEIIIYQQQoic/h+KrD0Md6gnNgAAAABJRU5ErkJggg==\n", "text/plain": ["<matplotlib.figure.Figure at 0x1139866a0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["pf.create_round_trip_tear_sheet(returns, positions, transactions, sector_mappings=sect_map)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Under the hood, several functions are being called. `extract_round_trips()` does the portfolio reconstruction and creates the round-trip trades."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["rts = pf.round_trips.extract_round_trips(transactions, \n", "                                         portfolio_value=positions.sum(axis='columns') / (returns + 1))"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style>\n", "    .dataframe thead tr:only-child th {\n", "        text-align: right;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>close_dt</th>\n", "      <th>long</th>\n", "      <th>open_dt</th>\n", "      <th>pnl</th>\n", "      <th>rt_returns</th>\n", "      <th>symbol</th>\n", "      <th>duration</th>\n", "      <th>returns</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2004-01-13</td>\n", "      <td>True</td>\n", "      <td>2004-01-09</td>\n", "      <td>-126.000000</td>\n", "      <td>-0.022523</td>\n", "      <td>AMD</td>\n", "      <td>4 days</td>\n", "      <td>-0.001249</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2004-01-16</td>\n", "      <td>True</td>\n", "      <td>2004-01-09</td>\n", "      <td>50.020000</td>\n", "      <td>0.078507</td>\n", "      <td>AMD</td>\n", "      <td>7 days</td>\n", "      <td>0.000503</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2004-01-20</td>\n", "      <td>True</td>\n", "      <td>2004-01-09</td>\n", "      <td>1540.099065</td>\n", "      <td>0.104696</td>\n", "      <td>AMD</td>\n", "      <td>11 days</td>\n", "      <td>0.015257</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2004-01-21</td>\n", "      <td>False</td>\n", "      <td>2004-01-20</td>\n", "      <td>287.119806</td>\n", "      <td>0.085155</td>\n", "      <td>AMD</td>\n", "      <td>1 days</td>\n", "      <td>0.002861</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2004-01-22</td>\n", "      <td>False</td>\n", "      <td>2004-01-20</td>\n", "      <td>103.349947</td>\n", "      <td>0.112198</td>\n", "      <td>AMD</td>\n", "      <td>2 days</td>\n", "      <td>0.001032</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    close_dt   long    open_dt          pnl  rt_returns symbol duration  \\\n", "0 2004-01-13   True 2004-01-09  -126.000000   -0.022523    AMD   4 days   \n", "1 2004-01-16   True 2004-01-09    50.020000    0.078507    AMD   7 days   \n", "2 2004-01-20   True 2004-01-09  1540.099065    0.104696    AMD  11 days   \n", "3 2004-01-21  False 2004-01-20   287.119806    0.085155    AMD   1 days   \n", "4 2004-01-22  False 2004-01-20   103.349947    0.112198    AMD   2 days   \n", "\n", "    returns  \n", "0 -0.001249  \n", "1  0.000503  \n", "2  0.015257  \n", "3  0.002861  \n", "4  0.001032  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["rts.head()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style>\n", "    .dataframe thead tr:only-child th {\n", "        text-align: right;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>Summary stats</th>\n", "      <th>All trades</th>\n", "      <th>Short trades</th>\n", "      <th>Long trades</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Total number of round_trips</th>\n", "      <td>5819.00</td>\n", "      <td>1155.00</td>\n", "      <td>4664.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Percent profitable</th>\n", "      <td>0.50</td>\n", "      <td>0.52</td>\n", "      <td>0.49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Winning round_trips</th>\n", "      <td>2888.00</td>\n", "      <td>596.00</td>\n", "      <td>2292.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Losing round_trips</th>\n", "      <td>2914.00</td>\n", "      <td>553.00</td>\n", "      <td>2361.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Even round_trips</th>\n", "      <td>17.00</td>\n", "      <td>6.00</td>\n", "      <td>11.00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Summary stats                All trades  Short trades  Long trades\n", "Total number of round_trips     5819.00       1155.00      4664.00\n", "Percent profitable                 0.50          0.52         0.49\n", "Winning round_trips             2888.00        596.00      2292.00\n", "Losing round_trips              2914.00        553.00      2361.00\n", "Even round_trips                  17.00          6.00        11.00"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style>\n", "    .dataframe thead tr:only-child th {\n", "        text-align: right;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>PnL stats</th>\n", "      <th>All trades</th>\n", "      <th>Short trades</th>\n", "      <th>Long trades</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Total profit</th>\n", "      <td>$67003.94</td>\n", "      <td>$3531.32</td>\n", "      <td>$63472.61</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Gross profit</th>\n", "      <td>$448674.42</td>\n", "      <td>$20579.67</td>\n", "      <td>$428094.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Gross loss</th>\n", "      <td>$-381670.48</td>\n", "      <td>$-17048.35</td>\n", "      <td>$-364622.13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Profit factor</th>\n", "      <td>$1.18</td>\n", "      <td>$1.21</td>\n", "      <td>$1.17</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Avg. trade net profit</th>\n", "      <td>$11.51</td>\n", "      <td>$3.06</td>\n", "      <td>$13.61</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Avg. winning trade</th>\n", "      <td>$155.36</td>\n", "      <td>$34.53</td>\n", "      <td>$186.78</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Avg. losing trade</th>\n", "      <td>$-130.98</td>\n", "      <td>$-30.83</td>\n", "      <td>$-154.44</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Ratio Avg. Win:Avg. Loss</th>\n", "      <td>$1.19</td>\n", "      <td>$1.12</td>\n", "      <td>$1.21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Largest winning trade</th>\n", "      <td>$9500.14</td>\n", "      <td>$1623.24</td>\n", "      <td>$9500.14</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Largest losing trade</th>\n", "      <td>$-22902.83</td>\n", "      <td>$-661.29</td>\n", "      <td>$-22902.83</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["PnL stats                 All trades  Short trades  Long trades\n", "Total profit               $67003.94      $3531.32    $63472.61\n", "Gross profit              $448674.42     $20579.67   $428094.75\n", "Gross loss               $-381670.48    $-17048.35  $-364622.13\n", "Profit factor                  $1.18         $1.21        $1.17\n", "Avg. trade net profit         $11.51         $3.06       $13.61\n", "Avg. winning trade           $155.36        $34.53      $186.78\n", "Avg. losing trade           $-130.98       $-30.83     $-154.44\n", "Ratio Avg. Win:Avg. Loss       $1.19         $1.12        $1.21\n", "Largest winning trade       $9500.14      $1623.24     $9500.14\n", "Largest losing trade      $-22902.83      $-661.29   $-22902.83"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style>\n", "    .dataframe thead tr:only-child th {\n", "        text-align: right;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>Duration stats</th>\n", "      <th>All trades</th>\n", "      <th>Short trades</th>\n", "      <th>Long trades</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Avg duration</th>\n", "      <td>13 days 03:27:07.702354</td>\n", "      <td>2 days 10:39:35.064935</td>\n", "      <td>15 days 19:02:40.548885</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Median duration</th>\n", "      <td>8 days 00:00:00</td>\n", "      <td>2 days 00:00:00</td>\n", "      <td>12 days 00:00:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Longest duration</th>\n", "      <td>84 days 00:00:00</td>\n", "      <td>13 days 00:00:00</td>\n", "      <td>84 days 00:00:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Shortest duration</th>\n", "      <td>1 days 00:00:00</td>\n", "      <td>1 days 00:00:00</td>\n", "      <td>1 days 00:00:00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Duration stats                 All trades           Short trades  \\\n", "Avg duration      13 days 03:27:07.702354 2 days 10:39:35.064935   \n", "Median duration           8 days 00:00:00        2 days 00:00:00   \n", "Longest duration         84 days 00:00:00       13 days 00:00:00   \n", "Shortest duration         1 days 00:00:00        1 days 00:00:00   \n", "\n", "Duration stats                Long trades  \n", "Avg duration      15 days 19:02:40.548885  \n", "Median duration          12 days 00:00:00  \n", "Longest duration         84 days 00:00:00  \n", "Shortest duration         1 days 00:00:00  "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style>\n", "    .dataframe thead tr:only-child th {\n", "        text-align: right;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>Return stats</th>\n", "      <th>All trades</th>\n", "      <th>Short trades</th>\n", "      <th>Long trades</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Avg returns all round_trips</th>\n", "      <td>0.01%</td>\n", "      <td>0.00%</td>\n", "      <td>0.01%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Avg returns winning</th>\n", "      <td>0.13%</td>\n", "      <td>0.03%</td>\n", "      <td>0.15%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Avg returns losing</th>\n", "      <td>-0.11%</td>\n", "      <td>-0.03%</td>\n", "      <td>-0.13%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Median returns all round_trips</th>\n", "      <td>-0.00%</td>\n", "      <td>0.00%</td>\n", "      <td>-0.00%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Median returns winning</th>\n", "      <td>0.02%</td>\n", "      <td>0.01%</td>\n", "      <td>0.03%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Median returns losing</th>\n", "      <td>-0.01%</td>\n", "      <td>-0.00%</td>\n", "      <td>-0.02%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Largest winning trade</th>\n", "      <td>6.78%</td>\n", "      <td>1.37%</td>\n", "      <td>6.78%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Largest losing trade</th>\n", "      <td>-17.23%</td>\n", "      <td>-0.72%</td>\n", "      <td>-17.23%</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Return stats                    All trades  Short trades  Long trades\n", "Avg returns all round_trips          0.01%         0.00%        0.01%\n", "Avg returns winning                  0.13%         0.03%        0.15%\n", "Avg returns losing                  -0.11%        -0.03%       -0.13%\n", "Median returns all round_trips      -0.00%         0.00%       -0.00%\n", "Median returns winning               0.02%         0.01%        0.03%\n", "Median returns losing               -0.01%        -0.00%       -0.02%\n", "Largest winning trade                6.78%         1.37%        6.78%\n", "Largest losing trade               -17.23%        -0.72%      -17.23%"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style>\n", "    .dataframe thead tr:only-child th {\n", "        text-align: right;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>Symbol stats</th>\n", "      <th>AMD</th>\n", "      <th>CERN</th>\n", "      <th>COST</th>\n", "      <th>DELL</th>\n", "      <th>GPS</th>\n", "      <th>INTC</th>\n", "      <th>MMM</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Avg returns all round_trips</th>\n", "      <td>-0.00%</td>\n", "      <td>0.02%</td>\n", "      <td>0.02%</td>\n", "      <td>-0.03%</td>\n", "      <td>0.00%</td>\n", "      <td>0.02%</td>\n", "      <td>0.01%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Avg returns winning</th>\n", "      <td>0.20%</td>\n", "      <td>0.15%</td>\n", "      <td>0.10%</td>\n", "      <td>0.11%</td>\n", "      <td>0.10%</td>\n", "      <td>0.11%</td>\n", "      <td>0.10%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Avg returns losing</th>\n", "      <td>-0.19%</td>\n", "      <td>-0.13%</td>\n", "      <td>-0.07%</td>\n", "      <td>-0.15%</td>\n", "      <td>-0.09%</td>\n", "      <td>-0.06%</td>\n", "      <td>-0.09%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Median returns all round_trips</th>\n", "      <td>-0.00%</td>\n", "      <td>0.00%</td>\n", "      <td>0.00%</td>\n", "      <td>-0.00%</td>\n", "      <td>-0.00%</td>\n", "      <td>-0.00%</td>\n", "      <td>0.00%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Median returns winning</th>\n", "      <td>0.03%</td>\n", "      <td>0.02%</td>\n", "      <td>0.02%</td>\n", "      <td>0.02%</td>\n", "      <td>0.02%</td>\n", "      <td>0.01%</td>\n", "      <td>0.02%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Median returns losing</th>\n", "      <td>-0.02%</td>\n", "      <td>-0.01%</td>\n", "      <td>-0.01%</td>\n", "      <td>-0.02%</td>\n", "      <td>-0.01%</td>\n", "      <td>-0.01%</td>\n", "      <td>-0.01%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Largest winning trade</th>\n", "      <td>6.78%</td>\n", "      <td>6.14%</td>\n", "      <td>3.96%</td>\n", "      <td>2.78%</td>\n", "      <td>1.80%</td>\n", "      <td>2.40%</td>\n", "      <td>2.45%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Largest losing trade</th>\n", "      <td>-17.23%</td>\n", "      <td>-3.92%</td>\n", "      <td>-2.32%</td>\n", "      <td>-6.39%</td>\n", "      <td>-6.86%</td>\n", "      <td>-4.45%</td>\n", "      <td>-1.79%</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Symbol stats                       AMD   CERN   COST   DELL    GPS   INTC  \\\n", "Avg returns all round_trips     -0.00%  0.02%  0.02% -0.03%  0.00%  0.02%   \n", "Avg returns winning              0.20%  0.15%  0.10%  0.11%  0.10%  0.11%   \n", "Avg returns losing              -0.19% -0.13% -0.07% -0.15% -0.09% -0.06%   \n", "Median returns all round_trips  -0.00%  0.00%  0.00% -0.00% -0.00% -0.00%   \n", "Median returns winning           0.03%  0.02%  0.02%  0.02%  0.02%  0.01%   \n", "Median returns losing           -0.02% -0.01% -0.01% -0.02% -0.01% -0.01%   \n", "Largest winning trade            6.78%  6.14%  3.96%  2.78%  1.80%  2.40%   \n", "Largest losing trade           -17.23% -3.92% -2.32% -6.39% -6.86% -4.45%   \n", "\n", "Symbol stats                      MMM  \n", "Avg returns all round_trips     0.01%  \n", "Avg returns winning             0.10%  \n", "Avg returns losing             -0.09%  \n", "Median returns all round_trips  0.00%  \n", "Median returns winning          0.02%  \n", "Median returns losing          -0.01%  \n", "Largest winning trade           2.45%  \n", "Largest losing trade           -1.79%  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["pf.round_trips.print_round_trip_stats(rts)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.1"}}, "nbformat": 4, "nbformat_minor": 1}