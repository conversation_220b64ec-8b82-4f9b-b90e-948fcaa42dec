import inspect
import torch
import importlib
from torch.nn import functional as F
import torch.optim.lr_scheduler as lrs
from torch.optim.lr_scheduler import ReduceLROnPlateau
import pytorch_lightning as pl
from torch.optim import Adam, AdamW
from torchmetrics.functional import accuracy

class PLModel(pl.LightningModule):
    def __init__(self, model_name, loss, lr, **kargs):
        super().__init__()
        self.kargs = kargs
        self.save_hyperparameters()
        self.load_model()
        self.configure_loss()

    def forward(self, *args, **kwargs):
        if self.hparams.model_name == 'bar_gpt' and len(args) == 4:
            code, pos, x, y = args
            return self.model(code, pos, x, y)
        elif self.hparams.model_name == 'time_series_model2dr' and len(args) == 2:
            inputs, embeds = args
            return self.model(embeds, inputs)
        else:
            raise ValueError(f"Invalid Model Name: {self.hparams.model_name} or Invalid Arguments {len(args)}!")

    def training_step(self, batch, batch_idx):
        if self.hparams.model_name == 'bar_gpt':
            code, pos, x, targets = batch
            outputs, loss = self(code, pos, x, targets)
            return loss
        elif self.hparams.model_name == 'time_series_model2dr':
            inputs, targets, embeds = batch
            outputs = self(embeds, inputs)
            loss = self.loss_function(outputs, targets)
        else:
            raise ValueError("Invalid Model Name!")

        self.log('loss', loss, on_step=True, on_epoch=True, prog_bar=True)
        if self.is_acc:
            outputs = torch.argmax(outputs,dim=1)
            acc = accuracy(outputs, targets, task='multiclass', num_classes=3)
            self.log('acc', acc, on_step=True, on_epoch=True, prog_bar=True)
            return {"loss": loss, "acc": acc}
        else:
            return loss

    def validation_step(self, batch, batch_idx):
        if self.hparams.model_name == 'bar_gpt':
            code, pos, inputs, targets = batch
            outputs, loss = self(code, pos, inputs, targets)
            self.log('val_loss', loss, on_step=False, on_epoch=True, prog_bar=True)
            return loss
        elif self.hparams.model_name == 'time_series_model2dr':
            inputs, targets, embeds = batch
            outputs = self(embeds, inputs)
            loss = self.loss_function(outputs, targets)
        else:
            raise ValueError("Invalid Model Name!")
        self.log('val_loss', loss, on_step=False, on_epoch=True, prog_bar=True)
        if self.is_acc:
            preds = torch.argmax(outputs,dim=1)
            acc = accuracy(preds, targets, task='multiclass', num_classes=3)
            self.log('val_acc', acc, on_step=False, on_epoch=True, prog_bar=True)
            return {"loss": loss, "acc": acc}
        else:
            return loss

    def test_step(self, batch, batch_idx):
        # Here we just reuse the validation_step for testing
        return self.validation_step(batch, batch_idx)
    
    # def on_train_epoch_end(self):
    #     # Make the Progress Bar leave there
    #     self.log('dummy', 0, on_step=False, on_epoch=True, prog_bar=True)

    # def on_validation_epoch_end(self):
    #     # Make the Progress Bar leave there
    #     self.log('dummy', 0, on_step=False, on_epoch=True, prog_bar=True)

    def configure_optimizers(self):
        if hasattr(self.hparams, 'weight_decay'):
            weight_decay = self.hparams.weight_decay
        else:
            weight_decay = 0
        print(self.hparams.optimizer)
        if self.hparams.optimizer == 'adamw':
            optimizer = AdamW(self.parameters(), lr=self.hparams.lr, weight_decay=weight_decay)
        else:
            optimizer = Adam(self.parameters(), lr=self.hparams.lr, weight_decay=weight_decay)

        if self.hparams.lr_scheduler is None:
            return optimizer
        else:
            if self.hparams.lr_scheduler == 'step':
                scheduler = lrs.StepLR(optimizer,
                                       step_size=self.hparams.lr_decay_steps,
                                       gamma=self.hparams.lr_decay_rate)
            elif self.hparams.lr_scheduler == 'cosine':
                scheduler = lrs.CosineAnnealingLR(optimizer,
                                                  T_max=self.hparams.lr_decay_steps,
                                                  eta_min=self.hparams.lr_decay_min_lr)
            elif self.hparams.lr_scheduler == 'plateau':
                scheduler = lrs.ReduceLROnPlateau(optimizer,
                                              mode='min',
                                              factor=self.hparams.lr_decay_rate,
                                              patience=self.hparams.lr_decay_steps,
                                              min_lr=self.hparams.lr_decay_min_lr,
                                              verbose=True)
            elif self.hparams.lr_scheduler == 'reduce_on_plateau':
                return {
                    "optimizer": optimizer,
                    "lr_scheduler": {
                        "scheduler": ReduceLROnPlateau(optimizer,
                                              mode='min',
                                              factor=self.hparams.lr_decay_rate,
                                              patience=self.hparams.lr_decay_steps,
                                              min_lr=self.hparams.lr_decay_min_lr,
                                              verbose=True),
                        "interval": "epoch",
                        "monitor": "val_loss",
                    }
                }
            else:
                raise ValueError('Invalid lr_scheduler type!')
            return [optimizer], [scheduler]

    def configure_loss(self):
        loss = self.hparams.loss.lower()
        if loss == 'mse':
            self.loss_function = F.mse_loss
            self.is_acc = False
        elif loss == 'l1':
            self.loss_function = F.l1_loss
            self.is_acc = False
        elif loss == 'bce':
            self.loss_function = F.binary_cross_entropy
            self.is_acc = True
        elif loss == 'ce':
            self.loss_function = F.cross_entropy
            self.is_acc = True
        else:
            raise ValueError("Invalid Loss Type!")
        
    def load_model(self):
        name = self.hparams.model_name
        # Change the `snake_case.py` file name to `CamelCase` class name.
        # Please always name your model file name as `snake_case.py` and
        # class name corresponding `CamelCase`.
        camel_name = ''.join([i.capitalize() for i in name.split('_')])
        try:
            Model = getattr(importlib.import_module(
                '.'+name, package=__package__), camel_name)
        except:
            raise ValueError(
                f'Invalid Module File Name or Invalid Class Name {name}.{camel_name}!')
        self.model = self.instancialize(Model)

    def instancialize(self, Model, **other_args):
        """ Instancialize a model using the corresponding parameters
            from self.hparams dictionary. You can also input any args
            to overwrite the corresponding value in self.hparams.
        """
        class_args = inspect.getfullargspec (Model.__init__).args[1:]
        inkeys = self.hparams.keys()
        args1 = {}
        for arg in class_args:
            if arg in inkeys:
                args1[arg] = getattr(self.hparams, arg)
            if arg in self.kargs.keys():
                if arg == 'num_embeds':
                    args1[arg] = self.kargs[arg]
                elif arg == 'out_channels':
                    args1[arg] = self.kargs[arg]
                elif arg == 'ins_nums':
                    args1[arg] = self.kargs[arg]
                else:
                    args1[arg] = self.kargs[arg]
        args1.update(other_args)
        return Model(**args1)

