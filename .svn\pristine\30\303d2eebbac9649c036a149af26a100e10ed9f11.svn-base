import errno
import socket
import threading
import time
import json
import struct
import zlib
from pprint import pprint
from logging import getLogger
from datetime import datetime

import pandas as pd
import numpy as np
from torch import normal

from pyqlab.net.message import Message

from rllab.data.utils import calc_5min_tick_features
from rllab.predict import FuturesTrading

class RlExtendApi():

    def __init__(self, name=None):
        self.name = name
        # self.ip = ip
        # self.port = port
        self.BUFSIZE = 8192
        self.connected = False
        self.thrd = None
        self.thrd_heart = None
        self.logger = getLogger(__name__)
        self.s = socket.socket()

        # init futurestrading
        self.trading = FuturesTrading(
            cwd="./test_ppo/checkpoint_000030/checkpoint-30",
            agent='ppo')

    def heart_handler(self):
        '''发送心跳包'''
        while True:
            if self.connected:
                self.heart()
            time.sleep(24)

    def connect(self, ip, port):
        try:
            self.s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.s.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1) # 在客户端开启心跳维护
            self.logger.info('Socket created.')
            self.s.settimeout(12)
            self.s.connect((ip, port))
            self.s.settimeout(None)
            self.connected = True
            # thread sending heart beat message
            self.thrd_heart = threading.Thread(target = self.heart_handler)
            self.thrd_heart.setDaemon(True)
            self.thrd_heart.start()
        except socket.error as e:
            self.logger.error('Failed to create socket %s' % e)
            return False

        return True
            
    def is_connect(self):
        return self.connected

    def start(self):
        self.thrd = threading.Thread(target=self.recv_worker)
        self.thrd.setDaemon(True)
        self.thrd.start()

    def stop(self):
        self.thrd.join()
        self.close()

    def close(self):
        # Clean up
        self.logger.debug('closing socket')
        # self.thrd_heart.join()
        self.s.shutdown(2)
        self.s.close()
        self.logger.debug('done')


    def heart(self):
        msg = Message()
        msg.command = 0
        msg.business_type = 0
        msg.app_id = 1
        msg.body = "H"
        self.send(msg.package_msg())

    def regist(self):
        msg = Message()
        msg.command = 1
        msg.business_type = 0
        msg.app_id = 1
        msg.body = "R"
        self.send(msg.package_msg())

    def send(self, message):
        try:
            self.s.sendall(message)
        except socket.error as e:
            errmsg = ''
            if e.errno == errno.ECONNRESET:
                self.connected = False
                errmsg = '%s sendall failed, errno:%d %s' % (self.name, e.errno, e)
                print(errmsg)
                self.logger.error(errmsg)
            else:
                errmsg = '%s send failed, errno:%d, %s' % (self.name, e.errno, e)
                print(errmsg)
                self.logger.error(errmsg)
                self.connected = False
                # raise
            self.close()

    def process_recv_data(self, msg):
        if msg.command == 3: # 是压缩包
            msg.body = zlib.decompress(msg.body, zlib.MAX_WBITS|32)
        if msg.business_type == 1:
            label = str(msg.body[0:9], encoding='utf-8')
            if label[8] == ' ':
                label = label[0:8]
            df_tick = self.process_tickdata(label, msg.body[9:])

            state, price = calc_5min_tick_features(df_tick)
            state = state.values.reshape(-1) * 2 ** -7
            # action = self.trading.predict(state)
            # print(action)
            self.commit(label, state)
        else:
            print(f"Unknow business_type={msg.business_type} message")

    def process_tickdata(self, label, data):
        buff = []
        for i in range(len(data)//48):
            tt, p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2 = struct.unpack("qfl2f2l2f2l", data[i*48:(i+1)*48])
            # print(i, datetime.fromtimestamp(tt), p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2)
            buff.append([label, tt, p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2])
        return pd.DataFrame(buff, columns=['label','datetime','price','volume','bid_price1','bid_price2','bid_size1','bid_size2','ask_price1','ask_price2','ask_size1','ask_size2'])

    def recv_worker(self):
        remain_size = 0
        recv_len = 0
        total_data = b''
        data = b''
        while True:
            try:
                if remain_size == 0 or remain_size >= self.BUFSIZE:
                    recv_len = self.BUFSIZE
                else:
                    recv_len = remain_size

                data = self.s.recv(recv_len)
                if not data:
                    # print("error:not data.")
                    break
                total_data = total_data + data
                if len(total_data) < 8:
                    self.logger.error("recev message data error. %s", total_data)
                    total_data = b''
                    remain_size == 0
                    continue
                header = total_data[0:8]
                tag, st, msg_size = struct.unpack('<3sbi', header)
                if msg_size > 7:
                    # print("Header tag: %s %d msg_size: %d" % (tag, st, msg_size))
                    pass
                if tag.decode('ascii', 'ignore') == 'XXH':
                    if (len(total_data) - 8) < msg_size: # 包未收全
                        remain_size = msg_size - len(total_data) + 8
                        # print("remain_size: %d = %d - %d" % (remain_size, msg_size, len(total_data)+8))
                    else: # 包已收全
                        if msg_size > 15:
                            # print("next msg size: %d = %d - %d" % (len(total_data) - 8 - msg_size, len(total_data) - 8, msg_size))
                            pass
                        msg = Message()
                        msg.unpackage_msg(total_data[8:msg_size + 8], msg_size) # 解包
                        if msg.command == 0:
                            # print("heart beat.")
                            pass
                        elif msg.command == 1:
                            # 注册客户端
                            print("regist client")
                            # self.regist()
                        else:
                            self.process_recv_data(msg)
                            
                        if remain_size - 8 > msg_size: # 将多余部分转给下一个包
                            remain_size = len(total_data) - 8 - msg_size
                            total_data = total_data[(8 + msg_size):]
                            # print("next msg size: %d " % remain_size)
                        else:
                            remain_size = 0
                            total_data = b''
                else:
                    if remain_size > 0:
                        self.logger.error(f"unknow message({remain_size})")
                    data = b''
                    total_data = b''
                    remain_size = 0
                    continue
                time.sleep(1)
            except socket.error as e:
                if e.errno == errno.ECONNRESET:
                    self.connected = False
                else:
                    total_data = b''
                    remain_size = 0
                    # raise
                # self.logger.error('recv failed %s' % e)
                self.close()
                time.sleep(3)

    def trade(self, label, state):
        trade_action = ["Hold", "OpenLong", "CloseLong"]
        action = self.trading.predict(state)
        msg = Message()
        msg.app_id = 1
        msg.command = 2
        msg.business_type = 10
        msg.body = json.dumps({"label": label, "action": trade_action[action], "portfolio": "FUT-MLP-1.1"})
        print(msg.body)
        self.send(msg.package_msg())

    def commit(self, label, state):
        trade = threading.Thread(target=self.trade, args=(label, state))
        trade.start()
        trade.join()

if __name__ == "__main__":
    rle = RlExtendApi()

    item = input("waiting input e or q to exit!\n")
    while item != 'q' and item != 'e':
        if not rle.is_connect():
            if rle.connect('127.0.0.1', 51300):
                rle.regist()
                rle.start()
        time.sleep(5)
    # rle.stop()

    