{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Model eval check"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import pandas as pd\n", "import numpy as np\n", "import json"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["with open('d:\\\\RoboQuant\\\\rpt\\\\model_test_data.json') as f:\n", "    md_data = json.load(f)"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["14\n", "tensor([0.3416], grad_fn=<ViewBackward0>) 0.3415907025337219\n", "14\n", "tensor([0.5744], grad_fn=<ViewBackward0>) 0.5743871331214905\n", "34\n", "tensor([0.9873], grad_fn=<ViewBackward0>) 0.9873132109642029\n", "34\n", "tensor([6.9535e-09], grad_fn=<ViewBackward0>) 6.9534684676852976e-09\n"]}], "source": ["for md in md_data:\n", "    # 加载模型\n", "    model = torch.jit.load(f\"d:\\\\RoboQuant\\\\model\\\\{md['model']}.model\")\n", "    model.eval()\n", "    print(md['encoding'])\n", "    encoding = torch.tensor([md['encoding']], dtype=torch.int32)\n", "    input_tensor = torch.tensor(md['data'])\n", "    if '05' in md['model']:\n", "        channel = 5\n", "    elif '10' in md['model']:\n", "        channel = 10\n", "    elif '15' in md['model']:\n", "        channel = 15\n", "    else:\n", "        continue\n", "    input_tensor = input_tensor.reshape(1, channel, len(md['data'])//channel)\n", "    # print(encoding.shape, input_tensor.shape)\n", "    output_tensor = model(encoding, input_tensor)\n", "    print(output_tensor, md['predict'])\n"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["with open('e:/lab/RoboQuant/bin/x64/Release/rpt/model_test_data.json') as f:\n", "    md_data = json.load(f)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/plain": ["560"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["len(md_data[0]['feat'])"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["feat = md_data[0]['feat'][-11:]\n", "data = md_data[0]['data'][-11:]\n", "means = md_data[0]['means'][-11:]\n", "stds = md_data[0]['stds'][-11:]"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1.6589628458023071, 0.0, -0.6534249186515808, 0.2763267755508423, 1.0, 0.16439345479011536, -0.7624899744987488, -1.0, -0.02448284812271595, -0.12306361645460129, 1.0]\n", "[1.9540044069290161, -4.955992221832275, -0.12699022889137268, 0.2434171885251999, 0.4251939356327057, -1.3482558727264404, -1.1690670251846313, -0.5492770671844482, -0.0646616667509079, -0.1042919009923935, 0.7405258417129517]\n", "[1.370863914489746, 15.722155570983887, -0.04414869099855423, -0.038297999650239944, -0.05515841394662857, 0.26343804597854614, 0.35119858384132385, 0.16802211105823517, 0.2408970147371292, 0.016221705824136734, -0.04293831065297127]\n", "[0.14744026958942413, 3.1723527908325195, 4.7978196144104, 1.2925331592559814, 2.481593132019043, 0.07346127182245255, 0.9526302218437195, 2.126471757888794, 4.104129791259766, 1.3355334997177124, 1.4083753824234009]\n"]}], "source": ["print(feat)\n", "print(data)\n", "print(means)\n", "print(stds)"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ 1.95400437 -4.95599216 -0.12699023  0.24341718  0.42519396 -1.34825587\n", " -1.169067   -0.54927704 -0.06466166 -0.1042919   0.7405258 ]\n"]}], "source": ["print((np.array(feat) - np.array(means)) / np.array(stds))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Model dump check"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["['CONV1D_10HF_L3Y_SEL_long',\n", " 'CONV1D_10HF_L3Y_SEL_short',\n", " 'CONV1D_10HF_L4Y_SEL_long',\n", " 'CONV1D_10HF_L4Y_SEL_short',\n", " 'CONV1D_5HF_1921_SEL_long',\n", " 'CONV1D_5HF_1921_SEL_short',\n", " 'CONV1D_5HF_L3Y_SEL_long',\n", " 'CONV1D_5HF_L3Y_SEL_short']"]}, "metadata": {}, "output_type": "display_data"}], "source": ["rpt_path = 'd:/RoboQuant2/rpt'\n", "json_file = 'model_test_202306172311.json'\n", "with open(f'{rpt_path}/{json_file}') as file:\n", "    # Load the JSON data\n", "    data = json.load(file)\n", "models = data['models']\n", "data.pop('models')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(48381, 10)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>code</th>\n", "      <th>change</th>\n", "      <th>CONV1D_10HF_L3Y_SEL_long</th>\n", "      <th>CONV1D_10HF_L3Y_SEL_short</th>\n", "      <th>CONV1D_10HF_L4Y_SEL_long</th>\n", "      <th>CONV1D_10HF_L4Y_SEL_short</th>\n", "      <th>CONV1D_5HF_1921_SEL_long</th>\n", "      <th>CONV1D_5HF_1921_SEL_short</th>\n", "      <th>CONV1D_5HF_L3Y_SEL_long</th>\n", "      <th>CONV1D_5HF_L3Y_SEL_short</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>A9999.DC</td>\n", "      <td>-0.001255</td>\n", "      <td>0.497273</td>\n", "      <td>0.461983</td>\n", "      <td>0.471352</td>\n", "      <td>0.482342</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>A9999.DC</td>\n", "      <td>0.002723</td>\n", "      <td>0.490583</td>\n", "      <td>0.476598</td>\n", "      <td>0.471352</td>\n", "      <td>0.482342</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>A9999.DC</td>\n", "      <td>0.003551</td>\n", "      <td>0.499619</td>\n", "      <td>0.459217</td>\n", "      <td>0.495209</td>\n", "      <td>0.454266</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>A9999.DC</td>\n", "      <td>0.003551</td>\n", "      <td>0.484793</td>\n", "      <td>0.453703</td>\n", "      <td>0.513288</td>\n", "      <td>0.435544</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>A9999.DC</td>\n", "      <td>0.003551</td>\n", "      <td>0.527139</td>\n", "      <td>0.432393</td>\n", "      <td>0.513288</td>\n", "      <td>0.435544</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48376</th>\n", "      <td>Y9999.DC</td>\n", "      <td>-0.004355</td>\n", "      <td>0.489218</td>\n", "      <td>0.469886</td>\n", "      <td>0.474990</td>\n", "      <td>0.529387</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48377</th>\n", "      <td>Y9999.DC</td>\n", "      <td>-0.004355</td>\n", "      <td>0.489218</td>\n", "      <td>0.469886</td>\n", "      <td>0.474990</td>\n", "      <td>0.529387</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48378</th>\n", "      <td>Y9999.DC</td>\n", "      <td>-0.004355</td>\n", "      <td>0.489218</td>\n", "      <td>0.469886</td>\n", "      <td>0.474990</td>\n", "      <td>0.529387</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48379</th>\n", "      <td>Y9999.DC</td>\n", "      <td>-0.004355</td>\n", "      <td>0.489218</td>\n", "      <td>0.469886</td>\n", "      <td>0.474990</td>\n", "      <td>0.529387</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48380</th>\n", "      <td>Y9999.DC</td>\n", "      <td>-0.004355</td>\n", "      <td>0.489218</td>\n", "      <td>0.469886</td>\n", "      <td>0.474990</td>\n", "      <td>0.529387</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>48381 rows × 10 columns</p>\n", "</div>"], "text/plain": ["           code    change  CONV1D_10HF_L3Y_SEL_long  \\\n", "0      A9999.DC -0.001255                  0.497273   \n", "1      A9999.DC  0.002723                  0.490583   \n", "2      A9999.DC  0.003551                  0.499619   \n", "3      A9999.DC  0.003551                  0.484793   \n", "4      A9999.DC  0.003551                  0.527139   \n", "...         ...       ...                       ...   \n", "48376  Y9999.DC -0.004355                  0.489218   \n", "48377  Y9999.DC -0.004355                  0.489218   \n", "48378  Y9999.DC -0.004355                  0.489218   \n", "48379  Y9999.DC -0.004355                  0.489218   \n", "48380  Y9999.DC -0.004355                  0.489218   \n", "\n", "       CONV1D_10HF_L3Y_SEL_short  CONV1D_10HF_L4Y_SEL_long  \\\n", "0                       0.461983                  0.471352   \n", "1                       0.476598                  0.471352   \n", "2                       0.459217                  0.495209   \n", "3                       0.453703                  0.513288   \n", "4                       0.432393                  0.513288   \n", "...                          ...                       ...   \n", "48376                   0.469886                  0.474990   \n", "48377                   0.469886                  0.474990   \n", "48378                   0.469886                  0.474990   \n", "48379                   0.469886                  0.474990   \n", "48380                   0.469886                  0.474990   \n", "\n", "       CONV1D_10HF_L4Y_SEL_short  CONV1D_5HF_1921_SEL_long  \\\n", "0                       0.482342                       0.0   \n", "1                       0.482342                       0.0   \n", "2                       0.454266                       0.0   \n", "3                       0.435544                       0.0   \n", "4                       0.435544                       0.0   \n", "...                          ...                       ...   \n", "48376                   0.529387                       0.0   \n", "48377                   0.529387                       0.0   \n", "48378                   0.529387                       0.0   \n", "48379                   0.529387                       0.0   \n", "48380                   0.529387                       0.0   \n", "\n", "       CONV1D_5HF_1921_SEL_short  CONV1D_5HF_L3Y_SEL_long  \\\n", "0                            0.0                      0.0   \n", "1                            0.0                      0.0   \n", "2                            0.0                      0.0   \n", "3                            0.0                      0.0   \n", "4                            0.0                      0.0   \n", "...                          ...                      ...   \n", "48376                        0.0                      0.0   \n", "48377                        0.0                      0.0   \n", "48378                        0.0                      0.0   \n", "48379                        0.0                      0.0   \n", "48380                        0.0                      0.0   \n", "\n", "       CONV1D_5HF_L3Y_SEL_short  \n", "0                           0.0  \n", "1                           0.0  \n", "2                           0.0  \n", "3                           0.0  \n", "4                           0.0  \n", "...                         ...  \n", "48376                       0.0  \n", "48377                       0.0  \n", "48378                       0.0  \n", "48379                       0.0  \n", "48380                       0.0  \n", "\n", "[48381 rows x 10 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["dfs = pd.DataFrame()\n", "for code in data.keys():\n", "    df = pd.DataFrame(data[code], columns=['change'] + models)\n", "    df [\"change\"] = df[\"change\"].shift(-1)\n", "    df.dropna(inplace=True)\n", "    df = df.loc[df['change'] != 0.0, :]\n", "    df.insert(0, 'code', code)\n", "    dfs = pd.concat([dfs, df], axis=0)\n", "# 删除loc.columns[2:]列所有列元素都为0的列\n", "# dfs = dfs.loc[:, dfs[dfs.columns[2:]].sum(axis=0) > 0.0]\n", "# # 删除所有行元素都为0的行\n", "dfs = dfs.loc[dfs[dfs.columns[2:]].sum(axis=1) > 0.0, :]\n", "dfs.reset_index(drop=True, inplace=True)\n", "print(dfs.shape)\n", "dfs.to_csv(f'{rpt_path}/{json_file}.csv', index=False)\n", "dfs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>change</th>\n", "      <th>CONV1D_10HF_L3Y_SEL_long</th>\n", "      <th>CONV1D_10HF_L3Y_SEL_short</th>\n", "      <th>CONV1D_10HF_L4Y_SEL_long</th>\n", "      <th>CONV1D_10HF_L4Y_SEL_short</th>\n", "      <th>CONV1D_5HF_1921_SEL_long</th>\n", "      <th>CONV1D_5HF_1921_SEL_short</th>\n", "      <th>CONV1D_5HF_L3Y_SEL_long</th>\n", "      <th>CONV1D_5HF_L3Y_SEL_short</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>48381.000000</td>\n", "      <td>48381.000000</td>\n", "      <td>48381.000000</td>\n", "      <td>48381.000000</td>\n", "      <td>48381.000000</td>\n", "      <td>48381.0</td>\n", "      <td>48381.0</td>\n", "      <td>48381.0</td>\n", "      <td>48381.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>-0.000844</td>\n", "      <td>0.491529</td>\n", "      <td>0.478086</td>\n", "      <td>0.495842</td>\n", "      <td>0.482323</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>0.004099</td>\n", "      <td>0.053849</td>\n", "      <td>0.046065</td>\n", "      <td>0.047605</td>\n", "      <td>0.048304</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>-0.019148</td>\n", "      <td>0.063315</td>\n", "      <td>0.322603</td>\n", "      <td>0.070603</td>\n", "      <td>0.192518</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>-0.002015</td>\n", "      <td>0.497181</td>\n", "      <td>0.473533</td>\n", "      <td>0.505161</td>\n", "      <td>0.476548</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>0.002608</td>\n", "      <td>0.530226</td>\n", "      <td>0.506843</td>\n", "      <td>0.529657</td>\n", "      <td>0.512604</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>95%</th>\n", "      <td>0.005323</td>\n", "      <td>0.564942</td>\n", "      <td>0.553916</td>\n", "      <td>0.558200</td>\n", "      <td>0.563807</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>98%</th>\n", "      <td>0.006365</td>\n", "      <td>0.594199</td>\n", "      <td>0.574156</td>\n", "      <td>0.577706</td>\n", "      <td>0.586922</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99%</th>\n", "      <td>0.007617</td>\n", "      <td>0.594199</td>\n", "      <td>0.587056</td>\n", "      <td>0.589834</td>\n", "      <td>0.603789</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>0.017676</td>\n", "      <td>0.658992</td>\n", "      <td>0.941987</td>\n", "      <td>0.659611</td>\n", "      <td>0.706748</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             change  CONV1D_10HF_L3Y_SEL_long  CONV1D_10HF_L3Y_SEL_short  \\\n", "count  48381.000000              48381.000000               48381.000000   \n", "mean      -0.000844                  0.491529                   0.478086   \n", "std        0.004099                  0.053849                   0.046065   \n", "min       -0.019148                  0.063315                   0.322603   \n", "50%       -0.002015                  0.497181                   0.473533   \n", "75%        0.002608                  0.530226                   0.506843   \n", "95%        0.005323                  0.564942                   0.553916   \n", "98%        0.006365                  0.594199                   0.574156   \n", "99%        0.007617                  0.594199                   0.587056   \n", "max        0.017676                  0.658992                   0.941987   \n", "\n", "       CONV1D_10HF_L4Y_SEL_long  CONV1D_10HF_L4Y_SEL_short  \\\n", "count              48381.000000               48381.000000   \n", "mean                   0.495842                   0.482323   \n", "std                    0.047605                   0.048304   \n", "min                    0.070603                   0.192518   \n", "50%                    0.505161                   0.476548   \n", "75%                    0.529657                   0.512604   \n", "95%                    0.558200                   0.563807   \n", "98%                    0.577706                   0.586922   \n", "99%                    0.589834                   0.603789   \n", "max                    0.659611                   0.706748   \n", "\n", "       CONV1D_5HF_1921_SEL_long  CONV1D_5HF_1921_SEL_short  \\\n", "count                   48381.0                    48381.0   \n", "mean                        0.0                        0.0   \n", "std                         0.0                        0.0   \n", "min                         0.0                        0.0   \n", "50%                         0.0                        0.0   \n", "75%                         0.0                        0.0   \n", "95%                         0.0                        0.0   \n", "98%                         0.0                        0.0   \n", "99%                         0.0                        0.0   \n", "max                         0.0                        0.0   \n", "\n", "       CONV1D_5HF_L3Y_SEL_long  CONV1D_5HF_L3Y_SEL_short  \n", "count                  48381.0                   48381.0  \n", "mean                       0.0                       0.0  \n", "std                        0.0                       0.0  \n", "min                        0.0                       0.0  \n", "50%                        0.0                       0.0  \n", "75%                        0.0                       0.0  \n", "95%                        0.0                       0.0  \n", "98%                        0.0                       0.0  \n", "99%                        0.0                       0.0  \n", "max                        0.0                       0.0  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["dfs.describe(percentiles=[0.75, 0.95, 0.98, 0.99])"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}