{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\Anaconda3\\lib\\site-packages\\sklearn\\cross_validation.py:41: DeprecationWarning: This module was deprecated in version 0.18 in favor of the model_selection module into which all the refactored classes and functions are moved. Also note that the interface of the new CV iterators are different from that of this module. This module will be removed in 0.20.\n", "  \"This module will be removed in 0.20.\", DeprecationWarning)\n"]}], "source": ["import pandas as pd\n", "from sklearn.cross_validation import train_test_split\n", "from sklearn.metrics import roc_auc_score\n", "import lightgbm as lgb\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "import matplotlib.pyplot as plt\n", "\n", "%matplotlib inline\n", "\n", "try:\n", "    # To enable interactive mode you should install ipywidgets\n", "    # https://github.com/jupyter-widgets/ipywidgets\n", "    from ipywidgets import interact, SelectMultiple\n", "    INTERACTIVE = True\n", "except ImportError:\n", "    INTERACTIVE = False"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["FD_PATH = \"d:/QuantLab/log/\"\n", "FD_FILE = \"short3\"\n", "train_long = pd.read_csv(FD_PATH + FD_FILE + '.train', header = None)\n", "test_long = pd.read_csv(FD_PATH + FD_FILE + '.test', header = None)\n", "\n", "y_train = train_long[0]\n", "X_train = train_long.drop(0, axis=1)\n", "\n", "y_test = test_long[0]\n", "X_test = test_long.drop(0, axis=1)\n", "\n", "# X_train, X_test, y_train, y_test = train_test_split(X_long,\n", "#                                                    y_long,\n", "#                                                    test_size = 0.2,\n", "#                                                    random_state = 0)\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["lgb_train = lgb.Dataset(X_train, y_train)\n", "lgb_test = lgb.Dataset(X_test, y_test, reference=lgb_train)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# params = {\n", "#     'app': 'binary',\n", "#     'num_leaves': 50,\n", "#     'metric': ['l1', 'l2'],\n", "#     'verbose': -1\n", "# }\n", "# params = {\n", "#     'bagging_freq': 5,\n", "#     'bagging_fraction': 0.335,\n", "#     'boost_from_average':'true',\n", "#     'boost': 'gbdt',\n", "#     'feature_fraction': 0.041,\n", "#     'learning_rate': 0.01,\n", "#     'max_depth': 12,\n", "#     'metric':'auc',\n", "#     'min_data_in_leaf': 80,\n", "#     'min_sum_hessian_in_leaf': 10.0,\n", "#     'num_leaves': 13,\n", "#     'num_threads': 8,\n", "#     'tree_learner': 'serial',\n", "#     'objective': 'binary',\n", "#     'verbosity': 1\n", "# }\n", "params = {\n", "    'learning_rate': 0.01,\n", "    'max_depth': 12,\n", "    'boosting': 'gbdt',\n", "    'objective': 'binary',\n", "    'metric': 'auc',\n", "    'is_training_metric': True,\n", "    'seed': 42\n", "}\n", "params = {\n", "    'learning_rate': 0.01,  # 0.1\n", "    'feature_fraction': 0.95,  # 1.0 [0.0,1.0]\n", "    'bagging_fraction': 0.95,  # 1.0 [0.0,1.0]\n", "    'bagging_freq': 2,\n", "    'num_leaves': 30,  # 31\n", "    'max_depth': 4,  # -1\n", "    'max_bin': 30,  # 255\n", "    'lambda_l1': 0.1,\n", "    'lambda_l2': 40,\n", "    'boosting': 'gbdt',\n", "    'objective': 'binary',\n", "    'metric': 'auc',\n", "    'is_training_metric': True,\n", "    'seed': 42\n", "}"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[10]\ttraining's auc: 0.708521\tvalid_1's auc: 0.500612\n", "[20]\ttraining's auc: 0.716845\tvalid_1's auc: 0.487012\n", "[30]\ttraining's auc: 0.733162\tvalid_1's auc: 0.480325\n", "[40]\ttraining's auc: 0.744788\tvalid_1's auc: 0.477511\n", "[50]\ttraining's auc: 0.753284\tvalid_1's auc: 0.476247\n", "[60]\ttraining's auc: 0.756524\tvalid_1's auc: 0.475594\n", "[70]\ttraining's auc: 0.760724\tvalid_1's auc: 0.468336\n", "[80]\ttraining's auc: 0.762757\tvalid_1's auc: 0.464543\n", "[90]\ttraining's auc: 0.767463\tvalid_1's auc: 0.459242\n", "[100]\ttraining's auc: 0.767615\tvalid_1's auc: 0.46067\n"]}], "source": ["evals_result = {}  # to record eval results for plotting\n", "gbm = lgb.train(params,\n", "                lgb_train,\n", "                num_boost_round=100,\n", "                valid_sets=[lgb_train, lgb_test],\n", "                feature_name=['f' + str(i + 1) for i in range(X_train.shape[-1])],\n", "                evals_result=evals_result,\n", "                verbose_eval=10)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Save model...\n", "Start predicting...\n", "The roc of prediction is: 0.4606695755005505\n", "Dump model to JSON...\n", "Feature names: ['f1', 'f2', 'f3', 'f4', 'f5', 'f6', 'f7', 'f8', 'f9', 'f10', 'f11', 'f12', 'f13', 'f14', 'f15', 'f16', 'f17', 'f18', 'f19', 'f20', 'f21', 'f22', 'f23', 'f24', 'f25', 'f26', 'f27', 'f28', 'f29', 'f30', 'f31']\n", "Calculate feature importances...\n", "Feature importances: [34, 26, 10, 42, 21, 59, 81, 81, 15, 21, 37, 65, 55, 38, 61, 14, 16, 20, 18, 44, 15, 16, 40, 28, 85, 29, 45, 60, 14, 15, 20]\n"]}], "source": ["print('Save model...')\n", "# save model to file\n", "gbm.save_model(FD_PATH + 'LightGBM_' + FD_FILE + '.model')\n", "print('Start predicting...')\n", "# predict\n", "y_pred = gbm.predict(X_test, num_iteration=gbm.best_iteration)\n", "# eval\n", "# print(y_pred)\n", "print('The roc of prediction is:', roc_auc_score(y_test, y_pred) )\n", "print('Dump model to JSON...')\n", "# dump model to json (and save to file)\n", "model_json = gbm.dump_model()\n", "with open(FD_PATH + 'model.json', 'w+') as f:\n", "    json.dump(model_json, f, indent=4)\n", "print('Feature names:', gbm.feature_name())\n", "print('Calculate feature importances...')\n", "# feature importances\n", "print('Feature importances:', list(gbm.feature_importance()))"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def render_metric(metric_name):\n", "    ax = lgb.plot_metric(evals_result, metric=metric_name, figsize = (10, 5))\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "291f56ad6d724d17add68519e7af1aa3", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(Text(value='auc', description='metric_name'), Output()), _dom_classes=('widget-interact'…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["if INTERACTIVE:\n", "    # create widget to switch between metrics\n", "    interact(render_metric, metric_name=params['metric'])\n", "else:\n", "    render_metric(params['metric'][0])"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def render_plot_importance(importance_type, max_features=10,\n", "                          ignore_zero=True, precision=4):\n", "    ax = lgb.plot_importance(gbm, importance_type=importance_type,\n", "                             max_num_features=max_features,\n", "                             ignore_zero=ignore_zero, figsize=(12, 8),\n", "                             precision=precision)\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e14a9e7f2cde48dbad8e910dc2245c3c", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(Dropdown(description='importance_type', options=('split', 'gain'), value='split'), IntSl…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["if INTERACTIVE:\n", "    # create widget for interactive feature importance plot\n", "    interact(render_plot_importance,\n", "             importance_type=['split', 'gain'],\n", "             max_features=(1, X_train.shape[-1]),\n", "             precision=(0, 10))\n", "else:\n", "    render_plot_importance(importance_type='split')"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def render_tree(tree_index, show_info, precision=4):\n", "    show_info = None if 'None' in show_info else show_info\n", "    return lgb.create_tree_digraph(gbm, tree_index=tree_index,\n", "                                  show_info=show_info, precision=precision)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8a5e6e9094154e70a6787e3bab4c9b7b", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=49, description='tree_index', max=99), SelectMultiple(description='show_…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["if INTERACTIVE:\n", "    # create widget to switch between trees and control info in nodes\n", "    interact(render_tree,\n", "             tree_index=(0, gbm.num_trees() - 1),\n", "             show_info=SelectMultiple(  # allow multiple values to be selected\n", "                 options=['None',\n", "                          'split_gain',\n", "                          'internal_value',\n", "                          'internal_count',\n", "                          'leaf_count'],\n", "                 value=['None']),\n", "             precision=(0, 10))\n", "    tree = None\n", "else:\n", "    tree = render_tree(84, ['None'])\n", "tree"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 2}