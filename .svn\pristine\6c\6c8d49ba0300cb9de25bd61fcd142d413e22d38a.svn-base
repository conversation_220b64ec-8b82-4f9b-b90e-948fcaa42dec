{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# String Initialization\n", "\n", "After having some experience with the library and its capabilities, it becomes common to import the same types of components. As more components get added to the library it will be hard to keep track of them, therefore being able to call components by string can make experimenting with different exchanges and strategies much easier."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Calling components from packages\n", "In order for a component to be callable from a package, it first must be submitted into the registry located in the ```__init__.py``` file that defines the components package. Once the component is registered it is then callable by running ```<component-package>.get(<component-name>)```."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"ename": "ImportError", "evalue": "DLL load failed: 找不到指定的模块。", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mImportError\u001b[0m                               Traceback (most recent call last)", "\u001b[1;32m<ipython-input-2-3e80ce047638>\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[1;32m----> 1\u001b[1;33m \u001b[1;32mimport\u001b[0m \u001b[0mtensortrade\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0mtd\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      2\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      3\u001b[0m \u001b[0mexchange\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mtd\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mexchanges\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mget\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;34m'fbm'\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      4\u001b[0m \u001b[0maction_strategy\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mtd\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mactions\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mget\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;34m'discrete'\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      5\u001b[0m \u001b[0mreward_strategy\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mtd\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mrewards\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mget\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;34m'simple'\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\Anaconda3\\lib\\site-packages\\tensortrade\\__init__.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m      5\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mrewards\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      6\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mslippage\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 7\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mstrategies\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      8\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mtrades\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      9\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\Anaconda3\\lib\\site-packages\\tensortrade\\strategies\\__init__.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0mtrading_strategy\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mTradingStrategy\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 2\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0mstable_baselines_strategy\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mStableBaselinesTradingStrategy\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      3\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0mtensorforce_trading_strategy\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mTensorforceTradingStrategy\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      4\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      5\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\Anaconda3\\lib\\site-packages\\tensortrade\\strategies\\stable_baselines_strategy.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m     26\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mstable_baselines\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mcommon\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpolicies\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mBasePolicy\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     27\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mstable_baselines\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mcommon\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mbase_class\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mBaseRLModel\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 28\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[0mstable_baselines\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mDQN\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     29\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     30\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensortrade\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0menvironments\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mtrading_environment\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mTradingEnvironment\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\Anaconda3\\lib\\site-packages\\stable_baselines\\__init__.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m     15\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     16\u001b[0m \u001b[1;32mif\u001b[0m \u001b[0mmpi4py\u001b[0m \u001b[1;32mis\u001b[0m \u001b[1;32mnot\u001b[0m \u001b[1;32mNone\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 17\u001b[1;33m     \u001b[1;32mfrom\u001b[0m \u001b[0mstable_baselines\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mddpg\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mDDPG\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     18\u001b[0m     \u001b[1;32mfrom\u001b[0m \u001b[0mstable_baselines\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mgail\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mGAIL\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     19\u001b[0m     \u001b[1;32mfrom\u001b[0m \u001b[0mstable_baselines\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mppo1\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mPPO1\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\Anaconda3\\lib\\site-packages\\stable_baselines\\ddpg\\__init__.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mstable_baselines\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mcommon\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mnoise\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mAdaptiveParamNoiseSpec\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mNormalActionNoise\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mOrnsteinUhlenbeckActionNoise\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 2\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[0mstable_baselines\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mddpg\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mddpg\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mDDPG\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      3\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mstable_baselines\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mddpg\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpolicies\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mMlpPolicy\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mCnnPolicy\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mLnMlpPolicy\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mLnCnnPolicy\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\Anaconda3\\lib\\site-packages\\stable_baselines\\ddpg\\ddpg.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m     10\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mtensorflow\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0mtf\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     11\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mcontrib\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0mtc\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 12\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[0mmpi4py\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mMPI\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     13\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     14\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mstable_baselines\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mlogger\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;31mImportError\u001b[0m: DLL load failed: 找不到指定的模块。"]}], "source": ["import tensortrade as td\n", "\n", "exchange = td.exchanges.get('fbm')\n", "action_strategy = td.actions.get('discrete')\n", "reward_strategy = td.rewards.get('simple')\n", "\n", "exchange"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Given this capability you can simply instantiate an environment using the following code."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["<tensortrade.exchanges.simulated.fbm_exchange.FBMExchange at 0x1c62548c630>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from tensortrade.environments import TradingEnvironment\n", "\n", "env = TradingEnvironment(exchange='fbm',\n", "                         action_strategy='discrete',\n", "                         reward_strategy='simple')\n", "\n", "env.exchange"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Once default trading environment configurations are developed they can be added to the environment registry in order to be called by only one line."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"ename": "ImportError", "evalue": "DLL load failed: 找不到指定的模块。", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mImportError\u001b[0m                               Traceback (most recent call last)", "\u001b[1;32m<ipython-input-4-d025a1f91ae5>\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[1;32m----> 1\u001b[1;33m \u001b[1;32mimport\u001b[0m \u001b[0mtensortrade\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0menvironments\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0menvs\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      2\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      3\u001b[0m \u001b[0menv\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0menvs\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mget\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;34m'basic'\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      4\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      5\u001b[0m \u001b[1;31m### Equivalent creation\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\Anaconda3\\lib\\site-packages\\tensortrade\\__init__.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m      5\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mrewards\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      6\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mslippage\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 7\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mstrategies\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      8\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mtrades\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      9\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\Anaconda3\\lib\\site-packages\\tensortrade\\strategies\\__init__.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0mtrading_strategy\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mTradingStrategy\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 2\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0mstable_baselines_strategy\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mStableBaselinesTradingStrategy\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      3\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0mtensorforce_trading_strategy\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mTensorforceTradingStrategy\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      4\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      5\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\Anaconda3\\lib\\site-packages\\tensortrade\\strategies\\stable_baselines_strategy.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m     26\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mstable_baselines\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mcommon\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpolicies\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mBasePolicy\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     27\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mstable_baselines\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mcommon\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mbase_class\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mBaseRLModel\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 28\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[0mstable_baselines\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mDQN\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     29\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     30\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensortrade\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0menvironments\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mtrading_environment\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mTradingEnvironment\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\Anaconda3\\lib\\site-packages\\stable_baselines\\__init__.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m     15\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     16\u001b[0m \u001b[1;32mif\u001b[0m \u001b[0mmpi4py\u001b[0m \u001b[1;32mis\u001b[0m \u001b[1;32mnot\u001b[0m \u001b[1;32mNone\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 17\u001b[1;33m     \u001b[1;32mfrom\u001b[0m \u001b[0mstable_baselines\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mddpg\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mDDPG\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     18\u001b[0m     \u001b[1;32mfrom\u001b[0m \u001b[0mstable_baselines\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mgail\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mGAIL\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     19\u001b[0m     \u001b[1;32mfrom\u001b[0m \u001b[0mstable_baselines\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mppo1\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mPPO1\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\Anaconda3\\lib\\site-packages\\stable_baselines\\ddpg\\__init__.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mstable_baselines\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mcommon\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mnoise\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mAdaptiveParamNoiseSpec\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mNormalActionNoise\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mOrnsteinUhlenbeckActionNoise\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 2\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[0mstable_baselines\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mddpg\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mddpg\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mDDPG\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      3\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mstable_baselines\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mddpg\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpolicies\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mMlpPolicy\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mCnnPolicy\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mLnMlpPolicy\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mLnCnnPolicy\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32md:\\Anaconda3\\lib\\site-packages\\stable_baselines\\ddpg\\ddpg.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m     10\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mtensorflow\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0mtf\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     11\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mcontrib\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0mtc\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 12\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[0mmpi4py\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mMPI\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     13\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     14\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mstable_baselines\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mlogger\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;31mImportError\u001b[0m: DLL load failed: 找不到指定的模块。"]}], "source": ["import tensortrade.environments as envs\n", "\n", "env = envs.get('basic')\n", "\n", "### Equivalent creation\n", "\n", "env = envs.TradingEnvironment(exchange='simulated',\n", "                              action_strategy='discrete',\n", "                              reward_strategy='simple')\n", "\n", "env.exchange"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 2}