# import os
# import time
# import sys
from copy import deepcopy

# import torch
# import torch.nn as nn
import numpy as np
import numpy.random as rd
import pandas as pd
import gym
from gym import spaces

from rllab.data.tickdata_fut import PreprocessingPipeline, TickFeatureDataset

"""
- state的含义、设置,如account,price,technical indicator,
  具体看代码 https://github.com/AI4Finance-Foundation/FinRL/blob/master/finrl/finrl_meta/env_stock_trading/env_stocktrading.py
- action的含义,买入卖出,以及小额交易忽略
- reward function的设置(直接影响 能否训练出更加谨慎,回撤更小的智能体)交易滑点,
- turbulence达到阈值就强制卖出,等
- env reset 里的随机性对于强化学习训练的益处

金融强化学习算法与其他自动交易算法的区别如下：
1. 强化学习算法中的无模型算法 不需要对环境进行建模（也就是它不预测市场）。
   这与部分深度学习的交易算法对市场进行一定程度的预测不同。市场的预测一直是一个难题,而深度强化学习
   有机会在不对市场进行预测的情况下,直接学习交易策略。
2. 数据导向,增量学习。
   深度强化学习算法和其他深度学习算法一样,都是数据导向的。这与一些基于基本逻辑语句写就的交易算法明显不同。
   市场每时每刻都在产出大量数据,依靠人类经验总结出来的交易策略大家都在使用。而深度学习可以使用这些数据,
   提高交易策略的自动化程度。

"""

class FuturesLSDiscEnv(gym.Env):
    """
    def __init__(self, 
        initial_amount=1e6,  # 初始本金
        max_stock=1e2,  # 最大交易额度,买入或卖出100个单位
        buy_cost_pct=1e-3,  # 交易损耗率设为 0.001
        sell_cost_pct=1e-3,  # 交易损耗率设为 0.001
        gamma=0.99,  # 强化学习的折扣比率,给人为设置的终止状态的reward进行补偿的时候会用到
     ):

    """
    def __init__(self, name="FuturesLSDiscEnv-v1",
                 initial_amount=1e5,
                 gamma=0.98,
                 mode='train'):

        # print(initial_amount, buy_cost_pct, sell_cost_pct, gamma)
        if mode == 'train' or mode == 'test':
            ppp = PreprocessingPipeline(n_splits=5, shuffle=True, random_state=42)
            self.df_train, self.df_valid = ppp.transform()
            if mode=='train':
                self.fd = TickFeatureDataset(self.df_train)
            else:
                self.fd = TickFeatureDataset(self.df_valid)
            self.tick_data, _ = self.fd.getitem(0)

        self.long_cost_price = None
        self.short_cost_price = None

        # self.direct = direct # 头寸方向： Long 多头 Short 空头

        self.max_shares = 5

        self.volume_multiple=10   # {"RB": 10, "HC": 10 }
        self.margin_ratio=0.09    # {"RB": 0.09, "HC": 0.09}
        self.commission_ratio = 5e-5

        # self.name=name
        # self.buy_cost_rate = 1 + buy_cost_pct
        # self.sell_cost_rate = 1 - sell_cost_pct
        self.initial_amount = initial_amount
        self.gamma = gamma

        # reset()
        self.cur_idx = 0
        self.rewards = None
        self.total_asset = None
        self.cumulative_returns = 0
        self.if_random_reset = False

        self.amount = 0
        self.long_shares = 0
        self.short_shares = 0
        self.shares_num = 1 # self.close_ary.shape[1]
        self.amount_dim = 1

        # environment information
        self.env_name = name
        # self.action_space = spaces.Box(low=-1, high=1, shape=(self.action_space,))
        self.action_space = spaces.Discrete(5)
        self.state_dim = self.tick_data.shape[1] # + 2
        self.action_dim = self.shares_num

        # self.observation_space = spaces.Box(low=-np.inf, high=np.inf, shape=(self.state_space,))        
        self.observation_space = gym.spaces.Box(
            low=-np.inf, high=np.inf, shape=(self.state_dim,), dtype=np.float32
        )
        self.if_discrete = True
        self.max_step = self.fd.len()

        # self.target_return = 100

    def _buy_pnl(self, last_price, cost_price, quantity)->float:
        return (last_price - cost_price) * quantity * self.volume_multiple

    def _sell_pnl(self, last_price, cost_price, quantity)->float:
        return (cost_price - last_price) * quantity * self.volume_multiple

    def _margin(self, last_price, quantity):
        return (last_price * quantity * self.margin_ratio) # margin ratio

    def _commission(self, last_price, quantity):
        return last_price * quantity * self.volume_multiple * self.commission_ratio

    def reset(self):
        self.cur_idx = 0
        if self.if_random_reset:
            self.amount = self.initial_amount * rd.uniform(0.9, 1.1)
            self.long_shares = (np.abs(rd.randn(self.shares_num).clip(-2, +2)) * 2 ** 3).astype(int)
            self.short_shares = (np.abs(rd.randn(self.shares_num).clip(-2, +2)) * 2 ** 3).astype(int)
        else:
            self.amount = self.initial_amount
            self.long_shares = 0
            self.short_shares = 0

        self.rewards = list()
        # self.total_asset = (self.close_ary[self.day] * self.shares).sum() + self.amount
        self.total_asset = self.amount
        # print("====================reset=====================\n")
        # print(self.get_state())
        return self.get_state()

    def get_state(self):
        """
        如果变更, 注意更新self.state_dim的值
        """
        # amount = np.array(self.amount * 2 ** -10, dtype=np.float32)
        # shares = np.array(self.shares * 2 ** -4, dtype=np.float32)
        # feats = np.array(self.tick_data.values.reshape(-1) * 2 ** -7, dtype=np.float32)
        # state = np.hstack((amount, shares, feats))

        state = self.tick_data.values.reshape(-1) * 2 ** -7
        # print(state)
        return state

    def step(self, action):
        self.cur_idx += 1

        self.tick_data, close_price = self.fd.getitem(self.cur_idx)

        if action == 1:  # open_long
            if self.long_shares == 0:
                delta_qty = min(self.max_shares, self.amount // close_price * 0.5) #, stock_action)
                self.amount -= self._margin(close_price, delta_qty) - self._commission(close_price, delta_qty) #* self.buy_cost_rate
                self.long_shares += int(delta_qty)
                self.long_cost_price = close_price
        elif action == 2:  # close_long
            if self.long_shares > 0:
                self.amount += self._margin(self.long_cost_price, self.long_shares) - self._commission(close_price, self.long_shares) # * self.sell_cost_rate
                self.amount += self._buy_pnl(close_price, self.long_cost_price, self.long_shares)
                self.long_cost_price = 0.0
                self.long_shares = 0
        elif action == 3:  # open_short
            if self.short_shares == 0:
                delta_qty = min(self.max_shares, self.amount // close_price * 0.5) #, stock_action)
                self.amount -= self._margin(close_price, delta_qty) - self._commission(close_price, delta_qty) #* self.buy_cost_rate
                self.short_shares += int(delta_qty)
                self.short_cost_price = close_price
        elif action == 4:  # close_short
            if self.short_shares > 0:
                self.amount += self._margin(self.short_cost_price, self.short_shares) - self._commission(close_price, self.short_shares) # * self.sell_cost_rate
                self.amount += self._sell_pnl(close_price, self.short_cost_price, self.short_shares)
                self.short_cost_price = 0.0
                self.short_shares = 0
        else: # hold
            # print(f"action:{action}")
            pass

        state = self.get_state()

        # 计算reward
        total_asset = self.amount
        if self.long_shares > 0:
            total_asset += (self._margin(self.long_cost_price, self.long_shares) + self._buy_pnl(close_price, self.long_cost_price, self.long_shares))
        if self.short_shares > 0:
            total_asset += (self._margin(self.short_cost_price, self.short_shares) + self._buy_pnl(close_price, self.short_cost_price, self.short_shares))
        reward = (total_asset - self.total_asset) * 2 ** -6

        self.rewards.append(reward)
        self.total_asset = total_asset
        # if reward != 0.0:
        #     print(f"total_asset: {total_asset}, reward: {reward}")

        done = self.cur_idx == self.max_step - 1
        if done:
            reward += 1 / (1 - self.gamma) * np.mean(self.rewards)
            self.cumulative_returns = total_asset / self.initial_amount
            print("===============================")
            print(f"cumulative returns: {self.cumulative_returns}")
        if isinstance(reward, np.ndarray):
            reward = reward[0]
        # print(type(reward))
        # print("step", total_asset, reward)
        return state, reward, done, {}



