# 盘口数据特征
# - tick数据量太大，如果保存所有数据，没有足够的存储空间
#     - 只保存主连合约
#     - 保存文件格式除考虑支持压缩
# 

import pandas as pd
import os
from datetime import date, datetime
from pyqlab.const import MAIN_FUT_MARKET_CODES, SF_FUT_CODES
from argparse import ArgumentParser

def is_exist_folder(path: str, sub_folder: str):
    return sub_folder in os.listdir(path) 
    
def is_exist_file(filename: str):
    return os.path.isfile(filename)

def get_dir_list(dir_path):
    # Get a list of the content inside the directory
    files = os.listdir(dir_path)

    # Print out the list of files
    dir_list = []
    for file in files:
        dir_list.append(file)
    return dir_list    

def year_month_segment(year: int):
    seg = []
    for month in range(1, 13):
        first_day = datetime.date(year, month, 1)
        if month == 12:
            last_day = datetime.date(year+1, 1, 1) - datetime.timedelta(days=1)
        else:
            last_day = datetime.date(year, month+1, 1) - datetime.timedelta(days=1)
        seg.append((first_day.strftime('%Y%m%d'), last_day.strftime('%Y%m%d')))
        # print(f"Month {month}: First day: {first_day.strftime('%Y%m%d')}, Last day: {last_day.strftime('%Y%m%d')}")
    return seg

def combine_datetime(row):
    return datetime.strptime(f"{row['交易日']} {row['最后修改时间']} {row['最后修改毫秒']}", "%Y%m%d %H:%M:%S %f")

def restore_order_of_night_trading_time(df: pd.DataFrame):
    """恢复夜盘时间顺序"""
    if "datetime" not in df.columns:
        raise Exception("datetime column not exists")
    # 分成两个dataframe
    df1 = df.loc[df['datetime'].dt.hour < 20]
    df2 = df.loc[df['datetime'].dt.hour >= 20] # 夜盘时间
    # 进一步分批处理
    df2_1 = df2.loc[(df2['datetime'].dt.weekday <= 4) & (df2['datetime'].dt.weekday > 0)]
    df2_2 = df2.loc[df2['datetime'].dt.weekday == 0]
    del df2
    #如果datetime是星期二到星期五，且时间在21:00到24:00之间，那么datetime减一天
    df2_1['datetime'] = df2_1['datetime'] - pd.Timedelta(days=1)
    #如果datetime是星期一，且时间在21:00到24:00之间，那么datetime减三天
    df2_2['datetime'] = df2_2['datetime'] - pd.Timedelta(days=3)
    dfs = pd.concat([df1, df2_1, df2_2])
    dfs.sort_values(by=['datetime'], inplace=True, ascending=True)
    dfs.reset_index(drop=True, inplace=True)
    return dfs

def read_csv_file(fname, code, market):
    # columns=[
    #     "交易日","合约代码","交易所代码","合约在交易所的代码",
    #     "最新价","上次结算价","昨收盘","昨持仓量","今开盘","最高价",
    #     "最低价","数量","成交金额","持仓量","今收盘","本次结算价","涨停板价",
    #     "跌停板价","昨虚实度","今虚实度","最后修改时间","最后修改毫秒",
    #     "申买价一","申买量一","申卖价一","申卖量一","申买价二","申买量二","申卖价二","申卖量二",
    #     "申买价三","申买量三","申卖价三","申卖量三","申买价四","申买量四","申卖价四","申卖量四",
    #     "申买价五","申买量五","申卖价五","申卖量五","当日均价","业务日期"
    # ]
    #read csv file 
    df = pd.read_csv(fname, encoding='gbk', header='infer')
    df = df[['合约代码', '交易日', '最后修改时间', '最后修改毫秒', '最新价', '数量']]
    if df.empty:
        print(f'empty file: {fname}')
        return pd.DataFrame()
    # TODO: 是否要合并秒内的tick数据
    # df = df.groupby(['合约代码', '交易日', '最后修改时间']).agg({'最后修改毫秒': 'last', '最新价': 'last', '数量': 'last'}).reset_index()
    df['交易时间'] = df.apply(combine_datetime, axis=1)
    df = df[['合约代码', '交易时间', '最新价', '数量']]
    df = df.rename(columns={'合约代码': 'code', '交易时间': 'datetime', '最新价': 'price', '数量':'volume'})
    df = df.loc[df['volume']>0]
    df['code'] = f'{code}9999.{market}'
    return df

def read_csv_file2(fname, code, market):
    # 对于上海期货交易所,有盘口数据
    # columns=[
    #     "交易日","合约代码","交易所代码","合约在交易所的代码",
    #     "最新价","上次结算价","昨收盘","昨持仓量","今开盘","最高价",
    #     "最低价","数量","成交金额","持仓量","今收盘","本次结算价","涨停板价",
    #     "跌停板价","昨虚实度","今虚实度","最后修改时间","最后修改毫秒",
    #     "申买价一","申买量一","申卖价一","申卖量一","申买价二","申买量二","申卖价二","申卖量二",
    #     "申买价三","申买量三","申卖价三","申卖量三","申买价四","申买量四","申卖价四","申卖量四",
    #     "申买价五","申买量五","申卖价五","申卖量五","当日均价","业务日期"
    # ]
    #read csv file 
    df = pd.read_csv(fname, encoding='gbk', header='infer')
    df = df[['合约代码', '交易日', '最后修改时间', '最后修改毫秒', '最新价', '数量',
        '申买价一','申买量一','申卖价一','申卖量一','申买价二','申买量二','申卖价二','申卖量二',
        '申买价三','申买量三','申卖价三','申卖量三','申买价四','申买量四','申卖价四','申卖量四',
        '申买价五','申买量五','申卖价五','申卖量五']]
    df['交易时间'] = df.apply(combine_datetime, axis=1)
    df.drop(['交易日', '最后修改时间', '最后修改毫秒'], axis=1, inplace=True)
    df = df.rename(columns={'合约代码': 'code', '交易时间': 'datetime', '最新价': 'price', '数量':'volume',
        '申买价一':'bid_price1','申买量一':'bid_volume1','申卖价一':'ask_price1','申卖量一':'ask_volume1',
        '申买价二':'bid_price2','申买量二':'bid_volume2','申卖价二':'ask_price2','申卖量二':'ask_volume2',
        '申买价三':'bid_price3','申买量三':'bid_volume3','申卖价三':'ask_price3','申卖量三':'ask_volume3',
        '申买价四':'bid_price4','申买量四':'bid_volume4','申卖价四':'ask_price4','申卖量四':'ask_volume4',
        '申买价五':'bid_price5','申买量五':'bid_volume5','申卖价五':'ask_price5','申卖量五':'ask_volume5',})
    df = df.loc[df['volume']>0]
    df['code'] = f'{code}9999.{market}'
    return df

def test_signle_code():
    # 加载主力日期切换表
    # print(maintb)
    # 起止日期间所有日期列表
    start = '20230101'
    end = '20230131'
    td=datetime.strptime(start, '%Y%m%d').date()
    td_end=datetime.strptime(end, '%Y%m%d').date()
    all_day = []
    while td < td_end:
        td = date.fromordinal(td.toordinal() + 1)
        all_day.append(td.strftime("%Y%m%d"))
    print(f'all day: {len(all_day)}')
    # Define directory to list from
    directory = 'f:/hqdata/raw/2023' 
    # 有交易数据的日期列表
    market = 'ZC' # 'SC', 'DC'
    code='SA' # SA
    dfs = pd.DataFrame()
    for day in all_day:
        # ag主力连续_20230213
        fname = f'{directory}/{code}主力连续_{day}.csv'
        if not is_exist_file(fname):
            continue
        print(f'read csv file: {fname}')
        df = read_csv_file(fname)
        dfs = pd.concat([dfs, df])
        print(dfs.shape)
    # 将按年汇总的tick数据写入文件
    # print(f'{code}: {dfs.shape}')
    # dfs.to_parquet(f'e:/hqdata/tick/{code}2020.parquet')
    print(dfs.tail())

def etl_fut_sc_tick_data(start: str, end: str, data_path: str='f:/hqdata', is_sec: bool=False):
    # 起止日期间所有日期列表
    # start = '20230201'
    # end = '20230231'
    td=datetime.strptime(start, '%Y%m%d').date()
    td_end=datetime.strptime(end, '%Y%m%d').date()
    all_day = []
    while td <= td_end:
        td = date.fromordinal(td.toordinal() + 1)
        all_day.append(td.strftime("%Y%m%d"))
    print(f'all day: {len(all_day)}')
    # Define directory to list from
    directory = f'{data_path}/raw/{start[0:4]}' 
    cnt = 0
    dfs = pd.DataFrame()
    for code in MAIN_FUT_MARKET_CODES['SC']:
        for day in all_day:
            # 获取当日主力期货的代码名即文件名
            if is_sec:
                fname = f'{directory}/{code}次主力连续_{day}.csv'
            else:
                fname = f'{directory}/{code}主力连续_{day}.csv'
            if not is_exist_file(fname):
                continue
            df = read_csv_file2(fname, code, 'SC')
            dfs = pd.concat([dfs, df])
        # 将按年汇总的tick数据写入文件
        print(f'{cnt} {code}: {dfs.shape}')
        cnt = cnt + 1
    # dfs.sort_values(by=['datetime'], ascending=True, inplace=True)
    dfs = restore_order_of_night_trading_time(dfs) # 恢复夜盘时间顺序
    if is_sec:
        dfs.to_parquet(f'{data_path}/tick/{start[0:4]}/{start[0:6]}.sc.sec.parquet')
    else:
        dfs.to_parquet(f'{data_path}/tick/{start[0:4]}/{start[0:6]}.sc.parquet')
    return dfs

def etl_fut_tick_data(start: str, end: str, data_path: str='f:/hqdata', is_sec: bool=False):
    # 起止日期间所有日期列表
    # start = '20230201'
    # end = '20230231'
    td=datetime.strptime(start, '%Y%m%d').date()
    td_end=datetime.strptime(end, '%Y%m%d').date()
    all_day = []
    while td <= td_end:
        td = date.fromordinal(td.toordinal() + 1)
        all_day.append(td.strftime("%Y%m%d"))
    print(f'all day: {len(all_day)}')
    # Define directory to list from
    directory = f'{data_path}/raw/{start[0:4]}' 
    cnt = 0
    # markets=['SC', 'ZC', 'DC']
    dfs = pd.DataFrame()
    for mk, codes in MAIN_FUT_MARKET_CODES.items():
        for code in codes:
            for day in all_day:
                # 获取当日主力期货的代码名即文件名
                if is_sec:
                    fname = f'{directory}/{code}次主力连续_{day}.csv'
                else:
                    fname = f'{directory}/{code}主力连续_{day}.csv'
                if not is_exist_file(fname):
                    continue
                df = read_csv_file(fname, code, mk)
                dfs = pd.concat([dfs, df])
            # 将按年汇总的tick数据写入文件
            print(f'{cnt} {code}: {dfs.shape}')
            cnt = cnt + 1
    # dfs.sort_values(by=['datetime'], ascending=True, inplace=True)
    dfs = restore_order_of_night_trading_time(dfs) # 恢复夜盘时间顺序
    if is_sec:
        dfs.to_parquet(f'{data_path}/tick/{start[0:4]}/{start[0:6]}.sec.parquet')
    else:
        dfs.to_parquet(f'{data_path}/tick/{start[0:4]}/{start[0:6]}.parquet')
    return dfs

def etl_sf_tick_data(start: str, end: str, data_path: str='f:/hqdata', is_sec: bool=False):
    td=datetime.strptime(start, '%Y%m%d').date()
    td_end=datetime.strptime(end, '%Y%m%d').date()
    all_day = []
    while td <= td_end:
        td = date.fromordinal(td.toordinal() + 1)
        all_day.append(td.strftime("%Y%m%d"))
    print(f'all day: {len(all_day)}')
    # Define directory to list from
    directory = f'{data_path}/raw/{start[0:4]}sf' 
    cnt = 0
    dfs = pd.DataFrame()
    for code in SF_FUT_CODES:
        for day in all_day:
            # 获取当日主力期货的代码名即文件名
            if is_sec:
                fname = f'{directory}/{code}次主力连续_{day}.csv'
            else:
                fname = f'{directory}/{code}主力连续_{day}.csv'
            if not is_exist_file(fname):
                continue
            df = read_csv_file(fname, code, 'SF')
            dfs = pd.concat([dfs, df])
        # 将按年汇总的tick数据写入文件
        print(f'{cnt} {code}: {dfs.shape}')
        cnt = cnt + 1
    if dfs.empty:
        print(f'no data for {start} to {end}')
        return dfs
    dfs.sort_values(by=['datetime'], ascending=True, inplace=True)
    if is_sec:
        dfs.to_parquet(f'{data_path}/tick/{start[0:4]}/SF{start[0:6]}.sec.parquet')
    else:
        dfs.to_parquet(f'{data_path}/tick/{start[0:4]}/SF{start[0:6]}.parquet')
    return dfs


def main(args):
    print(args)
    
    data_path = args.data_path # 'f:/hqdata'
    second = args.is_sec # 是否导出次主力

    if args.type == 'fut':
        df = etl_fut_tick_data(start=args.start, end=args.end, data_path=data_path, is_sec=second)
    elif args.type == 'sf':
        df = etl_sf_tick_data(start=args.start, end=args.end, data_path=data_path, is_sec=second)
    elif args.type == 'sc':
        df = etl_fut_sc_tick_data(start=args.start, end=args.end, data_path=data_path, is_sec=second)
    else:
        raise Exception("unknown data type")
    
    print(df.shape)
    del df  

if __name__ == '__main__':
    # dt_seg=[('20230101', '20230131'), ('20230201', '20230228'), ('20230301', '20230331'),
    #         ('20230401', '20230430'), ('20230501', '20230531'), ('20230601', '20230630'),
    #         ('20230701', '20230731'), ('20230801', '20230831'), ('20230901', '20230930'),
    #         ('20231001', '20231031'), ('20231101', '20231130'), ('20231201', '20231231'),]
    # for seg in dt_seg:
    #     print(seg)
    parser = ArgumentParser()
    parser.add_argument('--start', type=str, default='20240701', help='start date')
    parser.add_argument('--end', type=str, default='20240731', help='end date')
    parser.add_argument('--type', type=str, default='sf', choices=['fut', 'sf', 'sc'], help='fut:商品期货, sf:金融期货, sc:上期所有盘口数据')
    parser.add_argument('--data_path', type=str, default='f:/hqdata', help='data path')
    parser.add_argument('--is_sec', type=bool, default=False, help='is second contract')
    args = parser.parse_args()

    main(args)



