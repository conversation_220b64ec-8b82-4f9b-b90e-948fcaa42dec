{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# from pyecharts import online\n", "import datetime\n", "import pandas as pd\n", "import sys\n", "import matplotlib as mpl\n", "import matplotlib.pyplot as plt\n", "\n", "import numpy as np\n", "sys.path.append(\"d:/QuantLab\")\n", "from qtunnel import DB,Mode,Cursor,Transaction,create_db,registered_dbs"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"tags": ["outputPrepend"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["'fss:sf:00210409103015117'\n", "b'fss:sf:00210409103016119'\n", "b'fss:sf:00210409103016121'\n", "b'fss:sf:00210409103016123'\n", "b'fss:sf:00210409103016125'\n", "b'fss:sf:00210409103017127'\n", "b'fss:sf:00210409103017129'\n", "b'fss:sf:00210409103017131'\n", "b'fss:sf:00210409103017133'\n", "b'fss:sf:00210409103018135'\n", "b'fss:sf:00210409103018137'\n", "b'fss:sf:00210409103018139'\n", "b'fss:sf:00210409103018141'\n", "b'fss:sf:00210409103019143'\n", "b'fss:sf:00210409103019145'\n", "b'fss:sf:00210409103019147'\n", "b'fss:sf:00210409103019149'\n", "b'fss:sf:00210409103020151'\n", "b'fss:sf:00210409103020153'\n", "b'fss:sf:00210409103020155'\n", "b'fss:sf:00210409103020157'\n", "b'fss:sf:00210409103021159'\n", "b'fss:sf:00210409103021161'\n", "b'fss:sf:00210409103021163'\n", "b'fss:sf:00210409103021165'\n", "b'fss:sf:00210409103022167'\n", "b'fss:sf:00210409103022169'\n", "b'fss:sf:00210409103022171'\n", "b'fss:sf:00210409103022173'\n", "b'fss:sf:00210409103023175'\n", "b'fss:sf:00210409103023177'\n", "b'fss:sf:00210409103023179'\n", "b'fss:sf:00210409103023181'\n", "b'fss:sf:00210409103024183'\n", "b'fss:sf:00210409103024185'\n", "b'fss:sf:00210409103024187'\n", "b'fss:sf:00210409103025189'\n", "b'fss:sf:00210409103025191'\n", "b'fss:sf:00210409103025193'\n", "b'fss:sf:00210409103025195'\n", "b'fss:sf:00210409103026197'\n", "b'fss:sf:00210409103026199'\n", "b'fss:sf:00210409103026201'\n", "b'fss:sf:00210409103026203'\n", "b'fss:sf:00210409103027205'\n", "b'fss:sf:00210409103027207'\n", "b'fss:sf:00210409103027209'\n", "b'fss:sf:00210409103027211'\n", "b'fss:sf:00210409103027213'\n", "b'fss:sf:00210409103028215'\n", "b'fss:sf:00210409103028217'\n", "b'fss:sf:00210409103028219'\n", "b'fss:sf:00210409103028221'\n", "b'fss:sf:00210409103029223'\n", "b'fss:sf:00210409103029225'\n", "b'fss:sf:00210409103029227'\n", "b'fss:sf:00210409103029229'\n", "b'fss:sf:00210409103030231'\n", "b'fss:sf:00210409103030233'\n", "b'fss:sf:00210409103030235'\n", "b'fss:sf:00210409103030237'\n", "b'fss:sf:00210409103031239'\n", "b'fss:sf:00210409103031241'\n", "b'fss:sf:00210409103031243'\n", "b'fss:sf:00210409103031245'\n", "b'fss:sf:00210409103032247'\n", "b'fss:sf:00210409103032249'\n", "b'fss:sf:00210409103032251'\n", "b'fss:sf:00210409103032253'\n", "b'fss:sf:00210409103032255'\n", "b'fss:sf:00210409103032257'\n", "b'fss:sf:00210409103033259'\n", "b'fss:sf:00210409103033261'\n", "b'fss:sf:00210409103033263'\n", "b'fss:sf:00210409103033265'\n", "b'fss:sf:00210409103033267'\n", "b'fss:sf:00210409103033269'\n", "b'fss:sf:00210409103034271'\n", "b'fss:sf:00210409103034273'\n", "b'fss:sf:00210409103034275'\n", "b'fss:sf:00210409103034277'\n", "b'fss:sf:00210409103034279'\n", "b'fss:sf:00210409103035281'\n", "b'fss:sf:00210409103035283'\n", "b'fss:sf:00210409103035285'\n", "b'fss:sf:00210409103035287'\n", "b'fss:sf:00210409103035289'\n", "b'fss:sf:00210409103035291'\n", "b'fss:sf:00210409103035293'\n", "b'fss:sf:00210409103036295'\n", "b'fss:sf:00210409103036297'\n", "b'fss:sf:00210409103036299'\n", "b'fss:sf:00210409103036301'\n", "b'fss:sf:00210409103036303'\n", "b'fss:sf:00210409103036305'\n", "b'fss:sf:00210409103037307'\n", "b'fss:sf:00210409103037309'\n", "b'fss:sf:00210409103037311'\n", "b'fss:sf:00210409103037313'\n", "b'fss:sf:00210409103037315'\n", "b'fss:sf:00210409103037317'\n", "b'fss:sf:00210409103038319'\n", "b'fss:sf:00210409103038321'\n", "b'fss:sf:00210409103038323'\n", "b'fss:sf:00210409103038325'\n", "b'fss:sf:00210409103038327'\n", "b'fss:sf:00210409103039329'\n", "b'fss:sf:00210409103039331'\n", "b'fss:sf:00210409103039333'\n", "b'fss:sf:00210409103039335'\n", "b'fss:sf:00210409103039337'\n", "b'fss:sf:00210409103039339'\n", "b'fss:sf:00210409103040341'\n", "b'fss:sf:00210409103040343'\n", "b'fss:sf:00210409103040345'\n", "b'fss:sf:00210409103040347'\n", "b'fss:sf:00210409103041349'\n", "b'fss:sf:00210409103041351'\n", "b'fss:sf:00210409103041353'\n", "b'fss:sf:00210409103041355'\n", "b'fss:sf:00210409103041357'\n", "b'fss:sf:00210409103041359'\n", "b'fss:sf:00210409103042361'\n", "b'fss:sf:00210409103042363'\n", "b'fss:sf:00210409103042365'\n", "b'fss:sf:00210409103042367'\n", "b'fss:sf:00210409103042369'\n", "b'fss:sf:00210409103042371'\n", "b'fss:sf:00210409103043373'\n", "b'fss:sf:00210409103043375'\n", "b'fss:sf:00210409103043377'\n", "b'fss:sf:00210409103043379'\n", "b'fss:sf:00210409103043381'\n", "b'fss:sf:00210409103043383'\n", "b'fss:sf:00210409103044385'\n", "b'fss:sf:00210409103044387'\n", "b'fss:sf:00210409103044389'\n", "b'fss:sf:00210409103044391'\n", "b'fss:sf:00210409103044393'\n", "b'fss:sf:00210409103044395'\n", "b'fss:sf:00210409103045397'\n", "b'fss:sf:00210409103045399'\n", "b'fss:sf:00210409103045401'\n", "b'fss:sf:00210409103045403'\n", "b'fss:sf:00210409103045405'\n", "b'fss:sf:00210409103046407'\n", "b'fss:sf:00210409103046409'\n", "b'fss:sf:00210409103046411'\n", "b'fss:sf:00210409103046413'\n", "b'fss:sf:00210409103046415'\n", "b'fss:sf:00210409103046417'\n", "b'fss:sf:00210409103047419'\n", "b'fss:sf:00210409103047421'\n", "b'fss:sf:00210409103047423'\n", "b'fss:sf:00210409103047425'\n", "b'fss:sf:00210409103047427'\n", "b'fss:sf:00210409103047429'\n", "b'fss:sf:00210409103048431'\n", "b'fss:sf:00210409103048433'\n", "b'fss:sf:00210409103048435'\n", "b'fss:sf:00210409103048437'\n", "b'fss:sf:00210409103048439'\n", "b'fss:sf:00210409103048441'\n", "b'fss:sf:00210409103049443'\n", "b'fss:sf:00210409103049445'\n", "b'fss:sf:00210409103049447'\n", "b'fss:sf:00210409103049449'\n", "b'fss:sf:00210409103049451'\n", "b'fss:sf:00210409103050453'\n", "b'fss:sf:00210409103050455'\n", "b'fss:sf:00210409103050457'\n", "b'fss:sf:00210409103050459'\n", "b'fss:sf:00210409103050461'\n", "b'fss:sf:00210409103050463'\n", "b'fss:sf:00210409103051465'\n", "b'fss:sf:00210409103051467'\n", "b'fss:sf:00210409103051469'\n", "b'fss:sf:00210409103051471'\n", "b'fss:sf:00210409103051473'\n", "b'fss:sf:00210409103051475'\n", "b'fss:sf:00210409103052477'\n", "b'fss:sf:00210409103052479'\n", "b'fss:sf:00210409103052481'\n", "b'fss:sf:00210409103052483'\n", "b'fss:sf:00210409103052485'\n", "b'fss:sf:00210409103053487'\n", "b'fss:sf:00210409103053489'\n", "b'fss:sf:00210409103053491'\n", "b'fss:sf:00210409103053493'\n", "b'fss:sf:00210409103053495'\n", "b'fss:sf:00210409103053497'\n", "b'fss:sf:00210409103054499'\n", "b'fss:sf:00210409103054501'\n", "b'fss:sf:00210409103054503'\n", "b'fss:sf:00210409103054505'\n", "b'fss:sf:00210409103054507'\n", "b'fss:sf:00210409103054509'\n", "b'fss:sf:00210409103055511'\n", "b'fss:sf:00210409103055513'\n", "b'fss:sf:00210409103055515'\n", "b'fss:sf:00210409103055517'\n", "b'fss:sf:00210409103055519'\n", "b'fss:sf:00210409103056521'\n", "b'fss:sf:00210409103056523'\n", "b'fss:sf:00210409103056525'\n", "b'fss:sf:00210409103056527'\n", "b'fss:sf:00210409103056529'\n", "b'fss:sf:00210409103056531'\n", "b'fss:sf:00210409103057533'\n", "b'fss:sf:00210409103057535'\n", "b'fss:sf:00210409103057537'\n", "b'fss:sf:00210409103057539'\n", "b'fss:sf:00210409103057541'\n", "b'fss:sf:00210409103057543'\n", "b'fss:sf:00210409103058545'\n", "b'fss:sf:00210409103058547'\n", "b'fss:sf:00210409103058549'\n", "b'fss:sf:00210409103058551'\n", "b'fss:sf:00210409103058553'\n", "b'fss:sf:00210409103058555'\n", "b'fss:sf:00210409103059557'\n", "b'fss:sf:00210409103059559'\n", "b'fss:sf:00210409103059561'\n", "b'fss:sf:00210409103059563'\n", "b'fss:sf:00210409103059565'\n", "b'fss:sf:00210409103059567'\n", "b'fss:sf:00210409103100569'\n", "b'fss:sf:00210409103100571'\n", "b'fss:sf:00210409103100573'\n", "b'fss:sf:00210409103100575'\n", "b'fss:sf:00210409103100577'\n", "b'fss:sf:00210409103100579'\n", "b'fss:sf:00210409103101581'\n", "b'fss:sf:00210409103101583'\n", "b'fss:sf:00210409103101585'\n", "b'fss:sf:00210409103101587'\n", "b'fss:sf:00210409103101589'\n", "b'fss:sf:00210409103101591'\n", "b'fss:sf:00210409103102593'\n", "b'fss:sf:00210409103102595'\n", "b'fss:sf:00210409103102597'\n", "b'fss:sf:00210409103102599'\n", "b'fss:sf:00210409103102601'\n", "b'fss:sf:00210409103102603'\n", "b'fss:sf:00210409103103605'\n", "b'fss:sf:00210409103103607'\n", "b'fss:sf:00210409103103609'\n", "b'fss:sf:00210409103103611'\n", "b'fss:sf:00210409103103613'\n", "b'fss:sf:00210409103103615'\n", "b'fss:sf:00210409103104617'\n", "b'fss:sf:00210409103104619'\n", "b'fss:sf:00210409103104621'\n", "b'fss:sf:00210409103104623'\n", "b'fss:sf:00210409103104625'\n", "b'fss:sf:00210409103104627'\n", "b'fss:sf:00210409103105629'\n", "b'fss:sf:00210409103105631'\n", "b'fss:sf:00210409103105633'\n", "b'fss:sf:00210409103105635'\n", "b'fss:sf:00210409103105637'\n", "b'fss:sf:00210409103105639'\n", "b'fss:sf:00210409103106641'\n", "b'fss:sf:00210409103106643'\n", "b'fss:sf:00210409103106645'\n", "b'fss:sf:00210409103106647'\n", "b'fss:sf:00210409103106649'\n", "b'fss:sf:00210409103106651'\n", "b'fss:sf:00210409103107653'\n", "b'fss:sf:00210409103107655'\n", "b'fss:sf:00210409103107657'\n", "b'fss:sf:00210409103107659'\n", "b'fss:sf:00210409103107661'\n", "b'fss:sf:00210409103107663'\n", "b'fss:sf:00210409103108665'\n", "b'fss:sf:00210409103108667'\n", "b'fss:sf:00210409103108669'\n", "b'fss:sf:00210409103108671'\n", "b'fss:sf:00210409103108673'\n", "b'fss:sf:00210409103108675'\n", "b'fss:sf:00210409103109677'\n", "b'fss:sf:00210409103109679'\n", "b'fss:sf:00210409103109681'\n", "b'fss:sf:00210409103109683'\n", "b'fss:sf:00210409103109685'\n", "b'fss:sf:00210409103109687'\n", "b'fss:sf:00210409103110689'\n", "b'fss:sf:00210409103110691'\n", "b'fss:sf:00210409103110693'\n", "b'fss:sf:00210409103110695'\n", "b'fss:sf:00210409103110697'\n", "b'fss:sf:00210409103110699'\n", "b'fss:sf:00210409103111701'\n", "b'fss:sf:00210409103111703'\n", "b'fss:sf:00210409103111705'\n", "b'fss:sf:00210409103111707'\n", "b'fss:sf:00210409103111709'\n", "b'fss:sf:00210409103111711'\n", "b'fss:sf:00210409103112713'\n", "b'fss:sf:00210409103112715'\n", "b'fss:sf:00210409103112717'\n", "b'fss:sf:00210409103112719'\n", "b'fss:sf:00210409103112721'\n", "b'fss:sf:00210409103112723'\n", "b'fss:sf:00210409103113725'\n", "b'fss:sf:00210409103113727'\n", "b'fss:sf:00210409103113729'\n", "b'fss:sf:00210409103113731'\n", "b'fss:sf:00210409103113733'\n", "b'fss:sf:00210409103113735'\n", "b'fss:sf:00210409103114737'\n", "b'fss:sf:00210409103114739'\n", "b'fss:sf:00210409103114741'\n", "b'fss:sf:00210409103114743'\n", "b'fss:sf:00210409103114745'\n", "b'fss:sf:00210409103114747'\n", "b'fss:sf:00210409103115749'\n", "b'fss:sf:00210409103115751'\n", "b'fss:sf:00210409103115753'\n", "b'fss:sf:00210409103115755'\n", "b'fss:sf:00210409103115757'\n", "b'fss:sf:00210409103115759'\n", "b'fss:sf:00210409103116761'\n", "b'fss:sf:00210409103116763'\n", "b'fss:sf:00210409103116765'\n", "b'fss:sf:00210409103116767'\n", "b'fss:sf:00210409103116769'\n", "b'fss:sf:00210409103116771'\n", "b'fss:sf:00210409103117773'\n", "b'fss:sf:00210409103117775'\n", "b'fss:sf:00210409103117777'\n", "b'fss:sf:00210409103117779'\n", "b'fss:sf:00210409103117781'\n", "b'fss:sf:00210409103117783'\n", "b'fss:sf:00210409103118785'\n", "b'fss:sf:00210409103118787'\n", "b'fss:sf:00210409103118789'\n", "b'fss:sf:00210409103118791'\n", "b'fss:sf:00210409103118793'\n", "b'fss:sf:00210409103118795'\n", "b'fss:sf:00210409103119797'\n", "b'fss:sf:00210409103119799'\n", "b'fss:sf:00210409103119801'\n", "b'fss:sf:00210409103119803'\n", "b'fss:sf:00210409103119805'\n", "b'fss:sf:00210409103119807'\n", "b'fss:sf:00210409103120809'\n", "b'fss:sf:00210409103120811'\n", "b'fss:sf:00210409103120813'\n", "b'fss:sf:00210409103120815'\n", "b'fss:sf:00210409103120817'\n", "b'fss:sf:00210409103120819'\n", "b'fss:sf:00210409103121821'\n", "b'fss:sf:00210409103121823'\n", "b'fss:sf:00210409103121825'\n", "b'fss:sf:00210409103121827'\n", "b'fss:sf:00210409103122829'\n", "b'fss:sf:00210409103122831'\n", "b'fss:sf:00210409103122833'\n", "b'fss:sf:00210409103122835'\n", "b'fss:sf:00210409103123837'\n", "b'fss:sf:00210409103123839'\n", "b'fss:sf:00210409103123841'\n", "b'fss:sf:00210409103123843'\n", "b'fss:sf:00210409103124845'\n", "b'fss:sf:00210409103124847'\n", "b'fss:sf:00210409103124849'\n", "b'fss:sf:00210409103124851'\n", "b'fss:sf:00210409103125853'\n", "b'fss:sf:00210409103125855'\n", "b'fss:sf:00210409103125857'\n", "b'fss:sf:00210409103125859'\n", "b'fss:sf:00210409103126861'\n", "b'fss:sf:00210409103126863'\n", "b'fss:sf:00210409103126865'\n", "b'fss:sf:00210409103126867'\n", "b'fss:sf:00210409103127869'\n", "b'fss:sf:00210409103127871'\n", "b'fss:sf:00210409103127873'\n", "b'fss:sf:00210409103127875'\n", "b'fss:sf:00210409103128877'\n", "b'fss:sf:00210409103128879'\n", "b'fss:sf:00210409103128881'\n", "b'fss:sf:00210409103128883'\n", "b'fss:sf:00210409103129885'\n", "b'fss:sf:00210409103129887'\n", "b'fss:sf:00210409103129889'\n", "b'fss:sf:00210409103129891'\n", "b'fss:sf:00210409103130893'\n", "b'fss:sf:00210409103130895'\n", "b'fss:sf:00210409103130897'\n", "b'fss:sf:00210409103130899'\n", "b'fss:sf:00210409103131901'\n", "b'fss:sf:00210409103131903'\n", "b'fss:sf:00210409103131905'\n", "b'fss:sf:00210409103131907'\n", "b'fss:sf:00210409103132909'\n", "b'fss:sf:00210409103132911'\n", "b'fss:sf:00210409103132913'\n", "b'fss:sf:00210409103133915'\n", "b'fss:sf:00210409103133917'\n", "b'fss:sf:00210409103133919'\n", "b'fss:sf:00210409103133921'\n", "b'fss:sf:00210409103133923'\n", "b'fss:sf:00210409103134925'\n", "b'fss:sf:00210409103134927'\n", "b'fss:sf:00210409103134929'\n", "b'fss:sf:00210409103134931'\n", "b'fss:sf:00210409103135933'\n", "b'fss:sf:00210409103135935'\n", "b'fss:sf:00210409103135937'\n", "b'fss:sf:00210409103135939'\n", "b'fss:sf:00210409103136941'\n", "b'fss:sf:00210409103136943'\n", "b'fss:sf:00210409103136945'\n", "b'fss:sf:00210409103136947'\n", "b'fss:sf:00210409103137949'\n", "b'fss:sf:00210409103137951'\n", "b'fss:sf:00210409103137953'\n", "b'fss:sf:00210409103137955'\n", "b'fss:sf:00210409103138957'\n", "b'fss:sf:00210409103138959'\n", "b'fss:sf:00210409103138961'\n", "b'fss:sf:00210409103138963'\n", "b'fss:sf:00210409103139965'\n", "b'fss:sf:00210409103139967'\n", "b'fss:sf:00210409103139969'\n", "b'fss:sf:00210409103139971'\n", "b'fss:sf:00210409103140973'\n", "b'fss:sf:00210409103140975'\n", "b'fss:sf:00210409103140977'\n", "b'fss:sf:00210409103140979'\n", "b'fss:sf:00210409103140981'\n", "b'fss:sf:00210409103141983'\n", "b'fss:sf:00210409103141985'\n", "b'fss:sf:00210409103141987'\n", "b'fss:sf:00210409103141989'\n", "b'fss:sf:00210409103141991'\n", "b'fss:sf:00210409103141993'\n", "b'fss:sf:00210409103142001'\n", "b'fss:sf:00210409103142003'\n", "b'fss:sf:00210409103142005'\n", "b'fss:sf:00210409103142995'\n", "b'fss:sf:00210409103142997'\n", "b'fss:sf:00210409103142999'\n", "b'fss:sf:00210409103143007'\n", "b'fss:sf:00210409103143009'\n", "b'fss:sf:00210409103143011'\n", "b'fss:sf:00210409103143013'\n", "b'fss:sf:00210409103143015'\n", "b'fss:sf:00210409103143017'\n", "b'fss:sf:00210409103144019'\n", "b'fss:sf:00210409103144021'\n", "b'fss:sf:00210409103144023'\n", "b'fss:sf:00210409103144025'\n", "b'fss:sf:00210409103144027'\n", "b'fss:sf:00210409103144029'\n", "b'fss:sf:00210409103145031'\n", "b'fss:sf:00210409103145033'\n", "b'fss:sf:00210409103145035'\n", "b'fss:sf:00210409103145037'\n", "b'fss:sf:00210409103145039'\n", "b'fss:sf:00210409103145041'\n", "b'fss:sf:00210409103146043'\n", "b'fss:sf:00210409103146045'\n", "b'fss:sf:00210409103146047'\n", "b'fss:sf:00210409103146049'\n", "b'fss:sf:00210409103146051'\n", "b'fss:sf:00210409103146053'\n", "b'fss:sf:00210409103147055'\n", "b'fss:sf:00210409103147057'\n", "b'fss:sf:00210409103147059'\n", "b'fss:sf:00210409103147061'\n", "b'fss:sf:00210409103147063'\n", "b'fss:sf:00210409103147065'\n", "b'fss:sf:00210409103148067'\n", "b'fss:sf:00210409103148069'\n", "b'fss:sf:00210409103148071'\n", "b'fss:sf:00210409103148073'\n", "b'fss:sf:00210409103148075'\n", "b'fss:sf:00210409103148077'\n", "b'fss:sf:00210409103149079'\n", "b'fss:sf:00210409103149081'\n", "b'fss:sf:00210409103149083'\n", "b'fss:sf:00210409103149085'\n", "b'fss:sf:00210409103149087'\n", "b'fss:sf:00210409103149089'\n", "b'fss:sf:00210409103150091'\n", "b'fss:sf:00210409103150093'\n", "b'fss:sf:00210409103150095'\n", "b'fss:sf:00210409103150097'\n", "b'fss:sf:00210409103150099'\n", "b'fss:sf:00210409103150101'\n", "b'fss:sf:00210409103151103'\n", "b'fss:sf:00210409103151105'\n", "b'fss:sf:00210409103151107'\n", "b'fss:sf:00210409103151109'\n", "b'fss:sf:00210409103151111'\n", "b'fss:sf:00210409103151113'\n", "b'fss:sf:00210409103152115'\n", "b'fss:sf:00210409103152117'\n", "b'fss:sf:00210409103152119'\n", "b'fss:sf:00210409103152121'\n", "b'fss:sf:00210409103152123'\n", "b'fss:sf:00210409103152125'\n", "b'fss:sf:00210409103153127'\n", "b'fss:sf:00210409103153129'\n", "b'fss:sf:00210409103153131'\n", "b'fss:sf:00210409103153133'\n", "b'fss:sf:00210409103153135'\n", "b'fss:sf:00210409103153137'\n", "b'fss:sf:00210409103154139'\n", "b'fss:sf:00210409103154141'\n", "b'fss:sf:00210409103154143'\n", "b'fss:sf:00210409103154145'\n", "b'fss:sf:00210409103154147'\n", "b'fss:sf:00210409103155149'\n", "b'fss:sf:00210409103155151'\n", "b'fss:sf:00210409103155153'\n", "b'fss:sf:00210409103155155'\n", "b'fss:sf:00210409103155157'\n", "b'fss:sf:00210409103155159'\n", "b'fss:sf:00210409103156161'\n", "b'fss:sf:00210409103156163'\n", "b'fss:sf:00210409103156165'\n", "b'fss:sf:00210409103156167'\n", "b'fss:sf:00210409103156169'\n", "b'fss:sf:00210409103157171'\n", "b'fss:sf:00210409103157173'\n", "b'fss:sf:00210409103157175'\n", "b'fss:sf:00210409103157177'\n", "b'fss:sf:00210409103157179'\n", "b'fss:sf:00210409103157181'\n", "b'fss:sf:00210409103158183'\n", "b'fss:sf:00210409103158185'\n", "b'fss:sf:00210409103158187'\n", "b'fss:sf:00210409103158189'\n", "b'fss:sf:00210409103158191'\n", "b'fss:sf:00210409103158193'\n", "b'fss:sf:00210409103159195'\n", "b'fss:sf:00210409103159197'\n", "b'fss:sf:00210409103159199'\n", "b'fss:sf:00210409103159201'\n", "b'fss:sf:00210409103159203'\n", "b'fss:sf:00210409103159205'\n", "b'fss:sf:00210409103200207'\n", "b'fss:sf:00210409103200209'\n", "b'fss:sf:00210409103200211'\n", "b'fss:sf:00210409103312001'\n", "b'fss:sf:00210409103316003'\n", "b'fss:sf:00210409103420005'\n", "b'fss:sf:00210409103422007'\n", "b'fss:sf:00210409103430009'\n", "b'fss:sf:00210409103527011'\n", "b'fss:sf:00210409103620013'\n", "b'fss:sf:00210409103930015'\n", "b'fss:sf:00210409103930017'\n", "b'fss:sf:00210409104249019'\n", "b'fss:sf:00210409104257021'\n", "b'fss:sf:00210409104427023'\n", "b'fss:sf:00210409104430025'\n", "b'fss:sf:00210409104456027'\n", "b'fss:sf:00210409104506029'\n", "b'fss:sf:00210409104550031'\n", "b'fss:sf:00210409104557033'\n", "b'fss:sf:00210409105012035'\n", "b'fss:sf:00210409105013037'\n", "b'fss:sf:00210409105015039'\n", "b'fss:sf:00210409105416041'\n", "b'fss:sf:00210409105545043'\n", "b'fss:sf:00210409105710045'\n", "b'fss:sf:00210409110002047'\n", "b'fss:sf:00210409110535049'\n", "b'fss:sf:00210409111142051'\n", "b'fss:sf:00210409111948053'\n", "b'fss:sf:00210409112756055'\n", "b'fss:sf:00210409112928057'\n", "b'fss:sf:00210409112949059'\n", "b'fss:sf:00210409133215065'\n", "b'fss:sf:00210409134053071'\n", "b'fss:sf:00210409134053073'\n", "b'fss:sf:00210409141904079'\n", "b'fss:sf:00210409141914081'\n", "b'fss:sf:00210409142637083'\n", "b'fss:sf:00210409142930085'\n", "b'fss:sf:00210409142933087'\n", "b'fss:sf:00210409143046091'\n", "b'fss:sf:00210409143439093'\n", "b'fss:sf:00210409144618095'\n", "b'fss:sf:00210409144839097'\n", "b'fss:sf:00210409144949099'\n", "b'fss:sf:00210409145211101'\n", "b'fss:sf:00210409210000001'\n", "b'fss:sf:00210409210001003'\n", "b'fss:sf:00210409210835005'\n", "b'fss:sf:00210409210846007'\n", "b'fss:sf:00210409211325001'\n", "b'fss:sf:00210409211851003'\n", "b'fss:sf:00210409212117005'\n", "b'fss:sf:00210409212533007'\n", "b'fss:sf:00210409212632009'\n", "b'fss:sf:00210409212739011'\n", "b'fss:sf:00210409213232013'\n", "b'fss:sf:00210409214436015'\n", "b'fss:sf:00210409214508017'\n", "b'fss:sf:00210409214510019'\n", "b'fss:sf:00210409215757021'\n", "b'fss:sf:00210409221845023'\n", "b'fss:sf:00210409222232025'\n", "b'fss:sf:00210409222828027'\n", "b'fss:sf:00210409222830029'\n", "b'fss:sf:00210409223525031'\n", "b'fss:sf:00210409224532033'\n", "b'fss:sf:00210409225739035'\n", "b'fss:sf:00210409225922037'\n", "b'fss:sf:00210409231130039'\n", "b'fss:sf:00210412090002001'\n", "b'fss:sf:00210412090002003'\n", "b'fss:sf:00210412090100005'\n", "b'fss:sf:00210412090101007'\n", "b'fss:sf:00210412090106009'\n", "b'fss:sf:00210412090223011'\n", "b'fss:sf:00210412090246013'\n", "b'fss:sf:00210412090405015'\n", "b'fss:sf:00210412090406017'\n", "b'fss:sf:00210412090502019'\n", "b'fss:sf:00210412090815021'\n", "b'fss:sf:00210412090816023'\n", "b'fss:sf:00210412090846025'\n", "b'fss:sf:00210412090938027'\n", "b'fss:sf:00210412091148029'\n", "b'fss:sf:00210412091411031'\n", "b'fss:sf:00210412091412033'\n", "b'fss:sf:00210412091640035'\n", "b'fss:sf:00210412091758037'\n", "b'fss:sf:00210412092023039'\n", "b'fss:sf:00210412092250041'\n", "b'fss:sf:00210412092308043'\n", "b'fss:sf:00210412092828045'\n", "b'fss:sf:00210412092828047'\n", "b'fss:sf:00210412093451055'\n", "b'fss:sf:00210412093451057'\n", "b'fss:sf:00210412093451059'\n", "b'fss:sf:00210412093607061'\n", "b'fss:sf:00210412093608063'\n", "b'fss:sf:00210412094409069'\n", "b'fss:sf:00210412094411071'\n", "b'fss:sf:00210412094436073'\n", "b'fss:sf:00210412095254077'\n", "b'fss:sf:00210412100003079'\n", "b'fss:sf:00210412100003081'\n", "b'fss:sf:00210412103002089'\n", "b'fss:sf:00210412103218091'\n", "b'fss:sf:00210412103219093'\n", "b'fss:sf:00210412103423095'\n", "b'fss:sf:00210412103423097'\n", "b'fss:sf:00210412103719099'\n", "b'fss:sf:00210412103719101'\n", "b'fss:sf:00210412104311103'\n", "b'fss:sf:00210412104311105'\n", "b'fss:sf:00210412104850107'\n", "b'fss:sf:00210412105236109'\n", "b'fss:sf:00210412105238111'\n", "b'fss:sf:00210412105347113'\n", "b'fss:sf:00210412105347115'\n", "b'fss:sf:00210412105636117'\n", "b'fss:sf:00210412105717119'\n", "b'fss:sf:00210412105743121'\n", "b'fss:sf:00210412105744123'\n", "b'fss:sf:00210412105847125'\n", "b'fss:sf:00210412105847127'\n", "b'fss:sf:00210412110001129'\n", "b'fut_subjective_trend'\n", "b'lf_00210331211748005'\n", "b'lf_00210331211835007'\n", "b'lf_00210331221000013'\n", "b'lf_00210331222208015'\n", "b'lf_00210331223332017'\n", "b'lf_00210331225728021'\n", "b'lf_00210401090004003'\n", "b'lf_00210401090012007'\n", "b'lf_00210401095555025'\n", "b'performance_00170516142406000'\n", "b'performance_00170516142419001'\n", "b'performance_00170516142453003'\n", "b'performance_00170607084458001'\n", "b'performance_00170607084524002'\n", "b'performance_00170607085233003'\n", "b'performance_00171106132928000'\n", "b'performance_00171113134127000'\n", "b'performance_00171122123535000'\n", "b'performance_00180625092853000'\n", "b'performance_00200910081110000'\n", "b'performance_00200910081133001'\n", "b'performance_00210226114642000'\n", "b'settlementinfoconfirm:\\xc4\\xcf\\xbb\\xaa\\xc6\\xda\\xbb\\xf5\\xca\\xb5\\xc5\\xccCTP\\xbd\\xbb\\xd2\\xd7-SE'\n", "b'settlementinfoconfirm:\\xd6\\xd0\\xcd\\xb6\\xcc\\xec\\xe7\\xf7\\xca\\xb5\\xc5\\xccCTP\\xbd\\xbb\\xd2\\xd7-LWJ'\n", "b'settlementinfoconfirm:\\xd6\\xd0\\xd0\\xc5\\xbd\\xa8\\xcd\\xb6\\xca\\xb5\\xc5\\xccCTP\\xbd\\xbb\\xd2\\xd7-SE'\n", "b'sf_00210331211748005'\n", "b'sf_00210331211835007'\n", "b'sf_00210331221000013'\n", "b'sf_00210331222208015'\n", "b'sf_00210331223332017'\n", "b'sf_00210331225728021'\n", "b'sf_00210401090004003'\n", "b'sf_00210401090012007'\n", "b'sf_00210401095555025'\n", "b'ui_lock'\n"]}], "source": ["def open_db(file_name, mode):\n", "    db=create_db(\"leveldb\", file_name, mode)\n", "    return db\n", "\n", "def write_db(db, key, value):\n", "    transaction = db.new_transaction()\n", "    transaction.put(key, value)\n", "    del transaction\n", "    \n", "def delete_db(db, key):\n", "    transaction = db.new_transaction()\n", "    transaction.delete(key)\n", "    del transaction\n", "    \n", "def query_db(db):\n", "    cursor = db.new_cursor()\n", "    while cursor.valid():\n", "        print(cursor.key())\n", "        # print(cursor.key(), cursor.value())\n", "        cursor.next()\n", "    del cursor\n", "    \n", "def test_1():\n", "    db = open_db(\"d:/QuantLab/store/historydata.db\", Mode.read)\n", "    query_db(db)\n", "    db.close()\n", "    del db\n", "\n", "def test_2():\n", "    db = open_db(\"d:/QuantLab/store/kv.db\", Mode.write)\n", "    write_db(db, \"welcome\", \"very good! very good!\")\n", "    #delete_db(db, \"welcome\")\n", "    query_db(db)\n", "    db.close()\n", "    del db\n", "\n", "def test_3():\n", "    db = open_db(\"d:/Quant/store/kv.db\", Mode.read)\n", "    query_db(db)\n", "    db.close()\n", "    del db\n", "test_3()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.6"}}, "nbformat": 4, "nbformat_minor": 2}