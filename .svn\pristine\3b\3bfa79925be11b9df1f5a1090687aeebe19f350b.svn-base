{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": true}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pylab as plt\n", "import datetime\n", "\n", "from sklearn.preprocessing import LabelBinarizer\n", "from sklearn.utils import shuffle\n", "from collections import Counter"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/anaconda2/lib/python2.7/site-packages/h5py/__init__.py:34: FutureWarning: Conversion of the second argument of issubdtype from `float` to `np.floating` is deprecated. In future, it will be treated as `np.float64 == np.dtype(float).type`.\n", "  from ._conv import register_converters as _register_converters\n", "Using TensorFlow backend.\n"]}], "source": ["from keras.models import Model\n", "from keras.layers import Dense, Dropout, GlobalAveragePooling1D, GaussianNoise, Conv1D, MaxPool1D, GlobalMaxPooling1D\n", "from keras.layers import Input, concatenate, SpatialDropout1D, Flatten, AvgPool1D, LSTM, Lambda, Reshape, GaussianDropout\n", "from keras.layers import BatchNormalization\n", "from keras.callbacks import ModelCheckpoint, EarlyStopping\n", "from keras import regularizers\n", "from keras.optimizers import RMSprop, Adam, SGD, Nadam\n", "import keras.backend as K\n", "from keras.regularizers import l1, l2 , l1_l2\n", "\n", "\n", "from sklearn.metrics import confusion_matrix\n", "from sklearn.metrics import classification_report, accuracy_score\n", "from sklearn.metrics import r2_score\n", "from sklearn.metrics import matthews_corrcoef\n", "from sklearn.metrics import f1_score"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data loading"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": true}, "outputs": [], "source": ["tick_bars = pd.read_csv('tick_bars.csv')"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAXoAAAD8CAYAAAB5Pm/hAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\niFRySvQiIpWcEr2ISCX3/6RNaVnziObMAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure()\n", "plt.plot(tick_bars.close[:int(len(tick_bars) * 0.5)])\n", "plt.plot(tick_bars.close[int(len(tick_bars) * 0.5):int(len(tick_bars) * 0.7)])\n", "plt.plot(tick_bars.close[int(len(tick_bars) * 0.7):])\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Differentiation test"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/anaconda2/lib/python2.7/site-packages/statsmodels/compat/pandas.py:56: FutureWarning: The pandas.core.datetools module is deprecated and will be removed in a future version. Please use the pandas.tseries module instead.\n", "  from pandas.core import datetools\n"]}], "source": ["from statsmodels.tsa.stattools import adfuller"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"collapsed": true}, "outputs": [], "source": ["# https://github.com/philipperemy/fractional-differentiation-time-series/blob/master/fracdiff/fracdiff.py\n", "\n", "def fast_fracdiff(x, d):\n", "    import pylab as pl\n", "    T = len(x)\n", "    np2 = int(2 ** np.ceil(np.log2(2 * T - 1)))\n", "    k = np.arange(1, T)\n", "    b = (1,) + tuple(np.cumprod((k - d - 1) / k))\n", "    z = (0,) * (np2 - T)\n", "    z1 = b + z\n", "    z2 = tuple(x) + z\n", "    dx = pl.ifft(pl.fft(z1) * pl.fft(z2))\n", "    return np.real(dx[0:T])"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.0\n", "0.5\n", "1.0\n"]}], "source": ["corrs, adfs = [], []\n", "\n", "for d in np.arange(0.0, 1.5, 0.5):\n", "    \n", "    print d\n", "    \n", "    ts1 = np.log(tick_bars.close.values)\n", "    ts2 = fast_fracdiff(ts1, d)\n", "\n", "    corr = np.corrcoef(ts1, ts2)[0,1]\n", "    adf = adfuller(ts2)[0]\n", "\n", "    corrs.append(corr)\n", "    adfs.append(adf)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"collapsed": true}, "outputs": [], "source": ["stats = pd.DataFrame({\n", "    'd': np.arange(0.0, 1.5, 0.5),\n", "    'corr': np.abs(corrs),\n", "    'adfs': adfs\n", "})\n", "stats.index = stats['d']"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAZAAAAEKCAYAAAA8QgPpAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nA0QppVS+/D8XrjShWERchQAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["stats[['corr', 'adfs']].plot(secondary_y='adfs')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Making features"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"collapsed": true}, "outputs": [], "source": ["from scipy.stats import entropy\n", "from scipy import stats"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"collapsed": true}, "outputs": [], "source": ["N_BARS = 64000\n", "WINDOW_LONG = 100\n", "WINDOW_SHORT = 50\n", "HORIZON = 25\n", "T = 0.01\n", "H = 0.05"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-0.04359708998044693\n", "(30474097.664683025, 0.0)\n", "(0.727247953414917, 0.0)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/anaconda2/lib/python2.7/site-packages/scipy/stats/morestats.py:1310: UserWarning: p-value may not be accurate for N > 5000.\n", "  warnings.warn(\"p-value may not be accurate for N > 5000.\")\n"]}], "source": ["print pd.Series.autocorr(np.clip(tick_bars.close.pct_change(), -H, H).dropna())\n", "print stats.jarque_bera(np.clip(tick_bars.close.pct_change(), -H, H).dropna())\n", "print stats.shapiro(np.clip(tick_bars.close.pct_change(), -H, H).dropna())"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"collapsed": true, "scrolled": true}, "outputs": [], "source": ["tick_bars['pct_change'] = np.clip(tick_bars.close.pct_change(), -H, H)\n", "tick_bars['pct_change_frac'] = np.clip(fast_fracdiff(np.log(tick_bars.close.values), d=0.5), 0, 0.1)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['pct_change_frac']\n"]}], "source": ["FEATURES = ['pct_change_frac']\n", "print FEATURES"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"collapsed": true}, "outputs": [], "source": ["def make_features_from_window(X_train_b, X_val, X_test, features):   \n", "    \n", "    X_train_normts = np.array([(x.close - x.close.iloc[0]) / np.std(x.close) for x in X_train_b]).reshape((len(X_train_b), WINDOW_LONG, 1))\n", "    X_val_normts = np.array([(x.close - x.close.iloc[0]) / np.std(x.close) for x in X_val]).reshape((len(X_val), WINDOW_LONG, 1))\n", "    X_test_normts = np.array([(x.close - x.close.iloc[0]) / np.std(x.close) for x in X_test]).reshape((len(X_test), WINDOW_LONG, 1))\n", "    \n", "    X_train_normv = np.array([(x.volume - x.volume.iloc[0]) / np.std(x.volume) for x in X_train_b]).reshape((len(X_train_b), WINDOW_LONG, 1))\n", "    X_val_normvv = np.array([(x.volume - x.volume.iloc[0]) / np.std(x.volume) for x in X_val]).reshape((len(X_val), WINDOW_LONG, 1))\n", "    X_test_normv = np.array([(x.volume - x.volume.iloc[0]) / np.std(x.volume) for x in X_test]).reshape((len(X_test), WINDOW_LONG, 1))\n", "  \n", "    X_train = np.array([x[features].fillna(0.).values.tolist() for x in X_train_b]) \n", "    X_val = np.array([x[features].fillna(0.).values.tolist() for x in X_val])\n", "    X_test = np.array([x[features].fillna(0.).values.tolist() for x in X_test])\n", "\n", "    X_train = np.concatenate((X_train, X_train_normts, X_train_normv), axis = -1)\n", "    X_val = np.concatenate((X_val, X_val_normts, X_val_normvv), axis = -1)\n", "    X_test = np.concatenate((X_test, X_test_normts, X_test_normv), axis = -1)\n", "    \n", "    return X_train, X_val, X_test"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"collapsed": true}, "outputs": [], "source": ["def get_class_weights(y):\n", "    y = [np.argmax(x) for x in y]\n", "    counter = Counter(y)\n", "    majority = max(counter.values())\n", "    return  {cls: round(float(majority)/float(count), 2) for cls, count in counter.items()}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Fixed horizon"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"collapsed": true, "scrolled": true}, "outputs": [], "source": ["X, labels = [], []\n", "for i in range(WINDOW_LONG, N_BARS, 1):\n", "    \n", "    window = tick_bars.iloc[i-WINDOW_LONG:i]\n", "#     window = tick_bars.iloc[i]\n", "    now = tick_bars.close[i]\n", "    future = tick_bars.close[i+HORIZON]\n", "    ret = (future - now) / now\n", "    \n", "    X.append(window)\n", "    if ret > T:\n", "        labels.append(1)\n", "    elif ret < -T:\n", "        labels.append(-1)\n", "    else:\n", "        labels.append(0)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYcAAAD8CAYAAACcjGjIAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n1DEcJEkdw0GS1DEcJEmd/w8bM79CUvwezgAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure()\n", "plt.hist(labels, bins = 5, alpha = 0.7)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"collapsed": true}, "outputs": [], "source": ["X_train, X_val, X_test = X[:int(len(X) * 0.5)], X[int(len(X) * 0.6):int(len(X) * 0.7)], X[int(len(X) * 0.8):]\n", "Y_train, Y_val, Y_test = labels[:int(len(X) * 0.5)], labels[int(len(X) * 0.6):int(len(X) * 0.7)], labels[int(len(X) * 0.8):]\n", "\n", "lbr = LabelBinarizer()\n", "Y_train = lbr.fit_transform(Y_train)\n", "Y_val = lbr.transform(Y_val)\n", "Y_test = lbr.transform(Y_test)\n", "\n", "X_train, X_val, X_test = make_features_from_window(X_train, X_val, X_test, FEATURES)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["_________________________________________________________________\n", "Layer (type)                 Output Shape              Param #   \n", "=================================================================\n", "main_input (InputLayer)      (None, 100, 3)            0         \n", "_________________________________________________________________\n", "flatten_1 (Flatten)          (None, 300)               0         \n", "_________________________________________________________________\n", "dropout_1 (Dropout)          (None, 300)               0         \n", "_________________________________________________________________\n", "dense_1 (<PERSON><PERSON>)              (None, 3)                 903       \n", "=================================================================\n", "Total params: 903\n", "Trainable params: 903\n", "Non-trainable params: 0\n", "_________________________________________________________________\n", "Train on 31950 samples, validate on 6390 samples\n", "Epoch 1/100\n", "31950/31950 [==============================] - 5s 158us/step - loss: 4.2498 - acc: 0.3177 - val_loss: 1.3153 - val_acc: 0.2951\n", "Epoch 2/100\n", "31950/31950 [==============================] - 5s 166us/step - loss: 4.1291 - acc: 0.3158 - val_loss: 1.3417 - val_acc: 0.3374\n", "Epoch 3/100\n", "31950/31950 [==============================] - 5s 157us/step - loss: 4.2157 - acc: 0.3238 - val_loss: 1.6448 - val_acc: 0.2894\n", "Epoch 4/100\n", "31950/31950 [==============================] - 6s 173us/step - loss: 4.2682 - acc: 0.3231 - val_loss: 2.0274 - val_acc: 0.2438\n", "Epoch 5/100\n", "31950/31950 [==============================] - 8s 248us/step - loss: 4.1309 - acc: 0.3288 - val_loss: 1.3703 - val_acc: 0.4897\n", "Epoch 6/100\n", "31950/31950 [==============================] - 5s 169us/step - loss: 4.1875 - acc: 0.3230 - val_loss: 1.3931 - val_acc: 0.2587\n"]}], "source": ["def cnn(shape):\n", "    main_input = Input(shape=shape, name='main_input')\n", "    x = Flatten()(main_input)\n", "    x = Dropout(0.25)(x)\n", "    output = Dense(3, activation = \"softmax\")(x)\n", "    \n", "    final_model = Model(inputs=[main_input], outputs=[output])\n", "    return final_model\n", "\n", "model = cnn((WINDOW_LONG, len(X_train[0][0]), ))\n", "model.summary()\n", "\n", "model.compile(optimizer=Adam(lr = 0.01),  \n", "                        loss=['categorical_crossentropy'], \n", "                        metrics = ['accuracy'])\n", "\n", "checkpointer = ModelCheckpoint(filepath=\"test.hdf5\", verbose=0, save_best_only=True)\n", "es = EarlyStopping(monitor='val_loss', patience=5)\n", "\n", "history = model.fit(X_train, Y_train, \n", "              epochs = 100, \n", "              batch_size = 16, \n", "              verbose=True, \n", "              validation_data = (X_val, Y_val),\n", "              callbacks=[checkpointer, es],\n", "              shuffle=True, \n", "              class_weight = get_class_weights(np.concatenate((Y_train, Y_val)))\n", "                )\n", "\n", "model.load_weights(\"test.hdf5\")"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["              precision    recall  f1-score   support\n", "\n", "           0       0.02      0.30      0.04       211\n", "           1       0.92      0.30      0.45      5934\n", "           2       0.04      0.26      0.06       245\n", "\n", "   micro avg       0.30      0.30      0.30      6390\n", "   macro avg       0.33      0.28      0.19      6390\n", "weighted avg       0.86      0.30      0.42      6390\n", "\n", "[[  63   68   80]\n", " [**************]\n", " [ 101   81   63]]\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYAAAAD8CAYAAAB+UHOxAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nSKQUACIikVIAiIhESgEgIhKp/wdj1v9vhmNSMAAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["              precision    recall  f1-score   support\n", "\n", "           0       0.07      0.38      0.11       899\n", "           1       0.89      0.29      0.44     11116\n", "           2       0.07      0.36      0.12       765\n", "\n", "   micro avg       0.30      0.30      0.30     12780\n", "   macro avg       0.34      0.34      0.22     12780\n", "weighted avg       0.78      0.30      0.40     12780\n", "\n", "[[ 346  189  364]\n", " [**************]\n", " [ 286  207  272]]\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYcAAAD8CAYAAACcjGjIAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["pred = model.predict(X_val)\n", "\n", "print classification_report([np.argmax(y) for y in Y_val],\n", "                            [np.argmax(y) for y in pred])\n", "\n", "print confusion_matrix([np.argmax(y) for y in Y_val],\n", "                            [np.argmax(y) for y in pred])\n", "\n", "plt.plot()\n", "plt.hist([np.argmax(y) for y in Y_val], bins = 5, alpha = 0.5, label = 'Test data')\n", "plt.hist([np.argmax(y) for y in pred], bins = 5, alpha = 0.5, label = 'Predicted data')\n", "plt.legend()\n", "plt.show()\n", "\n", "\n", "pred = model.predict(X_test)\n", "\n", "print classification_report([np.argmax(y) for y in Y_test],\n", "                            [np.argmax(y) for y in pred])\n", "\n", "print confusion_matrix([np.argmax(y) for y in Y_test],\n", "                            [np.argmax(y) for y in pred])\n", "\n", "plt.plot()\n", "plt.hist([np.argmax(y) for y in Y_test], bins = 5, alpha = 0.5, label = 'Test data')\n", "plt.hist([np.argmax(y) for y in pred], bins = 5, alpha = 0.5, label = 'Predicted data')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0.22662833, 0.42735487, 0.3460168 ],\n", "       [0.02252634, 0.31241223, 0.6650614 ],\n", "       [0.31027994, 0.28352553, 0.40619454],\n", "       ...,\n", "       [0.3319831 , 0.39908785, 0.26892897],\n", "       [0.4245464 , 0.23618537, 0.3392682 ],\n", "       [0.5815889 , 0.18120623, 0.2372049 ]], dtype=float32)"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["pred"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Volatility horizon"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"collapsed": true, "scrolled": true}, "outputs": [], "source": ["X, labels = [], []\n", "for i in range(WINDOW_LONG, N_BARS, 1):\n", "    window = tick_bars.iloc[i-WINDOW_LONG:i]\n", "    now = tick_bars.close[i]\n", "    future = tick_bars.close[i+HORIZON]\n", "    ret = (future - now) / now\n", "    \n", "    window_abs_returns = np.abs(window.close.pct_change())\n", "    Ti = np.std(window_abs_returns) + np.mean(window_abs_returns)\n", "    \n", "    X.append(window)\n", "    if ret > Ti:\n", "        labels.append(1)\n", "    elif ret < -Ti:\n", "        labels.append(-1)\n", "    else:\n", "        labels.append(0)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYcAAAD8CAYAAACcjGjIAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\ndQwHSVLn/wMChZuz54nZiQAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure()\n", "plt.hist(labels, bins = 5, alpha = 0.7)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 25, "metadata": {"collapsed": true}, "outputs": [], "source": ["X_train, X_val, X_test = X[:int(len(X) * 0.5)], X[int(len(X) * 0.6):int(len(X) * 0.7)], X[int(len(X) * 0.8):]\n", "Y_train, Y_val, Y_test = labels[:int(len(X) * 0.5)], labels[int(len(X) * 0.6):int(len(X) * 0.7)], labels[int(len(X) * 0.8):]\n", "\n", "lbr = LabelBinarizer()\n", "Y_train = lbr.fit_transform(Y_train)\n", "Y_val = lbr.transform(Y_val)\n", "Y_test = lbr.transform(Y_test)\n", "\n", "X_train, X_val, X_test = make_features_from_window(X_train, X_val, X_test, FEATURES)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["_________________________________________________________________\n", "Layer (type)                 Output Shape              Param #   \n", "=================================================================\n", "main_input (InputLayer)      (None, 100, 3)            0         \n", "_________________________________________________________________\n", "flatten_2 (<PERSON>ten)          (None, 300)               0         \n", "_________________________________________________________________\n", "dropout_2 (Dropout)          (None, 300)               0         \n", "_________________________________________________________________\n", "dense_2 (<PERSON><PERSON>)              (None, 3)                 903       \n", "=================================================================\n", "Total params: 903\n", "Trainable params: 903\n", "Non-trainable params: 0\n", "_________________________________________________________________\n", "Train on 31950 samples, validate on 6390 samples\n", "Epoch 1/100\n", "31950/31950 [==============================] - 11s 342us/step - loss: 1.9417 - acc: 0.3350 - val_loss: 1.3366 - val_acc: 0.3812\n", "Epoch 2/100\n", "31950/31950 [==============================] - 6s 188us/step - loss: 1.9176 - acc: 0.3333 - val_loss: 1.6338 - val_acc: 0.3563\n", "Epoch 3/100\n", "31950/31950 [==============================] - 8s 241us/step - loss: 1.9315 - acc: 0.3355 - val_loss: 1.2706 - val_acc: 0.3557\n", "Epoch 4/100\n", "31950/31950 [==============================] - 9s 291us/step - loss: 1.9169 - acc: 0.3336 - val_loss: 1.4230 - val_acc: 0.3473\n", "Epoch 5/100\n", "31950/31950 [==============================] - 8s 259us/step - loss: 1.9471 - acc: 0.3320 - val_loss: 1.7356 - val_acc: 0.2919\n", "Epoch 6/100\n", "31950/31950 [==============================] - 8s 254us/step - loss: 1.9141 - acc: 0.3372 - val_loss: 1.2237 - val_acc: 0.3174\n", "Epoch 7/100\n", "31950/31950 [==============================] - 7s 232us/step - loss: 1.9164 - acc: 0.3354 - val_loss: 1.2794 - val_acc: 0.3324\n", "Epoch 8/100\n", "31950/31950 [==============================] - 6s 194us/step - loss: 1.8991 - acc: 0.3338 - val_loss: 1.2609 - val_acc: 0.3196\n", "Epoch 9/100\n", "31950/31950 [==============================] - 6s 187us/step - loss: 1.8992 - acc: 0.3362 - val_loss: 1.2335 - val_acc: 0.3305\n", "Epoch 10/100\n", "31950/31950 [==============================] - 6s 186us/step - loss: 1.8802 - acc: 0.3382 - val_loss: 1.3925 - val_acc: 0.3285\n", "Epoch 11/100\n", "31950/31950 [==============================] - 8s 245us/step - loss: 1.9059 - acc: 0.3326 - val_loss: 1.2916 - val_acc: 0.3377\n"]}], "source": ["def cnn(shape):\n", "    main_input = Input(shape=shape, name='main_input')\n", "    x = Flatten()(main_input)\n", "    x = Dropout(0.25)(x)\n", "    output = Dense(3, activation = \"softmax\")(x)\n", "    \n", "    final_model = Model(inputs=[main_input], outputs=[output])\n", "    return final_model\n", "\n", "model = cnn((WINDOW_LONG, len(X_train[0][0]), ))\n", "model.summary()\n", "\n", "model.compile(optimizer=Adam(lr = 0.01),  \n", "                        loss=['categorical_crossentropy'], \n", "                        metrics = ['accuracy'])\n", "\n", "checkpointer = ModelCheckpoint(filepath=\"test.hdf5\", verbose=0, save_best_only=True)\n", "es = EarlyStopping(monitor='val_loss', patience=5)\n", "\n", "history = model.fit(X_train, Y_train, \n", "              epochs = 100, \n", "              batch_size = 16, \n", "              verbose=True, \n", "              validation_data = (X_val, Y_val),\n", "              callbacks=[checkpointer, es],\n", "              shuffle=True, \n", "              class_weight = get_class_weights(np.concatenate((Y_train, Y_val)))\n", "                )\n", "\n", "model.load_weights(\"test.hdf5\")"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["              precision    recall  f1-score   support\n", "\n", "           0       0.31      0.34      0.33      2095\n", "           1       0.23      0.33      0.27      1518\n", "           2       0.41      0.29      0.34      2777\n", "\n", "   micro avg       0.32      0.32      0.32      6390\n", "   macro avg       0.32      0.32      0.31      6390\n", "weighted avg       0.34      0.32      0.32      6390\n", "\n", "[[ 714  708  673]\n", " [ 536  496  486]\n", " [1045  914  818]]\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYAAAAD8CAYAAAB+UHOxAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nAElFTkSuQmCC\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["              precision    recall  f1-score   support\n", "\n", "           0       0.41      0.40      0.41      4922\n", "           1       0.23      0.32      0.27      2896\n", "           2       0.42      0.33      0.37      4962\n", "\n", "   micro avg       0.36      0.36      0.36     12780\n", "   macro avg       0.35      0.35      0.35     12780\n", "weighted avg       0.37      0.36      0.36     12780\n", "\n", "[[1965 1577 1380]\n", " [1045  940  911]\n", " [1757 1565 1640]]\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYAAAAD8CAYAAAB+UHOxAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nOMF2yB1vrHUAAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["pred = model.predict(X_val)\n", "\n", "print classification_report([np.argmax(y) for y in Y_val],\n", "                            [np.argmax(y) for y in pred])\n", "\n", "print confusion_matrix([np.argmax(y) for y in Y_val],\n", "                            [np.argmax(y) for y in pred])\n", "\n", "plt.plot()\n", "plt.hist([np.argmax(y) for y in Y_val], bins = 5, alpha = 0.5, label = 'Test data')\n", "plt.hist([np.argmax(y) for y in pred], bins = 5, alpha = 0.5, label = 'Predicted data')\n", "plt.legend()\n", "plt.show()\n", "\n", "\n", "pred = model.predict(X_test)\n", "\n", "print classification_report([np.argmax(y) for y in Y_test],\n", "                            [np.argmax(y) for y in pred])\n", "\n", "print confusion_matrix([np.argmax(y) for y in Y_test],\n", "                            [np.argmax(y) for y in pred])\n", "\n", "plt.plot()\n", "plt.hist([np.argmax(y) for y in Y_test], bins = 5, alpha = 0.5, label = 'Test data')\n", "plt.hist([np.argmax(y) for y in pred], bins = 5, alpha = 0.5, label = 'Predicted data')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{0: 1.23, 1: 1.82, 2: 1.0}\n", "[-1  0  1]\n"]}], "source": ["print get_class_weights(np.concatenate((Y_train, Y_val)))\n", "print lbr.classes_"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Meta labeling"]}, {"cell_type": "code", "execution_count": 29, "metadata": {"collapsed": true}, "outputs": [], "source": ["def get_meta_barier(future_window, last_close, min_ret, tp, sl, vertical_zero = False):\n", "    '''\n", "        XXX\n", "    '''\n", "    if vertical_zero:\n", "        min_ret_situation = [0, 0, 0]\n", "    else:\n", "        min_ret_situation = [0, 0]\n", "        \n", "        \n", "    differences = np.array([(fc - last_close) / last_close for fc in future_window])\n", "    \n", "    # Are there gonna be fluctuations within min_ret???\n", "    min_ret_ups = np.where((differences >= min_ret) == True)[0]\n", "    min_ret_downs = np.where((differences < -min_ret) == True)[0]\n", "  \n", "    if (len(min_ret_ups) == 0) and (len(min_ret_downs) == 0):\n", "        if vertical_zero:\n", "            min_ret_situation[2] = 1\n", "        else:\n", "            if differences[-1] > 0:\n", "                min_ret_situation[0] = 1\n", "            else:\n", "                min_ret_situation[1] = 1            \n", "    else:\n", "        if len(min_ret_ups) == 0: min_ret_ups = [np.inf]\n", "        if len(min_ret_downs) == 0: min_ret_downs = [np.inf]\n", "        if min_ret_ups[0] > min_ret_downs[0]:\n", "            min_ret_situation[0] = 1\n", "        else:\n", "            min_ret_situation[1] = 1\n", "        \n", "    #  Take profit and stop losses indices\n", "    take_profit = np.where((differences >= tp) == True)[0]\n", "    stop_loss = np.where((differences < sl) == True)[0]\n", "    \n", "    # Fluctuation directions coincide with take profit / stop loss actions?\n", "    if min_ret_situation[0] == 1 and len(take_profit) != 0:\n", "        take_action = 1\n", "    elif min_ret_situation[1] == 1 and len(stop_loss) != 0:\n", "        take_action = 1\n", "    else:\n", "        take_action = 0.\n", "    \n", "    return min_ret_situation, take_action"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"collapsed": true}, "outputs": [], "source": ["TP = T\n", "SL = -T\n", "\n", "X, Y, Y2 = [], [], []\n", "for i in range(WINDOW_LONG, N_BARS, 1):\n", "    window = tick_bars.iloc[i-WINDOW_LONG:i]\n", "    now = tick_bars.close[i]\n", "    future_window = tick_bars.close[i:i+HORIZON]\n", "    \n", "    window_abs_returns = np.abs(window.close.pct_change())\n", "    Ti = np.std(window_abs_returns) + np.mean(window_abs_returns)\n", "    \n", "    min_ret_situation, take_action = get_meta_barier(future_window, now, Ti, TP, SL, True)\n", "    X.append(window)\n", "    Y.append(min_ret_situation)\n", "    Y2.append(take_action)"]}, {"cell_type": "code", "execution_count": 31, "metadata": {"scrolled": true}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYcAAAD8CAYAAACcjGjIAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure()\n", "plt.hist([np.argmax(x) for x in Y], alpha = 0.5, bins = 5)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 32, "metadata": {"collapsed": true}, "outputs": [], "source": ["X_train, X_val, X_test = X[:int(len(X) * 0.5)], X[int(len(X) * 0.6):int(len(X) * 0.7)], X[int(len(X) * 0.8):]\n", "Y_train, Y_val, Y_test = Y[:int(len(X) * 0.5)], Y[int(len(X) * 0.6):int(len(X) * 0.7)], Y[int(len(X) * 0.8):]\n", "\n", "X_train, X_val, X_test = make_features_from_window(X_train, X_val, X_test, FEATURES)"]}, {"cell_type": "code", "execution_count": 33, "metadata": {"collapsed": true}, "outputs": [], "source": ["Y_train, Y_val, Y_test = np.array(Y_train), np.array(Y_val), np.array(Y_test)"]}, {"cell_type": "code", "execution_count": 34, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["_________________________________________________________________\n", "Layer (type)                 Output Shape              Param #   \n", "=================================================================\n", "main_input (InputLayer)      (None, 100, 3)            0         \n", "_________________________________________________________________\n", "flatten_3 (<PERSON>ten)          (None, 300)               0         \n", "_________________________________________________________________\n", "dropout_3 (Dropout)          (None, 300)               0         \n", "_________________________________________________________________\n", "dense_3 (<PERSON><PERSON>)              (None, 3)                 903       \n", "=================================================================\n", "Total params: 903\n", "Trainable params: 903\n", "Non-trainable params: 0\n", "_________________________________________________________________\n", "Train on 31950 samples, validate on 6390 samples\n", "Epoch 1/100\n", "31950/31950 [==============================] - 6s 202us/step - loss: 3.2768 - acc: 0.3455 - val_loss: 1.6196 - val_acc: 0.3700\n", "Epoch 2/100\n", "31950/31950 [==============================] - 6s 200us/step - loss: 3.3883 - acc: 0.3420 - val_loss: 1.7184 - val_acc: 0.3343\n", "Epoch 3/100\n", "31950/31950 [==============================] - 6s 184us/step - loss: 3.2865 - acc: 0.3484 - val_loss: 1.4454 - val_acc: 0.3729\n", "Epoch 4/100\n", "31950/31950 [==============================] - 6s 194us/step - loss: 3.2960 - acc: 0.3483 - val_loss: 1.3963 - val_acc: 0.3820\n", "Epoch 5/100\n", "31950/31950 [==============================] - 6s 192us/step - loss: 3.3174 - acc: 0.3511 - val_loss: 2.4761 - val_acc: 0.3682\n", "Epoch 6/100\n", "31950/31950 [==============================] - 8s 246us/step - loss: 3.4705 - acc: 0.3580 - val_loss: 1.2709 - val_acc: 0.4131\n", "Epoch 7/100\n", "31950/31950 [==============================] - 8s 251us/step - loss: 3.5451 - acc: 0.3438 - val_loss: 1.1883 - val_acc: 0.3876\n", "Epoch 8/100\n", "31950/31950 [==============================] - 6s 197us/step - loss: 3.1714 - acc: 0.3500 - val_loss: 1.2146 - val_acc: 0.3934\n", "Epoch 9/100\n", "31950/31950 [==============================] - 7s 216us/step - loss: 3.3911 - acc: 0.3502 - val_loss: 1.5469 - val_acc: 0.3152\n", "Epoch 10/100\n", "31950/31950 [==============================] - 9s 272us/step - loss: 3.2959 - acc: 0.3533 - val_loss: 1.8945 - val_acc: 0.3474\n", "Epoch 11/100\n", "31950/31950 [==============================] - 7s 207us/step - loss: 3.3394 - acc: 0.3530 - val_loss: 1.6301 - val_acc: 0.3535\n", "Epoch 12/100\n", "31950/31950 [==============================] - 6s 184us/step - loss: 3.3092 - acc: 0.3507 - val_loss: 1.5052 - val_acc: 0.4050\n"]}], "source": ["def cnn(shape):\n", "    main_input = Input(shape=shape, name='main_input')\n", "    x = Flatten()(main_input)\n", "    x = Dropout(0.25)(x)\n", "    output = Dense(3, activation = \"softmax\")(x)\n", "    \n", "    final_model = Model(inputs=[main_input], outputs=[output])\n", "    return final_model\n", "\n", "model = cnn((WINDOW_LONG, len(X_train[0][0]), ))\n", "model.summary()\n", "\n", "model.compile(optimizer=Adam(lr = 0.01),  \n", "                        loss=['categorical_crossentropy'], \n", "                        metrics = ['accuracy'])\n", "\n", "checkpointer = ModelCheckpoint(filepath=\"test.hdf5\", verbose=0, save_best_only=True)\n", "es = EarlyStopping(monitor='val_loss', patience=5)\n", "\n", "history = model.fit(X_train, Y_train, \n", "              epochs = 100, \n", "              batch_size = 16, \n", "              verbose=True, \n", "              validation_data = (X_val, Y_val),\n", "              callbacks=[checkpointer, es],\n", "              shuffle=True, \n", "              class_weight = get_class_weights(np.concatenate((Y_train, Y_val)))\n", "                )\n", "\n", "model.load_weights(\"test.hdf5\")"]}, {"cell_type": "code", "execution_count": 35, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["              precision    recall  f1-score   support\n", "\n", "           0       0.45      0.47      0.46      2865\n", "           1       0.51      0.33      0.40      3307\n", "           2       0.05      0.28      0.08       218\n", "\n", "   micro avg       0.39      0.39      0.39      6390\n", "   macro avg       0.33      0.36      0.31      6390\n", "weighted avg       0.47      0.39      0.41      6390\n", "\n", "[[1335  961  569]\n", " [1568 1081  658]\n", " [  72   85   61]]\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYAAAAD8CAYAAAB+UHOxAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["              precision    recall  f1-score   support\n", "\n", "           0       0.49      0.46      0.48      6225\n", "           1       0.49      0.36      0.41      6189\n", "           2       0.03      0.19      0.05       366\n", "\n", "   micro avg       0.41      0.41      0.41     12780\n", "   macro avg       0.34      0.34      0.31     12780\n", "weighted avg       0.48      0.41      0.43     12780\n", "\n", "[[**************]\n", " [**************]\n", " [ 184  113   69]]\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYAAAAD8CAYAAAB+UHOxAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\ngg==\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["pred = model.predict(X_val)\n", "\n", "print classification_report([np.argmax(y) for y in Y_val],\n", "                            [np.argmax(y) for y in pred])\n", "\n", "print confusion_matrix([np.argmax(y) for y in Y_val],\n", "                            [np.argmax(y) for y in pred])\n", "\n", "plt.plot()\n", "plt.hist([np.argmax(y) for y in Y_val], bins = 5, alpha = 0.5, label = 'Test data')\n", "plt.hist([np.argmax(y) for y in pred], bins = 5, alpha = 0.5, label = 'Predicted data')\n", "plt.legend()\n", "plt.show()\n", "\n", "\n", "pred = model.predict(X_test)\n", "\n", "print classification_report([np.argmax(y) for y in Y_test],\n", "                            [np.argmax(y) for y in pred])\n", "\n", "print confusion_matrix([np.argmax(y) for y in Y_test],\n", "                            [np.argmax(y) for y in pred])\n", "\n", "plt.plot()\n", "plt.hist([np.argmax(y) for y in Y_test], bins = 5, alpha = 0.5, label = 'Test data')\n", "plt.hist([np.argmax(y) for y in pred], bins = 5, alpha = 0.5, label = 'Predicted data')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 36, "metadata": {"collapsed": true, "scrolled": true}, "outputs": [], "source": ["X_train, X_val, X_test = X[:int(len(X) * 0.5)], X[int(len(X) * 0.6):int(len(X) * 0.7)], X[int(len(X) * 0.8):]\n", "Y_train, Y_val, Y_test = Y2[:int(len(X) * 0.5)], Y2[int(len(X) * 0.6):int(len(X) * 0.7)], Y2[int(len(X) * 0.8):]\n", "\n", "X_train, X_val, X_test = make_features_from_window(X_train, X_val, X_test, FEATURES)\n", "P_train, P_val, P_test = model.predict(X_train), model.predict(X_val), model.predict(X_test)"]}, {"cell_type": "code", "execution_count": 37, "metadata": {"collapsed": true}, "outputs": [], "source": ["Y_train = np.array([[1, 0] if x == 1 else [0, 1] for x in Y_train])\n", "Y_val = np.array([[1, 0] if x == 1 else [0, 1] for x in Y_val])\n", "Y_test = np.array([[1, 0] if x == 1 else [0, 1] for x in Y_test])"]}, {"cell_type": "code", "execution_count": 38, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["__________________________________________________________________________________________________\n", "Layer (type)                    Output Shape         Param #     Connected to                     \n", "==================================================================================================\n", "main_input (InputLayer)         (None, 100, 3)       0                                            \n", "__________________________________________________________________________________________________\n", "flatten_4 (<PERSON><PERSON>)             (None, 300)          0           main_input[0][0]                 \n", "__________________________________________________________________________________________________\n", "dropout_4 (Dropout)             (None, 300)          0           flatten_4[0][0]                  \n", "__________________________________________________________________________________________________\n", "meta (InputLayer)               (None, 3)            0                                            \n", "__________________________________________________________________________________________________\n", "concatenate_1 (Concatenate)     (None, 303)          0           dropout_4[0][0]                  \n", "                                                                 meta[0][0]                       \n", "__________________________________________________________________________________________________\n", "dense_4 (<PERSON><PERSON>)                 (None, 2)            608         concatenate_1[0][0]              \n", "==================================================================================================\n", "Total params: 608\n", "Trainable params: 608\n", "Non-trainable params: 0\n", "__________________________________________________________________________________________________\n", "Train on 31950 samples, validate on 12780 samples\n", "Epoch 1/100\n", "31950/31950 [==============================] - 6s 193us/step - loss: 2.9034 - acc: 0.4788 - val_loss: 1.0905 - val_acc: 0.5209\n", "Epoch 2/100\n", "31950/31950 [==============================] - 6s 194us/step - loss: 2.6902 - acc: 0.4894 - val_loss: 1.0154 - val_acc: 0.5297\n", "Epoch 3/100\n", "31950/31950 [==============================] - 6s 198us/step - loss: 2.8014 - acc: 0.4962 - val_loss: 0.7995 - val_acc: 0.6202\n", "Epoch 4/100\n", "31950/31950 [==============================] - 7s 207us/step - loss: 2.9782 - acc: 0.4863 - val_loss: 1.1041 - val_acc: 0.4821\n", "Epoch 5/100\n", "31950/31950 [==============================] - 6s 192us/step - loss: 2.7570 - acc: 0.4877 - val_loss: 0.9376 - val_acc: 0.5396\n", "Epoch 6/100\n", "31950/31950 [==============================] - 6s 188us/step - loss: 2.8829 - acc: 0.4860 - val_loss: 2.0553 - val_acc: 0.6032\n", "Epoch 7/100\n", "31950/31950 [==============================] - 6s 177us/step - loss: 2.8434 - acc: 0.4911 - val_loss: 1.2025 - val_acc: 0.4656\n", "Epoch 8/100\n", "31950/31950 [==============================] - 6s 190us/step - loss: 2.7781 - acc: 0.4955 - val_loss: 1.4456 - val_acc: 0.5119\n"]}], "source": ["def cnn(shape):\n", "    main_input = Input(shape=shape, name='main_input')\n", "    aux_input = Input((3, ), name='meta')\n", "    \n", "    x = Flatten()(main_input)\n", "    x = Dropout(0.25)(x)\n", "    x = concatenate([x, aux_input])\n", "    output = Dense(2, activation = \"softmax\")(x)\n", "    \n", "    final_model = Model(inputs=[main_input, aux_input], outputs=[output])\n", "    return final_model\n", "\n", "\n", "model = cnn((WINDOW_LONG, len(X_train[0][0]), ))\n", "model.summary()\n", "\n", "model.compile(optimizer=Adam(lr = 0.01),  \n", "                        loss=['categorical_crossentropy'], \n", "                        metrics = ['accuracy'])\n", "\n", "checkpointer = ModelCheckpoint(filepath=\"test.hdf5\", verbose=0, save_best_only=True)\n", "es = EarlyStopping(monitor='val_loss', patience=5)\n", "\n", "history = model.fit([X_train, P_train], Y_train, \n", "              epochs = 100, \n", "              batch_size = 16, \n", "              verbose=True, \n", "              validation_data = ([X_test, P_test], Y_test),\n", "              callbacks=[checkpointer, es],\n", "              shuffle=True, \n", "              class_weight = get_class_weights(np.concatenate((Y_train, Y_val))))\n", "\n", "model.load_weights(\"test.hdf5\")"]}, {"cell_type": "code", "execution_count": 39, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["              precision    recall  f1-score   support\n", "\n", "           0       0.02      0.43      0.04       120\n", "           1       0.98      0.62      0.76      6270\n", "\n", "   micro avg       0.62      0.62      0.62      6390\n", "   macro avg       0.50      0.53      0.40      6390\n", "weighted avg       0.96      0.62      0.75      6390\n", "\n", "[[  52   68]\n", " [2361 3909]]\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYAAAAD8CAYAAAB+UHOxAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\ngIhIpBQAIiKR+v9f+nMYdy6wrAAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["              precision    recall  f1-score   support\n", "\n", "           0       0.03      0.43      0.06       360\n", "           1       0.97      0.63      0.76     12420\n", "\n", "   micro avg       0.62      0.62      0.62     12780\n", "   macro avg       0.50      0.53      0.41     12780\n", "weighted avg       0.95      0.62      0.74     12780\n", "\n", "[[ 154  206]\n", " [4648 7772]]\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYcAAAD8CAYAAACcjGjIAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["pred = model.predict([X_val, P_val])\n", "\n", "print classification_report([np.argmax(y) for y in Y_val],\n", "                            [np.argmax(y) for y in pred])\n", "\n", "print confusion_matrix([np.argmax(y) for y in Y_val],\n", "                            [np.argmax(y) for y in pred])\n", "\n", "plt.plot()\n", "plt.hist([np.argmax(y) for y in Y_val], bins = 5, alpha = 0.5, label = 'Test data')\n", "plt.hist([np.argmax(y) for y in pred], bins = 5, alpha = 0.5, label = 'Predicted data')\n", "plt.legend()\n", "plt.show()\n", "\n", "\n", "pred = model.predict([X_test, P_test])\n", "\n", "print classification_report([np.argmax(y) for y in Y_test],\n", "                            [np.argmax(y) for y in pred])\n", "\n", "print confusion_matrix([np.argmax(y) for y in Y_test],\n", "                            [np.argmax(y) for y in pred])\n", "\n", "plt.plot()\n", "plt.hist([np.argmax(y) for y in Y_test], bins = 5, alpha = 0.5, label = 'Test data')\n", "plt.hist([np.argmax(y) for y in pred], bins = 5, alpha = 0.5, label = 'Predicted data')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAXoAAAD8CAYAAAB5Pm/hAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nSUVORK5CYII=\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure()\n", "plt.hist(pred, bins = 50)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/plain": ["{0: 14.8, 1: 1.0}"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["get_class_weights(np.concatenate((Y_train, Y_val)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 2", "language": "python", "name": "python2"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.14"}}, "nbformat": 4, "nbformat_minor": 2}