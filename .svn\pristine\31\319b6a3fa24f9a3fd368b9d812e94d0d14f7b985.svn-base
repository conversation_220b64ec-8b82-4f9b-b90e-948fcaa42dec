{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## OHCL w/ Volume"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"scrolled": true}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3e5094b040c64b7bbab10f8ab4e49c56", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=100000, description='t', max=1000000, min=1), FloatSlider(value=0.61, de…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import ipywidgets as widgets\n", "import pandas as pd\n", "import numpy as np\n", "\n", "from math import pi\n", "from stochastic.continuous import FractionalBrownianMotion\n", "from stochastic.noise import GaussianNoise\n", "from bokeh.plotting import figure, show, output_notebook\n", "from bokeh.layouts import column\n", "\n", "\n", "\n", "def show_plot(t, hurst, freq='1h'):\n", "    price_fbm = FractionalBrownianMotion(t=t, hurst=hurst)\n", "    volume_gen = GaussianNoise(t=t*t)\n", "\n", "    start_date = pd.to_datetime('2010-01-01', format='%Y-%m-%d')\n", "\n", "    price_volatility = price_fbm.sample(t, zero=False)\n", "    prices = price_volatility + 10000\n", "    volume_volatility = volume_gen.sample(t) * np.random.lognormal(5, 1.5, t)\n", "    volumes = volume_volatility * (10 * price_volatility) + 1\n", "\n", "    price_frame = pd.DataFrame([], columns=['date', 'price'], dtype=float)\n", "    volume_frame = pd.DataFrame(\n", "        [], columns=['date', 'volume'], dtype=float)\n", "\n", "    price_frame['date'] = pd.date_range(start=start_date, periods=t, freq=\"1min\")\n", "    price_frame['price'] = abs(prices)\n", "\n", "    volume_frame['date'] = price_frame['date'].copy()\n", "    volume_frame['volume'] = abs(volumes)\n", "\n", "    price_frame.set_index('date')\n", "    price_frame.index = pd.to_datetime(price_frame.index, unit='m', origin=start_date)\n", "\n", "    volume_frame.set_index('date')\n", "    volume_frame.index = pd.to_datetime(volume_frame.index, unit='m', origin=start_date)\n", "\n", "    ohlc = price_frame['price'].resample(freq).ohlc()\n", "    volumes = volume_frame['volume'].resample(freq).sum() / 1e7\n", "    \n", "    TOOLS = \"xpan,wheel_zoom,box_zoom,crosshair,reset,save\"\n", "    \n", "    price_plt = figure(title=\"Simulated Price\",\n", "                       y_axis_label='Price ($)',\n", "                       x_axis_type='datetime',\n", "                       tools=TOOLS)\n", "    price_plt.xaxis.major_label_orientation = pi/4\n", "    price_plt.grid.grid_line_alpha=0.3\n", "    \n", "    width = 60*1000*10\n", "    inc = ohlc.close > ohlc.open\n", "    dec = ohlc.open > ohlc.close\n", "    \n", "    price_plt.segment(ohlc.index, ohlc.high, ohlc.index, ohlc.low, color=\"black\")\n", "    price_plt.vbar(ohlc.index[inc], width, ohlc.open[inc], ohlc.close[inc], fill_color=\"#9BDE39\", line_color=\"black\")\n", "    price_plt.vbar(ohlc.index[dec], width, ohlc.open[dec], ohlc.close[dec], fill_color=\"#F2583E\", line_color=\"black\")\n", "\n", "    volume_plt = figure(title=\"Simulated Volume\",\n", "                        y_axis_label=\"Volume\",\n", "                        x_axis_label=\"Time\",\n", "                        x_axis_type=\"datetime\",\n", "                        x_range=price_plt.x_range,\n", "                        plot_height=250,\n", "                        tools=TOOLS)\n", "    \n", "    volume_plt.vbar(ohlc.index, bottom=0, top=volumes, width=width, color=\"#72B5C8\", alpha=0.3)\n", "    \n", "    output_notebook()\n", "\n", "    show(column(price_plt, volume_plt))\n", "\n", "widgets.interact(show_plot,\n", "                 t=widgets.IntSlider(min=1, max=1000000, value=100000),\n", "                 hurst=widgets.FloatSlider(min=0.5, max=0.8, step=0.01, value=0.61));"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## FBMExchange OHCLV Example"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "    <div class=\"bk-root\">\n", "        <a href=\"https://bokeh.pydata.org\" target=\"_blank\" class=\"bk-logo bk-logo-small bk-logo-notebook\"></a>\n", "        <span id=\"2411\">Loading BokehJS ...</span>\n", "    </div>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/javascript": ["\n", "(function(root) {\n", "  function now() {\n", "    return new Date();\n", "  }\n", "\n", "  var force = true;\n", "\n", "  if (typeof root._bokeh_onload_callbacks === \"undefined\" || force === true) {\n", "    root._bokeh_onload_callbacks = [];\n", "    root._bokeh_is_loading = undefined;\n", "  }\n", "\n", "  var JS_MIME_TYPE = 'application/javascript';\n", "  var HTML_MIME_TYPE = 'text/html';\n", "  var EXEC_MIME_TYPE = 'application/vnd.bokehjs_exec.v0+json';\n", "  var CLASS_NAME = 'output_bokeh rendered_html';\n", "\n", "  /**\n", "   * Render data to the DOM node\n", "   */\n", "  function render(props, node) {\n", "    var script = document.createElement(\"script\");\n", "    node.appendChild(script);\n", "  }\n", "\n", "  /**\n", "   * Handle when an output is cleared or removed\n", "   */\n", "  function handleClearOutput(event, handle) {\n", "    var cell = handle.cell;\n", "\n", "    var id = cell.output_area._bokeh_element_id;\n", "    var server_id = cell.output_area._bokeh_server_id;\n", "    // Clean up Bokeh references\n", "    if (id != null && id in Bokeh.index) {\n", "      Bokeh.index[id].model.document.clear();\n", "      delete Bokeh.index[id];\n", "    }\n", "\n", "    if (server_id !== undefined) {\n", "      // Clean up Bokeh references\n", "      var cmd = \"from bokeh.io.state import curstate; print(curstate().uuid_to_server['\" + server_id + \"'].get_sessions()[0].document.roots[0]._id)\";\n", "      cell.notebook.kernel.execute(cmd, {\n", "        iopub: {\n", "          output: function(msg) {\n", "            var id = msg.content.text.trim();\n", "            if (id in Bokeh.index) {\n", "              Bokeh.index[id].model.document.clear();\n", "              delete Bokeh.index[id];\n", "            }\n", "          }\n", "        }\n", "      });\n", "      // Destroy server and session\n", "      var cmd = \"import bokeh.io.notebook as ion; ion.destroy_server('\" + server_id + \"')\";\n", "      cell.notebook.kernel.execute(cmd);\n", "    }\n", "  }\n", "\n", "  /**\n", "   * Handle when a new output is added\n", "   */\n", "  function handleAddOutput(event, handle) {\n", "    var output_area = handle.output_area;\n", "    var output = handle.output;\n", "\n", "    // limit handleAddOutput to display_data with EXEC_MIME_TYPE content only\n", "    if ((output.output_type != \"display_data\") || (!output.data.hasOwnProperty(EXEC_MIME_TYPE))) {\n", "      return\n", "    }\n", "\n", "    var toinsert = output_area.element.find(\".\" + CLASS_NAME.split(' ')[0]);\n", "\n", "    if (output.metadata[EXEC_MIME_TYPE][\"id\"] !== undefined) {\n", "      toinsert[toinsert.length - 1].firstChild.textContent = output.data[JS_MIME_TYPE];\n", "      // store reference to embed id on output_area\n", "      output_area._bokeh_element_id = output.metadata[EXEC_MIME_TYPE][\"id\"];\n", "    }\n", "    if (output.metadata[EXEC_MIME_TYPE][\"server_id\"] !== undefined) {\n", "      var bk_div = document.createElement(\"div\");\n", "      bk_div.innerHTML = output.data[HTML_MIME_TYPE];\n", "      var script_attrs = bk_div.children[0].attributes;\n", "      for (var i = 0; i < script_attrs.length; i++) {\n", "        toinsert[toinsert.length - 1].firstChild.setAttribute(script_attrs[i].name, script_attrs[i].value);\n", "      }\n", "      // store reference to server id on output_area\n", "      output_area._bokeh_server_id = output.metadata[EXEC_MIME_TYPE][\"server_id\"];\n", "    }\n", "  }\n", "\n", "  function register_renderer(events, OutputArea) {\n", "\n", "    function append_mime(data, metadata, element) {\n", "      // create a DOM node to render to\n", "      var toinsert = this.create_output_subarea(\n", "        metadata,\n", "        CLASS_NAME,\n", "        EXEC_MIME_TYPE\n", "      );\n", "      this.keyboard_manager.register_events(toinsert);\n", "      // Render to node\n", "      var props = {data: data, metadata: metadata[EXEC_MIME_TYPE]};\n", "      render(props, toinsert[toinsert.length - 1]);\n", "      element.append(toinsert);\n", "      return toinsert\n", "    }\n", "\n", "    /* Handle when an output is cleared or removed */\n", "    events.on('clear_output.CodeCell', handleClearOutput);\n", "    events.on('delete.Cell', handleClearOutput);\n", "\n", "    /* Handle when a new output is added */\n", "    events.on('output_added.OutputArea', handleAddOutput);\n", "\n", "    /**\n", "     * Register the mime type and append_mime function with output_area\n", "     */\n", "    OutputArea.prototype.register_mime_type(EXEC_MIME_TYPE, append_mime, {\n", "      /* Is output safe? */\n", "      safe: true,\n", "      /* Index of renderer in `output_area.display_order` */\n", "      index: 0\n", "    });\n", "  }\n", "\n", "  // register the mime type if in Jupyter Notebook environment and previously unregistered\n", "  if (root.<PERSON>pyter !== undefined) {\n", "    var events = require('base/js/events');\n", "    var OutputArea = require('notebook/js/outputarea').OutputArea;\n", "\n", "    if (OutputArea.prototype.mime_types().indexOf(EXEC_MIME_TYPE) == -1) {\n", "      register_renderer(events, OutputArea);\n", "    }\n", "  }\n", "\n", "  \n", "  if (typeof (root._bokeh_timeout) === \"undefined\" || force === true) {\n", "    root._bokeh_timeout = Date.now() + 5000;\n", "    root._bokeh_failed_load = false;\n", "  }\n", "\n", "  var NB_LOAD_WARNING = {'data': {'text/html':\n", "     \"<div style='background-color: #fdd'>\\n\"+\n", "     \"<p>\\n\"+\n", "     \"BokehJS does not appear to have successfully loaded. If loading BokehJS from CDN, this \\n\"+\n", "     \"may be due to a slow or bad network connection. Possible fixes:\\n\"+\n", "     \"</p>\\n\"+\n", "     \"<ul>\\n\"+\n", "     \"<li>re-rerun `output_notebook()` to attempt to load from CDN again, or</li>\\n\"+\n", "     \"<li>use INLINE resources instead, as so:</li>\\n\"+\n", "     \"</ul>\\n\"+\n", "     \"<code>\\n\"+\n", "     \"from bokeh.resources import INLINE\\n\"+\n", "     \"output_notebook(resources=INLINE)\\n\"+\n", "     \"</code>\\n\"+\n", "     \"</div>\"}};\n", "\n", "  function display_loaded() {\n", "    var el = document.getElementById(\"2411\");\n", "    if (el != null) {\n", "      el.textContent = \"BokehJS is loading...\";\n", "    }\n", "    if (root.Bokeh !== undefined) {\n", "      if (el != null) {\n", "        el.textContent = \"BokehJS \" + root.Bokeh.version + \" successfully loaded.\";\n", "      }\n", "    } else if (Date.now() < root._bokeh_timeout) {\n", "      setTimeout(display_loaded, 100)\n", "    }\n", "  }\n", "\n", "\n", "  function run_callbacks() {\n", "    try {\n", "      root._bokeh_onload_callbacks.forEach(function(callback) {\n", "        if (callback != null)\n", "          callback();\n", "      });\n", "    } finally {\n", "      delete root._bokeh_onload_callbacks\n", "    }\n", "    console.debug(\"<PERSON>keh: all callbacks have finished\");\n", "  }\n", "\n", "  function load_libs(css_urls, js_urls, callback) {\n", "    if (css_urls == null) css_urls = [];\n", "    if (js_urls == null) js_urls = [];\n", "\n", "    root._bokeh_onload_callbacks.push(callback);\n", "    if (root._bokeh_is_loading > 0) {\n", "      console.debug(\"Bokeh: BokehJS is being loaded, scheduling callback at\", now());\n", "      return null;\n", "    }\n", "    if (js_urls == null || js_urls.length === 0) {\n", "      run_callbacks();\n", "      return null;\n", "    }\n", "    console.debug(\"Bokeh: BokehJS not loaded, scheduling load and callback at\", now());\n", "    root._bokeh_is_loading = css_urls.length + js_urls.length;\n", "\n", "    function on_load() {\n", "      root._bokeh_is_loading--;\n", "      if (root._bokeh_is_loading === 0) {\n", "        console.debug(\"Bokeh: all BokehJS libraries/stylesheets loaded\");\n", "        run_callbacks()\n", "      }\n", "    }\n", "\n", "    function on_error() {\n", "      console.error(\"failed to load \" + url);\n", "    }\n", "\n", "    for (var i = 0; i < css_urls.length; i++) {\n", "      var url = css_urls[i];\n", "      const element = document.createElement(\"link\");\n", "      element.onload = on_load;\n", "      element.onerror = on_error;\n", "      element.rel = \"stylesheet\";\n", "      element.type = \"text/css\";\n", "      element.href = url;\n", "      console.debug(\"Bokeh: injecting link tag for BokehJS stylesheet: \", url);\n", "      document.body.appendChild(element);\n", "    }\n", "\n", "    for (var i = 0; i < js_urls.length; i++) {\n", "      var url = js_urls[i];\n", "      var element = document.createElement('script');\n", "      element.onload = on_load;\n", "      element.onerror = on_error;\n", "      element.async = false;\n", "      element.src = url;\n", "      console.debug(\"Bokeh: injecting script tag for BokehJS library: \", url);\n", "      document.head.appendChild(element);\n", "    }\n", "  };var element = document.getElementById(\"2411\");\n", "  if (element == null) {\n", "    console.error(\"Bokeh: ERROR: autoload.js configured with elementid '2411' but no matching script tag was found. \")\n", "    return false;\n", "  }\n", "\n", "  function inject_raw_css(css) {\n", "    const element = document.createElement(\"style\");\n", "    element.appendChild(document.createTextNode(css));\n", "    document.body.appendChild(element);\n", "  }\n", "\n", "  var js_urls = [\"https://cdn.pydata.org/bokeh/release/bokeh-1.2.0.min.js\", \"https://cdn.pydata.org/bokeh/release/bokeh-widgets-1.2.0.min.js\", \"https://cdn.pydata.org/bokeh/release/bokeh-tables-1.2.0.min.js\", \"https://cdn.pydata.org/bokeh/release/bokeh-gl-1.2.0.min.js\"];\n", "  var css_urls = [\"https://cdn.pydata.org/bokeh/release/bokeh-1.2.0.min.css\", \"https://cdn.pydata.org/bokeh/release/bokeh-widgets-1.2.0.min.css\", \"https://cdn.pydata.org/bokeh/release/bokeh-tables-1.2.0.min.css\"];\n", "\n", "  var inline_js = [\n", "    function(Bokeh) {\n", "      Bokeh.set_log_level(\"info\");\n", "    },\n", "    \n", "    function(Bokeh) {\n", "      \n", "    },\n", "    function(Bokeh) {} // ensure no trailing comma for IE\n", "  ];\n", "\n", "  function run_inline_js() {\n", "    \n", "    if ((root.Bokeh !== undefined) || (force === true)) {\n", "      for (var i = 0; i < inline_js.length; i++) {\n", "        inline_js[i].call(root, root.Bokeh);\n", "      }if (force === true) {\n", "        display_loaded();\n", "      }} else if (Date.now() < root._bokeh_timeout) {\n", "      setTimeout(run_inline_js, 100);\n", "    } else if (!root._bokeh_failed_load) {\n", "      console.log(\"Bokeh: BokehJS failed to load within specified timeout.\");\n", "      root._bokeh_failed_load = true;\n", "    } else if (force !== true) {\n", "      var cell = $(document.getElementById(\"2411\")).parents('.cell').data().cell;\n", "      cell.output_area.append_execute_result(NB_LOAD_WARNING)\n", "    }\n", "\n", "  }\n", "\n", "  if (root._bokeh_is_loading === 0) {\n", "    console.debug(\"Bokeh: BokehJS loaded, going straight to plotting\");\n", "    run_inline_js();\n", "  } else {\n", "    load_libs(css_urls, js_urls, function() {\n", "      console.debug(\"Bokeh: BokehJS plotting callback run at\", now());\n", "      run_inline_js();\n", "    });\n", "  }\n", "}(window));"], "application/vnd.bokehjs_load.v0+json": "\n(function(root) {\n  function now() {\n    return new Date();\n  }\n\n  var force = true;\n\n  if (typeof root._bokeh_onload_callbacks === \"undefined\" || force === true) {\n    root._bokeh_onload_callbacks = [];\n    root._bokeh_is_loading = undefined;\n  }\n\n  \n\n  \n  if (typeof (root._bokeh_timeout) === \"undefined\" || force === true) {\n    root._bokeh_timeout = Date.now() + 5000;\n    root._bokeh_failed_load = false;\n  }\n\n  var NB_LOAD_WARNING = {'data': {'text/html':\n     \"<div style='background-color: #fdd'>\\n\"+\n     \"<p>\\n\"+\n     \"BokehJS does not appear to have successfully loaded. If loading BokehJS from CDN, this \\n\"+\n     \"may be due to a slow or bad network connection. Possible fixes:\\n\"+\n     \"</p>\\n\"+\n     \"<ul>\\n\"+\n     \"<li>re-rerun `output_notebook()` to attempt to load from CDN again, or</li>\\n\"+\n     \"<li>use INLINE resources instead, as so:</li>\\n\"+\n     \"</ul>\\n\"+\n     \"<code>\\n\"+\n     \"from bokeh.resources import INLINE\\n\"+\n     \"output_notebook(resources=INLINE)\\n\"+\n     \"</code>\\n\"+\n     \"</div>\"}};\n\n  function display_loaded() {\n    var el = document.getElementById(\"2411\");\n    if (el != null) {\n      el.textContent = \"BokehJS is loading...\";\n    }\n    if (root.Bokeh !== undefined) {\n      if (el != null) {\n        el.textContent = \"BokehJS \" + root.Bokeh.version + \" successfully loaded.\";\n      }\n    } else if (Date.now() < root._bokeh_timeout) {\n      setTimeout(display_loaded, 100)\n    }\n  }\n\n\n  function run_callbacks() {\n    try {\n      root._bokeh_onload_callbacks.forEach(function(callback) {\n        if (callback != null)\n          callback();\n      });\n    } finally {\n      delete root._bokeh_onload_callbacks\n    }\n    console.debug(\"Bokeh: all callbacks have finished\");\n  }\n\n  function load_libs(css_urls, js_urls, callback) {\n    if (css_urls == null) css_urls = [];\n    if (js_urls == null) js_urls = [];\n\n    root._bokeh_onload_callbacks.push(callback);\n    if (root._bokeh_is_loading > 0) {\n      console.debug(\"Bokeh: BokehJS is being loaded, scheduling callback at\", now());\n      return null;\n    }\n    if (js_urls == null || js_urls.length === 0) {\n      run_callbacks();\n      return null;\n    }\n    console.debug(\"Bokeh: BokehJS not loaded, scheduling load and callback at\", now());\n    root._bokeh_is_loading = css_urls.length + js_urls.length;\n\n    function on_load() {\n      root._bokeh_is_loading--;\n      if (root._bokeh_is_loading === 0) {\n        console.debug(\"Bokeh: all BokehJS libraries/stylesheets loaded\");\n        run_callbacks()\n      }\n    }\n\n    function on_error() {\n      console.error(\"failed to load \" + url);\n    }\n\n    for (var i = 0; i < css_urls.length; i++) {\n      var url = css_urls[i];\n      const element = document.createElement(\"link\");\n      element.onload = on_load;\n      element.onerror = on_error;\n      element.rel = \"stylesheet\";\n      element.type = \"text/css\";\n      element.href = url;\n      console.debug(\"Bokeh: injecting link tag for BokehJS stylesheet: \", url);\n      document.body.appendChild(element);\n    }\n\n    for (var i = 0; i < js_urls.length; i++) {\n      var url = js_urls[i];\n      var element = document.createElement('script');\n      element.onload = on_load;\n      element.onerror = on_error;\n      element.async = false;\n      element.src = url;\n      console.debug(\"Bokeh: injecting script tag for BokehJS library: \", url);\n      document.head.appendChild(element);\n    }\n  };var element = document.getElementById(\"2411\");\n  if (element == null) {\n    console.error(\"Bokeh: ERROR: autoload.js configured with elementid '2411' but no matching script tag was found. \")\n    return false;\n  }\n\n  function inject_raw_css(css) {\n    const element = document.createElement(\"style\");\n    element.appendChild(document.createTextNode(css));\n    document.body.appendChild(element);\n  }\n\n  var js_urls = [\"https://cdn.pydata.org/bokeh/release/bokeh-1.2.0.min.js\", \"https://cdn.pydata.org/bokeh/release/bokeh-widgets-1.2.0.min.js\", \"https://cdn.pydata.org/bokeh/release/bokeh-tables-1.2.0.min.js\", \"https://cdn.pydata.org/bokeh/release/bokeh-gl-1.2.0.min.js\"];\n  var css_urls = [\"https://cdn.pydata.org/bokeh/release/bokeh-1.2.0.min.css\", \"https://cdn.pydata.org/bokeh/release/bokeh-widgets-1.2.0.min.css\", \"https://cdn.pydata.org/bokeh/release/bokeh-tables-1.2.0.min.css\"];\n\n  var inline_js = [\n    function(Bokeh) {\n      Bokeh.set_log_level(\"info\");\n    },\n    \n    function(Bokeh) {\n      \n    },\n    function(Bokeh) {} // ensure no trailing comma for IE\n  ];\n\n  function run_inline_js() {\n    \n    if ((root.Bokeh !== undefined) || (force === true)) {\n      for (var i = 0; i < inline_js.length; i++) {\n        inline_js[i].call(root, root.Bokeh);\n      }if (force === true) {\n        display_loaded();\n      }} else if (Date.now() < root._bokeh_timeout) {\n      setTimeout(run_inline_js, 100);\n    } else if (!root._bokeh_failed_load) {\n      console.log(\"Bokeh: BokehJS failed to load within specified timeout.\");\n      root._bokeh_failed_load = true;\n    } else if (force !== true) {\n      var cell = $(document.getElementById(\"2411\")).parents('.cell').data().cell;\n      cell.output_area.append_execute_result(NB_LOAD_WARNING)\n    }\n\n  }\n\n  if (root._bokeh_is_loading === 0) {\n    console.debug(\"Bokeh: BokehJS loaded, going straight to plotting\");\n    run_inline_js();\n  } else {\n    load_libs(css_urls, js_urls, function() {\n      console.debug(\"Bokeh: BokehJS plotting callback run at\", now());\n      run_inline_js();\n    });\n  }\n}(window));"}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "\n", "\n", "\n", "\n", "\n", "  <div class=\"bk-root\" id=\"3b39cc3c-9da3-4634-9836-30f3a09766a7\" data-root-id=\"2412\"></div>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/javascript": ["(function(root) {\n", "  function embed_document(root) {\n", "    \n", "  var docs_json = {\"e7bdd31e-9cbb-4fad-beed-a7d9d8c2da01\":{\"roots\":{\"references\":[{\"attributes\":{\"children\":[{\"id\":\"2324\",\"subtype\":\"Figure\",\"type\":\"Plot\"},{\"id\":\"2373\",\"subtype\":\"Figure\",\"type\":\"Plot\"}]},\"id\":\"2412\",\"type\":\"Column\"},{\"attributes\":{\"days\":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31]},\"id\":\"2577\",\"type\":\"DaysTicker\"},{\"attributes\":{\"source\":{\"id\":\"2358\",\"type\":\"ColumnDataSource\"}},\"id\":\"2362\",\"type\":\"CDSView\"},{\"attributes\":{},\"id\":\"2396\",\"type\":\"CrosshairTool\"},{\"attributes\":{\"days\":[1,4,7,10,13,16,19,22,25,28]},\"id\":\"2578\",\"type\":\"DaysTicker\"},{\"attributes\":{},\"id\":\"2397\",\"type\":\"ResetTool\"},{\"attributes\":{\"days\":[1,8,15,22]},\"id\":\"2579\",\"type\":\"DaysTicker\"},{\"attributes\":{},\"id\":\"2346\",\"type\":\"WheelZoomTool\"},{\"attributes\":{},\"id\":\"2398\",\"type\":\"SaveTool\"},{\"attributes\":{\"days\":[1,15]},\"id\":\"2580\",\"type\":\"DaysTicker\"},{\"attributes\":{\"active_drag\":\"auto\",\"active_inspect\":\"auto\",\"active_multi\":null,\"active_scroll\":\"auto\",\"active_tap\":\"auto\",\"tools\":[{\"id\":\"2393\",\"type\":\"PanTool\"},{\"id\":\"2394\",\"type\":\"WheelZoomTool\"},{\"id\":\"2395\",\"type\":\"BoxZoomTool\"},{\"id\":\"2396\",\"type\":\"CrosshairTool\"},{\"id\":\"2397\",\"type\":\"ResetTool\"},{\"id\":\"2398\",\"type\":\"SaveTool\"}]},\"id\":\"2399\",\"type\":\"Toolbar\"},{\"attributes\":{\"months\":[0,1,2,3,4,5,6,7,8,9,10,11]},\"id\":\"2581\",\"type\":\"MonthsTicker\"},{\"attributes\":{\"callback\":null,\"data\":{\"bottom\":[10024.0,10016.0,9968.0,9952.0,9952.0,9960.0,9944.0,9872.0,9880.0,9888.0,9840.0,9856.0,9872.0,9896.0,9904.0,9912.0,9920.0,9904.0,9896.0,9904.0,9896.0,9912.0,9912.0,9912.0,9928.0,9944.0,9912.0,9896.0,9896.0,9880.0,9904.0,9912.0,9920.0,9904.0,9912.0,9920.0,9872.0,9880.0,9888.0,9896.0,9904.0,9888.0,9872.0,9824.0,9832.0,9760.0,9720.0,9728.0,9744.0,9704.0,9712.0,9712.0,9728.0,9720.0,9736.0,9744.0,9736.0,9744.0,9752.0,9728.0,9752.0,9752.0,9752.0,9760.0,9768.0,9784.0,9792.0,9824.0,9832.0,9800.0,9808.0,9824.0,9840.0,9864.0,9880.0,9888.0,9872.0,9888.0,9840.0,9824.0,9816.0,9784.0,9808.0,9784.0,9784.0,9800.0,9784.0,9760.0,9760.0,9760.0,9784.0,9824.0,9824.0,9824.0,9832.0,9840.0,9808.0,9776.0,9784.0,9744.0,9752.0,9704.0,9704.0,9712.0,9720.0,9720.0,9728.0,9736.0,9736.0,9704.0,9672.0,9704.0,9664.0,9680.0,9680.0,9680.0,9704.0,9712.0,9744.0,9744.0,9736.0,9752.0,9720.0,9728.0,9736.0,9744.0,9768.0,9792.0,9792.0,9808.0,9760.0,9704.0,9720.0,9672.0,9600.0,9600.0,9592.0,9544.0,9544.0,9544.0,9464.0,9472.0,9440.0,9448.0,9456.0,9464.0,9456.0,9480.0,9488.0,9504.0,9512.0,9528.0,9528.0,9536.0,9544.0,9552.0,9552.0,9560.0,9560.0,9576.0,9600.0,9608.0,9624.0,9632.0,9616.0,9624.0,9632.0,9640.0,9608.0,9616.0,9632.0,9640.0,9648.0,9664.0,9648.0,9672.0,9680.0,9688.0,9696.0,9720.0,9712.0,9720.0,9712.0,9704.0,9720.0,9720.0,9664.0,9672.0,9672.0,9680.0,9696.0,9680.0,9664.0,9560.0,9536.0,9552.0,9536.0,9528.0,9520.0,9536.0,9528.0,9528.0,9512.0,9528.0,9536.0,9536.0,9528.0,9512.0,9448.0,9432.0,9440.0,9464.0,9480.0,9496.0,9504.0,9520.0,9536.0,9544.0,9552.0,9528.0,9520.0,9528.0,9504.0,9424.0,9392.0,9408.0,9368.0,9368.0,9376.0,9368.0,9376.0,9384.0,9384.0,9384.0,9360.0,9304.0,9320.0,9336.0,9352.0,9312.0,9272.0,9280.0,9288.0,9272.0,9280.0,9288.0,9296.0,9288.0,9272.0,9288.0,9296.0,9256.0,9264.0,9272.0,9280.0,9280.0,9312.0,9248.0,9232.0,9216.0,9232.0,9248.0,9248.0,9184.0,9152.0,9160.0,9168.0,9184.0,9192.0,9192.0,9192.0,9208.0,9216.0,9192.0,9192.0,9216.0,9216.0,9216.0,9224.0,9248.0,9224.0,9240.0,9240.0,9248.0,9264.0,9272.0,9272.0,9272.0,9272.0,9280.0,9264.0,9272.0,9280.0,9312.0,9328.0,9336.0,9352.0,9368.0,9376.0,9352.0,9360.0,9328.0,9336.0,9352.0,9360.0,9360.0,9352.0,9344.0,9296.0,9272.0,9272.0,9280.0,9280.0,9248.0,9256.0,9216.0,9248.0,9264.0,9272.0,9288.0,9296.0,9312.0,9304.0,9320.0,9336.0,9328.0,9336.0,9336.0,9296.0,9320.0,9296.0,9304.0,9320.0,9304.0,9320.0,9320.0,9304.0,9312.0,9296.0,9296.0,9232.0,9240.0,9232.0,9232.0,9248.0,9264.0,9240.0,9248.0,9232.0,9256.0,9264.0,9192.0,9208.0,9224.0,9224.0,9208.0,9216.0,9232.0,9240.0,9192.0,9200.0,9200.0,9208.0,9224.0,9208.0,9160.0,9160.0,9168.0,9176.0,9048.0,9048.0,9064.0,9080.0,9096.0,9104.0,9104.0,9112.0,9080.0,9096.0,9120.0,9128.0,9136.0,9064.0,9072.0,9096.0,9112.0,9120.0,9144.0,9176.0,9184.0,9144.0,9064.0,9040.0,8936.0,8944.0,8952.0,8952.0,8896.0,8904.0,8912.0,8920.0,8928.0,8936.0,8952.0,8944.0,8960.0,8960.0,8936.0,8944.0,8968.0,8968.0,8856.0,8872.0,8888.0,8896.0,8912.0,8920.0,8904.0,8880.0,8896.0,8808.0,8776.0,8784.0,8768.0,8784.0,8792.0,8792.0,8800.0,8808.0,8824.0,8816.0,8824.0,8840.0,8744.0,8744.0,8728.0,8704.0,8728.0,8736.0,8752.0,8736.0,8752.0,8768.0,8760.0,8784.0,8792.0,8800.0,8808.0,8832.0,8856.0,8840.0,8784.0,8784.0,8800.0,8760.0,8760.0,8768.0,8776.0,8808.0,8816.0,8800.0,8808.0,8816.0,8784.0,8704.0,8728.0,8656.0,8672.0,8680.0,8688.0,8696.0,8696.0,8704.0,8720.0,8728.0,8736.0,8728.0,8672.0,8656.0,8664.0,8688.0,8696.0,8688.0,8664.0,8672.0,8640.0,8664.0,8680.0,8696.0,8712.0,8720.0,8736.0,8736.0,8712.0,8720.0,8728.0,8752.0,8760.0,8784.0,8744.0,8752.0,8760.0,8784.0,8776.0,8768.0,8792.0,8808.0,8816.0,8832.0,8808.0,8824.0,8840.0,8808.0,8760.0,8776.0,8800.0,8808.0,8816.0,8792.0,8808.0,8816.0,8816.0,8800.0,8784.0,8784.0,8792.0,8808.0,8816.0,8832.0,8840.0,8824.0,8832.0,8840.0,8864.0,8896.0,8896.0,8912.0,8920.0,8936.0,8944.0,8960.0,8976.0,9016.0,9032.0,9024.0,9000.0,8992.0,9000.0,9008.0,9008.0,9008.0,9032.0,9056.0,9064.0,9064.0,8992.0,9000.0,8984.0,8920.0,8904.0,8912.0,8872.0,8888.0,8864.0,8872.0,8848.0,8864.0,8808.0,8784.0],\"top\":[10000.0,10008.0,9960.0,9944.0,9944.0,9952.0,9936.0,9864.0,9872.0,9880.0,9832.0,9840.0,9856.0,9872.0,9896.0,9904.0,9912.0,9888.0,9872.0,9880.0,9888.0,9896.0,9904.0,9904.0,9912.0,9928.0,9904.0,9888.0,9888.0,9872.0,9888.0,9904.0,9912.0,9896.0,9904.0,9912.0,9848.0,9872.0,9872.0,9888.0,9896.0,9872.0,9856.0,9816.0,9824.0,9752.0,9712.0,9720.0,9720.0,9696.0,9704.0,9696.0,9712.0,9696.0,9712.0,9736.0,9720.0,9728.0,9744.0,9720.0,9728.0,9744.0,9744.0,9736.0,9760.0,9760.0,9784.0,9792.0,9824.0,9792.0,9800.0,9808.0,9824.0,9840.0,9864.0,9880.0,9848.0,9872.0,9824.0,9816.0,9808.0,9768.0,9784.0,9768.0,9768.0,9792.0,9776.0,9752.0,9752.0,9752.0,9768.0,9792.0,9816.0,9816.0,9808.0,9832.0,9792.0,9768.0,9776.0,9736.0,9744.0,9696.0,9696.0,9704.0,9712.0,9704.0,9720.0,9728.0,9728.0,9696.0,9664.0,9672.0,9656.0,9656.0,9672.0,9664.0,9680.0,9704.0,9712.0,9736.0,9720.0,9736.0,9712.0,9720.0,9728.0,9736.0,9744.0,9768.0,9776.0,9792.0,9752.0,9696.0,9704.0,9664.0,9584.0,9584.0,9576.0,9536.0,9536.0,9536.0,9456.0,9456.0,9416.0,9432.0,9448.0,9448.0,9448.0,9464.0,9472.0,9480.0,9504.0,9520.0,9520.0,9528.0,9536.0,9528.0,9544.0,9552.0,9552.0,9560.0,9576.0,9600.0,9608.0,9624.0,9608.0,9616.0,9624.0,9632.0,9600.0,9608.0,9616.0,9632.0,9640.0,9648.0,9640.0,9648.0,9672.0,9680.0,9688.0,9696.0,9704.0,9712.0,9704.0,9688.0,9704.0,9712.0,9656.0,9664.0,9664.0,9672.0,9688.0,9656.0,9656.0,9552.0,9528.0,9536.0,9528.0,9512.0,9504.0,9520.0,9504.0,9520.0,9504.0,9512.0,9528.0,9520.0,9520.0,9488.0,9440.0,9424.0,9416.0,9440.0,9464.0,9472.0,9488.0,9504.0,9520.0,9512.0,9536.0,9520.0,9512.0,9520.0,9496.0,9416.0,9384.0,9392.0,9360.0,9360.0,9352.0,9360.0,9360.0,9360.0,9376.0,9376.0,9352.0,9296.0,9304.0,9320.0,9328.0,9304.0,9264.0,9272.0,9280.0,9256.0,9272.0,9280.0,9288.0,9272.0,9264.0,9264.0,9280.0,9248.0,9256.0,9264.0,9272.0,9264.0,9280.0,9240.0,9224.0,9208.0,9208.0,9232.0,9240.0,9168.0,9144.0,9152.0,9160.0,9168.0,9176.0,9184.0,9184.0,9192.0,9208.0,9176.0,9184.0,9192.0,9208.0,9208.0,9216.0,9224.0,9216.0,9224.0,9232.0,9240.0,9248.0,9256.0,9264.0,9264.0,9264.0,9264.0,9240.0,9264.0,9264.0,9280.0,9312.0,9328.0,9336.0,9352.0,9368.0,9344.0,9344.0,9312.0,9328.0,9336.0,9352.0,9344.0,9336.0,9328.0,9288.0,9264.0,9256.0,9272.0,9264.0,9240.0,9240.0,9208.0,9208.0,9248.0,9264.0,9272.0,9288.0,9296.0,9296.0,9304.0,9320.0,9320.0,9328.0,9328.0,9288.0,9296.0,9280.0,9296.0,9304.0,9296.0,9304.0,9304.0,9296.0,9304.0,9288.0,9288.0,9224.0,9232.0,9224.0,9224.0,9232.0,9248.0,9224.0,9232.0,9224.0,9232.0,9256.0,9184.0,9192.0,9208.0,9216.0,9200.0,9208.0,9216.0,9232.0,9184.0,9192.0,9192.0,9200.0,9208.0,9200.0,9152.0,9152.0,9160.0,9168.0,9032.0,9024.0,9048.0,9064.0,9088.0,9096.0,9096.0,9096.0,9072.0,9080.0,9096.0,9120.0,9128.0,9056.0,9064.0,9072.0,9096.0,9112.0,9120.0,9144.0,9176.0,9120.0,9048.0,9032.0,8928.0,8936.0,8944.0,8936.0,8880.0,8896.0,8904.0,8912.0,8904.0,8928.0,8936.0,8936.0,8944.0,8952.0,8920.0,8936.0,8936.0,8952.0,8824.0,8856.0,8872.0,8888.0,8896.0,8912.0,8896.0,8872.0,8888.0,8792.0,8768.0,8776.0,8744.0,8768.0,8784.0,8776.0,8792.0,8792.0,8800.0,8808.0,8808.0,8824.0,8736.0,8736.0,8720.0,8696.0,8704.0,8728.0,8736.0,8728.0,8736.0,8752.0,8752.0,8760.0,8776.0,8784.0,8800.0,8808.0,8832.0,8824.0,8768.0,8768.0,8784.0,8752.0,8752.0,8760.0,8760.0,8776.0,8808.0,8792.0,8800.0,8800.0,8776.0,8688.0,8704.0,8640.0,8664.0,8672.0,8680.0,8688.0,8688.0,8696.0,8704.0,8720.0,8728.0,8720.0,8656.0,8640.0,8656.0,8664.0,8688.0,8672.0,8656.0,8664.0,8632.0,8640.0,8664.0,8680.0,8704.0,8712.0,8720.0,8712.0,8704.0,8712.0,8720.0,8728.0,8752.0,8760.0,8736.0,8744.0,8752.0,8752.0,8768.0,8760.0,8768.0,8792.0,8808.0,8816.0,8800.0,8808.0,8824.0,8792.0,8744.0,8768.0,8784.0,8800.0,8808.0,8768.0,8792.0,8808.0,8800.0,8792.0,8776.0,8776.0,8784.0,8792.0,8800.0,8816.0,8832.0,8816.0,8824.0,8832.0,8840.0,8864.0,8880.0,8904.0,8912.0,8920.0,8936.0,8944.0,8960.0,8976.0,9024.0,9008.0,8992.0,8976.0,8992.0,9000.0,8984.0,8992.0,9008.0,9032.0,9056.0,9056.0,8976.0,8992.0,8976.0,8912.0,8896.0,8888.0,8856.0,8872.0,8840.0,8864.0,8840.0,8840.0,8800.0,8776.0],\"x\":{\"__ndarray__\":\"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\",\"dtype\":\"float64\",\"shape\":[569]}},\"selected\":{\"id\":\"2570\",\"type\":\"Selection\"},\"selection_policy\":{\"id\":\"2571\",\"type\":\"UnionRenderers\"}},\"id\":\"2363\",\"type\":\"ColumnDataSource\"},{\"attributes\":{\"num_minor_ticks\":5,\"tickers\":[{\"id\":\"2555\",\"type\":\"AdaptiveTicker\"},{\"id\":\"2556\",\"type\":\"AdaptiveTicker\"},{\"id\":\"2557\",\"type\":\"AdaptiveTicker\"},{\"id\":\"2558\",\"type\":\"DaysTicker\"},{\"id\":\"2559\",\"type\":\"DaysTicker\"},{\"id\":\"2560\",\"type\":\"DaysTicker\"},{\"id\":\"2561\",\"type\":\"DaysTicker\"},{\"id\":\"2562\",\"type\":\"MonthsTicker\"},{\"id\":\"2563\",\"type\":\"MonthsTicker\"},{\"id\":\"2564\",\"type\":\"MonthsTicker\"},{\"id\":\"2565\",\"type\":\"MonthsTicker\"},{\"id\":\"2566\",\"type\":\"YearsTicker\"}]},\"id\":\"2336\",\"type\":\"DatetimeTicker\"},{\"attributes\":{\"months\":[0,2,4,6,8,10]},\"id\":\"2582\",\"type\":\"MonthsTicker\"},{\"attributes\":{\"data_source\":{\"id\":\"2406\",\"type\":\"ColumnDataSource\"},\"glyph\":{\"id\":\"2407\",\"type\":\"VBar\"},\"hover_glyph\":null,\"muted_glyph\":null,\"nonselection_glyph\":{\"id\":\"2408\",\"type\":\"VBar\"},\"selection_glyph\":null,\"view\":{\"id\":\"2410\",\"type\":\"CDSView\"}},\"id\":\"2409\",\"type\":\"GlyphRenderer\"},{\"attributes\":{\"source\":{\"id\":\"2406\",\"type\":\"ColumnDataSource\"}},\"id\":\"2410\",\"type\":\"CDSView\"},{\"attributes\":{\"months\":[0,4,8]},\"id\":\"2583\",\"type\":\"MonthsTicker\"},{\"attributes\":{\"dimensions\":\"width\"},\"id\":\"2345\",\"type\":\"PanTool\"},{\"attributes\":{\"dimension\":1,\"grid_line_alpha\":0.3,\"ticker\":{\"id\":\"2341\",\"type\":\"BasicTicker\"}},\"id\":\"2344\",\"type\":\"Grid\"},{\"attributes\":{\"months\":[0,6]},\"id\":\"2584\",\"type\":\"MonthsTicker\"},{\"attributes\":{\"bottom\":{\"field\":\"bottom\"},\"fill_alpha\":{\"value\":0.1},\"fill_color\":{\"value\":\"#1f77b4\"},\"line_alpha\":{\"value\":0.1},\"line_color\":{\"value\":\"#1f77b4\"},\"top\":{\"field\":\"top\"},\"width\":{\"value\":600000},\"x\":{\"field\":\"x\"}},\"id\":\"2365\",\"type\":\"VBar\"},{\"attributes\":{\"bottom\":{\"field\":\"bottom\"},\"fill_color\":{\"value\":\"#9BDE39\"},\"top\":{\"field\":\"top\"},\"width\":{\"value\":600000},\"x\":{\"field\":\"x\"}},\"id\":\"2364\",\"type\":\"VBar\"},{\"attributes\":{},\"id\":\"2585\",\"type\":\"YearsTicker\"},{\"attributes\":{\"data_source\":{\"id\":\"2363\",\"type\":\"ColumnDataSource\"},\"glyph\":{\"id\":\"2364\",\"type\":\"VBar\"},\"hover_glyph\":null,\"muted_glyph\":null,\"nonselection_glyph\":{\"id\":\"2365\",\"type\":\"VBar\"},\"selection_glyph\":null,\"view\":{\"id\":\"2367\",\"type\":\"CDSView\"}},\"id\":\"2366\",\"type\":\"GlyphRenderer\"},{\"attributes\":{\"bottom_units\":\"screen\",\"fill_alpha\":{\"value\":0.5},\"fill_color\":{\"value\":\"lightgrey\"},\"left_units\":\"screen\",\"level\":\"overlay\",\"line_alpha\":{\"value\":1.0},\"line_color\":{\"value\":\"black\"},\"line_dash\":[4,4],\"line_width\":{\"value\":2},\"render_mode\":\"css\",\"right_units\":\"screen\",\"top_units\":\"screen\"},\"id\":\"2586\",\"type\":\"BoxAnnotation\"},{\"attributes\":{\"source\":{\"id\":\"2363\",\"type\":\"ColumnDataSource\"}},\"id\":\"2367\",\"type\":\"CDSView\"},{\"attributes\":{},\"id\":\"2550\",\"type\":\"DatetimeTickFormatter\"},{\"attributes\":{},\"id\":\"2587\",\"type\":\"Selection\"},{\"attributes\":{\"below\":[{\"id\":\"2383\",\"type\":\"DatetimeAxis\"}],\"center\":[{\"id\":\"2387\",\"type\":\"Grid\"},{\"id\":\"2392\",\"type\":\"Grid\"}],\"left\":[{\"id\":\"2388\",\"type\":\"LinearAxis\"}],\"plot_height\":250,\"renderers\":[{\"id\":\"2409\",\"type\":\"GlyphRenderer\"}],\"title\":{\"id\":\"2374\",\"type\":\"Title\"},\"toolbar\":{\"id\":\"2399\",\"type\":\"Toolbar\"},\"x_range\":{\"id\":\"2327\",\"type\":\"DataRange1d\"},\"x_scale\":{\"id\":\"2379\",\"type\":\"LinearScale\"},\"y_range\":{\"id\":\"2377\",\"type\":\"DataRange1d\"},\"y_scale\":{\"id\":\"2381\",\"type\":\"LinearScale\"}},\"id\":\"2373\",\"subtype\":\"Figure\",\"type\":\"Plot\"},{\"attributes\":{},\"id\":\"2552\",\"type\":\"BasicTickFormatter\"},{\"attributes\":{},\"id\":\"2588\",\"type\":\"UnionRenderers\"},{\"attributes\":{},\"id\":\"2554\",\"type\":\"DatetimeTickFormatter\"},{\"attributes\":{\"mantissas\":[1,2,5],\"max_interval\":500.0,\"num_minor_ticks\":0},\"id\":\"2555\",\"type\":\"AdaptiveTicker\"},{\"attributes\":{\"callback\":null,\"data\":{\"bottom\":[10008.0,10000.0,9984.0,9984.0,9968.0,9960.0,9944.0,9944.0,9944.0,9936.0,9904.0,9896.0,9880.0,9864.0,9880.0,9864.0,9856.0,9848.0,9832.0,9896.0,9912.0,9904.0,9888.0,9888.0,9880.0,9872.0,9880.0,9896.0,9888.0,9904.0,9936.0,9920.0,9904.0,9904.0,9896.0,9888.0,9872.0,9896.0,9912.0,9896.0,9872.0,9864.0,9856.0,9848.0,9896.0,9888.0,9872.0,9880.0,9864.0,9856.0,9856.0,9856.0,9824.0,9816.0,9824.0,9800.0,9784.0,9760.0,9744.0,9712.0,9720.0,9720.0,9728.0,9696.0,9704.0,9720.0,9704.0,9712.0,9736.0,9720.0,9728.0,9736.0,9720.0,9744.0,9744.0,9744.0,9736.0,9808.0,9800.0,9840.0,9880.0,9856.0,9848.0,9880.0,9864.0,9848.0,9824.0,9824.0,9816.0,9800.0,9808.0,9800.0,9784.0,9768.0,9768.0,9768.0,9776.0,9776.0,9760.0,9752.0,9752.0,9784.0,9816.0,9816.0,9808.0,9808.0,9792.0,9792.0,9768.0,9776.0,9760.0,9752.0,9736.0,9736.0,9720.0,9696.0,9696.0,9704.0,9728.0,9728.0,9728.0,9696.0,9696.0,9680.0,9664.0,9696.0,9680.0,9664.0,9656.0,9656.0,9672.0,9664.0,9736.0,9728.0,9720.0,9744.0,9736.0,9712.0,9728.0,9784.0,9776.0,9800.0,9768.0,9752.0,9736.0,9720.0,9696.0,9704.0,9672.0,9664.0,9664.0,9648.0,9632.0,9608.0,9592.0,9592.0,9584.0,9584.0,9584.0,9576.0,9568.0,9560.0,9544.0,9528.0,9520.0,9496.0,9480.0,9464.0,9456.0,9456.0,9456.0,9440.0,9424.0,9416.0,9408.0,9432.0,9448.0,9448.0,9472.0,9472.0,9520.0,9528.0,9544.0,9552.0,9624.0,9608.0,9624.0,9600.0,9656.0,9648.0,9704.0,9704.0,9704.0,9688.0,9712.0,9712.0,9696.0,9680.0,9656.0,9680.0,9656.0,9656.0,9648.0,9648.0,9632.0,9624.0,9608.0,9592.0,9568.0,9560.0,9552.0,9552.0,9528.0,9544.0,9528.0,9512.0,9512.0,9504.0,9520.0,9512.0,9504.0,9520.0,9512.0,9520.0,9520.0,9512.0,9488.0,9496.0,9472.0,9456.0,9440.0,9424.0,9416.0,9472.0,9520.0,9536.0,9544.0,9520.0,9512.0,9512.0,9520.0,9512.0,9496.0,9496.0,9488.0,9464.0,9448.0,9440.0,9432.0,9416.0,9416.0,9400.0,9384.0,9400.0,9376.0,9360.0,9360.0,9360.0,9352.0,9360.0,9352.0,9360.0,9368.0,9360.0,9376.0,9368.0,9352.0,9352.0,9336.0,9320.0,9296.0,9328.0,9344.0,9336.0,9328.0,9328.0,9304.0,9296.0,9288.0,9264.0,9256.0,9280.0,9272.0,9272.0,9264.0,9264.0,9280.0,9264.0,9248.0,9264.0,9264.0,9256.0,9288.0,9280.0,9256.0,9240.0,9224.0,9208.0,9240.0,9232.0,9200.0,9184.0,9168.0,9168.0,9152.0,9208.0,9192.0,9184.0,9184.0,9240.0,9216.0,9232.0,9264.0,9264.0,9264.0,9264.0,9264.0,9272.0,9248.0,9240.0,9368.0,9352.0,9344.0,9344.0,9344.0,9320.0,9312.0,9320.0,9344.0,9344.0,9336.0,9328.0,9320.0,9288.0,9264.0,9256.0,9272.0,9272.0,9264.0,9240.0,9240.0,9232.0,9224.0,9216.0,9208.0,9208.0,9296.0,9320.0,9328.0,9336.0,9328.0,9320.0,9304.0,9304.0,9312.0,9296.0,9288.0,9296.0,9288.0,9280.0,9312.0,9304.0,9296.0,9304.0,9304.0,9312.0,9304.0,9296.0,9304.0,9288.0,9288.0,9288.0,9272.0,9256.0,9248.0,9232.0,9224.0,9224.0,9224.0,9256.0,9224.0,9232.0,9224.0,9256.0,9232.0,9208.0,9184.0,9216.0,9200.0,9232.0,9216.0,9208.0,9192.0,9184.0,9216.0,9200.0,9192.0,9184.0,9176.0,9152.0,9152.0,9168.0,9160.0,9136.0,9128.0,9112.0,9088.0,9088.0,9064.0,9056.0,9040.0,9032.0,9032.0,9024.0,9096.0,9104.0,9088.0,9072.0,9112.0,9104.0,9080.0,9056.0,9120.0,9168.0,9160.0,9144.0,9120.0,9136.0,9128.0,9080.0,9072.0,9064.0,9048.0,9048.0,9032.0,9032.0,9008.0,8992.0,8984.0,8976.0,8944.0,8928.0,8944.0,8936.0,8944.0,8936.0,8912.0,8896.0,8888.0,8880.0,8912.0,8896.0,8944.0,8936.0,8952.0,8952.0,8920.0,8920.0,8960.0,8960.0,8944.0,8920.0,8912.0,8904.0,8880.0,8856.0,8848.0,8848.0,8840.0,8824.0,8880.0,8896.0,8888.0,8864.0,8880.0,8864.0,8856.0,8840.0,8824.0,8808.0,8792.0,8784.0,8768.0,8760.0,8744.0,8776.0,8776.0,8800.0,8816.0,8808.0,8808.0,8832.0,8824.0,8808.0,8784.0,8768.0,8744.0,8736.0,8736.0,8736.0,8720.0,8720.0,8704.0,8696.0,8736.0,8728.0,8760.0,8752.0,8776.0,8784.0,8776.0,8800.0,8840.0,8824.0,8824.0,8808.0,8800.0,8768.0,8776.0,8768.0,8768.0,8776.0,8768.0,8760.0,8752.0,8744.0,8800.0,8792.0,8800.0,8792.0,8784.0,8776.0,8784.0,8768.0,8736.0,8728.0,8712.0,8704.0,8688.0,8720.0,8704.0,8696.0,8680.0,8672.0,8648.0,8640.0,8688.0,8728.0,8720.0,8720.0,8688.0,8672.0,8656.0,8656.0,8640.0,8680.0,8664.0,8656.0,8656.0,8648.0,8632.0,8712.0,8712.0,8720.0,8704.0,8720.0,8776.0,8768.0,8752.0,8736.0,8752.0,8768.0,8768.0,8760.0,8808.0,8800.0,8800.0,8832.0,8824.0,8808.0,8792.0,8800.0,8792.0,8768.0,8752.0,8744.0,8816.0,8792.0,8768.0,8808.0,8800.0,8792.0,8792.0,8776.0,8776.0,8824.0,8816.0,8880.0,9008.0,9008.0,9000.0,8992.0,8992.0,8992.0,8976.0,8984.0,8984.0,9056.0,9040.0,9032.0,9024.0,9008.0,8976.0,8992.0,8984.0,8976.0,8952.0,8944.0,8936.0,8912.0,8912.0,8896.0,8888.0,8888.0,8880.0,8856.0,8880.0,8872.0,8856.0,8840.0,8840.0,8840.0,8856.0,8840.0,8832.0,8824.0,8816.0,8816.0,8808.0,8800.0,8784.0,8776.0],\"top\":[10024.0,10024.0,10000.0,9992.0,9984.0,9968.0,9968.0,9952.0,9952.0,9944.0,9936.0,9904.0,9904.0,9880.0,9888.0,9880.0,9864.0,9856.0,9856.0,9904.0,9920.0,9912.0,9904.0,9904.0,9888.0,9880.0,9896.0,9904.0,9896.0,9912.0,9944.0,9944.0,9920.0,9912.0,9904.0,9896.0,9888.0,9920.0,9920.0,9912.0,9888.0,9872.0,9864.0,9856.0,9912.0,9896.0,9888.0,9888.0,9880.0,9864.0,9872.0,9864.0,9856.0,9824.0,9832.0,9824.0,9800.0,9784.0,9760.0,9744.0,9728.0,9728.0,9744.0,9720.0,9712.0,9728.0,9720.0,9720.0,9744.0,9736.0,9736.0,9752.0,9736.0,9752.0,9752.0,9752.0,9744.0,9832.0,9808.0,9848.0,9888.0,9880.0,9856.0,9888.0,9880.0,9864.0,9848.0,9840.0,9824.0,9816.0,9816.0,9808.0,9800.0,9784.0,9808.0,9784.0,9792.0,9784.0,9776.0,9760.0,9760.0,9792.0,9824.0,9824.0,9824.0,9832.0,9808.0,9808.0,9792.0,9784.0,9776.0,9760.0,9752.0,9752.0,9736.0,9720.0,9704.0,9712.0,9736.0,9736.0,9736.0,9728.0,9704.0,9688.0,9680.0,9704.0,9696.0,9680.0,9664.0,9664.0,9680.0,9672.0,9744.0,9744.0,9728.0,9752.0,9744.0,9736.0,9736.0,9792.0,9784.0,9808.0,9800.0,9768.0,9760.0,9736.0,9720.0,9720.0,9704.0,9672.0,9672.0,9664.0,9648.0,9632.0,9608.0,9600.0,9592.0,9600.0,9592.0,9584.0,9576.0,9568.0,9560.0,9544.0,9528.0,9512.0,9488.0,9488.0,9464.0,9464.0,9464.0,9472.0,9440.0,9424.0,9424.0,9440.0,9456.0,9464.0,9480.0,9480.0,9528.0,9544.0,9552.0,9560.0,9632.0,9624.0,9632.0,9624.0,9664.0,9656.0,9720.0,9720.0,9712.0,9712.0,9720.0,9720.0,9712.0,9696.0,9680.0,9696.0,9680.0,9680.0,9656.0,9664.0,9648.0,9632.0,9624.0,9608.0,9592.0,9568.0,9560.0,9560.0,9552.0,9552.0,9536.0,9536.0,9528.0,9512.0,9536.0,9520.0,9512.0,9528.0,9528.0,9536.0,9528.0,9528.0,9512.0,9512.0,9496.0,9464.0,9456.0,9448.0,9432.0,9480.0,9536.0,9544.0,9552.0,9544.0,9528.0,9520.0,9528.0,9520.0,9512.0,9504.0,9504.0,9488.0,9464.0,9448.0,9440.0,9432.0,9424.0,9416.0,9400.0,9408.0,9400.0,9376.0,9368.0,9368.0,9360.0,9376.0,9360.0,9368.0,9384.0,9368.0,9384.0,9376.0,9368.0,9360.0,9352.0,9336.0,9320.0,9336.0,9352.0,9344.0,9336.0,9336.0,9320.0,9304.0,9296.0,9280.0,9280.0,9296.0,9280.0,9288.0,9272.0,9272.0,9288.0,9296.0,9264.0,9280.0,9272.0,9264.0,9304.0,9288.0,9272.0,9256.0,9240.0,9232.0,9248.0,9240.0,9232.0,9200.0,9184.0,9184.0,9168.0,9216.0,9208.0,9192.0,9192.0,9248.0,9240.0,9240.0,9272.0,9272.0,9272.0,9272.0,9272.0,9280.0,9272.0,9248.0,9376.0,9368.0,9352.0,9352.0,9352.0,9344.0,9320.0,9328.0,9360.0,9360.0,9344.0,9352.0,9344.0,9320.0,9296.0,9272.0,9288.0,9280.0,9272.0,9264.0,9248.0,9248.0,9232.0,9224.0,9216.0,9216.0,9312.0,9336.0,9336.0,9344.0,9336.0,9328.0,9312.0,9312.0,9320.0,9312.0,9296.0,9320.0,9296.0,9288.0,9320.0,9312.0,9304.0,9320.0,9312.0,9320.0,9312.0,9304.0,9320.0,9296.0,9296.0,9296.0,9296.0,9272.0,9256.0,9248.0,9232.0,9240.0,9232.0,9264.0,9248.0,9248.0,9232.0,9264.0,9256.0,9232.0,9208.0,9224.0,9224.0,9240.0,9232.0,9216.0,9208.0,9192.0,9224.0,9224.0,9208.0,9192.0,9184.0,9168.0,9160.0,9176.0,9176.0,9160.0,9136.0,9128.0,9112.0,9096.0,9080.0,9064.0,9048.0,9040.0,9048.0,9032.0,9112.0,9112.0,9104.0,9080.0,9136.0,9112.0,9104.0,9080.0,9128.0,9184.0,9168.0,9160.0,9144.0,9144.0,9136.0,9120.0,9080.0,9072.0,9064.0,9064.0,9048.0,9040.0,9032.0,9008.0,8992.0,8984.0,8976.0,8944.0,8952.0,8944.0,8952.0,8944.0,8936.0,8912.0,8896.0,8888.0,8920.0,8912.0,8952.0,8944.0,8960.0,8960.0,8952.0,8928.0,8968.0,8968.0,8960.0,8944.0,8920.0,8912.0,8896.0,8880.0,8856.0,8856.0,8848.0,8840.0,8896.0,8920.0,8904.0,8888.0,8896.0,8880.0,8872.0,8856.0,8840.0,8824.0,8808.0,8808.0,8784.0,8784.0,8752.0,8784.0,8784.0,8808.0,8824.0,8816.0,8816.0,8840.0,8832.0,8824.0,8808.0,8784.0,8768.0,8744.0,8744.0,8744.0,8736.0,8728.0,8720.0,8704.0,8744.0,8736.0,8768.0,8768.0,8784.0,8792.0,8784.0,8808.0,8856.0,8832.0,8832.0,8824.0,8808.0,8800.0,8784.0,8776.0,8776.0,8800.0,8776.0,8768.0,8760.0,8760.0,8816.0,8800.0,8816.0,8816.0,8792.0,8784.0,8792.0,8784.0,8768.0,8736.0,8728.0,8712.0,8704.0,8728.0,8720.0,8704.0,8688.0,8680.0,8672.0,8648.0,8696.0,8736.0,8728.0,8728.0,8720.0,8688.0,8672.0,8664.0,8656.0,8696.0,8688.0,8672.0,8672.0,8656.0,8648.0,8736.0,8720.0,8728.0,8720.0,8728.0,8784.0,8776.0,8768.0,8752.0,8768.0,8784.0,8776.0,8768.0,8832.0,8808.0,8808.0,8840.0,8832.0,8824.0,8808.0,8808.0,8800.0,8792.0,8768.0,8752.0,8824.0,8816.0,8792.0,8816.0,8808.0,8816.0,8800.0,8800.0,8784.0,8840.0,8824.0,8896.0,9024.0,9024.0,9008.0,9000.0,9000.0,9000.0,8992.0,9008.0,9008.0,9064.0,9056.0,9040.0,9032.0,9024.0,9008.0,9000.0,8992.0,8984.0,8984.0,8952.0,8944.0,8936.0,8920.0,8912.0,8904.0,8904.0,8888.0,8872.0,8888.0,8880.0,8872.0,8856.0,8864.0,8848.0,8864.0,8848.0,8840.0,8832.0,8824.0,8824.0,8816.0,8808.0,8792.0,8784.0],\"x\":{\"__ndarray__\":\"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\",\"dtype\":\"float64\",\"shape\":[642]}},\"selected\":{\"id\":\"2572\",\"type\":\"Selection\"},\"selection_policy\":{\"id\":\"2573\",\"type\":\"UnionRenderers\"}},\"id\":\"2368\",\"type\":\"ColumnDataSource\"},{\"attributes\":{\"base\":60,\"mantissas\":[1,2,5,10,15,20,30],\"max_interval\":1800000.0,\"min_interval\":1000.0,\"num_minor_ticks\":0},\"id\":\"2556\",\"type\":\"AdaptiveTicker\"},{\"attributes\":{\"callback\":null},\"id\":\"2327\",\"type\":\"DataRange1d\"},{\"attributes\":{\"bottom\":{\"field\":\"bottom\"},\"fill_color\":{\"value\":\"#F2583E\"},\"top\":{\"field\":\"top\"},\"width\":{\"value\":600000},\"x\":{\"field\":\"x\"}},\"id\":\"2369\",\"type\":\"VBar\"},{\"attributes\":{\"base\":24,\"mantissas\":[1,2,4,6,8,12],\"max_interval\":43200000.0,\"min_interval\":3600000.0,\"num_minor_ticks\":0},\"id\":\"2557\",\"type\":\"AdaptiveTicker\"},{\"attributes\":{},\"id\":\"2341\",\"type\":\"BasicTicker\"},{\"attributes\":{},\"id\":\"2548\",\"type\":\"BasicTickFormatter\"},{\"attributes\":{\"days\":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31]},\"id\":\"2558\",\"type\":\"DaysTicker\"},{\"attributes\":{\"bottom\":{\"field\":\"bottom\"},\"fill_alpha\":{\"value\":0.1},\"fill_color\":{\"value\":\"#1f77b4\"},\"line_alpha\":{\"value\":0.1},\"line_color\":{\"value\":\"#1f77b4\"},\"top\":{\"field\":\"top\"},\"width\":{\"value\":600000},\"x\":{\"field\":\"x\"}},\"id\":\"2370\",\"type\":\"VBar\"},{\"attributes\":{\"days\":[1,4,7,10,13,16,19,22,25,28]},\"id\":\"2559\",\"type\":\"DaysTicker\"},{\"attributes\":{\"data_source\":{\"id\":\"2368\",\"type\":\"ColumnDataSource\"},\"glyph\":{\"id\":\"2369\",\"type\":\"VBar\"},\"hover_glyph\":null,\"muted_glyph\":null,\"nonselection_glyph\":{\"id\":\"2370\",\"type\":\"VBar\"},\"selection_glyph\":null,\"view\":{\"id\":\"2372\",\"type\":\"CDSView\"}},\"id\":\"2371\",\"type\":\"GlyphRenderer\"},{\"attributes\":{\"source\":{\"id\":\"2368\",\"type\":\"ColumnDataSource\"}},\"id\":\"2372\",\"type\":\"CDSView\"},{\"attributes\":{\"days\":[1,8,15,22]},\"id\":\"2560\",\"type\":\"DaysTicker\"},{\"attributes\":{},\"id\":\"2331\",\"type\":\"LinearScale\"},{\"attributes\":{\"days\":[1,15]},\"id\":\"2561\",\"type\":\"DaysTicker\"},{\"attributes\":{\"callback\":null,\"data\":{\"top\":[805.5,1275.0,711.0,679.5,451.0,480.5,829.5,955.0,2228.0,1621.0,1822.0,2522.0,2590.0,2744.0,1965.0,2508.0,2354.0,2652.0,3008.0,3146.0,4156.0,4996.0,5136.0,5688.0,6560.0,5452.0,6416.0,5436.0,7648.0,7344.0,6876.0,6636.0,7112.0,7648.0,7464.0,7552.0,6160.0,6512.0,6228.0,4852.0,5460.0,4812.0,4148.0,4768.0,4008.0,3512.0,4252.0,5260.0,5672.0,4960.0,5064.0,5232.0,5524.0,4932.0,5216.0,6040.0,3978.0,5052.0,4624.0,5236.0,5056.0,5372.0,5760.0,5208.0,4736.0,3642.0,5056.0,4392.0,4272.0,3490.0,2668.0,2990.0,4056.0,4816.0,4876.0,4128.0,4320.0,4868.0,5352.0,4772.0,4048.0,6000.0,5132.0,6284.0,5256.0,6524.0,5088.0,4976.0,3082.0,3976.0,4368.0,5984.0,3944.0,4396.0,3684.0,3516.0,4996.0,5484.0,6256.0,7184.0,7368.0,6364.0,6464.0,6488.0,6376.0,5272.0,5360.0,5180.0,5268.0,3880.0,4920.0,4284.0,5916.0,6704.0,5912.0,4600.0,5236.0,6632.0,6852.0,7732.0,6412.0,8288.0,7996.0,7528.0,10968.0,9912.0,6788.0,11040.0,9624.0,9056.0,8288.0,9008.0,8616.0,11904.0,11504.0,12872.0,11320.0,11928.0,10096.0,10928.0,14048.0,12144.0,13536.0,13784.0,14568.0,14312.0,13376.0,10536.0,14312.0,12120.0,14216.0,12112.0,12696.0,13096.0,12424.0,12832.0,11896.0,12176.0,13880.0,13016.0,13648.0,13496.0,12856.0,12336.0,13648.0,12632.0,13320.0,13360.0,10488.0,12672.0,12672.0,12312.0,13288.0,13680.0,11888.0,10896.0,12120.0,13008.0,12616.0,13816.0,12088.0,11800.0,12288.0,14608.0,11712.0,14024.0,13016.0,12472.0,13976.0,12248.0,10952.0,12344.0,11968.0,7536.0,7068.0,9096.0,6596.0,8856.0,8456.0,8664.0,9672.0,8384.0,9704.0,8520.0,7500.0,7456.0,6848.0,6496.0,7120.0,6448.0,7496.0,5988.0,6736.0,5476.0,5128.0,4768.0,7448.0,7044.0,7704.0,6860.0,5264.0,5512.0,4968.0,5168.0,7876.0,8028.0,8264.0,7184.0,9224.0,8952.0,10496.0,8216.0,7128.0,10088.0,10232.0,8288.0,9712.0,9184.0,10648.0,9720.0,10896.0,11288.0,10624.0,10744.0,11616.0,9256.0,10760.0,11384.0,9776.0,10400.0,11224.0,10976.0,11912.0,10560.0,10152.0,10328.0,11776.0,12624.0,13168.0,11600.0,12688.0,12976.0,10880.0,10008.0,9248.0,8552.0,7948.0,8108.0,9576.0,9056.0,8136.0,8544.0,7768.0,8912.0,7348.0,6948.0,7888.0,7008.0,10360.0,8672.0,8896.0,9824.0,10800.0,10296.0,9816.0,11640.0,11152.0,11240.0,11800.0,11736.0,10504.0,12224.0,12816.0,13432.0,13112.0,15856.0,13760.0,11976.0,11592.0,12968.0,13680.0,12088.0,14960.0,14952.0,13048.0,16608.0,15264.0,13336.0,13336.0,13408.0,12848.0,16008.0,15232.0,13136.0,12216.0,14528.0,12656.0,14760.0,14032.0,16576.0,12704.0,12176.0,14608.0,13072.0,17664.0,18112.0,15240.0,18448.0,16280.0,14264.0,15648.0,16136.0,14896.0,14360.0,14320.0,14616.0,16280.0,13232.0,15016.0,12704.0,13312.0,13184.0,12848.0,13736.0,13504.0,12768.0,13576.0,12480.0,13800.0,15032.0,11888.0,12184.0,10720.0,12880.0,13136.0,15336.0,15000.0,14656.0,14024.0,12032.0,12704.0,12288.0,10920.0,9840.0,11136.0,9832.0,9176.0,8488.0,10544.0,11040.0,11984.0,13760.0,12128.0,15424.0,13288.0,15544.0,12984.0,15672.0,17104.0,14464.0,13936.0,18208.0,17120.0,13464.0,16528.0,15288.0,16064.0,15720.0,19856.0,17824.0,19168.0,17040.0,20080.0,18208.0,16656.0,22496.0,20304.0,20464.0,16800.0,23568.0,21536.0,19824.0,25520.0,23872.0,21856.0,20736.0,23792.0,22512.0,25136.0,20240.0,24544.0,27952.0,25024.0,23264.0,23792.0,29632.0,25088.0,24848.0,26864.0,26656.0,29904.0,23120.0,29952.0,33920.0,31616.0,28576.0,24336.0,27680.0,23488.0,28320.0,29600.0,27072.0,22640.0,23776.0,24560.0,30432.0,29232.0,24176.0,22128.0,22384.0,23648.0,26528.0,27216.0,23424.0,24912.0,22512.0,25904.0,23984.0,22992.0,19968.0,25504.0,21952.0,23728.0,22240.0,26016.0,22240.0,22800.0,19424.0,23920.0,23232.0,23968.0,16656.0,24944.0,21328.0,21264.0,20080.0,20576.0,20432.0,14872.0,18464.0,13584.0,19504.0,16016.0,18192.0,18704.0,20992.0,19280.0,17904.0,16560.0,15464.0,18480.0,18432.0,20480.0,16432.0,22272.0,21504.0,16032.0,18128.0,17312.0,16352.0,14576.0,15960.0,17504.0,16880.0,14368.0,16376.0,15864.0,14808.0,17568.0,17568.0,16352.0,15104.0,13872.0,15496.0,13368.0,14056.0,13712.0,16312.0,13480.0,12904.0,12704.0,14088.0,16704.0,17120.0,16024.0,14152.0,12696.0,14856.0,14872.0,14992.0,18224.0,18352.0,16360.0,15512.0,17632.0,17040.0,12280.0,14944.0,13792.0,15664.0,14792.0,13304.0,16656.0,18048.0,17328.0,17424.0,19248.0,16768.0,19344.0,18272.0,18832.0,24784.0,18192.0,23808.0,21376.0,20032.0,24800.0,21888.0,23296.0,26816.0,21104.0,20368.0,23504.0,26000.0,23968.0,27392.0,26912.0,25232.0,25664.0,21920.0,20192.0,19840.0,23664.0,21264.0,23472.0,24432.0,26432.0,20080.0,19792.0,18704.0,22208.0,21952.0,18016.0,24448.0,22352.0,25296.0,20432.0,20416.0,21664.0,19728.0,28368.0,20784.0,28880.0,23840.0,26400.0,18784.0,24560.0,23264.0,28928.0,24944.0,24256.0,27472.0,26336.0,27504.0,23008.0,25616.0,29056.0,28144.0,28016.0,23792.0,27296.0,23648.0,25120.0,27808.0,24000.0,29792.0,24368.0,22608.0,24576.0,25216.0,24096.0,22192.0,20688.0,27168.0,24352.0,21760.0,20288.0,19792.0,19632.0,22352.0,24000.0,27920.0,27056.0,21792.0,21072.0,22304.0,22496.0,22944.0,25248.0,23984.0,27392.0,25952.0,24272.0,31232.0,29280.0,23104.0,27536.0,33536.0,24048.0,29072.0,25440.0,28848.0,28880.0,33568.0,27184.0,29840.0,27696.0,28096.0,30272.0,29856.0,30592.0,30352.0,33408.0,29888.0,28048.0,28800.0,27520.0,32464.0,30480.0,31504.0,29216.0,31088.0,30048.0,29056.0,35616.0,30160.0,28592.0,29328.0,30272.0,29328.0,25936.0,28640.0,31616.0,30000.0,29376.0,29072.0,30272.0,25168.0,32768.0,32512.0,31824.0,27360.0,33888.0,31744.0,33856.0,26848.0,29968.0,28720.0,30736.0,33952.0,33856.0,27840.0,32896.0,31744.0,39200.0,33888.0,34208.0,36640.0,32336.0,41344.0,32832.0,33408.0,28960.0,31088.0,33024.0,42752.0,39456.0,31664.0,36320.0,29680.0,32176.0,30272.0,35200.0,36768.0,37792.0,35200.0,32960.0,34624.0,34400.0,33600.0,34880.0,36352.0,38592.0,32560.0,31056.0,37632.0,36192.0,35200.0,31840.0,32032.0,28080.0,35584.0,29440.0,30288.0,36576.0,31168.0,35808.0,29232.0,39296.0,31952.0,37536.0,41824.0,42240.0,37312.0,36704.0,42272.0,37280.0,32592.0,40160.0,37248.0,39520.0,38688.0,35712.0,39872.0,38144.0,44992.0,31824.0,41728.0,34240.0,35904.0,42240.0,44960.0,38688.0,35872.0,36512.0,33088.0,42912.0,38176.0,37728.0,33088.0,35008.0,38528.0,35072.0,31648.0,30112.0,35424.0,37088.0,37664.0,39360.0,35456.0,33376.0,30480.0,34048.0,37952.0,33344.0,33088.0,41888.0,38112.0,34560.0,37184.0,34080.0,37056.0,25728.0,34592.0,30752.0,31776.0,33728.0,38912.0,32240.0,39520.0,40128.0,40832.0,30368.0,29280.0,37056.0,35648.0,36576.0,36320.0,30656.0,32432.0,31808.0,33504.0,33824.0,36640.0,30496.0,37408.0,35744.0,35360.0,34752.0,32000.0,31744.0,34272.0,30576.0,32896.0,31376.0,26176.0,31968.0,28208.0,27296.0,34272.0,31808.0,30800.0,27760.0,34080.0,35168.0,30560.0,36384.0,27456.0,30304.0,30944.0,29856.0,29856.0,33792.0,24368.0,33120.0,33632.0,26496.0,28928.0,35424.0,25568.0,25248.0,28512.0,35776.0,36992.0,32592.0,31248.0,30480.0,32768.0,33440.0,37696.0,30464.0,38400.0,30912.0,30560.0,37952.0,32000.0,35360.0,38528.0,41280.0,37120.0,36160.0,33856.0,38176.0,33600.0,31776.0,34944.0,28848.0,40832.0,34624.0,33728.0,37088.0,37696.0,42336.0,31696.0,38976.0,34752.0,39840.0,34400.0,37952.0,35616.0,34816.0,31392.0,33440.0,35712.0,38176.0,37472.0,31728.0,28880.0,34016.0,32752.0,30368.0,25072.0,25664.0,28048.0,25952.0,31232.0,32336.0,31664.0,31232.0,27520.0,29760.0,32224.0,32400.0,35168.0,39744.0,30432.0,27888.0,34688.0,31472.0,34912.0,32624.0,37152.0,31808.0,29248.0,34528.0,29344.0,31520.0,31424.0,34080.0,30784.0,36256.0,34784.0,35232.0,33504.0,27248.0,33728.0,38400.0,30096.0,37056.0,32800.0,26144.0,34240.0,31712.0,37824.0,32864.0,33696.0,27808.0,28800.0,36128.0,33344.0,33760.0,34720.0,33952.0,29200.0,30336.0,31328.0,37792.0,35104.0,33184.0,33088.0,35488.0,30704.0,30560.0,25456.0,30896.0,33856.0,28544.0,41888.0,35744.0,37504.0,34272.0,40096.0,40864.0,41248.0,40288.0,40608.0,30608.0,31568.0,39872.0,37888.0,36320.0,42560.0,42432.0,39776.0,33696.0,31632.0,36992.0,31376.0,38336.0,39936.0,36960.0,40800.0,35776.0,35552.0,35328.0,33280.0,39712.0,39712.0,34400.0,35424.0,41824.0,34688.0,38784.0,41600.0,35520.0,34944.0,37920.0,36864.0,39296.0,40000.0,35072.0,41120.0,39104.0,45088.0,37280.0,40960.0,41952.0,34528.0,45280.0,40000.0,37824.0,43104.0,40416.0,38272.0,39200.0,31920.0,37824.0,38720.0,33920.0,40352.0,39424.0,38688.0,45184.0,38368.0,34656.0,35072.0,32864.0,37248.0,36352.0,51328.0,38752.0,33184.0,41024.0,38720.0,37728.0,37120.0,40544.0,44096.0,40864.0,45664.0,35520.0,41504.0,36544.0,51936.0,40576.0,46304.0,45024.0,51616.0,43840.0,39104.0,44416.0,38400.0,46848.0,50528.0,38528.0,32000.0,45728.0,46848.0,40704.0,41216.0,43360.0,48288.0,46176.0,35264.0,33920.0,41824.0,44704.0,50528.0,48704.0,44064.0,37152.0,48320.0,36256.0,41728.0,46592.0,51360.0,39168.0,31456.0,41728.0,43296.0,40416.0,41984.0,40480.0,47104.0,51456.0,41408.0,50464.0,44896.0,43808.0,48192.0,45920.0,40032.0,49216.0,36544.0,45664.0,37344.0,40512.0,34944.0,36864.0,42336.0,39072.0,44192.0,39008.0,36864.0,46464.0,43424.0,46432.0,43424.0,39808.0,38272.0,40896.0,48064.0,46016.0,44128.0,43904.0,37568.0,48160.0,45504.0,40768.0,46048.0,53056.0,53024.0,48192.0,49376.0,54496.0,52096.0,45920.0,43008.0,49472.0,44928.0,53152.0,50336.0,39136.0,49120.0,50688.0,44224.0,48672.0,53568.0,49120.0,49632.0,55168.0,55936.0,44896.0,56608.0,42080.0,42208.0,53408.0,51744.0,48032.0,48928.0,47552.0,\"Infinity\",54624.0,50784.0,58848.0,46304.0,58432.0,52384.0,52032.0,47872.0,61792.0,63200.0,50816.0,54432.0,60256.0,42784.0,48832.0,51072.0,52608.0,46336.0,47584.0,52928.0,46144.0,52416.0,61664.0,50752.0,57184.0,55360.0,45120.0,46848.0,57824.0,49024.0,38400.0,53920.0,49056.0,48448.0,46048.0,51040.0,54176.0,43328.0,47808.0,42656.0,48704.0,48928.0,\"Infinity\",48896.0,50624.0,40064.0,62208.0,56384.0,56416.0,55680.0,57792.0,51168.0,49696.0,55808.0,58080.0,54848.0,52896.0,52256.0,51936.0,53632.0,50592.0,56352.0,42944.0,53440.0,64864.0,54816.0,55328.0,54016.0,59200.0,57824.0,50144.0,57728.0,45440.0,58816.0,61440.0,\"Infinity\",56064.0,55936.0,48736.0,63040.0,55648.0,51360.0,49600.0,42144.0,49088.0,\"Infinity\",63360.0,\"Infinity\",62368.0,63584.0,47392.0,57952.0,44416.0,64576.0,58944.0,53120.0,\"Infinity\",56928.0,57184.0,56416.0,60256.0,58080.0,59680.0,50720.0,50912.0,60576.0,61984.0,\"Infinity\",62528.0,54912.0,53696.0,\"Infinity\",64896.0,60800.0,58848.0,64640.0,\"Infinity\",62816.0,64416.0,49216.0,64928.0,57344.0,60800.0,58400.0,51040.0,64576.0,65472.0,49568.0,62624.0,59040.0,56416.0,56608.0,62816.0,60160.0,63296.0,\"Infinity\",52320.0,63136.0,58624.0,64992.0,53248.0,56672.0,54784.0,56480.0,63808.0,54144.0,58944.0,56896.0,51456.0,44000.0,52096.0,64832.0,58432.0,48864.0,62592.0,64832.0,62560.0,62112.0,60768.0,55648.0,51616.0,55360.0,53440.0,64544.0,61984.0,55936.0,59616.0,60992.0,\"Infinity\",60160.0,56096.0,\"Infinity\",61952.0,49600.0,58400.0,59776.0,48672.0,62848.0,49184.0,50304.0,64000.0,54432.0,63264.0,\"Infinity\",61440.0,55008.0,61696.0,\"Infinity\",\"Infinity\",58592.0,\"Infinity\",58208.0,59200.0,51520.0,63392.0,\"Infinity\",60032.0,61920.0,57408.0,\"Infinity\",\"Infinity\",62880.0,60352.0,56544.0,51936.0,58880.0,56288.0,54368.0,57216.0,59808.0,60640.0,64992.0,61344.0,\"Infinity\",55392.0,60864.0,\"Infinity\",\"Infinity\",62176.0,\"Infinity\",\"Infinity\",\"Infinity\",61664.0,54208.0,63552.0,58080.0,\"Infinity\",\"Infinity\",62368.0,53056.0,62752.0,63200.0,\"Infinity\",57504.0,54880.0,63808.0,62208.0,54560.0,\"Infinity\",62848.0,\"Infinity\",64064.0,50944.0,60256.0,59936.0,\"Infinity\",\"Infinity\",60704.0,58016.0,62560.0,\"Infinity\",58368.0,54816.0,\"Infinity\",59968.0,61440.0,51168.0,\"Infinity\",57568.0,56832.0,61376.0,53056.0,59072.0,63584.0,59232.0,\"Infinity\",62400.0,60768.0,\"Infinity\",60480.0,59744.0,\"Infinity\",59040.0,\"Infinity\",63616.0,\"Infinity\",\"Infinity\",\"Infinity\",64928.0,62816.0,60608.0,60416.0,59808.0,53568.0,\"Infinity\",54304.0,56384.0,\"Infinity\",62464.0,54976.0,60704.0,61344.0,62336.0,65312.0,61280.0,60768.0,55776.0,64064.0,60256.0,52608.0,56960.0,62240.0,48128.0,57440.0,57600.0,62400.0,60512.0,61280.0,55776.0,53312.0,57280.0,64224.0,51840.0,\"Infinity\",58912.0,\"Infinity\",56352.0,54848.0,58848.0,51136.0,58944.0,60192.0,51136.0,54208.0,62944.0,\"Infinity\",61632.0,65024.0,\"Infinity\",55808.0,57312.0,62912.0,60480.0,55328.0,62080.0,\"Infinity\",55808.0,58784.0,54208.0,60992.0,64000.0,64448.0,57824.0,\"Infinity\",63744.0,59008.0,\"Infinity\",56032.0,61920.0,61088.0,59680.0,63584.0,59840.0,52320.0,61280.0,64512.0,44192.0,64096.0,64832.0,57728.0,\"Infinity\",45728.0,48704.0,53824.0,56512.0,\"Infinity\",55232.0,58976.0,55040.0,57920.0,51616.0,61760.0,59200.0,58848.0,61088.0,64512.0,59616.0,61280.0,61216.0,57088.0,58944.0,61216.0,45376.0,51328.0,52960.0,48032.0,53984.0,51744.0,46720.0,61408.0,57216.0,65152.0,63616.0,56096.0,55904.0,60320.0,62720.0,51840.0,58912.0,56320.0,62720.0,57952.0,48672.0,53120.0,47904.0,48512.0,41408.0,47872.0,48416.0,53376.0,37728.0,56608.0,50208.0,42272.0,54944.0,52064.0,48704.0,45792.0,43744.0,54656.0,53312.0,44224.0,54016.0,45376.0,50112.0,50944.0,45504.0,47968.0,45248.0,46656.0,51040.0,46560.0,52960.0,42752.0,44928.0,39168.0,41632.0,50656.0,43904.0,53696.0,41056.0,54144.0,53728.0,44384.0,53664.0,48064.0,56032.0,47552.0,54176.0,42976.0,47008.0,51264.0,45504.0,46624.0,55872.0,51392.0,49856.0,46432.0,49280.0,48480.0,52800.0,41984.0,59456.0,61024.0,52896.0,55008.0,63392.0,55424.0,57472.0,47008.0,56320.0,56256.0,56992.0,56672.0,53120.0,49504.0,49696.0,51392.0,50976.0,59488.0,45152.0,61632.0,61408.0,64192.0,57088.0,53856.0,56320.0,53984.0,47648.0,49536.0,48288.0,55712.0,58464.0,65024.0,59584.0,56704.0,50016.0,55424.0,54208.0,63072.0,65024.0,60736.0,59840.0,61248.0,37632.0],\"x\":{\"__ndarray__\":\"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\",\"dtype\":\"float64\",\"shape\":[1667]}},\"selected\":{\"id\":\"2587\",\"type\":\"Selection\"},\"selection_policy\":{\"id\":\"2588\",\"type\":\"UnionRenderers\"}},\"id\":\"2406\",\"type\":\"ColumnDataSource\"},{\"attributes\":{\"months\":[0,1,2,3,4,5,6,7,8,9,10,11]},\"id\":\"2562\",\"type\":\"MonthsTicker\"},{\"attributes\":{\"formatter\":{\"id\":\"2550\",\"type\":\"DatetimeTickFormatter\"},\"major_label_orientation\":0.7853981633974483,\"ticker\":{\"id\":\"2336\",\"type\":\"DatetimeTicker\"}},\"id\":\"2335\",\"type\":\"DatetimeAxis\"},{\"attributes\":{\"text\":\"Simulated Volume\"},\"id\":\"2374\",\"type\":\"Title\"},{\"attributes\":{\"months\":[0,2,4,6,8,10]},\"id\":\"2563\",\"type\":\"MonthsTicker\"},{\"attributes\":{},\"id\":\"2333\",\"type\":\"LinearScale\"},{\"attributes\":{\"callback\":null},\"id\":\"2377\",\"type\":\"DataRange1d\"},{\"attributes\":{\"months\":[0,4,8]},\"id\":\"2564\",\"type\":\"MonthsTicker\"},{\"attributes\":{},\"id\":\"2379\",\"type\":\"LinearScale\"},{\"attributes\":{\"months\":[0,6]},\"id\":\"2565\",\"type\":\"MonthsTicker\"},{\"attributes\":{\"callback\":null},\"id\":\"2329\",\"type\":\"DataRange1d\"},{\"attributes\":{},\"id\":\"2381\",\"type\":\"LinearScale\"},{\"attributes\":{},\"id\":\"2566\",\"type\":\"YearsTicker\"},{\"attributes\":{\"axis_label\":\"Price ($)\",\"formatter\":{\"id\":\"2548\",\"type\":\"BasicTickFormatter\"},\"ticker\":{\"id\":\"2341\",\"type\":\"BasicTicker\"}},\"id\":\"2340\",\"type\":\"LinearAxis\"},{\"attributes\":{\"axis_label\":\"Time\",\"formatter\":{\"id\":\"2554\",\"type\":\"DatetimeTickFormatter\"},\"ticker\":{\"id\":\"2384\",\"type\":\"DatetimeTicker\"}},\"id\":\"2383\",\"type\":\"DatetimeAxis\"},{\"attributes\":{\"bottom_units\":\"screen\",\"fill_alpha\":{\"value\":0.5},\"fill_color\":{\"value\":\"lightgrey\"},\"left_units\":\"screen\",\"level\":\"overlay\",\"line_alpha\":{\"value\":1.0},\"line_color\":{\"value\":\"black\"},\"line_dash\":[4,4],\"line_width\":{\"value\":2},\"render_mode\":\"css\",\"right_units\":\"screen\",\"top_units\":\"screen\"},\"id\":\"2567\",\"type\":\"BoxAnnotation\"},{\"attributes\":{\"grid_line_alpha\":0.3,\"ticker\":{\"id\":\"2336\",\"type\":\"DatetimeTicker\"}},\"id\":\"2339\",\"type\":\"Grid\"},{\"attributes\":{\"num_minor_ticks\":5,\"tickers\":[{\"id\":\"2574\",\"type\":\"AdaptiveTicker\"},{\"id\":\"2575\",\"type\":\"AdaptiveTicker\"},{\"id\":\"2576\",\"type\":\"AdaptiveTicker\"},{\"id\":\"2577\",\"type\":\"DaysTicker\"},{\"id\":\"2578\",\"type\":\"DaysTicker\"},{\"id\":\"2579\",\"type\":\"DaysTicker\"},{\"id\":\"2580\",\"type\":\"DaysTicker\"},{\"id\":\"2581\",\"type\":\"MonthsTicker\"},{\"id\":\"2582\",\"type\":\"MonthsTicker\"},{\"id\":\"2583\",\"type\":\"MonthsTicker\"},{\"id\":\"2584\",\"type\":\"MonthsTicker\"},{\"id\":\"2585\",\"type\":\"YearsTicker\"}]},\"id\":\"2384\",\"type\":\"DatetimeTicker\"},{\"attributes\":{},\"id\":\"2568\",\"type\":\"Selection\"},{\"attributes\":{\"overlay\":{\"id\":\"2567\",\"type\":\"BoxAnnotation\"}},\"id\":\"2347\",\"type\":\"BoxZoomTool\"},{\"attributes\":{\"ticker\":{\"id\":\"2384\",\"type\":\"DatetimeTicker\"}},\"id\":\"2387\",\"type\":\"Grid\"},{\"attributes\":{},\"id\":\"2569\",\"type\":\"UnionRenderers\"},{\"attributes\":{},\"id\":\"2348\",\"type\":\"CrosshairTool\"},{\"attributes\":{\"axis_label\":\"Volume\",\"formatter\":{\"id\":\"2552\",\"type\":\"BasicTickFormatter\"},\"ticker\":{\"id\":\"2389\",\"type\":\"BasicTicker\"}},\"id\":\"2388\",\"type\":\"LinearAxis\"},{\"attributes\":{},\"id\":\"2570\",\"type\":\"Selection\"},{\"attributes\":{},\"id\":\"2349\",\"type\":\"ResetTool\"},{\"attributes\":{\"below\":[{\"id\":\"2335\",\"type\":\"DatetimeAxis\"}],\"center\":[{\"id\":\"2339\",\"type\":\"Grid\"},{\"id\":\"2344\",\"type\":\"Grid\"}],\"left\":[{\"id\":\"2340\",\"type\":\"LinearAxis\"}],\"renderers\":[{\"id\":\"2361\",\"type\":\"GlyphRenderer\"},{\"id\":\"2366\",\"type\":\"GlyphRenderer\"},{\"id\":\"2371\",\"type\":\"GlyphRenderer\"}],\"title\":{\"id\":\"2325\",\"type\":\"Title\"},\"toolbar\":{\"id\":\"2351\",\"type\":\"Toolbar\"},\"x_range\":{\"id\":\"2327\",\"type\":\"DataRange1d\"},\"x_scale\":{\"id\":\"2331\",\"type\":\"LinearScale\"},\"y_range\":{\"id\":\"2329\",\"type\":\"DataRange1d\"},\"y_scale\":{\"id\":\"2333\",\"type\":\"LinearScale\"}},\"id\":\"2324\",\"subtype\":\"Figure\",\"type\":\"Plot\"},{\"attributes\":{},\"id\":\"2389\",\"type\":\"BasicTicker\"},{\"attributes\":{},\"id\":\"2571\",\"type\":\"UnionRenderers\"},{\"attributes\":{},\"id\":\"2350\",\"type\":\"SaveTool\"},{\"attributes\":{\"dimension\":1,\"ticker\":{\"id\":\"2389\",\"type\":\"BasicTicker\"}},\"id\":\"2392\",\"type\":\"Grid\"},{\"attributes\":{},\"id\":\"2572\",\"type\":\"Selection\"},{\"attributes\":{\"active_drag\":\"auto\",\"active_inspect\":\"auto\",\"active_multi\":null,\"active_scroll\":\"auto\",\"active_tap\":\"auto\",\"tools\":[{\"id\":\"2345\",\"type\":\"PanTool\"},{\"id\":\"2346\",\"type\":\"WheelZoomTool\"},{\"id\":\"2347\",\"type\":\"BoxZoomTool\"},{\"id\":\"2348\",\"type\":\"CrosshairTool\"},{\"id\":\"2349\",\"type\":\"ResetTool\"},{\"id\":\"2350\",\"type\":\"SaveTool\"}]},\"id\":\"2351\",\"type\":\"Toolbar\"},{\"attributes\":{\"fill_alpha\":{\"value\":0.3},\"fill_color\":{\"value\":\"#72B5C8\"},\"line_alpha\":{\"value\":0.3},\"line_color\":{\"value\":\"#72B5C8\"},\"top\":{\"field\":\"top\"},\"width\":{\"value\":600000},\"x\":{\"field\":\"x\"}},\"id\":\"2407\",\"type\":\"VBar\"},{\"attributes\":{},\"id\":\"2573\",\"type\":\"UnionRenderers\"},{\"attributes\":{\"callback\":null,\"data\":{\"x0\":{\"__ndarray__\":\"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\",\"dtype\":\"float64\",\"shape\":[1667]},\"x1\":{\"__ndarray__\":\"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\",\"dtype\":\"float64\",\"shape\":[1667]},\"y0\":[10024.0,10032.0,10024.0,10016.0,10024.0,10000.0,9992.0,9984.0,9968.0,9968.0,9968.0,9952.0,9960.0,9952.0,9952.0,9960.0,9952.0,9952.0,9944.0,9944.0,9936.0,9912.0,9912.0,9880.0,9872.0,9880.0,9888.0,9896.0,9880.0,9864.0,9864.0,9864.0,9856.0,9832.0,9848.0,9848.0,9856.0,9872.0,9896.0,9904.0,9904.0,9920.0,9920.0,9920.0,9920.0,9936.0,9928.0,9912.0,9904.0,9904.0,9904.0,9896.0,9880.0,9896.0,9896.0,9880.0,9904.0,9904.0,9896.0,9896.0,9896.0,9896.0,9904.0,9904.0,9912.0,9920.0,9912.0,9912.0,9928.0,9928.0,9952.0,9952.0,9944.0,9920.0,9904.0,9920.0,9912.0,9912.0,9904.0,9896.0,9896.0,9896.0,9904.0,9888.0,9888.0,9888.0,9904.0,9912.0,9928.0,9920.0,9896.0,9904.0,9912.0,9912.0,9920.0,9920.0,9912.0,9912.0,9896.0,9872.0,9864.0,9856.0,9872.0,9872.0,9880.0,9896.0,9904.0,9904.0,9904.0,9912.0,9904.0,9896.0,9896.0,9888.0,9888.0,9888.0,9880.0,9864.0,9880.0,9872.0,9864.0,9864.0,9856.0,9832.0,9824.0,9832.0,9824.0,9816.0,9824.0,9824.0,9832.0,9832.0,9824.0,9800.0,9784.0,9760.0,9752.0,9760.0,9760.0,9768.0,9760.0,9744.0,9720.0,9728.0,9736.0,9728.0,9728.0,9728.0,9736.0,9744.0,9744.0,9736.0,9728.0,9728.0,9720.0,9704.0,9712.0,9712.0,9712.0,9720.0,9728.0,9736.0,9720.0,9712.0,9720.0,9728.0,9736.0,9744.0,9744.0,9736.0,9736.0,9736.0,9736.0,9744.0,9752.0,9760.0,9736.0,9728.0,9752.0,9752.0,9752.0,9760.0,9752.0,9752.0,9760.0,9752.0,9752.0,9752.0,9744.0,9760.0,9768.0,9784.0,9792.0,9824.0,9832.0,9848.0,9832.0,9832.0,9808.0,9800.0,9808.0,9816.0,9824.0,9840.0,9840.0,9840.0,9848.0,9848.0,9864.0,9872.0,9864.0,9872.0,9880.0,9888.0,9896.0,9888.0,9864.0,9856.0,9856.0,9872.0,9888.0,9888.0,9888.0,9880.0,9872.0,9848.0,9840.0,9840.0,9832.0,9832.0,9824.0,9832.0,9824.0,9824.0,9800.0,9816.0,9816.0,9816.0,9808.0,9800.0,9784.0,9784.0,9784.0,9784.0,9808.0,9808.0,9784.0,9784.0,9784.0,9792.0,9800.0,9792.0,9784.0,9784.0,9784.0,9784.0,9784.0,9760.0,9760.0,9768.0,9768.0,9768.0,9792.0,9792.0,9824.0,9832.0,9832.0,9832.0,9832.0,9824.0,9824.0,9824.0,9832.0,9840.0,9848.0,9848.0,9840.0,9832.0,9808.0,9808.0,9808.0,9792.0,9776.0,9792.0,9784.0,9776.0,9760.0,9760.0,9752.0,9744.0,9760.0,9752.0,9736.0,9720.0,9712.0,9704.0,9704.0,9712.0,9720.0,9720.0,9712.0,9712.0,9728.0,9728.0,9736.0,9736.0,9744.0,9736.0,9736.0,9744.0,9728.0,9704.0,9712.0,9712.0,9704.0,9688.0,9688.0,9680.0,9672.0,9704.0,9712.0,9704.0,9696.0,9680.0,9664.0,9672.0,9664.0,9664.0,9664.0,9680.0,9680.0,9680.0,9688.0,9688.0,9680.0,9672.0,9680.0,9704.0,9712.0,9720.0,9744.0,9752.0,9744.0,9744.0,9744.0,9728.0,9720.0,9720.0,9728.0,9736.0,9752.0,9752.0,9752.0,9744.0,9736.0,9728.0,9728.0,9736.0,9736.0,9736.0,9752.0,9768.0,9792.0,9808.0,9784.0,9792.0,9808.0,9816.0,9800.0,9776.0,9768.0,9760.0,9736.0,9720.0,9704.0,9720.0,9728.0,9712.0,9672.0,9672.0,9672.0,9672.0,9680.0,9672.0,9672.0,9664.0,9648.0,9640.0,9640.0,9608.0,9600.0,9608.0,9600.0,9600.0,9608.0,9584.0,9592.0,9600.0,9584.0,9584.0,9576.0,9568.0,9560.0,9544.0,9552.0,9552.0,9544.0,9528.0,9512.0,9496.0,9488.0,9464.0,9464.0,9472.0,9472.0,9464.0,9464.0,9480.0,9472.0,9440.0,9440.0,9424.0,9424.0,9424.0,9416.0,9440.0,9440.0,9448.0,9456.0,9456.0,9456.0,9448.0,9472.0,9464.0,9456.0,9464.0,9464.0,9488.0,9488.0,9480.0,9480.0,9480.0,9488.0,9488.0,9504.0,9512.0,9504.0,9512.0,9528.0,9528.0,9536.0,9536.0,9544.0,9544.0,9552.0,9560.0,9552.0,9552.0,9552.0,9560.0,9560.0,9568.0,9568.0,9560.0,9560.0,9576.0,9600.0,9608.0,9608.0,9632.0,9640.0,9640.0,9640.0,9624.0,9624.0,9616.0,9632.0,9632.0,9632.0,9640.0,9640.0,9624.0,9608.0,9608.0,9608.0,9624.0,9632.0,9640.0,9648.0,9664.0,9672.0,9656.0,9656.0,9648.0,9648.0,9672.0,9680.0,9680.0,9680.0,9680.0,9688.0,9696.0,9728.0,9720.0,9728.0,9720.0,9704.0,9712.0,9720.0,9720.0,9712.0,9712.0,9712.0,9704.0,9720.0,9720.0,9720.0,9720.0,9720.0,9696.0,9680.0,9656.0,9672.0,9672.0,9672.0,9688.0,9688.0,9696.0,9696.0,9680.0,9680.0,9680.0,9664.0,9648.0,9664.0,9664.0,9648.0,9632.0,9624.0,9608.0,9592.0,9584.0,9568.0,9552.0,9560.0,9560.0,9560.0,9552.0,9552.0,9552.0,9560.0,9536.0,9544.0,9536.0,9528.0,9528.0,9512.0,9504.0,9520.0,9536.0,9536.0,9528.0,9512.0,9512.0,9512.0,9528.0,9536.0,9536.0,9520.0,9528.0,9528.0,9512.0,9512.0,9520.0,9528.0,9544.0,9544.0,9536.0,9536.0,9536.0,9528.0,9528.0,9512.0,9512.0,9512.0,9504.0,9496.0,9496.0,9472.0,9464.0,9456.0,9448.0,9448.0,9448.0,9432.0,9432.0,9440.0,9432.0,9432.0,9440.0,9464.0,9480.0,9488.0,9472.0,9496.0,9504.0,9520.0,9536.0,9536.0,9520.0,9544.0,9552.0,9552.0,9552.0,9544.0,9528.0,9536.0,9536.0,9528.0,9520.0,9520.0,9536.0,9520.0,9520.0,9536.0,9536.0,9520.0,9520.0,9504.0,9504.0,9504.0,9488.0,9464.0,9456.0,9448.0,9440.0,9432.0,9432.0,9424.0,9416.0,9408.0,9392.0,9408.0,9408.0,9400.0,9376.0,9368.0,9376.0,9376.0,9368.0,9368.0,9368.0,9368.0,9360.0,9376.0,9376.0,9368.0,9368.0,9368.0,9368.0,9384.0,9384.0,9384.0,9368.0,9384.0,9392.0,9384.0,9384.0,9384.0,9376.0,9384.0,9384.0,9376.0,9360.0,9368.0,9360.0,9352.0,9336.0,9320.0,9296.0,9304.0,9320.0,9328.0,9336.0,9336.0,9336.0,9352.0,9360.0,9344.0,9344.0,9336.0,9344.0,9328.0,9312.0,9312.0,9296.0,9280.0,9272.0,9280.0,9280.0,9288.0,9280.0,9272.0,9280.0,9296.0,9304.0,9296.0,9280.0,9280.0,9288.0,9288.0,9272.0,9272.0,9272.0,9280.0,9272.0,9288.0,9288.0,9296.0,9296.0,9272.0,9256.0,9272.0,9272.0,9288.0,9280.0,9272.0,9264.0,9264.0,9280.0,9288.0,9312.0,9312.0,9304.0,9296.0,9280.0,9256.0,9248.0,9240.0,9232.0,9232.0,9232.0,9208.0,9216.0,9232.0,9248.0,9248.0,9248.0,9240.0,9232.0,9200.0,9192.0,9168.0,9184.0,9184.0,9176.0,9160.0,9160.0,9168.0,9184.0,9192.0,9192.0,9192.0,9208.0,9208.0,9216.0,9216.0,9208.0,9192.0,9184.0,9192.0,9200.0,9192.0,9216.0,9224.0,9224.0,9216.0,9224.0,9248.0,9248.0,9240.0,9224.0,9224.0,9240.0,9248.0,9248.0,9248.0,9240.0,9248.0,9248.0,9264.0,9272.0,9280.0,9272.0,9272.0,9272.0,9272.0,9272.0,9272.0,9272.0,9272.0,9272.0,9264.0,9280.0,9280.0,9272.0,9256.0,9248.0,9248.0,9256.0,9264.0,9272.0,9280.0,9288.0,9288.0,9312.0,9328.0,9336.0,9352.0,9368.0,9376.0,9384.0,9376.0,9384.0,9376.0,9368.0,9360.0,9344.0,9352.0,9352.0,9368.0,9352.0,9344.0,9320.0,9328.0,9336.0,9328.0,9328.0,9336.0,9352.0,9368.0,9360.0,9360.0,9368.0,9368.0,9360.0,9344.0,9352.0,9352.0,9360.0,9352.0,9352.0,9344.0,9344.0,9320.0,9288.0,9296.0,9296.0,9280.0,9280.0,9280.0,9272.0,9280.0,9288.0,9288.0,9296.0,9296.0,9272.0,9264.0,9280.0,9288.0,9280.0,9272.0,9280.0,9272.0,9264.0,9248.0,9248.0,9256.0,9264.0,9248.0,9240.0,9232.0,9216.0,9216.0,9208.0,9216.0,9216.0,9216.0,9248.0,9264.0,9264.0,9280.0,9288.0,9296.0,9312.0,9312.0,9304.0,9304.0,9320.0,9336.0,9344.0,9336.0,9328.0,9336.0,9344.0,9344.0,9344.0,9336.0,9336.0,9336.0,9320.0,9304.0,9312.0,9312.0,9320.0,9328.0,9312.0,9296.0,9296.0,9296.0,9304.0,9304.0,9296.0,9296.0,9320.0,9320.0,9296.0,9288.0,9280.0,9296.0,9312.0,9320.0,9328.0,9328.0,9312.0,9312.0,9304.0,9304.0,9320.0,9320.0,9320.0,9312.0,9312.0,9320.0,9328.0,9320.0,9320.0,9312.0,9304.0,9312.0,9304.0,9304.0,9304.0,9312.0,9312.0,9312.0,9320.0,9328.0,9328.0,9320.0,9304.0,9296.0,9304.0,9296.0,9288.0,9296.0,9304.0,9296.0,9272.0,9256.0,9248.0,9240.0,9224.0,9232.0,9240.0,9240.0,9240.0,9232.0,9232.0,9248.0,9264.0,9264.0,9256.0,9264.0,9248.0,9248.0,9240.0,9248.0,9256.0,9240.0,9240.0,9240.0,9256.0,9264.0,9272.0,9256.0,9232.0,9208.0,9192.0,9208.0,9224.0,9224.0,9224.0,9224.0,9224.0,9224.0,9216.0,9224.0,9224.0,9232.0,9240.0,9240.0,9232.0,9224.0,9224.0,9208.0,9192.0,9192.0,9192.0,9200.0,9192.0,9192.0,9192.0,9192.0,9200.0,9200.0,9208.0,9200.0,9208.0,9224.0,9232.0,9224.0,9224.0,9208.0,9208.0,9192.0,9184.0,9176.0,9168.0,9160.0,9160.0,9176.0,9176.0,9176.0,9184.0,9160.0,9144.0,9128.0,9112.0,9096.0,9096.0,9080.0,9064.0,9056.0,9040.0,9040.0,9048.0,9056.0,9048.0,9032.0,9048.0,9064.0,9080.0,9088.0,9096.0,9104.0,9112.0,9112.0,9112.0,9104.0,9104.0,9120.0,9120.0,9104.0,9088.0,9080.0,9088.0,9096.0,9088.0,9080.0,9096.0,9096.0,9120.0,9128.0,9136.0,9136.0,9120.0,9120.0,9120.0,9112.0,9104.0,9080.0,9064.0,9080.0,9080.0,9080.0,9080.0,9072.0,9072.0,9096.0,9112.0,9112.0,9112.0,9128.0,9128.0,9144.0,9176.0,9176.0,9184.0,9184.0,9176.0,9168.0,9160.0,9144.0,9144.0,9144.0,9144.0,9144.0,9136.0,9128.0,9088.0,9072.0,9064.0,9048.0,9072.0,9064.0,9064.0,9048.0,9040.0,9048.0,9048.0,9040.0,9040.0,9008.0,9000.0,8992.0,8992.0,8976.0,8944.0,8936.0,8952.0,8952.0,8952.0,8944.0,8952.0,8952.0,8952.0,8936.0,8944.0,8952.0,8952.0,8944.0,8944.0,8936.0,8912.0,8904.0,8896.0,8896.0,8880.0,8896.0,8896.0,8904.0,8920.0,8920.0,8912.0,8928.0,8928.0,8920.0,8912.0,8904.0,8928.0,8944.0,8952.0,8960.0,8952.0,8952.0,8952.0,8944.0,8944.0,8952.0,8952.0,8960.0,8960.0,8968.0,8960.0,8960.0,8960.0,8952.0,8928.0,8936.0,8952.0,8944.0,8968.0,8968.0,8960.0,8968.0,8976.0,8968.0,8960.0,8952.0,8944.0,8920.0,8912.0,8896.0,8880.0,8864.0,8856.0,8848.0,8856.0,8864.0,8848.0,8848.0,8848.0,8840.0,8856.0,8880.0,8896.0,8896.0,8904.0,8912.0,8920.0,8920.0,8912.0,8904.0,8896.0,8896.0,8872.0,8880.0,8904.0,8896.0,8880.0,8888.0,8880.0,8872.0,8856.0,8840.0,8824.0,8808.0,8816.0,8808.0,8808.0,8816.0,8808.0,8784.0,8784.0,8776.0,8792.0,8784.0,8760.0,8768.0,8784.0,8792.0,8784.0,8784.0,8792.0,8792.0,8800.0,8816.0,8808.0,8824.0,8824.0,8824.0,8816.0,8816.0,8824.0,8840.0,8840.0,8848.0,8832.0,8832.0,8808.0,8784.0,8768.0,8752.0,8744.0,8744.0,8736.0,8744.0,8744.0,8744.0,8728.0,8736.0,8728.0,8728.0,8720.0,8720.0,8712.0,8704.0,8704.0,8728.0,8736.0,8752.0,8744.0,8736.0,8728.0,8736.0,8752.0,8768.0,8768.0,8768.0,8768.0,8784.0,8784.0,8784.0,8776.0,8792.0,8792.0,8800.0,8784.0,8784.0,8792.0,8792.0,8808.0,8816.0,8816.0,8808.0,8808.0,8808.0,8832.0,8856.0,8856.0,8840.0,8840.0,8840.0,8832.0,8824.0,8808.0,8816.0,8800.0,8784.0,8784.0,8784.0,8776.0,8784.0,8776.0,8776.0,8768.0,8784.0,8800.0,8808.0,8800.0,8776.0,8768.0,8760.0,8760.0,8760.0,8760.0,8768.0,8768.0,8776.0,8808.0,8816.0,8816.0,8824.0,8816.0,8800.0,8808.0,8816.0,8816.0,8816.0,8816.0,8792.0,8784.0,8792.0,8784.0,8792.0,8792.0,8784.0,8768.0,8736.0,8728.0,8712.0,8720.0,8704.0,8696.0,8704.0,8736.0,8728.0,8720.0,8704.0,8696.0,8696.0,8688.0,8680.0,8672.0,8656.0,8648.0,8648.0,8656.0,8664.0,8672.0,8688.0,8696.0,8696.0,8696.0,8696.0,8704.0,8704.0,8704.0,8704.0,8728.0,8736.0,8736.0,8744.0,8744.0,8736.0,8728.0,8720.0,8728.0,8728.0,8728.0,8688.0,8672.0,8672.0,8672.0,8664.0,8656.0,8656.0,8664.0,8672.0,8688.0,8696.0,8696.0,8688.0,8688.0,8672.0,8672.0,8656.0,8664.0,8672.0,8672.0,8672.0,8656.0,8648.0,8640.0,8640.0,8664.0,8680.0,8704.0,8712.0,8712.0,8720.0,8728.0,8736.0,8736.0,8720.0,8720.0,8720.0,8736.0,8728.0,8720.0,8728.0,8720.0,8712.0,8728.0,8728.0,8728.0,8736.0,8752.0,8760.0,8784.0,8784.0,8776.0,8768.0,8752.0,8744.0,8760.0,8760.0,8768.0,8768.0,8768.0,8752.0,8752.0,8784.0,8784.0,8776.0,8776.0,8776.0,8776.0,8792.0,8808.0,8816.0,8832.0,8840.0,8832.0,8824.0,8808.0,8808.0,8808.0,8824.0,8840.0,8840.0,8832.0,8832.0,8824.0,8824.0,8808.0,8808.0,8824.0,8816.0,8816.0,8800.0,8792.0,8768.0,8752.0,8752.0,8752.0,8760.0,8776.0,8784.0,8792.0,8784.0,8808.0,8808.0,8808.0,8816.0,8816.0,8824.0,8824.0,8824.0,8816.0,8792.0,8792.0,8776.0,8792.0,8808.0,8816.0,8816.0,8824.0,8808.0,8816.0,8816.0,8808.0,8800.0,8800.0,8808.0,8808.0,8784.0,8784.0,8784.0,8800.0,8800.0,8808.0,8816.0,8816.0,8824.0,8832.0,8840.0,8840.0,8840.0,8824.0,8832.0,8824.0,8832.0,8832.0,8848.0,8864.0,8896.0,8896.0,8904.0,8912.0,8920.0,8928.0,8936.0,8944.0,8944.0,8960.0,8976.0,9024.0,9032.0,9032.0,9016.0,9016.0,9024.0,9024.0,9016.0,9016.0,9008.0,9000.0,9000.0,9008.0,9000.0,8992.0,8992.0,9008.0,9000.0,9008.0,9008.0,9008.0,9024.0,9008.0,8992.0,9008.0,9008.0,9032.0,9056.0,9064.0,9064.0,9064.0,9064.0,9048.0,9032.0,9024.0,9008.0,9008.0,9008.0,8976.0,8984.0,8992.0,9000.0,9008.0,9000.0,8992.0,8992.0,8984.0,8992.0,8992.0,8960.0,8944.0,8936.0,8928.0,8920.0,8912.0,8904.0,8904.0,8912.0,8912.0,8904.0,8888.0,8912.0,8912.0,8904.0,8888.0,8880.0,8872.0,8872.0,8896.0,8888.0,8880.0,8872.0,8856.0,8864.0,8872.0,8872.0,8864.0,8848.0,8864.0,8848.0,8864.0,8864.0,8848.0,8856.0,8848.0,8840.0,8832.0,8832.0,8824.0,8816.0,8808.0,8808.0,8800.0,8800.0,8792.0,8784.0,8792.0,8784.0],\"y1\":[10000.0,10024.0,10000.0,10008.0,10000.0,9984.0,9984.0,9968.0,9952.0,9952.0,9944.0,9936.0,9944.0,9944.0,9944.0,9944.0,9944.0,9936.0,9928.0,9936.0,9904.0,9896.0,9880.0,9864.0,9864.0,9872.0,9880.0,9880.0,9856.0,9856.0,9848.0,9848.0,9832.0,9824.0,9832.0,9840.0,9840.0,9856.0,9872.0,9896.0,9888.0,9904.0,9912.0,9912.0,9912.0,9920.0,9904.0,9896.0,9880.0,9888.0,9888.0,9880.0,9864.0,9872.0,9880.0,9872.0,9880.0,9896.0,9896.0,9880.0,9888.0,9888.0,9888.0,9888.0,9896.0,9904.0,9904.0,9896.0,9912.0,9920.0,9928.0,9936.0,9920.0,9904.0,9888.0,9904.0,9896.0,9904.0,9888.0,9888.0,9888.0,9888.0,9888.0,9872.0,9872.0,9880.0,9880.0,9904.0,9912.0,9896.0,9888.0,9896.0,9896.0,9904.0,9912.0,9912.0,9904.0,9888.0,9872.0,9848.0,9856.0,9840.0,9848.0,9864.0,9872.0,9872.0,9888.0,9888.0,9888.0,9896.0,9896.0,9880.0,9888.0,9872.0,9864.0,9880.0,9864.0,9856.0,9856.0,9856.0,9848.0,9856.0,9824.0,9816.0,9816.0,9808.0,9808.0,9808.0,9816.0,9808.0,9816.0,9816.0,9800.0,9784.0,9752.0,9744.0,9744.0,9752.0,9752.0,9760.0,9744.0,9712.0,9712.0,9720.0,9720.0,9712.0,9720.0,9728.0,9712.0,9720.0,9728.0,9728.0,9720.0,9720.0,9696.0,9696.0,9704.0,9696.0,9696.0,9704.0,9712.0,9712.0,9704.0,9704.0,9696.0,9712.0,9712.0,9736.0,9736.0,9720.0,9720.0,9728.0,9728.0,9728.0,9744.0,9736.0,9720.0,9720.0,9728.0,9744.0,9744.0,9744.0,9744.0,9744.0,9736.0,9736.0,9736.0,9744.0,9728.0,9736.0,9752.0,9760.0,9776.0,9792.0,9816.0,9832.0,9824.0,9808.0,9800.0,9792.0,9800.0,9808.0,9808.0,9816.0,9832.0,9840.0,9840.0,9840.0,9832.0,9856.0,9848.0,9856.0,9856.0,9872.0,9880.0,9856.0,9856.0,9848.0,9848.0,9848.0,9864.0,9880.0,9880.0,9864.0,9848.0,9824.0,9824.0,9824.0,9816.0,9816.0,9808.0,9824.0,9816.0,9800.0,9792.0,9808.0,9808.0,9800.0,9800.0,9784.0,9776.0,9768.0,9768.0,9776.0,9784.0,9768.0,9768.0,9768.0,9760.0,9784.0,9784.0,9776.0,9768.0,9776.0,9768.0,9776.0,9760.0,9744.0,9744.0,9752.0,9744.0,9752.0,9760.0,9784.0,9792.0,9824.0,9816.0,9816.0,9816.0,9808.0,9816.0,9808.0,9808.0,9824.0,9840.0,9840.0,9832.0,9808.0,9792.0,9792.0,9792.0,9768.0,9768.0,9776.0,9776.0,9760.0,9744.0,9752.0,9736.0,9736.0,9744.0,9728.0,9712.0,9696.0,9696.0,9688.0,9696.0,9704.0,9704.0,9712.0,9704.0,9704.0,9704.0,9720.0,9720.0,9720.0,9728.0,9720.0,9720.0,9728.0,9696.0,9696.0,9696.0,9696.0,9688.0,9680.0,9680.0,9664.0,9664.0,9672.0,9696.0,9688.0,9680.0,9664.0,9656.0,9648.0,9648.0,9656.0,9656.0,9656.0,9672.0,9664.0,9672.0,9672.0,9672.0,9664.0,9664.0,9680.0,9696.0,9704.0,9712.0,9728.0,9728.0,9736.0,9728.0,9712.0,9720.0,9712.0,9720.0,9720.0,9736.0,9744.0,9744.0,9728.0,9712.0,9712.0,9712.0,9720.0,9728.0,9728.0,9736.0,9744.0,9760.0,9784.0,9776.0,9776.0,9792.0,9800.0,9768.0,9752.0,9752.0,9736.0,9720.0,9696.0,9688.0,9704.0,9704.0,9672.0,9664.0,9664.0,9664.0,9664.0,9664.0,9656.0,9664.0,9648.0,9632.0,9624.0,9608.0,9592.0,9584.0,9592.0,9576.0,9584.0,9584.0,9576.0,9576.0,9584.0,9576.0,9568.0,9560.0,9552.0,9536.0,9536.0,9536.0,9536.0,9528.0,9512.0,9496.0,9480.0,9464.0,9456.0,9456.0,9464.0,9464.0,9456.0,9456.0,9456.0,9440.0,9432.0,9424.0,9416.0,9408.0,9408.0,9408.0,9416.0,9432.0,9432.0,9440.0,9448.0,9448.0,9440.0,9448.0,9440.0,9448.0,9448.0,9456.0,9464.0,9480.0,9472.0,9472.0,9472.0,9472.0,9480.0,9480.0,9496.0,9496.0,9504.0,9512.0,9512.0,9520.0,9528.0,9536.0,9520.0,9528.0,9544.0,9536.0,9544.0,9544.0,9544.0,9552.0,9552.0,9552.0,9544.0,9552.0,9552.0,9576.0,9600.0,9600.0,9600.0,9624.0,9624.0,9624.0,9616.0,9608.0,9600.0,9616.0,9624.0,9624.0,9624.0,9624.0,9600.0,9600.0,9600.0,9600.0,9608.0,9616.0,9632.0,9632.0,9648.0,9656.0,9640.0,9648.0,9640.0,9640.0,9648.0,9672.0,9664.0,9664.0,9664.0,9680.0,9680.0,9696.0,9712.0,9712.0,9704.0,9696.0,9696.0,9712.0,9704.0,9704.0,9704.0,9688.0,9688.0,9704.0,9712.0,9704.0,9712.0,9696.0,9680.0,9656.0,9648.0,9656.0,9656.0,9664.0,9672.0,9672.0,9688.0,9680.0,9656.0,9656.0,9648.0,9648.0,9640.0,9648.0,9640.0,9632.0,9624.0,9608.0,9592.0,9568.0,9560.0,9552.0,9544.0,9544.0,9544.0,9552.0,9528.0,9528.0,9536.0,9536.0,9520.0,9520.0,9512.0,9512.0,9512.0,9504.0,9496.0,9504.0,9520.0,9520.0,9512.0,9488.0,9504.0,9504.0,9504.0,9528.0,9520.0,9520.0,9520.0,9512.0,9504.0,9496.0,9504.0,9512.0,9528.0,9536.0,9520.0,9512.0,9520.0,9520.0,9504.0,9488.0,9488.0,9488.0,9488.0,9488.0,9464.0,9456.0,9456.0,9432.0,9432.0,9432.0,9424.0,9424.0,9424.0,9424.0,9424.0,9416.0,9416.0,9440.0,9464.0,9464.0,9464.0,9472.0,9488.0,9504.0,9520.0,9520.0,9512.0,9512.0,9536.0,9536.0,9544.0,9520.0,9520.0,9520.0,9528.0,9504.0,9504.0,9512.0,9512.0,9504.0,9512.0,9520.0,9520.0,9512.0,9496.0,9496.0,9496.0,9488.0,9464.0,9448.0,9440.0,9432.0,9424.0,9416.0,9408.0,9408.0,9400.0,9384.0,9376.0,9392.0,9400.0,9376.0,9360.0,9360.0,9368.0,9360.0,9360.0,9352.0,9352.0,9360.0,9352.0,9352.0,9360.0,9352.0,9352.0,9352.0,9352.0,9360.0,9368.0,9360.0,9352.0,9360.0,9384.0,9376.0,9376.0,9376.0,9368.0,9368.0,9368.0,9352.0,9344.0,9352.0,9352.0,9336.0,9320.0,9296.0,9288.0,9296.0,9304.0,9312.0,9320.0,9328.0,9328.0,9328.0,9344.0,9336.0,9328.0,9328.0,9320.0,9304.0,9304.0,9296.0,9280.0,9256.0,9256.0,9264.0,9272.0,9280.0,9256.0,9256.0,9264.0,9280.0,9288.0,9280.0,9272.0,9272.0,9272.0,9264.0,9256.0,9264.0,9264.0,9272.0,9264.0,9264.0,9272.0,9280.0,9264.0,9248.0,9248.0,9256.0,9264.0,9272.0,9264.0,9256.0,9256.0,9256.0,9264.0,9280.0,9280.0,9304.0,9280.0,9272.0,9256.0,9240.0,9240.0,9224.0,9224.0,9224.0,9208.0,9200.0,9208.0,9208.0,9232.0,9240.0,9232.0,9232.0,9200.0,9184.0,9168.0,9160.0,9168.0,9168.0,9152.0,9144.0,9144.0,9160.0,9168.0,9176.0,9184.0,9184.0,9192.0,9200.0,9208.0,9200.0,9192.0,9184.0,9168.0,9168.0,9184.0,9184.0,9192.0,9208.0,9216.0,9200.0,9216.0,9224.0,9240.0,9216.0,9208.0,9216.0,9224.0,9232.0,9232.0,9240.0,9232.0,9240.0,9240.0,9240.0,9256.0,9264.0,9264.0,9264.0,9264.0,9256.0,9264.0,9264.0,9264.0,9264.0,9256.0,9256.0,9264.0,9264.0,9248.0,9248.0,9240.0,9232.0,9240.0,9232.0,9264.0,9264.0,9280.0,9280.0,9280.0,9312.0,9320.0,9336.0,9352.0,9368.0,9368.0,9368.0,9368.0,9368.0,9352.0,9344.0,9336.0,9336.0,9344.0,9344.0,9336.0,9320.0,9312.0,9304.0,9320.0,9312.0,9320.0,9320.0,9336.0,9352.0,9344.0,9344.0,9352.0,9360.0,9344.0,9336.0,9336.0,9336.0,9352.0,9352.0,9328.0,9328.0,9320.0,9288.0,9280.0,9280.0,9264.0,9264.0,9272.0,9256.0,9256.0,9264.0,9280.0,9272.0,9280.0,9272.0,9256.0,9256.0,9264.0,9272.0,9272.0,9264.0,9264.0,9256.0,9232.0,9240.0,9240.0,9240.0,9256.0,9232.0,9224.0,9216.0,9208.0,9208.0,9200.0,9208.0,9208.0,9200.0,9208.0,9248.0,9256.0,9264.0,9272.0,9288.0,9288.0,9296.0,9296.0,9296.0,9304.0,9320.0,9336.0,9320.0,9312.0,9328.0,9320.0,9328.0,9336.0,9336.0,9328.0,9312.0,9304.0,9296.0,9304.0,9304.0,9312.0,9312.0,9296.0,9296.0,9288.0,9288.0,9288.0,9288.0,9280.0,9288.0,9296.0,9288.0,9288.0,9280.0,9272.0,9280.0,9288.0,9304.0,9312.0,9304.0,9304.0,9296.0,9296.0,9296.0,9304.0,9312.0,9304.0,9296.0,9296.0,9304.0,9312.0,9312.0,9304.0,9296.0,9296.0,9304.0,9288.0,9296.0,9280.0,9304.0,9304.0,9296.0,9312.0,9320.0,9320.0,9304.0,9288.0,9288.0,9288.0,9288.0,9280.0,9288.0,9296.0,9264.0,9256.0,9240.0,9232.0,9224.0,9216.0,9224.0,9224.0,9224.0,9224.0,9224.0,9216.0,9232.0,9248.0,9256.0,9248.0,9248.0,9232.0,9224.0,9224.0,9224.0,9232.0,9224.0,9224.0,9224.0,9232.0,9248.0,9256.0,9232.0,9208.0,9184.0,9184.0,9184.0,9208.0,9216.0,9216.0,9216.0,9216.0,9200.0,9200.0,9208.0,9216.0,9216.0,9232.0,9232.0,9208.0,9216.0,9208.0,9192.0,9176.0,9184.0,9184.0,9192.0,9184.0,9184.0,9184.0,9192.0,9176.0,9192.0,9192.0,9184.0,9192.0,9208.0,9216.0,9216.0,9200.0,9200.0,9184.0,9184.0,9176.0,9152.0,9144.0,9144.0,9152.0,9160.0,9168.0,9168.0,9160.0,9136.0,9128.0,9104.0,9088.0,9088.0,9088.0,9056.0,9048.0,9040.0,9032.0,9024.0,9032.0,9048.0,9024.0,9016.0,9024.0,9048.0,9056.0,9080.0,9080.0,9096.0,9096.0,9096.0,9088.0,9088.0,9088.0,9096.0,9104.0,9080.0,9080.0,9072.0,9072.0,9080.0,9080.0,9072.0,9080.0,9088.0,9096.0,9112.0,9120.0,9112.0,9104.0,9112.0,9096.0,9104.0,9080.0,9056.0,9048.0,9064.0,9064.0,9072.0,9072.0,9072.0,9064.0,9072.0,9096.0,9104.0,9104.0,9112.0,9120.0,9120.0,9144.0,9168.0,9176.0,9160.0,9160.0,9160.0,9144.0,9120.0,9120.0,9136.0,9128.0,9128.0,9120.0,9080.0,9072.0,9064.0,9048.0,9032.0,9048.0,9056.0,9040.0,9032.0,9032.0,9040.0,9040.0,9024.0,9008.0,8984.0,8984.0,8984.0,8976.0,8944.0,8928.0,8928.0,8936.0,8936.0,8944.0,8936.0,8944.0,8944.0,8936.0,8928.0,8936.0,8936.0,8944.0,8936.0,8928.0,8912.0,8896.0,8888.0,8888.0,8880.0,8880.0,8880.0,8880.0,8888.0,8904.0,8904.0,8904.0,8912.0,8912.0,8912.0,8896.0,8888.0,8904.0,8928.0,8936.0,8952.0,8944.0,8936.0,8936.0,8920.0,8936.0,8936.0,8936.0,8944.0,8952.0,8952.0,8944.0,8944.0,8944.0,8920.0,8904.0,8920.0,8936.0,8928.0,8936.0,8960.0,8944.0,8944.0,8960.0,8960.0,8944.0,8944.0,8920.0,8912.0,8896.0,8880.0,8856.0,8848.0,8840.0,8840.0,8840.0,8848.0,8840.0,8832.0,8840.0,8824.0,8824.0,8856.0,8872.0,8880.0,8888.0,8896.0,8912.0,8888.0,8896.0,8880.0,8888.0,8864.0,8864.0,8864.0,8888.0,8880.0,8872.0,8880.0,8864.0,8856.0,8840.0,8824.0,8808.0,8800.0,8808.0,8792.0,8792.0,8808.0,8784.0,8776.0,8768.0,8768.0,8776.0,8760.0,8744.0,8744.0,8760.0,8784.0,8776.0,8776.0,8776.0,8784.0,8792.0,8792.0,8792.0,8800.0,8816.0,8800.0,8800.0,8800.0,8808.0,8824.0,8832.0,8832.0,8824.0,8808.0,8784.0,8768.0,8744.0,8736.0,8728.0,8736.0,8736.0,8736.0,8736.0,8720.0,8720.0,8720.0,8720.0,8712.0,8712.0,8704.0,8696.0,8688.0,8688.0,8704.0,8712.0,8736.0,8728.0,8720.0,8728.0,8720.0,8736.0,8744.0,8760.0,8752.0,8752.0,8760.0,8768.0,8776.0,8768.0,8776.0,8784.0,8784.0,8776.0,8776.0,8776.0,8776.0,8784.0,8800.0,8800.0,8800.0,8800.0,8792.0,8808.0,8832.0,8840.0,8816.0,8824.0,8832.0,8816.0,8808.0,8800.0,8800.0,8768.0,8768.0,8776.0,8776.0,8768.0,8768.0,8760.0,8768.0,8760.0,8768.0,8784.0,8792.0,8776.0,8760.0,8760.0,8752.0,8752.0,8744.0,8752.0,8760.0,8760.0,8760.0,8776.0,8800.0,8816.0,8816.0,8792.0,8792.0,8792.0,8800.0,8792.0,8800.0,8792.0,8784.0,8776.0,8776.0,8776.0,8784.0,8784.0,8768.0,8736.0,8720.0,8704.0,8704.0,8704.0,8680.0,8680.0,8688.0,8704.0,8720.0,8696.0,8696.0,8680.0,8680.0,8672.0,8664.0,8648.0,8648.0,8640.0,8640.0,8640.0,8656.0,8664.0,8672.0,8680.0,8688.0,8680.0,8688.0,8688.0,8696.0,8688.0,8696.0,8704.0,8720.0,8720.0,8736.0,8728.0,8720.0,8720.0,8720.0,8712.0,8720.0,8688.0,8672.0,8656.0,8656.0,8656.0,8648.0,8640.0,8640.0,8648.0,8656.0,8664.0,8680.0,8680.0,8672.0,8664.0,8664.0,8656.0,8640.0,8648.0,8656.0,8664.0,8656.0,8648.0,8632.0,8632.0,8632.0,8632.0,8664.0,8680.0,8704.0,8704.0,8712.0,8720.0,8720.0,8712.0,8712.0,8712.0,8704.0,8712.0,8720.0,8712.0,8712.0,8704.0,8696.0,8712.0,8720.0,8712.0,8720.0,8728.0,8744.0,8760.0,8776.0,8768.0,8752.0,8736.0,8736.0,8744.0,8752.0,8760.0,8760.0,8752.0,8736.0,8744.0,8744.0,8768.0,8768.0,8768.0,8760.0,8760.0,8768.0,8792.0,8808.0,8816.0,8824.0,8800.0,8808.0,8800.0,8800.0,8792.0,8808.0,8824.0,8832.0,8832.0,8816.0,8816.0,8808.0,8792.0,8792.0,8808.0,8808.0,8800.0,8792.0,8768.0,8752.0,8744.0,8736.0,8744.0,8744.0,8768.0,8776.0,8776.0,8768.0,8784.0,8800.0,8800.0,8800.0,8792.0,8808.0,8816.0,8816.0,8792.0,8784.0,8768.0,8768.0,8768.0,8792.0,8800.0,8808.0,8808.0,8792.0,8792.0,8792.0,8792.0,8792.0,8792.0,8792.0,8776.0,8776.0,8776.0,8776.0,8784.0,8792.0,8792.0,8800.0,8808.0,8808.0,8816.0,8824.0,8832.0,8824.0,8808.0,8808.0,8816.0,8824.0,8824.0,8824.0,8840.0,8864.0,8880.0,8880.0,8896.0,8904.0,8912.0,8920.0,8928.0,8928.0,8936.0,8960.0,8976.0,9024.0,9008.0,9008.0,9000.0,9008.0,9008.0,9000.0,9000.0,8992.0,8992.0,8984.0,8992.0,8992.0,8968.0,8968.0,8992.0,8992.0,8992.0,8984.0,8984.0,9000.0,8984.0,8984.0,8992.0,9000.0,9008.0,9032.0,9048.0,9056.0,9056.0,9040.0,9032.0,9016.0,9008.0,9000.0,9000.0,8976.0,8968.0,8976.0,8976.0,8984.0,9000.0,8992.0,8984.0,8976.0,8968.0,8984.0,8952.0,8944.0,8936.0,8912.0,8912.0,8904.0,8896.0,8888.0,8896.0,8904.0,8904.0,8888.0,8888.0,8888.0,8904.0,8880.0,8880.0,8872.0,8848.0,8856.0,8872.0,8880.0,8872.0,8856.0,8840.0,8840.0,8856.0,8864.0,8840.0,8840.0,8840.0,8840.0,8840.0,8856.0,8840.0,8840.0,8832.0,8824.0,8824.0,8816.0,8816.0,8808.0,8792.0,8800.0,8792.0,8784.0,8776.0,8776.0,8776.0,8784.0]},\"selected\":{\"id\":\"2568\",\"type\":\"Selection\"},\"selection_policy\":{\"id\":\"2569\",\"type\":\"UnionRenderers\"}},\"id\":\"2358\",\"type\":\"ColumnDataSource\"},{\"attributes\":{\"mantissas\":[1,2,5],\"max_interval\":500.0,\"num_minor_ticks\":0},\"id\":\"2574\",\"type\":\"AdaptiveTicker\"},{\"attributes\":{\"fill_alpha\":{\"value\":0.1},\"fill_color\":{\"value\":\"#1f77b4\"},\"line_alpha\":{\"value\":0.1},\"line_color\":{\"value\":\"#1f77b4\"},\"top\":{\"field\":\"top\"},\"width\":{\"value\":600000},\"x\":{\"field\":\"x\"}},\"id\":\"2408\",\"type\":\"VBar\"},{\"attributes\":{\"text\":\"Simulated Price\"},\"id\":\"2325\",\"type\":\"Title\"},{\"attributes\":{\"x0\":{\"field\":\"x0\"},\"x1\":{\"field\":\"x1\"},\"y0\":{\"field\":\"y0\"},\"y1\":{\"field\":\"y1\"}},\"id\":\"2359\",\"type\":\"Segment\"},{\"attributes\":{\"dimensions\":\"width\"},\"id\":\"2393\",\"type\":\"PanTool\"},{\"attributes\":{\"base\":60,\"mantissas\":[1,2,5,10,15,20,30],\"max_interval\":1800000.0,\"min_interval\":1000.0,\"num_minor_ticks\":0},\"id\":\"2575\",\"type\":\"AdaptiveTicker\"},{\"attributes\":{\"line_alpha\":{\"value\":0.1},\"line_color\":{\"value\":\"#1f77b4\"},\"x0\":{\"field\":\"x0\"},\"x1\":{\"field\":\"x1\"},\"y0\":{\"field\":\"y0\"},\"y1\":{\"field\":\"y1\"}},\"id\":\"2360\",\"type\":\"Segment\"},{\"attributes\":{},\"id\":\"2394\",\"type\":\"WheelZoomTool\"},{\"attributes\":{\"base\":24,\"mantissas\":[1,2,4,6,8,12],\"max_interval\":43200000.0,\"min_interval\":3600000.0,\"num_minor_ticks\":0},\"id\":\"2576\",\"type\":\"AdaptiveTicker\"},{\"attributes\":{\"overlay\":{\"id\":\"2586\",\"type\":\"BoxAnnotation\"}},\"id\":\"2395\",\"type\":\"BoxZoomTool\"},{\"attributes\":{\"data_source\":{\"id\":\"2358\",\"type\":\"ColumnDataSource\"},\"glyph\":{\"id\":\"2359\",\"type\":\"Segment\"},\"hover_glyph\":null,\"muted_glyph\":null,\"nonselection_glyph\":{\"id\":\"2360\",\"type\":\"Segment\"},\"selection_glyph\":null,\"view\":{\"id\":\"2362\",\"type\":\"CDSView\"}},\"id\":\"2361\",\"type\":\"GlyphRenderer\"}],\"root_ids\":[\"2412\"]},\"title\":\"Bokeh Application\",\"version\":\"1.2.0\"}};\n", "  var render_items = [{\"docid\":\"e7bdd31e-9cbb-4fad-beed-a7d9d8c2da01\",\"roots\":{\"2412\":\"3b39cc3c-9da3-4634-9836-30f3a09766a7\"}}];\n", "  root.Bokeh.embed.embed_items_notebook(docs_json, render_items);\n", "\n", "  }\n", "  if (root.Bokeh !== undefined) {\n", "    embed_document(root);\n", "  } else {\n", "    var attempts = 0;\n", "    var timer = setInterval(function(root) {\n", "      if (root.Bokeh !== undefined) {\n", "        embed_document(root);\n", "        clearInterval(timer);\n", "      }\n", "      attempts++;\n", "      if (attempts > 100) {\n", "        console.log(\"Bokeh: ERROR: Unable to run BokehJS code because BokehJS library is missing\");\n", "        clearInterval(timer);\n", "      }\n", "    }, 10, root)\n", "  }\n", "})(window);"], "application/vnd.bokehjs_exec.v0+json": ""}, "metadata": {"application/vnd.bokehjs_exec.v0+json": {"id": "2412"}}, "output_type": "display_data"}], "source": ["import sys\n", "import os\n", "import time\n", "import warnings\n", "import numpy as np\n", "\n", "sys.path.append(os.path.dirname(os.path.abspath('')))\n", "\n", "warnings.filterwarnings('ignore')\n", "\n", "from tensortrade.exchanges.simulated import FBMExchange\n", "\n", "exchange = FBMExchange()\n", "\n", "exchange.reset()\n", "\n", "TOOLS = \"xpan,wheel_zoom,box_zoom,crosshair,reset,save\"\n", "\n", "price_plt = figure(title=\"Simulated Price\",\n", "                   y_axis_label='Price ($)',\n", "                   x_axis_type='datetime',\n", "                   tools=TOOLS)\n", "price_plt.xaxis.major_label_orientation = pi/4\n", "price_plt.grid.grid_line_alpha=0.3\n", "\n", "width = 60*1000*10\n", "inc = exchange.data_frame.close > exchange.data_frame.open\n", "dec = exchange.data_frame.open > exchange.data_frame.close\n", "\n", "price_plt.segment(exchange.data_frame.index, exchange.data_frame.high, exchange.data_frame.index, exchange.data_frame.low, color=\"black\")\n", "price_plt.vbar(exchange.data_frame.index[inc], width, exchange.data_frame.open[inc], exchange.data_frame.close[inc], fill_color=\"#9BDE39\", line_color=\"black\")\n", "price_plt.vbar(exchange.data_frame.index[dec], width, exchange.data_frame.open[dec], exchange.data_frame.close[dec], fill_color=\"#F2583E\", line_color=\"black\")\n", "\n", "volume_plt = figure(title=\"Simulated Volume\",\n", "                    y_axis_label=\"Volume\",\n", "                    x_axis_label=\"Time\",\n", "                    x_axis_type=\"datetime\",\n", "                    x_range=price_plt.x_range,\n", "                    plot_height=250,\n", "                    tools=TOOLS)\n", "\n", "volume_plt.vbar(exchange.data_frame.index, bottom=0, top=exchange.data_frame['volume'], width=width, color=\"#72B5C8\", alpha=0.3)\n", "\n", "output_notebook()\n", "\n", "show(column(price_plt, volume_plt))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Tick trade data and volume"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# TODO"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 2}