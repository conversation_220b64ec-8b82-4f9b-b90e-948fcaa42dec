
import torch
from torch.utils.data import Dataset
from pyqlab.data.pipeline import Pipeline
from pyqlab.const import SF_FUT_CODES, MAIN_FUT_CODES
from pyqlab.utils.config import set_seed, setup_logging, CfgNode as CN
import numpy as np

class BarDataset(Dataset):
    """
    Emits batches of bars
    """

    @staticmethod
    def get_default_config():
        C = CN()
        C.block_size = 15
        C.is_sf = False
        C.data_path = 'd:/RoboQuant2/store/barenc/sf'
        C.start_year = None
        C.end_year = None
        return C
    
    def __init__(self, config, data):
        self.config = config

        poss = [i for i in range(0, 48)] if self.config.is_sf else [i for i in range(0, 68)]
        bars = Pipeline.get_vocab()
        data_size, vocab_size = len(data), len(bars)

        # self.codetoi = { ch:i for i,ch in enumerate(codes) }
        # self.itocode = { i:ch for i,ch in enumerate(codes) }

        self.itobar = { i:ch for i,ch in enumerate(bars) }

        self.vocab_size = vocab_size
        self.data = data
        self.block_size = self.config.block_size

    def get_vocab_size(self):
        return self.vocab_size

    def get_block_size(self):
        return self.block_size

    def __len__(self):
        return self.data.shape[0]
    
    def __getitem__(self, idx):
        code = self.data[idx][:, 0].astype(np.int32)
        pos = self.data[idx][:, 2].astype(np.int32)
        dix = self.data[idx][:, 7].astype(np.int32)
        code = torch.tensor(code[:-1], dtype=torch.int32)
        pos = torch.tensor(pos[:-1], dtype=torch.int32)
        x = torch.tensor(dix[:-1], dtype=torch.int32)
        y = torch.tensor(dix[1:], dtype=torch.int32)
        return code, pos, x, y