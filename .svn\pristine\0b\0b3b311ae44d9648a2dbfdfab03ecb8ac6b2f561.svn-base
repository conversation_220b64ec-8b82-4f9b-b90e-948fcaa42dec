{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": true}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pylab as plt\n", "import datetime"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": true}, "outputs": [], "source": ["data_raw = pd.read_csv('IVE_tickbidask.txt', header=None, names=['Date','Time','Price','Bid','Ask','Size'])"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": true}, "outputs": [], "source": ["data = data_raw.iloc[:100000]"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/anaconda2/lib/python2.7/site-packages/ipykernel_launcher.py:1: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: http://pandas.pydata.org/pandas-docs/stable/indexing.html#indexing-view-versus-copy\n", "  \"\"\"Entry point for launching an IPython kernel.\n"]}], "source": ["data['DateTime'] = pd.to_datetime(data['Date'] + ' ' + data['Time'])\n", "data = data.drop(columns = ['Date', 'Time'])\n", "data = data.set_index('DateTime')"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"collapsed": true}, "outputs": [], "source": ["from bars import *"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"collapsed": true}, "outputs": [], "source": ["# bars = ImbalanceTickBarSeries(data)\n", "# imbtick_bars = bars.process_ticks(init = 100, min_bar = 10, max_bar = 1000)\n", "# print imbtick_bars.head()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                     close   high    low   open   volume\n", "DateTime                                                \n", "2009-09-28 09:41:53  50.84  50.85  50.71  50.79  10144.0\n", "2009-09-28 09:46:35  51.07  51.07  50.83  50.85  11900.0\n", "2009-09-28 09:47:25  51.06  51.07  51.05  51.07  10000.0\n", "2009-09-28 09:53:47  51.13  51.15  51.06  51.06  10824.0\n", "2009-09-28 09:54:34  51.13  51.14  51.13  51.13  10851.0\n"]}], "source": ["bars = VolumeBarSeries(data)\n", "volume_bars = bars.process_ticks(frequency = 10000)\n", "print volume_bars.head()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                     close        dollar    high     low   open  volume\n", "DateTime                                                               \n", "2009-09-28 09:46:34  51.07  1.020027e+06  51.070  50.710  50.79   20044\n", "2009-09-28 09:52:47  51.13  1.073986e+06  51.148  51.050  51.07   21024\n", "2009-09-28 09:54:38  51.13  1.020145e+06  51.150  51.129  51.14   19951\n", "2009-09-28 09:55:26  51.14  1.004999e+06  51.150  51.130  51.13   19652\n", "2009-09-28 10:02:48  51.25  1.020512e+06  51.260  51.080  51.14   19943\n"]}], "source": ["bars = DollarBarSeries(data)\n", "dollar_bars = bars.process_ticks(frequency = 1000000)\n", "print dollar_bars.head()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                        open   high      low  close  volume\n", "DateTime                                                   \n", "2009-09-28 09:40:00  50.7900  50.85  50.7100  50.81    9197\n", "2009-09-28 09:50:00  50.8100  51.12  50.7833  51.07   26447\n", "2009-09-28 10:00:00  51.0988  51.15  51.0800  51.12   53716\n", "2009-09-28 10:10:00  51.1500  51.29  51.1500  51.27   47673\n", "2009-09-28 10:20:00  51.2800  51.29  51.2100  51.21   30631\n"]}], "source": ["bars = BarSeries(data)\n", "time_bars = bars.process_ticks(frequency='10Min')\n", "print time_bars.head()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                     close   high    low   open  volume\n", "DateTime                                               \n", "2009-09-28 09:47:22  51.06  51.07  50.71  50.79   30044\n", "2009-09-28 09:54:38  51.13  51.15  51.06  51.06   28975\n", "2009-09-28 10:00:15  51.21  51.21  51.08  51.13   32841\n", "2009-09-28 10:05:50  51.21  51.28  51.20  51.21   33764\n", "2009-09-28 10:15:13  51.25  51.29  51.19  51.22   37104\n"]}], "source": ["bars = TickBarSeries(data)\n", "tick_bars = bars.process_ticks(frequency = 100)\n", "print tick_bars.head()"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABBsAAAHiCAYAAACp58SyAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\n", "text/plain": ["<Figure size 1296x576 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize = (18, 8))\n", "plt.title('Bars over the prices')\n", "plt.plot(data.index.values[:1100], data.Price.values[:1100], label = 'Raw prices', ls = '--', color = 'black')\n", "plt.plot(time_bars.index.values[:12], time_bars.close.values[:12], ls = '', markersize = 10, marker = '.')\n", "plt.plot(tick_bars.index.values[:11], tick_bars.close.values[:11], ls = '', markersize = 10, marker = 'o', label = '100 ticks bars')\n", "plt.plot(volume_bars.index.values[:30], volume_bars.close.values[:30], ls = '', markersize = 10, marker = '*', label = '10K volume bars')\n", "plt.plot(dollar_bars.index.values[:16], dollar_bars.close.values[:16], ls = '', markersize = 10, marker = '*', label = '1M dollar bars ')\n", "\n", "for e, t in enumerate(time_bars.index.values[:12]):\n", "    if e == 0:\n", "        plt.axvline(t, ls = '--', label = '10 Minutes time bars')\n", "    else:\n", "        plt.axvline(t, ls = '--')\n", "\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"collapsed": true}, "outputs": [], "source": ["N_BARS = 100000"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"scrolled": false}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/anaconda2/lib/python2.7/site-packages/matplotlib/axes/_axes.py:6462: UserWarning: The 'normed' kwarg is deprecated, and has been replaced by the 'density' kwarg.\n", "  warnings.warn(\"The 'normed' kwarg is deprecated, and has been \"\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA3cAAAHVCAYAAABSeALaAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nAKADwh0AAEAHhDsAAIAO/G8ZGRAfaFn5NAAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 1080x576 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize = (15, 8))\n", "plt.hist(time_bars.close.pct_change().dropna().values.tolist()[:N_BARS], label = 'Price bars', alpha = 0.5, normed=True, bins=50, range = (-0.01, 0.01))\n", "# plt.hist(tick_bars.close.pct_change().dropna().values.tolist()[:N_BARS], label = 'Tick bars', alpha = 0.5, normed=True, bins=50, range = (-0.01, 0.01))\n", "# plt.hist(volume_bars.close.pct_change().dropna().values.tolist()[:N_BARS], label = 'Volume bars', alpha = 0.5, normed=True, bins=50, range = (-0.01, 0.01))\n", "# plt.hist(dollar_bars.close.pct_change().dropna().values.tolist()[:N_BARS], label = 'Dollar bars', alpha = 0.5, normed=True, bins=50, range = (-0.01, 0.01))\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-0.030588125306375218\n", "-0.07683926120173647\n", "-0.021983481646673717\n", "-0.06697105174688109\n", "----------\n", "7.7351832147e-07\n", "6.69627624126e-06\n", "2.99889161344e-06\n", "5.6186145009e-06\n"]}], "source": ["print pd.Series.autocorr(time_bars.close.pct_change().dropna()[:N_BARS])\n", "print pd.Series.autocorr(tick_bars.close.pct_change().dropna()[:N_BARS])\n", "print pd.Series.autocorr(volume_bars.close.pct_change().dropna()[:N_BARS])\n", "print pd.Series.autocorr(dollar_bars.close.pct_change().dropna()[:N_BARS])\n", "# print pd.Series.autocorr(imbtick_bars.close.diff().dropna())\n", "\n", "print '-' * 10\n", "\n", "print np.var(time_bars.close.pct_change().dropna()[:N_BARS])\n", "print np.var(tick_bars.close.pct_change().dropna()[:N_BARS])\n", "print np.var(volume_bars.close.pct_change().dropna()[:N_BARS])\n", "print np.var(dollar_bars.close.pct_change().dropna()[:N_BARS])\n", "# print np.var(imbtick_bars.close.diff().dropna())"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"collapsed": true}, "outputs": [], "source": ["from scipy import stats"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(11346217.883499108, 0.0)\n", "(12135.453902602945, 0.0)\n", "(118016.0917197462, 0.0)\n", "(16007.585291770612, 0.0)\n"]}], "source": ["print stats.jarque_bera(time_bars.close.pct_change().dropna()[:N_BARS])\n", "print stats.jarque_bera(tick_bars.close.pct_change().dropna()[:N_BARS])\n", "print stats.jarque_bera(volume_bars.close.pct_change().dropna()[:N_BARS])\n", "print stats.jarque_bera(dollar_bars.close.pct_change().dropna()[:N_BARS])\n", "# print stats.jarque_bera(imbtick_bars.close.diff().dropna())"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(0.3610457181930542, 0.0)\n", "(0.8831203579902649, 9.326734838051088e-27)\n", "(0.8196291327476501, 8.407790785948902e-45)\n", "(0.8652165532112122, 9.826826203947629e-31)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/anaconda2/lib/python2.7/site-packages/scipy/stats/morestats.py:1310: UserWarning: p-value may not be accurate for N > 5000.\n", "  warnings.warn(\"p-value may not be accurate for N > 5000.\")\n"]}], "source": ["print stats.shapiro(time_bars.close.pct_change().dropna()[:N_BARS])\n", "print stats.shapiro(tick_bars.close.pct_change().dropna()[:N_BARS])\n", "print stats.shapiro(volume_bars.close.pct_change().dropna()[:N_BARS])\n", "print stats.shapiro(dollar_bars.close.pct_change().dropna()[:N_BARS])\n", "# print stats.shapiro(imbtick_bars.close.diff().dropna())"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["(9389, 999, 2262, 1187)"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["len(time_bars), len(tick_bars), len(volume_bars), len(dollar_bars)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python [conda root]", "language": "python", "name": "conda-root-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.0"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 2}