{"cells": [{"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import leveldb\n", "import lmdb\n", "import os, sys"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["codes = [\"IF\", \"IH\", \"IC\",\n", "          \"M\", \"Y\", \"A\", \"P\", \"J\", \"JM\", \"I\", \"C\", \"CS\", \"JD\", \"L\", \"V\", \"PP\", \"EG\",\n", "          \"SR\", \"CF\", \"ZC\", \"FG\", \"TA\", \"MA\", \"OI\", \"RM\", \"RS\", \"SF\", \"SM\", \"AP\",\n", "          \"CU\", \"AL\", \"ZN\", \"PB\", \"NI\", \"SN\", \"AU\", \"AG\", \"RB\", \"HC\", \"BU\", \"RU\", \"FU\", \"SP\"]\n", "\n", "def open_leveldb():\n", "    db = leveldb.LevelDB(\"d:/QuantLab/store/historydata.db\")\n", "    return db\n", "\n", "def insert_leveldb(db, sid, name):\n", "    db.<PERSON>(str(sid), name)\n", "    \n", "#遍历\n", "def display_leveldb(db):\n", "    for key, value in db.RangeIter():\n", "        print (key, value)\n", "\n", "def open_lmdb():\n", "    # path = \"e:/Lab/RoboQuant/bin/x64/Debug/store/historydata\"\n", "    path = \"d:/QuantLab/store/historydata\"\n", "    env = lmdb.open(path, map_size=1024*1024*100)\n", "    return env\n", "\n", "def insert_lmdb(env, sid, name):\n", "    txn = env.begin(write = True)\n", "    txn.put(sid, name)\n", "    txn.commit()\n", "    \n", "def display_lmdb(env):\n", "    txn = env.begin()\n", "    cur = txn.cursor()\n", "    for key, value in cur:\n", "        print (key, value)\n", "        \n", "def delete(env, sid):\n", "    txn = env.begin(write = True)\n", "    txn.delete(str(sid).encode('utf-8'))\n", "    txn.commit()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["b'min5_MA8888.ZC_2019_07'\n"]}, {"ename": "Error", "evalue": "Attempt to operate on closed/deleted/dropped object.", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON>r\u001b[0m                                     <PERSON><PERSON> (most recent call last)", "\u001b[1;32m<ipython-input-8-bc4fc59a8ef7>\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m      3\u001b[0m \u001b[0mtxn\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mdb\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mbegin\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mwrite\u001b[0m \u001b[1;33m=\u001b[0m \u001b[1;32mTrue\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      4\u001b[0m \u001b[0mcur\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mtxn\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mcursor\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 5\u001b[1;33m \u001b[1;32mfor\u001b[0m \u001b[0mkey\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mvalue\u001b[0m \u001b[1;32min\u001b[0m \u001b[0mcur\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      6\u001b[0m     \u001b[1;31m# if key[-7:] == b\"2019_07\":\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      7\u001b[0m     \u001b[1;32mif\u001b[0m \u001b[0mkey\u001b[0m \u001b[1;33m==\u001b[0m \u001b[1;34mb\"min5_MA8888.ZC_2019_07\"\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;31mError\u001b[0m: Attempt to operate on closed/deleted/dropped object."]}], "source": ["db = open_lmdb()\n", "\n", "txn = db.begin(write = True)\n", "cur = txn.cursor()\n", "for key, value in cur:\n", "    # if key[-7:] == b\"2019_07\":\n", "    if key == b\"min5_CF8888.ZC_2019_07\":\n", "        txn.delete(key)\n", "        txn.commit()\n", "        print(key)\n", "    \n", "db.close()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["db = open_lmdb()\n", "\n", "txn = db.begin()\n", "cur = txn.cursor()\n", "for key, value in cur:\n", "    if key[-7:] == b\"2019_07\":\n", "#         delete(db, key)\n", "        print(key)\n", "    \n", "db.close()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 2}