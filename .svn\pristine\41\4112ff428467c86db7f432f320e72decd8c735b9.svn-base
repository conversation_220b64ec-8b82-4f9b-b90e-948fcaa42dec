# Copyright (c) Microsoft Corporation.
# Licensed under the MIT License.

import abc
import warnings
import pandas as pd
import numpy as np
import datetime
import json
import os

from sklearn.model_selection import StratifiedKFold # 将全部训练集S分成k个不相交的子集
from sklearn.preprocessing import LabelEncoder # 标签编码LabelEncoder 作用： 利用LabelEncoder() 将转换成连续的数值型变量。即是对不连续的数字或者文本进行编号

from typing import Tuple, Union, List

from qlib.data import D
from qlib.utils import load_dataset, init_instance_by_config, time_to_slc_point
from qlib.log import get_module_logger
from qlib.data.dataset.loader import DataLoader

class AFDataLoader(DataLoader):
    '''
    (A)icm (F)actor Data Loader
    '''
    def __init__(
           self,
           direct, model_name, model_path, data_path, portfolios,only_trading_code=True,
           n_splits=5, shuffle=True, random_state=42, model_name_suff=""
       ) -> None:
            self._direct = direct
            self._model_name = model_name
            self._model_name_suff = model_name_suff
            self._model_path = model_path
            self._data_path = data_path
            self._portfolios = portfolios
            self.lb_df = pd.DataFrame()
            self.lf_df = pd.DataFrame()
            self.sf_df = pd.DataFrame()
            self.ct_df = pd.DataFrame()
            self.only_trading_code = only_trading_code
            self.n_splits = n_splits
            self.shuffle = shuffle
            self.random_state = random_state
            self.interface_params = {
                'input_dim': 1, # 1: expression call 2: API call
                'code_encoding': 2, # 0:unsing, 1:onehot, 2:embedding
            }
            super().__init__()

    def _load_data(self):

        TRAD_FUT_CODES = ['M', 'Y', 'A', 'P', 'JM', 'I', 'V', 'EG', 'EB', 'SR', 'CF', 'FG',
            'TA', 'MA', 'OI', 'RM', 'RS', 'SF', 'SM', 'AP', 'UR', 'SA', 'RB', 'HC', 'AG', \
            'SP', 'BU', 'SS', 'RU', 'LH', 'PK', 'C', 'CY']

        for pf in self._portfolios:
            if os.path.isfile('%s/factors_%s_lf.%s.csv'%(self._data_path, self._direct, pf)):
                self.lf_df = self.lf_df.append(pd.read_csv('%s/factors_%s_lf.%s.csv'%(self._data_path, self._direct, pf)))
            if os.path.isfile('%s/factors_%s_sf.%s.csv'%(self._data_path, self._direct, pf)):
                self.sf_df = self.sf_df.append(pd.read_csv('%s/factors_%s_sf.%s.csv'%(self._data_path, self._direct, pf)))
            if os.path.isfile('%s/factors_%s_ct.%s.csv'%(self._data_path, self._direct, pf)):
                self.ct_df = self.ct_df.append(pd.read_csv('%s/factors_%s_ct.%s.csv'%(self._data_path, self._direct, pf)))
            if os.path.isfile('%s/orders_%s_label.%s.csv'%(self._data_path, self._direct, pf)):
                self.lb_df = self.lb_df.append(pd.read_csv('%s/orders_%s_label.%s.csv'%(self._data_path, self._direct, pf)), ignore_index=True)
        
        assert not self.lb_df.empty, "load raw data is empty."

        # 清除不需要的数据
        if self.only_trading_code:
            self.lb_df = self.lb_df[self.lb_df['CODE'].isin(TRAD_FUT_CODES)]
        # lb_df的排序和重新索引步骤不能少，否则FOLD标记有问题
        self.lb_df.drop_duplicates(subset=['ord_id'], keep=False, inplace=True)
        self.sf_df.drop_duplicates(subset=['ord_id'], keep=False, inplace=True)
        self.lf_df.drop_duplicates(subset=['ord_id'], keep=False, inplace=True)
        self.ct_df.drop_duplicates(subset=['ord_id'], keep=False, inplace=True)
        # self.lb_df.sort_values(by='ord_id', inplace=True)
        # self.lb_df.reset_index(drop=True, inplace=True)
        self.lb_df.set_index("ord_id", inplace=True)
        self.lf_df.set_index("ord_id", inplace=True)
        self.sf_df.set_index("ord_id", inplace=True)
        self.ct_df.set_index("ord_id", inplace=True)
        self.lf_df = self.lf_df[self.lf_df.index.isin(self.lb_df.index)]
        self.sf_df = self.sf_df[self.sf_df.index.isin(self.lb_df.index)]
        self.ct_df = self.ct_df[self.ct_df.index.isin(self.lb_df.index)]
        self.lb_df = self.lb_df[self.lb_df.index.isin(self.lf_df.index)]

        # 汇总后保存
        # self.lf_df.to_csv("%s/factors_%s_lf.csv"%(self._data_path, self._direct), index=0)
        # self.sf_df.to_csv("%s/factors_%s_sf.csv"%(self._data_path, self._direct), index=0)
        # self.ct_df.to_csv("%s/factors_%s_ct.csv"%(self._data_path, self._direct), index=0)
        # self.lb_df.to_csv("%s/orders_%s_label.csv"%(self._data_path, self._direct), index=0)

        print(f'Feature total: {len(self.lf_df)}')
        # print(self.lb_df.label.value_counts())
        print(f"Today add {self._direct} count: {(self.lb_df['datetime'] >= datetime.datetime.now().strftime('%Y%m%d 00:00:00')).sum()}")
        
    def _label_encode(self):

        # Encoding instrument_id for embeddings
        le = LabelEncoder()
        self.lb_df['code_encoded'] = le.fit_transform(self.lb_df['CODE'].values)
    
    def _get_folds(self):
        skf = StratifiedKFold(n_splits=self.n_splits, shuffle=self.shuffle, random_state=self.random_state)
        for fold, (_, val_idx) in enumerate(skf.split(X=self.lb_df, y=self.lb_df['label']), 1):
            self.lb_df.loc[val_idx, 'fold'] = fold
        self.lb_df['fold'] = self.lb_df['fold'].astype(np.uint8)

    def _dump_input_param_json(self):
        f_sel = {}
        f_sel['codes'] = sorted(self.lb_df.CODE.unique().tolist())

        f_sel['mean'] = self.lf_df.values.mean(axis=0).tolist()
        f_sel['mean'] += (self.sf_df.values.mean(axis=0).tolist())
        f_sel['mean'] += (self.ct_df.values.mean(axis=0).tolist())
        f_sel['std'] = self.lf_df.values.std(axis=0).tolist()
        f_sel['std'] += (self.sf_df.values.std(axis=0).tolist())
        f_sel['std'] += (self.ct_df.values.std(axis=0).tolist())

        f_sel['lf_len'] = self.lf_df.shape[1]
        f_sel['sf_len'] = self.sf_df.shape[1]
        f_sel['ct_len'] = self.ct_df.shape[1]

        with open(f'{self._data_path}/using_factor.json', 'r') as using_file:
            using_factor = json.load(using_file)
            f_sel.update(using_factor)
        f_sel.update(self.interface_params)
        if self._model_name_suff != "":
            jfile_name = f"{self._model_name}_{self._model_name_suff}_{self._direct}"
        else:
            jfile_name = f"{self._model_name}_{self._direct}"
        with open(f'{self._model_path}/{jfile_name}.json', 'w') as factor_sel_file:
            json.dump(f_sel, factor_sel_file)
                        
    def transform(self):
        self._load_data()
        self._label_encode()
        # self._get_folds()
        self._dump_input_param_json()
        
        return self.lb_df, self.lf_df, self.sf_df, self.ct_df

    def get_num_embeddings(self):
        return len(self.lb_df.CODE.unique())    

    def load(self, instruments, start_time=None, end_time=None) -> pd.DataFrame:
        return self.transform()   
        