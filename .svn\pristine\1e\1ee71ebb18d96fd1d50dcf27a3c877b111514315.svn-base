import qlib
from qlib.config import R<PERSON>_CN
from qlib.utils import init_instance_by_config, flatten_dict
from qlib.workflow import R

# from pyqlab.data.dataset.handler import DataHandlerAF
from pyqlab.data.dataset.handler import DataHandlerAHF

import pandas as pd
import matplotlib.pyplot as plt
import ipywidgets as widgets

import warnings
warnings.filterwarnings("ignore")

provider_uri = "~/.qlib/qlib_data/cn_data"  # target_dir
qlib.init(provider_uri=provider_uri, region=REG_CN)

###################################
# train model
###################################
SEL_LONG_FACTOR_NAMES = [ # Slow period factor
    # "MACD", "MACD_DIFF", "MACD_DEA", "MOM", "RSI",

    # "LR_SLOPE_FAST", "LR_SLOPE_MIDD", "LR_SLOPE_SLOW",
    # "LR_SLOPE_FAST_THRESHOLD", "LR_SLOPE_SLOW_THRESHOLD",

    # "SQUEEZE_ZERO_BARS", 
    # "SQUEEZE_GAP", "SQUEEZE_GAP_FAST", "SQUEEZE_GAP_SLOW",
    # "SQUEEZE_GAP_THRESHOLD", "SQUEEZE_NARROW_BARS",

    # "BAND_POSITION", "BAND_WIDTH",
    # "BAND_EXPAND", "BAND_GRADIENT", "BAND_GRADIENT_THRESHOLD", "BAND_GAP",

    # "TL_FAST", "TL_SLOW", "TL_THRESHOLD",

    # "TREND_VALUE", "TREND_BARS", "TREND_INBARS", "TREND_INPOSR", "TREND_HLR",
    # "TREND_LEVEL"
]

SEL_SHORT_FACTOR_NAMES = [ # Fast period factor
    "MACD", "MACD_DIFF", "MACD_DEA", "MOM", "RSI",

    "LR_SLOPE_FAST", "LR_SLOPE_MIDD", "LR_SLOPE_SLOW",
    "LR_SLOPE_FAST_THRESHOLD", "LR_SLOPE_SLOW_THRESHOLD",

    "SQUEEZE_ZERO_BARS", 
    "SQUEEZE_GAP", "SQUEEZE_GAP_FAST", "SQUEEZE_GAP_SLOW",
    "SQUEEZE_GAP_THRESHOLD", "SQUEEZE_NARROW_BARS",

    "BAND_POSITION", "BAND_WIDTH",
    "BAND_EXPAND", "BAND_GRADIENT", "BAND_GRADIENT_THRESHOLD", "BAND_GAP",

    "TL_FAST", "TL_SLOW", "TL_THRESHOLD",

    "TREND_VALUE",
    "TREND_BARS",
    "TREND_INBARS",
    "TREND_INPOSR", "TREND_HLR",
    "TREND_LEVEL"
]

SEL_CONTEXT_FACTOR_NAMES = [
#   "STDDEV_RNG", "SHORT_RANGE",
#   "FAST_QH_RSI", "FAST_QH_ZSCORE", "FAST_QH_DIRECT",
#   "FAST_QH_NATR", "FAST_QH_NATR_ZSCORE", "FAST_QH_NATR_DIRECT",
#   "FAST_QH_MOM", "FAST_QH_MOM_ZSCORE", "FAST_QH_MOM_DIRECT",
]

pfs = {
    '5HF_20_SC': ['sel.2020',],
    # '5HF_23_MAIN': ['sel.2023',],
    # '5HF_23_SEL': ['sel.2023',],
    # '5HF_23_SF': ['sel.2023',],
}

data_handler_config = {
    "start_time": "",
    "end_time": "",
    "instruments": ['2020',],
    "kwargs": {
        "win": 5,
        "step": 3,                      # 采样步长
        "direct": "long",
        "model_name": "MLP1D",
        "model_name_suff": "5HF",
        "model_path": "e:/lab/RoboQuant/pylab/model",
        "sel_lf_names": SEL_LONG_FACTOR_NAMES,
        "sel_sf_names": SEL_SHORT_FACTOR_NAMES,
        "sel_ct_names": SEL_CONTEXT_FACTOR_NAMES,
    },
    "data_loader": {
        "class": "AHFDataLoader",
        "module_path": "pyqlab.data.dataset.loader",
        "kwargs": {
            "data_path": "e:/featdata",
            "only_trading_code": False,   # 仅仅常用交易标的
        },
    },
}

dataset_config = {
    "class": "AHFDatasetH",
    "module_path": "pyqlab.data.dataset",
    "kwargs": {
        "handler": {
            "class": "DataHandlerAHF",
            "module_path": "pyqlab.data.dataset.handler",
            "kwargs": data_handler_config,
        },
        "segments": ["train", "valid"],
        "col_set": ["feature", "label", "encoded"],
    },
}

task = {
    "model": {
        "class": "MLP1DModelPytorch",
        "module_path": "pyqlab.contrib.model.pytorch_mlp1d",
        "kwargs": {
            "loss": "binary",
            "num_code": 60,
            "num_channels": 5,
            "num_input": 45,
            "output_dim": 1,
            "dropout": 0.4,
            "lr": 0.001,
            "lr_decay": 0.99,
            "lr_decay_steps": 100,
            "optimizer": "adam",
            "batch_size": 256,
            "GPU": 0,
            "early_stop": 10,
            "best_cond": "accuracy", # or loss
            "layers": (256, 128, 32),
        },
    },
}

def plt_show(df, title=""):
    wdt = widgets.Output()
    wdt.clear_output(wait=False)
    with wdt:
        ylim = [df.min().min(), df.quantile(0.95).max()]
        ylim[0] -= (ylim[1] - ylim[0]) * 0.05
        df.plot(color=['C1', 'C0'], style=['--', '-'], ylim=ylim, title=title)
        plt.show()

def display_result(evals_result):
    for key, val in evals_result.items():
        if not isinstance(val, dict):
            plt_show(pd.DataFrame(evals_result), key)
            break
        else:
            plt_show(pd.DataFrame(val), key)
            

# 优化: datahandler可以重复使用，提高运行效率
# prepare processed data in memory.
data_handler_config["kwargs"]["direct"] = "long"
hd_long: DataHandlerAHF = init_instance_by_config(dataset_config["kwargs"]["handler"])
data_handler_config["kwargs"]["direct"] = "short"
hd_short: DataHandlerAHF = init_instance_by_config(dataset_config["kwargs"]["handler"])

def trainer(show=True, pfs_name=None, reset_hp=True, train_result={}):
    for direct in ["long", "short"]:
        if reset_hp:
            hparam_df=pd.read_csv("./data/HP_MLP.csv")
            if not hparam_df.empty:
                hparam_df.set_index("model_name", inplace=True)
                task["model"]["kwargs"]["dropout"]=hparam_df.loc[f"MLP_{pfs_name}_{direct}", "dropout"]
                task["model"]["kwargs"]["lr"]=hparam_df.loc[f"MLP_{pfs_name}_{direct}", "lr"]
                print(f'Set dropout: {task["model"]["kwargs"]["dropout"]}')
                print(f'Set lr: {task["model"]["kwargs"]["lr"]}')

        if pfs_name:
            data_handler_config["instruments"] = pfs[pfs_name]
            data_handler_config["kwargs"]["model_name_suff"] = pfs_name

        if direct=="long":
            data_handler_config["kwargs"]["direct"] = "long"
            dataset_config["kwargs"]["handler"] = hd_long
        else:
            data_handler_config["kwargs"]["direct"] = "short"
            dataset_config["kwargs"]["handler"] = hd_short
        # dataset_config["kwargs"]["handler"] = init_instance_by_config(dataset_config["kwargs"]["handler"])
        dataset = init_instance_by_config(dataset_config)
        dataset.setup_data(handler_kwargs=data_handler_config)

        # model initiaiton
        model = init_instance_by_config(task["model"])

        # start exp to train model
        with R.start(experiment_name="train_model"):
            R.log_params(**flatten_dict(task))
            path=data_handler_config["kwargs"]["model_path"]
            name=data_handler_config["kwargs"]["model_name"]
            
            if pfs_name:
                model_name=f"{name}_{pfs_name}_{direct}"
            else:
                model_name=f"{name}_{direct}"
            model_path=f"{path}/{model_name}.model"

            result={}
            best_epoch, best_loss, best_acc = model.fit(
                dataset,
                evals_result=result,
                save_path=model_path,
                # save_jit_script=False
            )
            train_result[model_name] = []
            train_result[model_name].append(best_epoch)
            train_result[model_name].append(round(best_loss, 6))
            train_result[model_name].append(round(best_acc, 3))
            # R.save_objects(trained_model=model)
            # rid = R.get_recorder().id
            # preds = np.array(model.predict(dataset, segment="valid"))
            # valid_auc = roc_auc_score(y_score=preds[:,1], y_true=y_valid)
            # print("valid accuracy: ", model.test(dataset, segment="valid"))
            if show:
                display_result(result)

if __name__ == "__main__":
    result={}
    for pf in pfs.keys():
        trainer(show=False, pfs_name=pf, reset_hp=False, train_result=result)
        print("============train dataset=============")
        print(f"long dataset length: {len(hd_long.x_data)}")
        print(f"short dataset length: {len(hd_short.x_data)}")
    print("============train result=============")
    for key, item in result.items():
        print(key, item)
    print("============train result=============")
