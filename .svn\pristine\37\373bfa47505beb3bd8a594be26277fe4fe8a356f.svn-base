# %%
import os
import random
from tqdm.notebook import tqdm

import numpy as np
import pandas as pd
pd.set_option('display.max_rows', 500)
pd.set_option('display.max_columns', 500)
pd.set_option('display.width', 1000)

import matplotlib.pyplot as plt
import seaborn as sns

from sklearn.model_selection import StratifiedKFold
from sklearn.preprocessing import LabelEncoder

import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader, SequentialSampler, RandomSampler
import torch.optim as optim
# from fastai.layers import SigmoidRange

import datetime
import copy
import warnings
warnings.filterwarnings("ignore")

# %%
class PreprocessingPipeline:
    """
    生成df table: code, time_id
    """
    def __init__(self, n_splits, shuffle, random_state):
        self.df = pd.DataFrame()
        self.df_train = pd.DataFrame()
        self.df_valid = pd.DataFrame()
        # self.df_train = df_train.copy(deep=True)
        # self.df_test = df_test.copy(deep=True)
        self.n_splits = n_splits
        self.shuffle = shuffle
        self.random_state = random_state
        self.LABEL = "RB8888.SC"

    def _load_data(self):
        self.df = pd.read_parquet(f"../data/tickdata_target.{self.LABEL}.parquet")
        # self.df = df[["label","time_id"]]
        # self.df.drop_duplicates(subset="time_id", inplace=True)
        
    def _label_encode(self):

        # Encoding stock_id for embeddings
        le = LabelEncoder()
        self.df['stock_id_encoded'] = le.fit_transform(self.df['stock_id'].values)
        # self.df_test['stock_id_encoded'] = le.transform(self.df_test['stock_id'].values)
    
    def _get_folds(self):
        
        skf = StratifiedKFold(n_splits=self.n_splits, shuffle=self.shuffle, random_state=self.random_state)
        for fold, (_, val_idx) in enumerate(skf.split(X=self.df, y=self.df['target']), 1):
            self.df.loc[val_idx, 'fold'] = fold
        self.df['fold'] = self.df['fold'].astype(np.uint8)
            
    def transform(self):
        
        # self._label_encode()
        self._load_data()
        self._get_folds()
        # return self.df_train, self.df_test
        fold = 5
        self.df_train, self.df_valid = self.df.loc[self.df['fold'] != fold], self.df.loc[self.df['fold'] == fold]
        return self.df_train, self.df_valid


def visualize_learning_curve(training_losses, validation_losses, title, path=None):
    
    """
    Visualize learning curves of the models

    Parameters
    ----------
    training_losses [array-like of shape (n_epochs)]: Array of training losses computed after every epoch
    validation_losses [array-like of shape (n_epochs)]: Array of validation losses computed after every epoch
    title (str): Title of the plot
    path (str or None): Path of the output file (if path is None, plot is displayed with selected backend)
    """

    fig, ax = plt.subplots(figsize=(32, 8), dpi=100)

    sns.lineplot(
        x=np.arange(1, len(training_losses) + 1),
        y=training_losses,
        ax=ax,
        label='train_loss'
    )
    sns.lineplot(
        x=np.arange(1, len(validation_losses) + 1),
        y=validation_losses,
        ax=ax,
        label='val_loss'
    )

    ax.set_xlabel('Epochs/Steps', size=15, labelpad=12.5)
    ax.set_ylabel('Loss', size=15, labelpad=12.5)
    ax.tick_params(axis='x', labelsize=12.5, pad=10)
    ax.tick_params(axis='y', labelsize=12.5, pad=10)
    ax.legend(prop={'size': 18})
    ax.set_title(title, size=20, pad=15)

    if path is None:
        plt.show()
    else:
        plt.savefig(path)


# Function to calculate first WAP
def calc_wap1(df):
    wap = (df['bid_price1'] * df['ask_size1'] + df['ask_price1'] * df['bid_size1']) / (df['bid_size1'] + df['ask_size1'])
    return wap

# Function to calculate second WAP
def calc_wap2(df):
    wap = (df['bid_price2'] * df['ask_size2'] + df['ask_price2'] * df['bid_size2']) / (df['bid_size2'] + df['ask_size2'])
    return wap

def calc_wap3(df):
    wap = (df['bid_price1'] * df['bid_size1'] + df['ask_price1'] * df['ask_size1']) / (df['bid_size1'] + df['ask_size1'])
    return wap

def calc_wap4(df):
    wap = (df['bid_price2'] * df['bid_size2'] + df['ask_price2'] * df['ask_size2']) / (df['bid_size2'] + df['ask_size2'])
    return wap

# Function to calculate the log of the return
# Remember that logb(x / y) = logb(x) - logb(y)
def log_return(series):
    return np.log(series).diff()

# Calculate the realized volatility
def realized_volatility(series):
    return np.sqrt(np.sum(series**2))

# Function to count unique elements of a series
def count_unique(series):
    return len(np.unique(series))

def set_seed(seed, deterministic_cudnn=False):

    """
    Set random seed for reproducible results
    
    Parameters
    ----------
    seed (int): Random seed
    deterministic_cudnn (bool): Whether to set deterministic cuDNN or not
    """

    if deterministic_cudnn:
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False

    os.environ['PYTHONHASHSEED'] = str(seed)
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)


def rmspe_metric(y_true, y_pred):

    """
    Calculate root mean squared percentage error between ground-truth and predictions
    
    Parameters
    ----------
    y_true [array-like of shape (n_samples)]: Ground-truth
    y_pred [array-like of shape (n_samples)]: Predictions
    
    Returns
    -------
    rmspe (float): Root mean squared percentage error
    """

    rmspe = np.sqrt(np.mean(np.square((y_true - y_pred) / y_true)))
    return rmspe


def rmspe_loss(y_true, y_pred):

    """
    Calculate root mean squared percentage error between ground-truth and predictions
    
    Parameters
    ----------
    y_true [torch.tensor of shape (n_samples)]: Ground-truth
    y_pred [torch.tensor of shape (n_samples)]: Predictions
    
    Returns
    -------
    rmspe (torch.FloatTensor): Root mean squared percentage error
    """

    rmspe = torch.sqrt(torch.mean(torch.square((y_true - y_pred) / y_true)))
    return rmspe

# 定义计算正确率函数
def accuracy(y_true, y_pred):
    # preds = (out>0.5).type(torch.IntTensor)
    # return (preds == yb).float().mean()
    return (y_true == y_pred).astype(float).mean()



class Factor2DDataset(Dataset):

    def __init__(self, df, flip_probability=0.):

        self.LABEL = "RB8888.SC"
        self.df = df
        self.data_df = pd.read_parquet(f"../data/tickdata.{self.LABEL}.parquet")
        self.data_df.set_index(["label", "time_id"], inplace=True)

        '''
        # Normalizing sequences with global means and stds across stocks
        book_means = np.array([
            0.99969482421875, 1.000321388244629, 0.9995064735412598, 1.0005191564559937,
            769.990177708821, 766.7345672818379, 959.3416027831918, 928.2202512713748,
            1.0000068043192514, 1.0000055320253616, 5.129816581143487e-08, 9.831598141593519e-08
        ])
        book_stds = np.array([
            0.0036880988627672195, 0.003687119111418724, 0.0037009266670793295, 0.0036990800872445107,
            5354.051690318169, 4954.947103063445, 6683.816183660414, 5735.299917793827,
            0.003689893218043926, 0.00370745215558702, 6.618708642293018e-07, 1.2508970015188411e-06
        ])
        # Not normalizing trade price and trade price log returns because of the sparsity
        trade_means = np.array([0, 352.9736760331942, 4.1732040971227145, 0])
        trade_stds = np.array([1, 1041.9441951057488, 7.79955795393431, 1])
        self.transforms = {
            'flip': flip_probability,
            'normalize': {
                'book_means': book_means,
                'book_stds': book_stds,
                'trade_means': trade_means,
                'trade_stds': trade_stds
            }
        }
        '''

    def _completion(self, df):
        i=None
        data=[]
        for idx, row in df.iterrows():
            if not i:
                i=int(row['datetime'])
                continue
            i=i+1
            if i==int(row['datetime']):
                continue
            elif len(df)+len(data)<300:
                tmp=row
                tmp['datetime']=i
                data.append(tmp)
                print(f"add: {i}")

        if len(data) > 0:
            df = df.append(data)
        for i in range(300-len(df)):
            df = df.append(df.iloc[-1])
        df = df.sort_values(by='datetime')
        return df
        

    def __len__(self):
        return len(self.df)

    def __getitem__(self, idx):

        """
        Get the idxth element in the dataset

        Parameters
        ----------
        idx (int): Index of the sample (0 <= idx < len(self.df))

        Returns
        -------
        stock_id_encoded [torch.LongTensor of shape (1)]: Encoded stock_id for stock embeddings
        sequences [torch.FloatTensor of shape (600, 16)]: Concatenated sequences from book and trade data
        target [torch.Tensor of shape (1)]: Target
        """

        sample = self.df.iloc[idx]
        stock_id = sample['label']
        time_id = sample['time_id']


        # Sequences from book data
        df = self.data_df.loc[(stock_id, time_id)]
        if len(df) < 300:
            df = self._completion(df)
        # df['datetime']=df['datetime'].apply(datetime.datetime.fromtimestamp)
        # print(df['datetime'])

        # Calculate Wap
        df['wap1'] = calc_wap1(df)
        df['wap2'] = calc_wap2(df)
        df['wap3'] = calc_wap3(df)
        df['wap4'] = calc_wap4(df)

        # Calculate log returns
        df['log_return1'] = df.groupby(['time_id'])['wap1'].apply(log_return)
        df['log_return2'] = df.groupby(['time_id'])['wap2'].apply(log_return)
        df['log_return3'] = df.groupby(['time_id'])['wap3'].apply(log_return)
        df['log_return4'] = df.groupby(['time_id'])['wap4'].apply(log_return)

        # Calculate wap balance
        df['wap_balance'] = abs(df['wap1'] - df['wap2'])
        # Calculate spread
        df['price_spread'] = (df['ask_price1'] - df['bid_price1']) / ((df['ask_price1'] + df['bid_price1']) / 2)
        df['price_spread2'] = (df['ask_price2'] - df['bid_price2']) / ((df['ask_price2'] + df['bid_price2']) / 2)
        df['bid_spread'] = df['bid_price1'] - df['bid_price2']
        df['ask_spread'] = df['ask_price1'] - df['ask_price2']
        df["bid_ask_spread"] = abs(df['bid_spread'] - df['ask_spread'])
        df['total_volume'] = (df['ask_size1'] + df['ask_size2']) + (df['bid_size1'] + df['bid_size2'])
        df['volume_imbalance'] = abs((df['ask_size1'] + df['ask_size2']) - (df['bid_size1'] + df['bid_size2']))

        df.drop(['datetime'], axis=1, inplace=True)
        sequences = torch.as_tensor(np.array(df.values), dtype=torch.float)

        # Flip sequences on zeroth dimension
        # if np.random.rand() < self.transforms['flip']:
        #     sequences = torch.flip(sequences, dims=[0])
            
        target = sample['target']
        target = torch.as_tensor(target, dtype=torch.float)
        return sequences, target

class Conv1dBlock(nn.Module):

    def __init__(self, in_channels, out_channels, kernel_size=(5,), stride=(1,), padding=(2,), skip_connection=False):

        super(Conv1dBlock, self).__init__()

        self.skip_connection = skip_connection
        self.conv_block = nn.Sequential(
            nn.Conv1d(in_channels, out_channels, kernel_size=kernel_size, stride=stride, padding=padding, padding_mode='replicate', bias=True),
            nn.BatchNorm1d(out_channels),
            nn.ReLU(inplace=True),
            nn.Conv1d(out_channels, out_channels, kernel_size=kernel_size, stride=stride, padding=padding, padding_mode='replicate', bias=True),
            nn.BatchNorm1d(out_channels),
        )
        self.downsample = nn.Sequential(
            nn.Conv1d(in_channels, out_channels, kernel_size=(1,), stride=(1,), bias=False),
            nn.BatchNorm1d(out_channels)
        )
        self.relu = nn.ReLU(inplace=True)

    def forward(self, x):

        print("================================")
        output = self.conv_block(x)
        print(output)
        if self.skip_connection:
            x = self.downsample(x)
            output += x
        output = self.relu(output)

        return output

class CNN1DModel(nn.Module):

    def __init__(self, in_channels): # num_embeddings

        super(CNN1DModel, self).__init__()

        # self.stock_embeddings = nn.Embedding(num_embeddings=num_embeddings, embedding_dim=10)
        self.conv_block1 = Conv1dBlock(in_channels=in_channels, out_channels=32, skip_connection=True)
        self.conv_block2 = Conv1dBlock(in_channels=32, out_channels=64, skip_connection=True)
        self.conv_block3 = Conv1dBlock(in_channels=64, out_channels=128, skip_connection=True)
        self.conv_block4 = Conv1dBlock(in_channels=128, out_channels=64, skip_connection=True)
        self.conv_block5 = Conv1dBlock(in_channels=64, out_channels=32, skip_connection=True)
        self.conv_block6 = Conv1dBlock(in_channels=32, out_channels=16, skip_connection=True)
        self.conv_block7 = Conv1dBlock(in_channels=16, out_channels=8, skip_connection=True)
        self.conv_block8 = Conv1dBlock(in_channels=8, out_channels=1, skip_connection=True)
        self.pooling = nn.AvgPool1d(kernel_size=(3,), stride=(1,), padding=(1,))
        self.linear = nn.Linear(300, 128, bias=True)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.25)
        self.head = nn.Sequential(
            nn.Linear(128, 1, bias=True),
            nn.Sigmoid()
        )

    def forward(self, sequences):

        x = torch.transpose(sequences, 1, 2)
        print(x.shape)
        x = self.conv_block1(x)
        x = self.pooling(x)
        x = self.conv_block2(x)
        x = self.pooling(x)
        x = self.conv_block3(x)
        x = self.pooling(x)
        x = self.conv_block4(x)
        x = self.pooling(x)
        x = self.conv_block5(x)
        x = self.pooling(x)
        x = self.conv_block6(x)
        x = self.pooling(x)
        x = self.conv_block7(x)
        x = self.pooling(x)
        x = self.conv_block8(x)
        x = self.pooling(x)
        x = x.view(x.size(0), -1)
        # embedded_stock_ids = self.stock_embeddings(stock_ids)
        # x = torch.cat([x, self.dropout(embedded_stock_ids)], dim=1)
        x = self.relu(self.linear(x))
        output = self.head(x)
        
        return output.view(-1)


class ConvTrainer:

    def __init__(self, model_name, model_path, model_parameters, training_parameters):

        self.model_name = model_name
        self.model_path = model_path
        self.model_params = model_parameters
        self.training_params = training_parameters

    def get_model(self):

        model = None

        if self.model_name == 'cnn1d':
            model = CNN1DModel(**self.model_params)

        return model

    def train_fn(self, train_loader, model, criterion, optimizer, device):

        # print('\n')
        model.train()
        # progress_bar = tqdm(train_loader)
        losses = []
        correct = 0
        total = 0

        if self.training_params['amp']:
            scaler = torch.cuda.amp.GradScaler()
        else:
            scaler = None

        # for stock_id_encoded, sequences, target in progress_bar:
        for sequences, target in train_loader:

            sequences, target = sequences.to(device), target.to(device)
            if scaler is not None:
                with torch.cuda.amp.autocast():
                    optimizer.zero_grad()
                    output = model(sequences)
                    print(output)
                    loss = criterion(output, target)
                scaler.scale(loss).backward()
                scaler.step(optimizer)
                scaler.update()
                with torch.no_grad():
                    correct += ((output>0.5).type(torch.IntTensor).to(device) == target).sum().item()
                    total += target.size(0)
            else:
                optimizer.zero_grad()
                # print(sequences.shape)
                # print(sequences[-1])                
                output = model(sequences)
                print(output)
                loss = criterion(output, target)
                loss.backward()
                optimizer.step()
                with torch.no_grad():
                    correct += ((output>0.5).type(torch.IntTensor).to(device) == target).sum().item()
                    total += target.size(0)

            losses.append(loss.item())
            average_loss = np.mean(losses)
            # progress_bar.set_description(f'train_rmspe: {average_loss:.6f}')

        train_loss = np.mean(losses)
        train_acc = correct / total
        return train_loss, train_acc

    def val_fn(self, val_loader, model, criterion, device):

        model.eval()
        # progress_bar = tqdm(val_loader)
        losses = []
        correct = 0
        total = 0

        with torch.no_grad():
            
            # for stock_id_encoded, sequences, target in progress_bar:
            for sequences, target in val_loader:
                
                sequences, target = sequences.to(device), target.to(device)
                output = model(sequences)
                print(output)
                loss = criterion(output, target)
                losses.append(loss.item())
                average_loss = np.mean(losses)
                correct += ((output>0.5).type(torch.IntTensor).to(device) == target).sum().item()
                total += target.size(0)
                # progress_bar.set_description(f'valid_rmspe: {average_loss:.6f}')

        val_loss = np.mean(losses)
        val_acc = correct / total
        return val_loss, val_acc

    def train_and_validate(self, df_train, df_valid, direct=None):

        print(f'\n{"-" * 26}\nRunning Model for Training\n{"-" * 26}\n')

        best_model_wts = None
        best_acc = 0.0
        best_fold = 0
        best_epoch = 0


        train_dataset = Factor2DDataset(df=df_train, flip_probability=0.)
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.training_params['batch_size'],
            sampler=RandomSampler(train_dataset),
            pin_memory=True,
            drop_last=False,
            # num_workers=self.training_params['num_workers'],
        )
        
        val_dataset = Factor2DDataset(df=df_valid, flip_probability=0.)
        val_loader = DataLoader(
            val_dataset,
            batch_size=self.training_params['batch_size'],
            sampler=SequentialSampler(val_dataset),
            pin_memory=True,
            drop_last=False,
            # num_workers=self.training_params['num_workers'],
        )

        set_seed(self.training_params['random_state'], deterministic_cudnn=self.training_params['deterministic_cudnn'])
        # device = torch.device('cuda') if torch.cuda.is_available() else torch.device('cpu')
        device = torch.device('cpu') # todo: cuda ERROR.
        model = self.get_model()
        model = model.to(device)

        optimizer = optim.Adam(
            model.parameters(),
            lr=self.training_params['learning_rate'],
            betas=self.training_params['betas'],
            weight_decay=self.training_params['weight_decay']
        )
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode='min',
            patience=self.training_params['reduce_lr_patience'],
            factor=self.training_params['reduce_lr_factor'],
            min_lr=self.training_params['reduce_lr_min'],
            verbose=True
        )

        early_stopping = False
        summary = {
            'train_loss': [],
            'val_loss': [],
            'train_acc': [],
            'val_acc': []
        }
        
        criterion = nn.BCELoss() # rmspe_loss

        for epoch in range(1, self.training_params['epochs'] + 1):

            if early_stopping:
                break

            train_loss, train_acc = self.train_fn(train_loader, model, criterion, optimizer, device)
            val_loss, val_acc = self.val_fn(val_loader, model, criterion, device)
            if epoch%10 == 0:
                print(f'Epoch {epoch} - Training & Validation Loss: [{train_loss:.6f} -  {val_loss:.6f}] Accuracy: [{train_acc:.6f} - {val_acc:.6f}]')
            scheduler.step(val_loss)

            best_val_loss = np.min(summary['val_loss']) if len(summary['val_loss']) > 0 else np.inf
            if val_loss < best_val_loss:
                model_path = f'{self.model_path}/{self.model_name}_{direct}.pt'
                torch.save(model.state_dict(), model_path)
                # print(f'Saving model to {model_path} (validation loss decreased from {best_val_loss:.6f} to {val_loss:.6f})')

            if val_acc > best_acc:
                # best_fold = fold
                best_epoch = epoch
                best_acc = val_acc
                best_model_wts = copy.deepcopy(model.state_dict())


            summary['train_loss'].append(train_loss)
            summary['val_loss'].append(val_loss)
            summary['train_acc'].append(train_acc)
            summary['val_acc'].append(val_acc)

            best_iteration = np.argmin(summary['val_loss']) + 1
            if len(summary['val_loss']) - best_iteration >= self.training_params['early_stopping_patience']:
                print(f'Early stopping (validation loss didn\'t increase for {self.training_params["early_stopping_patience"]} epochs/steps)')
                print(f'Best validation loss is {np.min(summary["val_loss"]):.6f}')
                visualize_learning_curve(
                    training_losses=summary['train_loss'],
                    validation_losses=summary['val_loss'],
                    title=f'{self.model_name} -Learning Curve',
                    path=f'{self.model_path}/{self.model_name}_learning_curve.png'
                )
                early_stopping = True

        # 保持最优模型
        model.load_state_dict(best_model_wts)
        model.eval() # 如果要使用，要调用eval()表明运行模式
        sm = torch.jit.script(model)
        print(f'\n{"-" * 60}\nBest model Fold: {best_fold:d} epoch: {best_epoch:d} accuracy: {best_acc:.3f}\n{"-" * 60}')
        sm.save(f'{self.model_path}/{ self.model_name}_{direct}.model')


def train_cnn1d():
    cnn1d_parameters = {
        'model_name': 'cnn1d',
        'model_path': 'e:/lab/RoboQuant/pylab/model',
        'model_parameters': {
            # 'num_embeddings': 38,
            'in_channels': 26,
        },
        'training_parameters': {
            'amp': False,
            'learning_rate': 0.0005,
            'betas': (0.9, 0.999),
            'weight_decay': 0,
            'epochs': 100,
            'batch_size': 8,
            'reduce_lr_patience': 5,
            'reduce_lr_factor': 0.25,
            'reduce_lr_min': 0.000001,
            'early_stopping_patience': 20,
            'num_workers': 0,
            'random_state': 42,
            'deterministic_cudnn': False,
        }
    }

    preprocessing_parameters = {
        'n_splits': 5,
        'shuffle': True,
        'random_state': 42,
        # 'only_trading_code': True,
        # 'data_path': 'e:/lab/RoboQuant/pylab/data',
        # 'portfolios': ['00200910081133001', '00171106132928000', '00170623114649000'],
        # 'interface_params': {
        #     'input_dim': 1, # 1: expression call 2: API call
        #     'code_encoding': 2, # 0:unsing, 1:onehot, 2:embedding
        # }
    }

    ppp = PreprocessingPipeline(**preprocessing_parameters)

    directs = ['long', 'short']
    for direct in directs:
        print(f'\n{"*" * 30}\n{direct}\n{"*" * 30}')
        df_train, df_valid = ppp.transform()

        trainer = ConvTrainer(**cnn1d_parameters)
        trainer.train_and_validate(df_train, df_valid, direct)


# %%
train_cnn1d()

# %%



