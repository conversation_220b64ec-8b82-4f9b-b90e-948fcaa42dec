from pprint import pprint
# from config import DQN_PARAMS, RLlib_PARAMS

# construct environment
# from rllab.env.env_futtrading import FutureTradingEnv
from rllab.env.env_futures_ls_discrete import FuturesLSDiscEnv


def train(
        drl_lib,
        env,
        model_name,
        **kwargs
):
    # load train data
    # env init
    # env_config = {
    #     "mode": "train",
    # }
    # env_instance = env()

    # env_args = get_gym_env_args(env_instance, True)

    # read parameters
    agent_path = kwargs.get("agent_path", "./" + str(model_name))

    if drl_lib == "rllib":
        total_episodes = kwargs.get("total_episodes", 50)
        rllib_params = kwargs.get("rllib_params")
        from rllab.agents.rllib.models import DRLAgent as DRLAgent_rllib
        agent_rllib = DRLAgent_rllib(env=env)

        model, model_config = agent_rllib.get_model(model_name)

        model_config["lr"] = rllib_params["lr"]
        model_config["train_batch_size"] = rllib_params["train_batch_size"]
        model_config["gamma"] = rllib_params["gamma"]
        model_config["env_config"] = rllib_params["env_config"]
        # pprint(model_config)

        # ray.shutdown()
        trained_model = agent_rllib.train_model(
            model=model,
            model_name=model_name,
            model_config=model_config,
            total_episodes=total_episodes,
            agent_path=agent_path,
            reload=True # 是否继续之前的训练
        )

    elif drl_lib == "stable_baselines3":
        total_timesteps = kwargs.get("total_timesteps", 1e6)
        agent_params = kwargs.get("agent_params")
        from rllab.agents.stablebaselines3.models import DRLAgent as DRLAgent_sb3
        agent = DRLAgent_sb3(env=env_instance)

        model = agent.get_model(model_name, model_kwargs=agent_params)
        trained_model = agent.train_model(
            model=model, tb_log_name=model_name, total_timesteps=total_timesteps
        )
        print("Training finished!")
        trained_model.save(cwd)
        print("Trained model saved in " + str(cwd))
    else:
        raise ValueError("DRL library input is NOT supported. Please check.")


if __name__ == "__main__":

    Env_PARAMS = {
        "name": "FuturesDiscEnv_v1",
        "initial_amount": 1e5,
        "gamma": 0.95,
        "mode": "train",
    }
    RLlib_PARAMS = {
        "lr": 5e-5,
        "train_batch_size": 128,
        "gamma": 0.99,
        "env_config": Env_PARAMS
    }

    env = FuturesLSDiscEnv

    # demo for rllib
    import ray
    ray.shutdown()  # always shutdown previous session if any
    train(
        drl_lib="rllib",
        env=env,
        model_name="ppo",
        agent_path="./model_rl/FuturesLSDiscEnv_opp",
        rllib_params=RLlib_PARAMS,
        total_episodes=100,
    )

    # demo for stable-baselines3
    # train(
    #     drl_lib="stable_baselines3",
    #     env=env,
    #     model_name="dqn",
    #     cwd="./test_dqn",
    #     agent_params=DQN_PARAMS,
    #     total_timesteps=1e5,
    # )
