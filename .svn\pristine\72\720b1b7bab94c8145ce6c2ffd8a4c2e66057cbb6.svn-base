{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from pyecharts import online\n", "import datetime\n", "import talib as ta\n", "import pandas as pd\n", "from pyecharts import Kline\n", "online()\n", "import sys\n", "sys.path.append(\"d:/QuantLab\")\n", "# sys.path.append(\"e:/Lab/RoboQuant/bin/x64/Debug\")\n", "from qtunnel import DataSource,Backtest,BarData,BarSize,DoRight,RunMode"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["ds=DataSource(RunMode.passive)\n", "# print(ds.get_block_data('ZLQH'))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<script>\n", "    require.config({\n", "        paths: {\n", "            'echarts': 'https://pyecharts.github.io/jupyter-echarts/echarts/echarts.min'\n", "        }\n", "    });\n", "</script>\n", "    <div id=\"953c3ee2d583424aa17f1564657036bf\" style=\"width:1000px;height:400px;\"></div>\n", "\n", "\n", "<script>\n", "    require(['echarts'], function(echarts) {\n", "        \n", "var myChart_953c3ee2d583424aa17f1564657036bf = echarts.init(document.getElementById('953c3ee2d583424aa17f1564657036bf'), 'light', {renderer: 'canvas'});\n", "function kline_tooltip_formatter(params) {\n", "    var text;\n", "    text = ((((((((((((params[0].seriesName + \"<br/>\") + \"- open:\") + params[0].data[1]) + \"<br/>\") + \"- close:\") + params[0].data[2]) + \"<br/>\") + \"- lowest:\") + params[0].data[3]) + \"<br/>\") + \"- highest:\") + params[0].data[4]);\n", "    return text;\n", "}\n", "\n", "var option_953c3ee2d583424aa17f1564657036bf = {\n", "    \"title\": [\n", "        {\n", "            \"text\": \"K \\u7ebf\\u56fe\",\n", "            \"left\": \"auto\",\n", "            \"top\": \"auto\",\n", "            \"textStyle\": {\n", "                \"fontSize\": 18\n", "            },\n", "            \"subtextStyle\": {\n", "                \"fontSize\": 12\n", "            }\n", "        }\n", "    ],\n", "    \"toolbox\": {\n", "        \"show\": true,\n", "        \"orient\": \"vertical\",\n", "        \"left\": \"95%\",\n", "        \"top\": \"center\",\n", "        \"feature\": {\n", "            \"saveAsImage\": {\n", "                \"show\": true,\n", "                \"title\": \"save as image\"\n", "            },\n", "            \"restore\": {\n", "                \"show\": true,\n", "                \"title\": \"restore\"\n", "            },\n", "            \"dataView\": {\n", "                \"show\": true,\n", "                \"title\": \"data view\"\n", "            }\n", "        }\n", "    },\n", "    \"series_id\": 2022597,\n", "    \"tooltip\": {\n", "        \"trigger\": \"axis\",\n", "        \"triggerOn\": \"mousemove|click\",\n", "        \"axisPointer\": {\n", "            \"type\": \"line\"\n", "        },\n", "        \"formatter\": kline_tooltip_formatter,\n", "        \"textStyle\": {\n", "            \"fontSize\": 14\n", "        },\n", "        \"backgroundColor\": \"rgba(50,50,50,0.7)\",\n", "        \"borderColor\": \"#333\",\n", "        \"borderWidth\": 0\n", "    },\n", "    \"series\": [\n", "        {\n", "            \"type\": \"candlestick\",\n", "            \"name\": \"MA8888.ZC BarSize.min5 KLine (bar count:500)(ATR:5.227348474200718)\",\n", "            \"data\": [\n", "                [\n", "                    2270.0,\n", "                    2271.0,\n", "                    2269.0,\n", "                    2273.0\n", "                ],\n", "                [\n", "                    2272.0,\n", "                    2271.0,\n", "                    2271.0,\n", "                    2272.0\n", "                ],\n", "                [\n", "                    2271.0,\n", "                    2276.0,\n", "                    2271.0,\n", "                    2278.0\n", "                ],\n", "                [\n", "                    2276.0,\n", "                    2274.0,\n", "                    2273.0,\n", "                    2277.0\n", "                ],\n", "                [\n", "                    2273.0,\n", "                    2275.0,\n", "                    2271.0,\n", "                    2277.0\n", "                ],\n", "                [\n", "                    2284.0,\n", "                    2276.0,\n", "                    2273.0,\n", "                    2288.0\n", "                ],\n", "                [\n", "                    2276.0,\n", "                    2276.0,\n", "                    2272.0,\n", "                    2277.0\n", "                ],\n", "                [\n", "                    2275.0,\n", "                    2272.0,\n", "                    2270.0,\n", "                    2277.0\n", "                ],\n", "                [\n", "                    2272.0,\n", "                    2275.0,\n", "                    2267.0,\n", "                    2276.0\n", "                ],\n", "                [\n", "                    2275.0,\n", "                    2273.0,\n", "                    2273.0,\n", "                    2278.0\n", "                ],\n", "                [\n", "                    2273.0,\n", "                    2271.0,\n", "                    2270.0,\n", "                    2275.0\n", "                ],\n", "                [\n", "                    2270.0,\n", "                    2269.0,\n", "                    2268.0,\n", "                    2273.0\n", "                ],\n", "                [\n", "                    2269.0,\n", "                    2266.0,\n", "                    2263.0,\n", "                    2270.0\n", "                ],\n", "                [\n", "                    2266.0,\n", "                    2268.0,\n", "                    2262.0,\n", "                    2268.0\n", "                ],\n", "                [\n", "                    2267.0,\n", "                    2264.0,\n", "                    2264.0,\n", "                    2268.0\n", "                ],\n", "                [\n", "                    2265.0,\n", "                    2257.0,\n", "                    2254.0,\n", "                    2266.0\n", "                ],\n", "                [\n", "                    2257.0,\n", "                    2254.0,\n", "                    2251.0,\n", "                    2259.0\n", "                ],\n", "                [\n", "                    2254.0,\n", "                    2262.0,\n", "                    2254.0,\n", "                    2264.0\n", "                ],\n", "                [\n", "                    2263.0,\n", "                    2259.0,\n", "                    2255.0,\n", "                    2263.0\n", "                ],\n", "                [\n", "                    2259.0,\n", "                    2257.0,\n", "                    2254.0,\n", "                    2259.0\n", "                ],\n", "                [\n", "                    2257.0,\n", "                    2260.0,\n", "                    2252.0,\n", "                    2260.0\n", "                ],\n", "                [\n", "                    2260.0,\n", "                    2262.0,\n", "                    2258.0,\n", "                    2263.0\n", "                ],\n", "                [\n", "                    2261.0,\n", "                    2270.0,\n", "                    2261.0,\n", "                    2271.0\n", "                ],\n", "                [\n", "                    2271.0,\n", "                    2266.0,\n", "                    2266.0,\n", "                    2271.0\n", "                ],\n", "                [\n", "                    2266.0,\n", "                    2267.0,\n", "                    2265.0,\n", "                    2268.0\n", "                ],\n", "                [\n", "                    2267.0,\n", "                    2266.0,\n", "                    2265.0,\n", "                    2268.0\n", "                ],\n", "                [\n", "                    2267.0,\n", "                    2261.0,\n", "                    2258.0,\n", "                    2267.0\n", "                ],\n", "                [\n", "                    2261.0,\n", "                    2261.0,\n", "                    2260.0,\n", "                    2264.0\n", "                ],\n", "                [\n", "                    2260.0,\n", "                    2261.0,\n", "                    2259.0,\n", "                    2264.0\n", "                ],\n", "                [\n", "                    2262.0,\n", "                    2264.0,\n", "                    2259.0,\n", "                    2266.0\n", "                ],\n", "                [\n", "                    2263.0,\n", "                    2266.0,\n", "                    2263.0,\n", "                    2266.0\n", "                ],\n", "                [\n", "                    2266.0,\n", "                    2273.0,\n", "                    2265.0,\n", "                    2275.0\n", "                ],\n", "                [\n", "                    2272.0,\n", "                    2267.0,\n", "                    2267.0,\n", "                    2274.0\n", "                ],\n", "                [\n", "                    2267.0,\n", "                    2269.0,\n", "                    2267.0,\n", "                    2269.0\n", "                ],\n", "                [\n", "                    2269.0,\n", "                    2264.0,\n", "                    2262.0,\n", "                    2269.0\n", "                ],\n", "                [\n", "                    2265.0,\n", "                    2261.0,\n", "                    2260.0,\n", "                    2266.0\n", "                ],\n", "                [\n", "                    2261.0,\n", "                    2261.0,\n", "                    2256.0,\n", "                    2262.0\n", "                ],\n", "                [\n", "                    2260.0,\n", "                    2260.0,\n", "                    2258.0,\n", "                    2262.0\n", "                ],\n", "                [\n", "                    2260.0,\n", "                    2264.0,\n", "                    2259.0,\n", "                    2264.0\n", "                ],\n", "                [\n", "                    2264.0,\n", "                    2264.0,\n", "                    2261.0,\n", "                    2266.0\n", "                ],\n", "                [\n", "                    2264.0,\n", "                    2265.0,\n", "                    2263.0,\n", "                    2266.0\n", "                ],\n", "                [\n", "                    2266.0,\n", "                    2265.0,\n", "                    2264.0,\n", "                    2270.0\n", "                ],\n", "                [\n", "                    2266.0,\n", "                    2274.0,\n", "                    2265.0,\n", "                    2275.0\n", "                ],\n", "                [\n", "                    2273.0,\n", "                    2277.0,\n", "                    2270.0,\n", "                    2279.0\n", "                ],\n", "                [\n", "                    2278.0,\n", "                    2274.0,\n", "                    2271.0,\n", "                    2278.0\n", "                ],\n", "                [\n", "                    2275.0,\n", "                    2273.0,\n", "                    2271.0,\n", "                    2276.0\n", "                ],\n", "                [\n", "                    2274.0,\n", "                    2281.0,\n", "                    2272.0,\n", "                    2282.0\n", "                ],\n", "                [\n", "                    2281.0,\n", "                    2274.0,\n", "                    2273.0,\n", "                    2282.0\n", "                ],\n", "                [\n", "                    2274.0,\n", "                    2275.0,\n", "                    2271.0,\n", "                    2276.0\n", "                ],\n", "                [\n", "                    2274.0,\n", "                    2278.0,\n", "                    2274.0,\n", "                    2281.0\n", "                ],\n", "                [\n", "                    2288.0,\n", "                    2281.0,\n", "                    2276.0,\n", "                    2288.0\n", "                ],\n", "                [\n", "                    2281.0,\n", "                    2284.0,\n", "                    2278.0,\n", "                    2295.0\n", "                ],\n", "                [\n", "                    2284.0,\n", "                    2276.0,\n", "                    2273.0,\n", "                    2289.0\n", "                ],\n", "                [\n", "                    2275.0,\n", "                    2273.0,\n", "                    2267.0,\n", "                    2275.0\n", "                ],\n", "                [\n", "                    2273.0,\n", "                    2276.0,\n", "                    2269.0,\n", "                    2276.0\n", "                ],\n", "                [\n", "                    2276.0,\n", "                    2273.0,\n", "                    2271.0,\n", "                    2276.0\n", "                ],\n", "                [\n", "                    2274.0,\n", "                    2274.0,\n", "                    2272.0,\n", "                    2278.0\n", "                ],\n", "                [\n", "                    2274.0,\n", "                    2276.0,\n", "                    2273.0,\n", "                    2277.0\n", "                ],\n", "                [\n", "                    2276.0,\n", "                    2276.0,\n", "                    2271.0,\n", "                    2276.0\n", "                ],\n", "                [\n", "                    2276.0,\n", "                    2276.0,\n", "                    2273.0,\n", "                    2276.0\n", "                ],\n", "                [\n", "                    2275.0,\n", "                    2277.0,\n", "                    2274.0,\n", "                    2277.0\n", "                ],\n", "                [\n", "                    2277.0,\n", "                    2278.0,\n", "                    2276.0,\n", "                    2281.0\n", "                ],\n", "                [\n", "                    2277.0,\n", "                    2274.0,\n", "                    2273.0,\n", "                    2278.0\n", "                ],\n", "                [\n", "                    2275.0,\n", "                    2274.0,\n", "                    2272.0,\n", "                    2278.0\n", "                ],\n", "                [\n", "                    2274.0,\n", "                    2277.0,\n", "                    2273.0,\n", "                    2277.0\n", "                ],\n", "                [\n", "                    2277.0,\n", "                    2264.0,\n", "                    2263.0,\n", "                    2279.0\n", "                ],\n", "                [\n", "                    2263.0,\n", "                    2267.0,\n", "                    2258.0,\n", "                    2269.0\n", "                ],\n", "                [\n", "                    2267.0,\n", "                    2268.0,\n", "                    2265.0,\n", "                    2271.0\n", "                ],\n", "                [\n", "                    2269.0,\n", "                    2271.0,\n", "                    2269.0,\n", "                    2276.0\n", "                ],\n", "                [\n", "                    2271.0,\n", "                    2273.0,\n", "                    2269.0,\n", "                    2273.0\n", "                ],\n", "                [\n", "                    2274.0,\n", "                    2272.0,\n", "                    2270.0,\n", "                    2274.0\n", "                ],\n", "                [\n", "                    2272.0,\n", "                    2272.0,\n", "                    2271.0,\n", "                    2274.0\n", "                ],\n", "                [\n", "                    2272.0,\n", "                    2270.0,\n", "                    2270.0,\n", "                    2274.0\n", "                ],\n", "                [\n", "                    2270.0,\n", "                    2271.0,\n", "                    2270.0,\n", "                    2274.0\n", "                ],\n", "                [\n", "                    2271.0,\n", "                    2276.0,\n", "                    2270.0,\n", "                    2278.0\n", "                ],\n", "                [\n", "                    2276.0,\n", "                    2273.0,\n", "                    2272.0,\n", "                    2276.0\n", "                ],\n", "                [\n", "                    2273.0,\n", "                    2276.0,\n", "                    2273.0,\n", "                    2276.0\n", "                ],\n", "                [\n", "                    2276.0,\n", "                    2270.0,\n", "                    2268.0,\n", "                    2276.0\n", "                ],\n", "                [\n", "                    2270.0,\n", "                    2272.0,\n", "                    2269.0,\n", "                    2273.0\n", "                ],\n", "                [\n", "                    2273.0,\n", "                    2267.0,\n", "                    2266.0,\n", "                    2273.0\n", "                ],\n", "                [\n", "                    2277.0,\n", "                    2273.0,\n", "                    2270.0,\n", "                    2277.0\n", "                ],\n", "                [\n", "                    2273.0,\n", "                    2274.0,\n", "                    2272.0,\n", "                    2276.0\n", "                ],\n", "                [\n", "                    2274.0,\n", "                    2272.0,\n", "                    2271.0,\n", "                    2276.0\n", "                ],\n", "                [\n", "                    2271.0,\n", "                    2273.0,\n", "                    2271.0,\n", "                    2274.0\n", "                ],\n", "                [\n", "                    2273.0,\n", "                    2274.0,\n", "                    2272.0,\n", "                    2275.0\n", "                ],\n", "                [\n", "                    2274.0,\n", "                    2276.0,\n", "                    2273.0,\n", "                    2278.0\n", "                ],\n", "                [\n", "                    2275.0,\n", "                    2273.0,\n", "                    2272.0,\n", "                    2276.0\n", "                ],\n", "                [\n", "                    2272.0,\n", "                    2269.0,\n", "                    2264.0,\n", "                    2273.0\n", "                ],\n", "                [\n", "                    2270.0,\n", "                    2268.0,\n", "                    2265.0,\n", "                    2271.0\n", "                ],\n", "                [\n", "                    2268.0,\n", "                    2268.0,\n", "                    2267.0,\n", "                    2272.0\n", "                ],\n", "                [\n", "                    2268.0,\n", "                    2268.0,\n", "                    2265.0,\n", "                    2269.0\n", "                ],\n", "                [\n", "                    2268.0,\n", "                    2271.0,\n", "                    2266.0,\n", "                    2271.0\n", "                ],\n", "                [\n", "                    2271.0,\n", "                    2264.0,\n", "                    2262.0,\n", "                    2272.0\n", "                ],\n", "                [\n", "                    2264.0,\n", "                    2268.0,\n", "                    2262.0,\n", "                    2269.0\n", "                ],\n", "                [\n", "                    2267.0,\n", "                    2266.0,\n", "                    2265.0,\n", "                    2268.0\n", "                ],\n", "                [\n", "                    2266.0,\n", "                    2267.0,\n", "                    2265.0,\n", "                    2269.0\n", "                ],\n", "                [\n", "                    2268.0,\n", "                    2270.0,\n", "                    2267.0,\n", "                    2272.0\n", "                ],\n", "                [\n", "                    2270.0,\n", "                    2270.0,\n", "                    2268.0,\n", "                    2271.0\n", "                ],\n", "                [\n", "                    2271.0,\n", "                    2272.0,\n", "                    2269.0,\n", "                    2272.0\n", "                ],\n", "                [\n", "                    2271.0,\n", "                    2272.0,\n", "                    2269.0,\n", "                    2275.0\n", "                ],\n", "                [\n", "                    2273.0,\n", "                    2269.0,\n", "                    2268.0,\n", "                    2274.0\n", "                ],\n", "                [\n", "                    2269.0,\n", "                    2270.0,\n", "                    2269.0,\n", "                    2272.0\n", "                ],\n", "                [\n", "                    2270.0,\n", "                    2273.0,\n", "                    2270.0,\n", "                    2273.0\n", "                ],\n", "                [\n", "                    2273.0,\n", "                    2274.0,\n", "                    2271.0,\n", "                    2275.0\n", "                ],\n", "                [\n", "                    2274.0,\n", "                    2273.0,\n", "                    2272.0,\n", "                    2281.0\n", "                ],\n", "                [\n", "                    2274.0,\n", "                    2273.0,\n", "                    2269.0,\n", "                    2274.0\n", "                ],\n", "                [\n", "                    2272.0,\n", "                    2274.0,\n", "                    2272.0,\n", "                    2275.0\n", "                ],\n", "                [\n", "                    2273.0,\n", "                    2275.0,\n", "                    2270.0,\n", "                    2277.0\n", "                ],\n", "                [\n", "                    2276.0,\n", "                    2274.0,\n", "                    2274.0,\n", "                    2278.0\n", "                ],\n", "                [\n", "                    2274.0,\n", "                    2275.0,\n", "                    2273.0,\n", "                    2278.0\n", "                ],\n", "                [\n", "                    2275.0,\n", "                    2273.0,\n", "                    2271.0,\n", "                    2276.0\n", "                ],\n", "                [\n", "                    2273.0,\n", "                    2271.0,\n", "                    2268.0,\n", "                    2273.0\n", "                ],\n", "                [\n", "                    2270.0,\n", "                    2271.0,\n", "                    2269.0,\n", "                    2272.0\n", "                ],\n", "                [\n", "                    2272.0,\n", "                    2272.0,\n", "                    2270.0,\n", "                    2274.0\n", "                ],\n", "                [\n", "                    2272.0,\n", "                    2275.0,\n", "                    2271.0,\n", "                    2276.0\n", "                ],\n", "                [\n", "                    2275.0,\n", "                    2268.0,\n", "                    2262.0,\n", "                    2275.0\n", "                ],\n", "                [\n", "                    2268.0,\n", "                    2270.0,\n", "                    2265.0,\n", "                    2271.0\n", "                ],\n", "                [\n", "                    2269.0,\n", "                    2268.0,\n", "                    2267.0,\n", "                    2271.0\n", "                ],\n", "                [\n", "                    2268.0,\n", "                    2267.0,\n", "                    2265.0,\n", "                    2269.0\n", "                ],\n", "                [\n", "                    2267.0,\n", "                    2268.0,\n", "                    2262.0,\n", "                    2269.0\n", "                ],\n", "                [\n", "                    2267.0,\n", "                    2266.0,\n", "                    2266.0,\n", "                    2273.0\n", "                ],\n", "                [\n", "                    2266.0,\n", "                    2262.0,\n", "                    2259.0,\n", "                    2267.0\n", "                ],\n", "                [\n", "                    2262.0,\n", "                    2261.0,\n", "                    2255.0,\n", "                    2263.0\n", "                ],\n", "                [\n", "                    2262.0,\n", "                    2261.0,\n", "                    2259.0,\n", "                    2264.0\n", "                ],\n", "                [\n", "                    2261.0,\n", "                    2258.0,\n", "                    2257.0,\n", "                    2262.0\n", "                ],\n", "                [\n", "                    2250.0,\n", "                    2255.0,\n", "                    2247.0,\n", "                    2256.0\n", "                ],\n", "                [\n", "                    2255.0,\n", "                    2259.0,\n", "                    2254.0,\n", "                    2260.0\n", "                ],\n", "                [\n", "                    2259.0,\n", "                    2256.0,\n", "                    2254.0,\n", "                    2259.0\n", "                ],\n", "                [\n", "                    2256.0,\n", "                    2260.0,\n", "                    2255.0,\n", "                    2260.0\n", "                ],\n", "                [\n", "                    2260.0,\n", "                    2264.0,\n", "                    2257.0,\n", "                    2264.0\n", "                ],\n", "                [\n", "                    2264.0,\n", "                    2260.0,\n", "                    2259.0,\n", "                    2264.0\n", "                ],\n", "                [\n", "                    2259.0,\n", "                    2261.0,\n", "                    2257.0,\n", "                    2263.0\n", "                ],\n", "                [\n", "                    2262.0,\n", "                    2258.0,\n", "                    2256.0,\n", "                    2262.0\n", "                ],\n", "                [\n", "                    2258.0,\n", "                    2260.0,\n", "                    2257.0,\n", "                    2261.0\n", "                ],\n", "                [\n", "                    2260.0,\n", "                    2259.0,\n", "                    2257.0,\n", "                    2260.0\n", "                ],\n", "                [\n", "                    2259.0,\n", "                    2256.0,\n", "                    2252.0,\n", "                    2260.0\n", "                ],\n", "                [\n", "                    2256.0,\n", "                    2253.0,\n", "                    2251.0,\n", "                    2256.0\n", "                ],\n", "                [\n", "                    2253.0,\n", "                    2254.0,\n", "                    2253.0,\n", "                    2258.0\n", "                ],\n", "                [\n", "                    2254.0,\n", "                    2251.0,\n", "                    2248.0,\n", "                    2254.0\n", "                ],\n", "                [\n", "                    2252.0,\n", "                    2252.0,\n", "                    2249.0,\n", "                    2254.0\n", "                ],\n", "                [\n", "                    2253.0,\n", "                    2248.0,\n", "                    2247.0,\n", "                    2256.0\n", "                ],\n", "                [\n", "                    2247.0,\n", "                    2241.0,\n", "                    2237.0,\n", "                    2248.0\n", "                ],\n", "                [\n", "                    2242.0,\n", "                    2245.0,\n", "                    2239.0,\n", "                    2246.0\n", "                ],\n", "                [\n", "                    2245.0,\n", "                    2244.0,\n", "                    2243.0,\n", "                    2248.0\n", "                ],\n", "                [\n", "                    2244.0,\n", "                    2244.0,\n", "                    2243.0,\n", "                    2248.0\n", "                ],\n", "                [\n", "                    2245.0,\n", "                    2245.0,\n", "                    2243.0,\n", "                    2248.0\n", "                ],\n", "                [\n", "                    2245.0,\n", "                    2246.0,\n", "                    2243.0,\n", "                    2248.0\n", "                ],\n", "                [\n", "                    2246.0,\n", "                    2248.0,\n", "                    2245.0,\n", "                    2249.0\n", "                ],\n", "                [\n", "                    2248.0,\n", "                    2242.0,\n", "                    2241.0,\n", "                    2248.0\n", "                ],\n", "                [\n", "                    2242.0,\n", "                    2247.0,\n", "                    2241.0,\n", "                    2248.0\n", "                ],\n", "                [\n", "                    2246.0,\n", "                    2242.0,\n", "                    2242.0,\n", "                    2248.0\n", "                ],\n", "                [\n", "                    2242.0,\n", "                    2245.0,\n", "                    2242.0,\n", "                    2247.0\n", "                ],\n", "                [\n", "                    2244.0,\n", "                    2246.0,\n", "                    2244.0,\n", "                    2246.0\n", "                ],\n", "                [\n", "                    2245.0,\n", "                    2245.0,\n", "                    2244.0,\n", "                    2248.0\n", "                ],\n", "                [\n", "                    2245.0,\n", "                    2245.0,\n", "                    2243.0,\n", "                    2247.0\n", "                ],\n", "                [\n", "                    2246.0,\n", "                    2246.0,\n", "                    2242.0,\n", "                    2247.0\n", "                ],\n", "                [\n", "                    2245.0,\n", "                    2229.0,\n", "                    2223.0,\n", "                    2245.0\n", "                ],\n", "                [\n", "                    2229.0,\n", "                    2230.0,\n", "                    2224.0,\n", "                    2233.0\n", "                ],\n", "                [\n", "                    2229.0,\n", "                    2224.0,\n", "                    2217.0,\n", "                    2230.0\n", "                ],\n", "                [\n", "                    2223.0,\n", "                    2222.0,\n", "                    2222.0,\n", "                    2228.0\n", "                ],\n", "                [\n", "                    2223.0,\n", "                    2226.0,\n", "                    2219.0,\n", "                    2226.0\n", "                ],\n", "                [\n", "                    2226.0,\n", "                    2224.0,\n", "                    2220.0,\n", "                    2226.0\n", "                ],\n", "                [\n", "                    2223.0,\n", "                    2227.0,\n", "                    2222.0,\n", "                    2228.0\n", "                ],\n", "                [\n", "                    2226.0,\n", "                    2227.0,\n", "                    2224.0,\n", "                    2228.0\n", "                ],\n", "                [\n", "                    2228.0,\n", "                    2229.0,\n", "                    2225.0,\n", "                    2230.0\n", "                ],\n", "                [\n", "                    2229.0,\n", "                    2230.0,\n", "                    2227.0,\n", "                    2233.0\n", "                ],\n", "                [\n", "                    2230.0,\n", "                    2230.0,\n", "                    2228.0,\n", "                    2232.0\n", "                ],\n", "                [\n", "                    2231.0,\n", "                    2227.0,\n", "                    2226.0,\n", "                    2232.0\n", "                ],\n", "                [\n", "                    2226.0,\n", "                    2225.0,\n", "                    2224.0,\n", "                    2229.0\n", "                ],\n", "                [\n", "                    2225.0,\n", "                    2229.0,\n", "                    2225.0,\n", "                    2229.0\n", "                ],\n", "                [\n", "                    2229.0,\n", "                    2224.0,\n", "                    2224.0,\n", "                    2229.0\n", "                ],\n", "                [\n", "                    2224.0,\n", "                    2224.0,\n", "                    2223.0,\n", "                    2226.0\n", "                ],\n", "                [\n", "                    2225.0,\n", "                    2222.0,\n", "                    2219.0,\n", "                    2226.0\n", "                ],\n", "                [\n", "                    2222.0,\n", "                    2213.0,\n", "                    2211.0,\n", "                    2223.0\n", "                ],\n", "                [\n", "                    2213.0,\n", "                    2219.0,\n", "                    2213.0,\n", "                    2220.0\n", "                ],\n", "                [\n", "                    2219.0,\n", "                    2215.0,\n", "                    2214.0,\n", "                    2220.0\n", "                ],\n", "                [\n", "                    2215.0,\n", "                    2228.0,\n", "                    2215.0,\n", "                    2232.0\n", "                ],\n", "                [\n", "                    2227.0,\n", "                    2228.0,\n", "                    2225.0,\n", "                    2230.0\n", "                ],\n", "                [\n", "                    2227.0,\n", "                    2225.0,\n", "                    2225.0,\n", "                    2228.0\n", "                ],\n", "                [\n", "                    2225.0,\n", "                    2229.0,\n", "                    2225.0,\n", "                    2230.0\n", "                ],\n", "                [\n", "                    2229.0,\n", "                    2228.0,\n", "                    2227.0,\n", "                    2230.0\n", "                ],\n", "                [\n", "                    2228.0,\n", "                    2221.0,\n", "                    2221.0,\n", "                    2229.0\n", "                ],\n", "                [\n", "                    2220.0,\n", "                    2223.0,\n", "                    2220.0,\n", "                    2225.0\n", "                ],\n", "                [\n", "                    2222.0,\n", "                    2225.0,\n", "                    2222.0,\n", "                    2225.0\n", "                ],\n", "                [\n", "                    2225.0,\n", "                    2231.0,\n", "                    2223.0,\n", "                    2235.0\n", "                ],\n", "                [\n", "                    2231.0,\n", "                    2230.0,\n", "                    2228.0,\n", "                    2233.0\n", "                ],\n", "                [\n", "                    2229.0,\n", "                    2228.0,\n", "                    2227.0,\n", "                    2230.0\n", "                ],\n", "                [\n", "                    2229.0,\n", "                    2229.0,\n", "                    2228.0,\n", "                    2231.0\n", "                ],\n", "                [\n", "                    2229.0,\n", "                    2229.0,\n", "                    2227.0,\n", "                    2231.0\n", "                ],\n", "                [\n", "                    2229.0,\n", "                    2230.0,\n", "                    2228.0,\n", "                    2231.0\n", "                ],\n", "                [\n", "                    2230.0,\n", "                    2229.0,\n", "                    2226.0,\n", "                    2230.0\n", "                ],\n", "                [\n", "                    2229.0,\n", "                    2230.0,\n", "                    2226.0,\n", "                    2230.0\n", "                ],\n", "                [\n", "                    2230.0,\n", "                    2239.0,\n", "                    2230.0,\n", "                    2242.0\n", "                ],\n", "                [\n", "                    2239.0,\n", "                    2239.0,\n", "                    2237.0,\n", "                    2242.0\n", "                ],\n", "                [\n", "                    2239.0,\n", "                    2237.0,\n", "                    2236.0,\n", "                    2240.0\n", "                ],\n", "                [\n", "                    2237.0,\n", "                    2239.0,\n", "                    2235.0,\n", "                    2240.0\n", "                ],\n", "                [\n", "                    2239.0,\n", "                    2236.0,\n", "                    2236.0,\n", "                    2239.0\n", "                ],\n", "                [\n", "                    2237.0,\n", "                    2236.0,\n", "                    2235.0,\n", "                    2238.0\n", "                ],\n", "                [\n", "                    2235.0,\n", "                    2230.0,\n", "                    2226.0,\n", "                    2236.0\n", "                ],\n", "                [\n", "                    2230.0,\n", "                    2234.0,\n", "                    2228.0,\n", "                    2236.0\n", "                ],\n", "                [\n", "                    2230.0,\n", "                    2237.0,\n", "                    2225.0,\n", "                    2237.0\n", "                ],\n", "                [\n", "                    2237.0,\n", "                    2236.0,\n", "                    2231.0,\n", "                    2237.0\n", "                ],\n", "                [\n", "                    2236.0,\n", "                    2236.0,\n", "                    2233.0,\n", "                    2237.0\n", "                ],\n", "                [\n", "                    2236.0,\n", "                    2237.0,\n", "                    2235.0,\n", "                    2241.0\n", "                ],\n", "                [\n", "                    2237.0,\n", "                    2235.0,\n", "                    2232.0,\n", "                    2240.0\n", "                ],\n", "                [\n", "                    2235.0,\n", "                    2233.0,\n", "                    2233.0,\n", "                    2236.0\n", "                ],\n", "                [\n", "                    2233.0,\n", "                    2233.0,\n", "                    2231.0,\n", "                    2235.0\n", "                ],\n", "                [\n", "                    2233.0,\n", "                    2233.0,\n", "                    2230.0,\n", "                    2234.0\n", "                ],\n", "                [\n", "                    2233.0,\n", "                    2235.0,\n", "                    2232.0,\n", "                    2237.0\n", "                ],\n", "                [\n", "                    2235.0,\n", "                    2237.0,\n", "                    2234.0,\n", "                    2238.0\n", "                ],\n", "                [\n", "                    2237.0,\n", "                    2234.0,\n", "                    2233.0,\n", "                    2238.0\n", "                ],\n", "                [\n", "                    2234.0,\n", "                    2236.0,\n", "                    2233.0,\n", "                    2236.0\n", "                ],\n", "                [\n", "                    2236.0,\n", "                    2232.0,\n", "                    2230.0,\n", "                    2236.0\n", "                ],\n", "                [\n", "                    2231.0,\n", "                    2233.0,\n", "                    2231.0,\n", "                    2236.0\n", "                ],\n", "                [\n", "                    2234.0,\n", "                    2234.0,\n", "                    2232.0,\n", "                    2236.0\n", "                ],\n", "                [\n", "                    2234.0,\n", "                    2231.0,\n", "                    2230.0,\n", "                    2235.0\n", "                ],\n", "                [\n", "                    2232.0,\n", "                    2232.0,\n", "                    2231.0,\n", "                    2233.0\n", "                ],\n", "                [\n", "                    2232.0,\n", "                    2231.0,\n", "                    2231.0,\n", "                    2233.0\n", "                ],\n", "                [\n", "                    2232.0,\n", "                    2232.0,\n", "                    2230.0,\n", "                    2233.0\n", "                ],\n", "                [\n", "                    2231.0,\n", "                    2229.0,\n", "                    2228.0,\n", "                    2231.0\n", "                ],\n", "                [\n", "                    2229.0,\n", "                    2231.0,\n", "                    2228.0,\n", "                    2232.0\n", "                ],\n", "                [\n", "                    2232.0,\n", "                    2233.0,\n", "                    2231.0,\n", "                    2234.0\n", "                ],\n", "                [\n", "                    2233.0,\n", "                    2232.0,\n", "                    2232.0,\n", "                    2234.0\n", "                ],\n", "                [\n", "                    2232.0,\n", "                    2236.0,\n", "                    2232.0,\n", "                    2236.0\n", "                ],\n", "                [\n", "                    2236.0,\n", "                    2236.0,\n", "                    2234.0,\n", "                    2239.0\n", "                ],\n", "                [\n", "                    2235.0,\n", "                    2235.0,\n", "                    2235.0,\n", "                    2237.0\n", "                ],\n", "                [\n", "                    2235.0,\n", "                    2235.0,\n", "                    2233.0,\n", "                    2237.0\n", "                ],\n", "                [\n", "                    2235.0,\n", "                    2235.0,\n", "                    2234.0,\n", "                    2236.0\n", "                ],\n", "                [\n", "                    2235.0,\n", "                    2234.0,\n", "                    2233.0,\n", "                    2235.0\n", "                ],\n", "                [\n", "                    2234.0,\n", "                    2234.0,\n", "                    2233.0,\n", "                    2235.0\n", "                ],\n", "                [\n", "                    2233.0,\n", "                    2233.0,\n", "                    2229.0,\n", "                    2238.0\n", "                ],\n", "                [\n", "                    2232.0,\n", "                    2225.0,\n", "                    2223.0,\n", "                    2234.0\n", "                ],\n", "                [\n", "                    2226.0,\n", "                    2220.0,\n", "                    2218.0,\n", "                    2227.0\n", "                ],\n", "                [\n", "                    2219.0,\n", "                    2226.0,\n", "                    2219.0,\n", "                    2230.0\n", "                ],\n", "                [\n", "                    2225.0,\n", "                    2220.0,\n", "                    2219.0,\n", "                    2226.0\n", "                ],\n", "                [\n", "                    2220.0,\n", "                    2223.0,\n", "                    2220.0,\n", "                    2226.0\n", "                ],\n", "                [\n", "                    2223.0,\n", "                    2222.0,\n", "                    2221.0,\n", "                    2227.0\n", "                ],\n", "                [\n", "                    2222.0,\n", "                    2225.0,\n", "                    2222.0,\n", "                    2226.0\n", "                ],\n", "                [\n", "                    2225.0,\n", "                    2224.0,\n", "                    2223.0,\n", "                    2226.0\n", "                ],\n", "                [\n", "                    2224.0,\n", "                    2225.0,\n", "                    2223.0,\n", "                    2227.0\n", "                ],\n", "                [\n", "                    2225.0,\n", "                    2230.0,\n", "                    2225.0,\n", "                    2230.0\n", "                ],\n", "                [\n", "                    2230.0,\n", "                    2230.0,\n", "                    2227.0,\n", "                    2233.0\n", "                ],\n", "                [\n", "                    2230.0,\n", "                    2233.0,\n", "                    2229.0,\n", "                    2234.0\n", "                ],\n", "                [\n", "                    2233.0,\n", "                    2237.0,\n", "                    2233.0,\n", "                    2244.0\n", "                ],\n", "                [\n", "                    2238.0,\n", "                    2240.0,\n", "                    2235.0,\n", "                    2240.0\n", "                ],\n", "                [\n", "                    2239.0,\n", "                    2237.0,\n", "                    2236.0,\n", "                    2242.0\n", "                ],\n", "                [\n", "                    2236.0,\n", "                    2239.0,\n", "                    2236.0,\n", "                    2240.0\n", "                ],\n", "                [\n", "                    2239.0,\n", "                    2242.0,\n", "                    2239.0,\n", "                    2245.0\n", "                ],\n", "                [\n", "                    2243.0,\n", "                    2243.0,\n", "                    2242.0,\n", "                    2247.0\n", "                ],\n", "                [\n", "                    2243.0,\n", "                    2242.0,\n", "                    2240.0,\n", "                    2244.0\n", "                ],\n", "                [\n", "                    2242.0,\n", "                    2239.0,\n", "                    2238.0,\n", "                    2243.0\n", "                ],\n", "                [\n", "                    2239.0,\n", "                    2235.0,\n", "                    2234.0,\n", "                    2242.0\n", "                ],\n", "                [\n", "                    2235.0,\n", "                    2236.0,\n", "                    2232.0,\n", "                    2236.0\n", "                ],\n", "                [\n", "                    2235.0,\n", "                    2238.0,\n", "                    2235.0,\n", "                    2239.0\n", "                ],\n", "                [\n", "                    2238.0,\n", "                    2237.0,\n", "                    2235.0,\n", "                    2238.0\n", "                ],\n", "                [\n", "                    2237.0,\n", "                    2241.0,\n", "                    2236.0,\n", "                    2242.0\n", "                ],\n", "                [\n", "                    2240.0,\n", "                    2244.0,\n", "                    2240.0,\n", "                    2246.0\n", "                ],\n", "                [\n", "                    2244.0,\n", "                    2239.0,\n", "                    2238.0,\n", "                    2245.0\n", "                ],\n", "                [\n", "                    2239.0,\n", "                    2230.0,\n", "                    2227.0,\n", "                    2239.0\n", "                ],\n", "                [\n", "                    2229.0,\n", "                    2231.0,\n", "                    2226.0,\n", "                    2231.0\n", "                ],\n", "                [\n", "                    2230.0,\n", "                    2230.0,\n", "                    2228.0,\n", "                    2232.0\n", "                ],\n", "                [\n", "                    2229.0,\n", "                    2231.0,\n", "                    2229.0,\n", "                    2233.0\n", "                ],\n", "                [\n", "                    2231.0,\n", "                    2232.0,\n", "                    2229.0,\n", "                    2232.0\n", "                ],\n", "                [\n", "                    2231.0,\n", "                    2231.0,\n", "                    2230.0,\n", "                    2234.0\n", "                ],\n", "                [\n", "                    2231.0,\n", "                    2231.0,\n", "                    2230.0,\n", "                    2233.0\n", "                ],\n", "                [\n", "                    2231.0,\n", "                    2232.0,\n", "                    2231.0,\n", "                    2233.0\n", "                ],\n", "                [\n", "                    2233.0,\n", "                    2232.0,\n", "                    2231.0,\n", "                    2233.0\n", "                ],\n", "                [\n", "                    2233.0,\n", "                    2232.0,\n", "                    2229.0,\n", "                    2233.0\n", "                ],\n", "                [\n", "                    2232.0,\n", "                    2232.0,\n", "                    2229.0,\n", "                    2232.0\n", "                ],\n", "                [\n", "                    2231.0,\n", "                    2233.0,\n", "                    2231.0,\n", "                    2235.0\n", "                ],\n", "                [\n", "                    2233.0,\n", "                    2234.0,\n", "                    2232.0,\n", "                    2236.0\n", "                ],\n", "                [\n", "                    2235.0,\n", "                    2234.0,\n", "                    2233.0,\n", "                    2237.0\n", "                ],\n", "                [\n", "                    2234.0,\n", "                    2232.0,\n", "                    2231.0,\n", "                    2235.0\n", "                ],\n", "                [\n", "                    2231.0,\n", "                    2230.0,\n", "                    2228.0,\n", "                    2232.0\n", "                ],\n", "                [\n", "                    2231.0,\n", "                    2229.0,\n", "                    2228.0,\n", "                    2231.0\n", "                ],\n", "                [\n", "                    2234.0,\n", "                    2235.0,\n", "                    2228.0,\n", "                    2236.0\n", "                ],\n", "                [\n", "                    2235.0,\n", "                    2235.0,\n", "                    2233.0,\n", "                    2240.0\n", "                ],\n", "                [\n", "                    2235.0,\n", "                    2235.0,\n", "                    2234.0,\n", "                    2239.0\n", "                ],\n", "                [\n", "                    2235.0,\n", "                    2237.0,\n", "                    2235.0,\n", "                    2239.0\n", "                ],\n", "                [\n", "                    2237.0,\n", "                    2232.0,\n", "                    2229.0,\n", "                    2238.0\n", "                ],\n", "                [\n", "                    2231.0,\n", "                    2231.0,\n", "                    2230.0,\n", "                    2235.0\n", "                ],\n", "                [\n", "                    2232.0,\n", "                    2234.0,\n", "                    2231.0,\n", "                    2234.0\n", "                ],\n", "                [\n", "                    2234.0,\n", "                    2232.0,\n", "                    2232.0,\n", "                    2235.0\n", "                ],\n", "                [\n", "                    2232.0,\n", "                    2232.0,\n", "                    2231.0,\n", "                    2235.0\n", "                ],\n", "                [\n", "                    2232.0,\n", "                    2229.0,\n", "                    2226.0,\n", "                    2232.0\n", "                ],\n", "                [\n", "                    2229.0,\n", "                    2228.0,\n", "                    2223.0,\n", "                    2230.0\n", "                ],\n", "                [\n", "                    2229.0,\n", "                    2228.0,\n", "                    2223.0,\n", "                    2229.0\n", "                ],\n", "                [\n", "                    2227.0,\n", "                    2228.0,\n", "                    2226.0,\n", "                    2229.0\n", "                ],\n", "                [\n", "                    2228.0,\n", "                    2228.0,\n", "                    2227.0,\n", "                    2230.0\n", "                ],\n", "                [\n", "                    2228.0,\n", "                    2226.0,\n", "                    2225.0,\n", "                    2228.0\n", "                ],\n", "                [\n", "                    2226.0,\n", "                    2228.0,\n", "                    2226.0,\n", "                    2229.0\n", "                ],\n", "                [\n", "                    2228.0,\n", "                    2231.0,\n", "                    2228.0,\n", "                    2231.0\n", "                ],\n", "                [\n", "                    2231.0,\n", "                    2231.0,\n", "                    2228.0,\n", "                    2231.0\n", "                ],\n", "                [\n", "                    2231.0,\n", "                    2235.0,\n", "                    2230.0,\n", "                    2236.0\n", "                ],\n", "                [\n", "                    2235.0,\n", "                    2234.0,\n", "                    2233.0,\n", "                    2236.0\n", "                ],\n", "                [\n", "                    2233.0,\n", "                    2235.0,\n", "                    2233.0,\n", "                    2236.0\n", "                ],\n", "                [\n", "                    2234.0,\n", "                    2230.0,\n", "                    2230.0,\n", "                    2235.0\n", "                ],\n", "                [\n", "                    2231.0,\n", "                    2233.0,\n", "                    2230.0,\n", "                    2233.0\n", "                ],\n", "                [\n", "                    2233.0,\n", "                    2235.0,\n", "                    2231.0,\n", "                    2235.0\n", "                ],\n", "                [\n", "                    2235.0,\n", "                    2231.0,\n", "                    2230.0,\n", "                    2235.0\n", "                ],\n", "                [\n", "                    2231.0,\n", "                    2232.0,\n", "                    2230.0,\n", "                    2233.0\n", "                ],\n", "                [\n", "                    2232.0,\n", "                    2231.0,\n", "                    2230.0,\n", "                    2233.0\n", "                ],\n", "                [\n", "                    2230.0,\n", "                    2228.0,\n", "                    2227.0,\n", "                    2231.0\n", "                ],\n", "                [\n", "                    2228.0,\n", "                    2225.0,\n", "                    2223.0,\n", "                    2229.0\n", "                ],\n", "                [\n", "                    2225.0,\n", "                    2225.0,\n", "                    2222.0,\n", "                    2226.0\n", "                ],\n", "                [\n", "                    2223.0,\n", "                    2228.0,\n", "                    2220.0,\n", "                    2228.0\n", "                ],\n", "                [\n", "                    2228.0,\n", "                    2202.0,\n", "                    2194.0,\n", "                    2228.0\n", "                ],\n", "                [\n", "                    2201.0,\n", "                    2200.0,\n", "                    2199.0,\n", "                    2207.0\n", "                ],\n", "                [\n", "                    2200.0,\n", "                    2199.0,\n", "                    2198.0,\n", "                    2203.0\n", "                ],\n", "                [\n", "                    2200.0,\n", "                    2200.0,\n", "                    2198.0,\n", "                    2204.0\n", "                ],\n", "                [\n", "                    2201.0,\n", "                    2197.0,\n", "                    2196.0,\n", "                    2201.0\n", "                ],\n", "                [\n", "                    2197.0,\n", "                    2201.0,\n", "                    2191.0,\n", "                    2201.0\n", "                ],\n", "                [\n", "                    2200.0,\n", "                    2201.0,\n", "                    2198.0,\n", "                    2203.0\n", "                ],\n", "                [\n", "                    2202.0,\n", "                    2199.0,\n", "                    2197.0,\n", "                    2202.0\n", "                ],\n", "                [\n", "                    2199.0,\n", "                    2204.0,\n", "                    2197.0,\n", "                    2206.0\n", "                ],\n", "                [\n", "                    2204.0,\n", "                    2209.0,\n", "                    2204.0,\n", "                    2212.0\n", "                ],\n", "                [\n", "                    2209.0,\n", "                    2205.0,\n", "                    2204.0,\n", "                    2210.0\n", "                ],\n", "                [\n", "                    2206.0,\n", "                    2204.0,\n", "                    2204.0,\n", "                    2207.0\n", "                ],\n", "                [\n", "                    2205.0,\n", "                    2207.0,\n", "                    2204.0,\n", "                    2207.0\n", "                ],\n", "                [\n", "                    2206.0,\n", "                    2205.0,\n", "                    2204.0,\n", "                    2207.0\n", "                ],\n", "                [\n", "                    2205.0,\n", "                    2203.0,\n", "                    2202.0,\n", "                    2206.0\n", "                ],\n", "                [\n", "                    2204.0,\n", "                    2208.0,\n", "                    2202.0,\n", "                    2209.0\n", "                ],\n", "                [\n", "                    2208.0,\n", "                    2208.0,\n", "                    2207.0,\n", "                    2210.0\n", "                ],\n", "                [\n", "                    2208.0,\n", "                    2207.0,\n", "                    2206.0,\n", "                    2209.0\n", "                ],\n", "                [\n", "                    2206.0,\n", "                    2205.0,\n", "                    2203.0,\n", "                    2207.0\n", "                ],\n", "                [\n", "                    2205.0,\n", "                    2206.0,\n", "                    2204.0,\n", "                    2206.0\n", "                ],\n", "                [\n", "                    2205.0,\n", "                    2202.0,\n", "                    2201.0,\n", "                    2206.0\n", "                ],\n", "                [\n", "                    2202.0,\n", "                    2201.0,\n", "                    2200.0,\n", "                    2203.0\n", "                ],\n", "                [\n", "                    2201.0,\n", "                    2201.0,\n", "                    2200.0,\n", "                    2204.0\n", "                ],\n", "                [\n", "                    2201.0,\n", "                    2202.0,\n", "                    2201.0,\n", "                    2203.0\n", "                ],\n", "                [\n", "                    2201.0,\n", "                    2201.0,\n", "                    2200.0,\n", "                    2203.0\n", "                ],\n", "                [\n", "                    2201.0,\n", "                    2199.0,\n", "                    2198.0,\n", "                    2202.0\n", "                ],\n", "                [\n", "                    2200.0,\n", "                    2198.0,\n", "                    2194.0,\n", "                    2201.0\n", "                ],\n", "                [\n", "                    2198.0,\n", "                    2202.0,\n", "                    2197.0,\n", "                    2203.0\n", "                ],\n", "                [\n", "                    2201.0,\n", "                    2202.0,\n", "                    2200.0,\n", "                    2203.0\n", "                ],\n", "                [\n", "                    2202.0,\n", "                    2202.0,\n", "                    2201.0,\n", "                    2204.0\n", "                ],\n", "                [\n", "                    2202.0,\n", "                    2203.0,\n", "                    2202.0,\n", "                    2205.0\n", "                ],\n", "                [\n", "                    2204.0,\n", "                    2200.0,\n", "                    2199.0,\n", "                    2204.0\n", "                ],\n", "                [\n", "                    2200.0,\n", "                    2198.0,\n", "                    2196.0,\n", "                    2201.0\n", "                ],\n", "                [\n", "                    2199.0,\n", "                    2198.0,\n", "                    2195.0,\n", "                    2201.0\n", "                ],\n", "                [\n", "                    2197.0,\n", "                    2185.0,\n", "                    2184.0,\n", "                    2197.0\n", "                ],\n", "                [\n", "                    2186.0,\n", "                    2186.0,\n", "                    2183.0,\n", "                    2189.0\n", "                ],\n", "                [\n", "                    2186.0,\n", "                    2178.0,\n", "                    2177.0,\n", "                    2186.0\n", "                ],\n", "                [\n", "                    2177.0,\n", "                    2183.0,\n", "                    2177.0,\n", "                    2184.0\n", "                ],\n", "                [\n", "                    2182.0,\n", "                    2184.0,\n", "                    2180.0,\n", "                    2185.0\n", "                ],\n", "                [\n", "                    2184.0,\n", "                    2180.0,\n", "                    2179.0,\n", "                    2184.0\n", "                ],\n", "                [\n", "                    2180.0,\n", "                    2174.0,\n", "                    2168.0,\n", "                    2181.0\n", "                ],\n", "                [\n", "                    2174.0,\n", "                    2177.0,\n", "                    2172.0,\n", "                    2178.0\n", "                ],\n", "                [\n", "                    2177.0,\n", "                    2175.0,\n", "                    2173.0,\n", "                    2178.0\n", "                ],\n", "                [\n", "                    2175.0,\n", "                    2172.0,\n", "                    2169.0,\n", "                    2175.0\n", "                ],\n", "                [\n", "                    2177.0,\n", "                    2176.0,\n", "                    2175.0,\n", "                    2182.0\n", "                ],\n", "                [\n", "                    2177.0,\n", "                    2181.0,\n", "                    2176.0,\n", "                    2184.0\n", "                ],\n", "                [\n", "                    2181.0,\n", "                    2183.0,\n", "                    2181.0,\n", "                    2185.0\n", "                ],\n", "                [\n", "                    2182.0,\n", "                    2177.0,\n", "                    2177.0,\n", "                    2183.0\n", "                ],\n", "                [\n", "                    2177.0,\n", "                    2174.0,\n", "                    2173.0,\n", "                    2180.0\n", "                ],\n", "                [\n", "                    2173.0,\n", "                    2174.0,\n", "                    2173.0,\n", "                    2177.0\n", "                ],\n", "                [\n", "                    2175.0,\n", "                    2175.0,\n", "                    2174.0,\n", "                    2177.0\n", "                ],\n", "                [\n", "                    2176.0,\n", "                    2176.0,\n", "                    2172.0,\n", "                    2176.0\n", "                ],\n", "                [\n", "                    2176.0,\n", "                    2178.0,\n", "                    2175.0,\n", "                    2179.0\n", "                ],\n", "                [\n", "                    2178.0,\n", "                    2180.0,\n", "                    2178.0,\n", "                    2182.0\n", "                ],\n", "                [\n", "                    2180.0,\n", "                    2180.0,\n", "                    2179.0,\n", "                    2183.0\n", "                ],\n", "                [\n", "                    2180.0,\n", "                    2178.0,\n", "                    2176.0,\n", "                    2181.0\n", "                ],\n", "                [\n", "                    2178.0,\n", "                    2178.0,\n", "                    2177.0,\n", "                    2180.0\n", "                ],\n", "                [\n", "                    2179.0,\n", "                    2183.0,\n", "                    2178.0,\n", "                    2184.0\n", "                ],\n", "                [\n", "                    2183.0,\n", "                    2182.0,\n", "                    2182.0,\n", "                    2185.0\n", "                ],\n", "                [\n", "                    2182.0,\n", "                    2182.0,\n", "                    2181.0,\n", "                    2185.0\n", "                ],\n", "                [\n", "                    2181.0,\n", "                    2185.0,\n", "                    2181.0,\n", "                    2185.0\n", "                ],\n", "                [\n", "                    2184.0,\n", "                    2185.0,\n", "                    2183.0,\n", "                    2187.0\n", "                ],\n", "                [\n", "                    2184.0,\n", "                    2184.0,\n", "                    2183.0,\n", "                    2185.0\n", "                ],\n", "                [\n", "                    2185.0,\n", "                    2187.0,\n", "                    2184.0,\n", "                    2189.0\n", "                ],\n", "                [\n", "                    2187.0,\n", "                    2193.0,\n", "                    2185.0,\n", "                    2194.0\n", "                ],\n", "                [\n", "                    2193.0,\n", "                    2190.0,\n", "                    2190.0,\n", "                    2194.0\n", "                ],\n", "                [\n", "                    2190.0,\n", "                    2194.0,\n", "                    2189.0,\n", "                    2195.0\n", "                ],\n", "                [\n", "                    2193.0,\n", "                    2191.0,\n", "                    2189.0,\n", "                    2193.0\n", "                ],\n", "                [\n", "                    2190.0,\n", "                    2191.0,\n", "                    2190.0,\n", "                    2194.0\n", "                ],\n", "                [\n", "                    2191.0,\n", "                    2189.0,\n", "                    2187.0,\n", "                    2192.0\n", "                ],\n", "                [\n", "                    2189.0,\n", "                    2188.0,\n", "                    2186.0,\n", "                    2190.0\n", "                ],\n", "                [\n", "                    2188.0,\n", "                    2189.0,\n", "                    2188.0,\n", "                    2190.0\n", "                ],\n", "                [\n", "                    2189.0,\n", "                    2190.0,\n", "                    2186.0,\n", "                    2190.0\n", "                ],\n", "                [\n", "                    2190.0,\n", "                    2189.0,\n", "                    2187.0,\n", "                    2190.0\n", "                ],\n", "                [\n", "                    2167.0,\n", "                    2180.0,\n", "                    2163.0,\n", "                    2183.0\n", "                ],\n", "                [\n", "                    2181.0,\n", "                    2183.0,\n", "                    2178.0,\n", "                    2184.0\n", "                ],\n", "                [\n", "                    2182.0,\n", "                    2177.0,\n", "                    2175.0,\n", "                    2183.0\n", "                ],\n", "                [\n", "                    2177.0,\n", "                    2177.0,\n", "                    2171.0,\n", "                    2177.0\n", "                ],\n", "                [\n", "                    2177.0,\n", "                    2177.0,\n", "                    2174.0,\n", "                    2178.0\n", "                ],\n", "                [\n", "                    2177.0,\n", "                    2181.0,\n", "                    2176.0,\n", "                    2181.0\n", "                ],\n", "                [\n", "                    2181.0,\n", "                    2180.0,\n", "                    2178.0,\n", "                    2181.0\n", "                ],\n", "                [\n", "                    2180.0,\n", "                    2180.0,\n", "                    2177.0,\n", "                    2180.0\n", "                ],\n", "                [\n", "                    2179.0,\n", "                    2183.0,\n", "                    2179.0,\n", "                    2183.0\n", "                ],\n", "                [\n", "                    2182.0,\n", "                    2181.0,\n", "                    2179.0,\n", "                    2183.0\n", "                ],\n", "                [\n", "                    2182.0,\n", "                    2177.0,\n", "                    2176.0,\n", "                    2183.0\n", "                ],\n", "                [\n", "                    2177.0,\n", "                    2180.0,\n", "                    2177.0,\n", "                    2180.0\n", "                ],\n", "                [\n", "                    2180.0,\n", "                    2178.0,\n", "                    2177.0,\n", "                    2180.0\n", "                ],\n", "                [\n", "                    2179.0,\n", "                    2179.0,\n", "                    2178.0,\n", "                    2183.0\n", "                ],\n", "                [\n", "                    2179.0,\n", "                    2180.0,\n", "                    2177.0,\n", "                    2181.0\n", "                ],\n", "                [\n", "                    2181.0,\n", "                    2181.0,\n", "                    2178.0,\n", "                    2182.0\n", "                ],\n", "                [\n", "                    2180.0,\n", "                    2178.0,\n", "                    2178.0,\n", "                    2182.0\n", "                ],\n", "                [\n", "                    2178.0,\n", "                    2179.0,\n", "                    2177.0,\n", "                    2180.0\n", "                ],\n", "                [\n", "                    2179.0,\n", "                    2176.0,\n", "                    2175.0,\n", "                    2179.0\n", "                ],\n", "                [\n", "                    2176.0,\n", "                    2174.0,\n", "                    2173.0,\n", "                    2177.0\n", "                ],\n", "                [\n", "                    2174.0,\n", "                    2177.0,\n", "                    2172.0,\n", "                    2178.0\n", "                ],\n", "                [\n", "                    2178.0,\n", "                    2178.0,\n", "                    2177.0,\n", "                    2180.0\n", "                ],\n", "                [\n", "                    2179.0,\n", "                    2179.0,\n", "                    2177.0,\n", "                    2180.0\n", "                ],\n", "                [\n", "                    2179.0,\n", "                    2180.0,\n", "                    2178.0,\n", "                    2182.0\n", "                ],\n", "                [\n", "                    2180.0,\n", "                    2180.0,\n", "                    2178.0,\n", "                    2181.0\n", "                ],\n", "                [\n", "                    2180.0,\n", "                    2185.0,\n", "                    2180.0,\n", "                    2188.0\n", "                ],\n", "                [\n", "                    2186.0,\n", "                    2185.0,\n", "                    2183.0,\n", "                    2187.0\n", "                ],\n", "                [\n", "                    2185.0,\n", "                    2183.0,\n", "                    2183.0,\n", "                    2187.0\n", "                ],\n", "                [\n", "                    2184.0,\n", "                    2185.0,\n", "                    2184.0,\n", "                    2187.0\n", "                ],\n", "                [\n", "                    2185.0,\n", "                    2184.0,\n", "                    2182.0,\n", "                    2186.0\n", "                ],\n", "                [\n", "                    2183.0,\n", "                    2183.0,\n", "                    2182.0,\n", "                    2184.0\n", "                ],\n", "                [\n", "                    2183.0,\n", "                    2183.0,\n", "                    2180.0,\n", "                    2183.0\n", "                ],\n", "                [\n", "                    2183.0,\n", "                    2181.0,\n", "                    2180.0,\n", "                    2183.0\n", "                ],\n", "                [\n", "                    2181.0,\n", "                    2177.0,\n", "                    2175.0,\n", "                    2181.0\n", "                ],\n", "                [\n", "                    2177.0,\n", "                    2178.0,\n", "                    2174.0,\n", "                    2179.0\n", "                ],\n", "                [\n", "                    2178.0,\n", "                    2177.0,\n", "                    2176.0,\n", "                    2179.0\n", "                ],\n", "                [\n", "                    2176.0,\n", "                    2178.0,\n", "                    2175.0,\n", "                    2179.0\n", "                ],\n", "                [\n", "                    2177.0,\n", "                    2179.0,\n", "                    2176.0,\n", "                    2179.0\n", "                ],\n", "                [\n", "                    2178.0,\n", "                    2178.0,\n", "                    2176.0,\n", "                    2179.0\n", "                ],\n", "                [\n", "                    2178.0,\n", "                    2176.0,\n", "                    2175.0,\n", "                    2178.0\n", "                ],\n", "                [\n", "                    2176.0,\n", "                    2180.0,\n", "                    2175.0,\n", "                    2181.0\n", "                ],\n", "                [\n", "                    2180.0,\n", "                    2180.0,\n", "                    2178.0,\n", "                    2181.0\n", "                ],\n", "                [\n", "                    2179.0,\n", "                    2180.0,\n", "                    2178.0,\n", "                    2180.0\n", "                ],\n", "                [\n", "                    2179.0,\n", "                    2178.0,\n", "                    2177.0,\n", "                    2180.0\n", "                ],\n", "                [\n", "                    2178.0,\n", "                    2180.0,\n", "                    2177.0,\n", "                    2182.0\n", "                ],\n", "                [\n", "                    2188.0,\n", "                    2180.0,\n", "                    2178.0,\n", "                    2188.0\n", "                ],\n", "                [\n", "                    2179.0,\n", "                    2177.0,\n", "                    2174.0,\n", "                    2181.0\n", "                ],\n", "                [\n", "                    2177.0,\n", "                    2176.0,\n", "                    2173.0,\n", "                    2178.0\n", "                ],\n", "                [\n", "                    2176.0,\n", "                    2181.0,\n", "                    2176.0,\n", "                    2182.0\n", "                ],\n", "                [\n", "                    2180.0,\n", "                    2181.0,\n", "                    2179.0,\n", "                    2181.0\n", "                ],\n", "                [\n", "                    2181.0,\n", "                    2183.0,\n", "                    2180.0,\n", "                    2184.0\n", "                ],\n", "                [\n", "                    2183.0,\n", "                    2181.0,\n", "                    2180.0,\n", "                    2184.0\n", "                ],\n", "                [\n", "                    2181.0,\n", "                    2180.0,\n", "                    2180.0,\n", "                    2182.0\n", "                ],\n", "                [\n", "                    2180.0,\n", "                    2181.0,\n", "                    2177.0,\n", "                    2181.0\n", "                ],\n", "                [\n", "                    2180.0,\n", "                    2180.0,\n", "                    2179.0,\n", "                    2182.0\n", "                ],\n", "                [\n", "                    2181.0,\n", "                    2180.0,\n", "                    2180.0,\n", "                    2182.0\n", "                ],\n", "                [\n", "                    2180.0,\n", "                    2180.0,\n", "                    2179.0,\n", "                    2181.0\n", "                ],\n", "                [\n", "                    2180.0,\n", "                    2181.0,\n", "                    2180.0,\n", "                    2182.0\n", "                ],\n", "                [\n", "                    2182.0,\n", "                    2183.0,\n", "                    2181.0,\n", "                    2183.0\n", "                ],\n", "                [\n", "                    2182.0,\n", "                    2181.0,\n", "                    2181.0,\n", "                    2185.0\n", "                ],\n", "                [\n", "                    2181.0,\n", "                    2183.0,\n", "                    2181.0,\n", "                    2184.0\n", "                ],\n", "                [\n", "                    2183.0,\n", "                    2184.0,\n", "                    2181.0,\n", "                    2184.0\n", "                ],\n", "                [\n", "                    2184.0,\n", "                    2180.0,\n", "                    2180.0,\n", "                    2184.0\n", "                ],\n", "                [\n", "                    2181.0,\n", "                    2184.0,\n", "                    2180.0,\n", "                    2185.0\n", "                ],\n", "                [\n", "                    2184.0,\n", "                    2188.0,\n", "                    2184.0,\n", "                    2192.0\n", "                ],\n", "                [\n", "                    2189.0,\n", "                    2187.0,\n", "                    2185.0,\n", "                    2189.0\n", "                ],\n", "                [\n", "                    2186.0,\n", "                    2186.0,\n", "                    2184.0,\n", "                    2187.0\n", "                ],\n", "                [\n", "                    2186.0,\n", "                    2185.0,\n", "                    2184.0,\n", "                    2188.0\n", "                ],\n", "                [\n", "                    2186.0,\n", "                    2184.0,\n", "                    2184.0,\n", "                    2187.0\n", "                ],\n", "                [\n", "                    2184.0,\n", "                    2185.0,\n", "                    2184.0,\n", "                    2186.0\n", "                ],\n", "                [\n", "                    2185.0,\n", "                    2182.0,\n", "                    2182.0,\n", "                    2185.0\n", "                ],\n", "                [\n", "                    2182.0,\n", "                    2182.0,\n", "                    2181.0,\n", "                    2184.0\n", "                ],\n", "                [\n", "                    2183.0,\n", "                    2179.0,\n", "                    2178.0,\n", "                    2185.0\n", "                ],\n", "                [\n", "                    2179.0,\n", "                    2178.0,\n", "                    2178.0,\n", "                    2182.0\n", "                ],\n", "                [\n", "                    2179.0,\n", "                    2178.0,\n", "                    2175.0,\n", "                    2180.0\n", "                ],\n", "                [\n", "                    2175.0,\n", "                    2179.0,\n", "                    2173.0,\n", "                    2180.0\n", "                ],\n", "                [\n", "                    2180.0,\n", "                    2178.0,\n", "                    2176.0,\n", "                    2180.0\n", "                ],\n", "                [\n", "                    2178.0,\n", "                    2179.0,\n", "                    2178.0,\n", "                    2182.0\n", "                ],\n", "                [\n", "                    2179.0,\n", "                    2180.0,\n", "                    2177.0,\n", "                    2181.0\n", "                ],\n", "                [\n", "                    2180.0,\n", "                    2184.0,\n", "                    2178.0,\n", "                    2184.0\n", "                ],\n", "                [\n", "                    2184.0,\n", "                    2183.0,\n", "                    2181.0,\n", "                    2186.0\n", "                ],\n", "                [\n", "                    2183.0,\n", "                    2183.0,\n", "                    2183.0,\n", "                    2185.0\n", "                ],\n", "                [\n", "                    2183.0,\n", "                    2185.0,\n", "                    2183.0,\n", "                    2186.0\n", "                ],\n", "                [\n", "                    2184.0,\n", "                    2182.0,\n", "                    2181.0,\n", "                    2185.0\n", "                ],\n", "                [\n", "                    2181.0,\n", "                    2178.0,\n", "                    2178.0,\n", "                    2182.0\n", "                ],\n", "                [\n", "                    2179.0,\n", "                    2178.0,\n", "                    2176.0,\n", "                    2180.0\n", "                ],\n", "                [\n", "                    2177.0,\n", "                    2178.0,\n", "                    2176.0,\n", "                    2179.0\n", "                ],\n", "                [\n", "                    2178.0,\n", "                    2178.0,\n", "                    2177.0,\n", "                    2179.0\n", "                ],\n", "                [\n", "                    2177.0,\n", "                    2177.0,\n", "                    2173.0,\n", "                    2178.0\n", "                ],\n", "                [\n", "                    2177.0,\n", "                    2176.0,\n", "                    2175.0,\n", "                    2178.0\n", "                ],\n", "                [\n", "                    2175.0,\n", "                    2176.0,\n", "                    2175.0,\n", "                    2178.0\n", "                ],\n", "                [\n", "                    2177.0,\n", "                    2178.0,\n", "                    2175.0,\n", "                    2178.0\n", "                ],\n", "                [\n", "                    2178.0,\n", "                    2177.0,\n", "                    2177.0,\n", "                    2179.0\n", "                ],\n", "                [\n", "                    2178.0,\n", "                    2178.0,\n", "                    2177.0,\n", "                    2179.0\n", "                ],\n", "                [\n", "                    2178.0,\n", "                    2178.0,\n", "                    2177.0,\n", "                    2180.0\n", "                ],\n", "                [\n", "                    2179.0,\n", "                    2170.0,\n", "                    2165.0,\n", "                    2180.0\n", "                ],\n", "                [\n", "                    2171.0,\n", "                    2171.0,\n", "                    2169.0,\n", "                    2174.0\n", "                ],\n", "                [\n", "                    2172.0,\n", "                    2173.0,\n", "                    2171.0,\n", "                    2174.0\n", "                ],\n", "                [\n", "                    2172.0,\n", "                    2172.0,\n", "                    2172.0,\n", "                    2174.0\n", "                ],\n", "                [\n", "                    2173.0,\n", "                    2169.0,\n", "                    2166.0,\n", "                    2173.0\n", "                ],\n", "                [\n", "                    2168.0,\n", "                    2169.0,\n", "                    2167.0,\n", "                    2171.0\n", "                ],\n", "                [\n", "                    2169.0,\n", "                    2161.0,\n", "                    2158.0,\n", "                    2169.0\n", "                ],\n", "                [\n", "                    2160.0,\n", "                    2157.0,\n", "                    2153.0,\n", "                    2161.0\n", "                ],\n", "                [\n", "                    2157.0,\n", "                    2145.0,\n", "                    2143.0,\n", "                    2157.0\n", "                ],\n", "                [\n", "                    2146.0,\n", "                    2147.0,\n", "                    2138.0,\n", "                    2149.0\n", "                ],\n", "                [\n", "                    2147.0,\n", "                    2149.0,\n", "                    2145.0,\n", "                    2154.0\n", "                ],\n", "                [\n", "                    2150.0,\n", "                    2151.0,\n", "                    2146.0,\n", "                    2152.0\n", "                ],\n", "                [\n", "                    2150.0,\n", "                    2150.0,\n", "                    2147.0,\n", "                    2151.0\n", "                ],\n", "                [\n", "                    2150.0,\n", "                    2149.0,\n", "                    2146.0,\n", "                    2150.0\n", "                ],\n", "                [\n", "                    2149.0,\n", "                    2153.0,\n", "                    2147.0,\n", "                    2154.0\n", "                ],\n", "                [\n", "                    2154.0,\n", "                    2153.0,\n", "                    2151.0,\n", "                    2155.0\n", "                ],\n", "                [\n", "                    2154.0,\n", "                    2152.0,\n", "                    2151.0,\n", "                    2155.0\n", "                ],\n", "                [\n", "                    2152.0,\n", "                    2149.0,\n", "                    2148.0,\n", "                    2154.0\n", "                ],\n", "                [\n", "                    2149.0,\n", "                    2153.0,\n", "                    2148.0,\n", "                    2154.0\n", "                ],\n", "                [\n", "                    2152.0,\n", "                    2150.0,\n", "                    2150.0,\n", "                    2154.0\n", "                ],\n", "                [\n", "                    2151.0,\n", "                    2155.0,\n", "                    2150.0,\n", "                    2156.0\n", "                ],\n", "                [\n", "                    2155.0,\n", "                    2154.0,\n", "                    2154.0,\n", "                    2157.0\n", "                ],\n", "                [\n", "                    2154.0,\n", "                    2156.0,\n", "                    2153.0,\n", "                    2156.0\n", "                ],\n", "                [\n", "                    2156.0,\n", "                    2159.0,\n", "                    2155.0,\n", "                    2159.0\n", "                ],\n", "                [\n", "                    2158.0,\n", "                    2159.0,\n", "                    2157.0,\n", "                    2164.0\n", "                ]\n", "            ],\n", "            \"markPoint\": {\n", "                \"data\": [\n", "                    {\n", "                        \"type\": \"max\",\n", "                        \"name\": \"Maximum\",\n", "                        \"symbol\": \"pin\",\n", "                        \"symbolSize\": 50,\n", "                        \"label\": {\n", "                            \"normal\": {\n", "                                \"textStyle\": {\n", "                                    \"color\": \"#fff\"\n", "                                }\n", "                            }\n", "                        }\n", "                    }\n", "                ]\n", "            },\n", "            \"markLine\": {\n", "                \"data\": []\n", "            },\n", "            \"seriesId\": 2022597\n", "        }\n", "    ],\n", "    \"legend\": [\n", "        {\n", "            \"data\": [\n", "                \"MA8888.ZC BarSize.min5 KLine (bar count:500)(ATR:5.227348474200718)\"\n", "            ],\n", "            \"selectedMode\": \"multiple\",\n", "            \"show\": true,\n", "            \"left\": \"center\",\n", "            \"top\": \"top\",\n", "            \"orient\": \"horizontal\",\n", "            \"textStyle\": {\n", "                \"fontSize\": 12\n", "            }\n", "        }\n", "    ],\n", "    \"animation\": true,\n", "    \"xAxis\": [\n", "        {\n", "            \"show\": true,\n", "            \"nameLocation\": \"middle\",\n", "            \"nameGap\": 25,\n", "            \"nameTextStyle\": {\n", "                \"fontSize\": 14\n", "            },\n", "            \"axisTick\": {\n", "                \"alignWithLabel\": false\n", "            },\n", "            \"inverse\": false,\n", "            \"boundaryGap\": true,\n", "            \"type\": \"category\",\n", "            \"splitLine\": {\n", "                \"show\": false\n", "            },\n", "            \"axisLine\": {\n", "                \"lineStyle\": {\n", "                    \"width\": 1\n", "                }\n", "            },\n", "            \"axisLabel\": {\n", "                \"interval\": \"auto\",\n", "                \"rotate\": 0,\n", "                \"margin\": 8,\n", "                \"textStyle\": {\n", "                    \"fontSize\": 12\n", "                }\n", "            },\n", "            \"data\": [\n", "                \"2019-07-09\",\n", "                \"2019-07-09\",\n", "                \"2019-07-09\",\n", "                \"2019-07-09\",\n", "                \"2019-07-09\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-10\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-11\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-12\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-15\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-16\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-17\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\",\n", "                \"2019-07-18\"\n", "            ],\n", "            \"scale\": true\n", "        }\n", "    ],\n", "    \"yAxis\": [\n", "        {\n", "            \"show\": true,\n", "            \"nameLocation\": \"middle\",\n", "            \"nameGap\": 25,\n", "            \"nameTextStyle\": {\n", "                \"fontSize\": 14\n", "            },\n", "            \"axisTick\": {\n", "                \"alignWithLabel\": false\n", "            },\n", "            \"inverse\": false,\n", "            \"boundaryGap\": true,\n", "            \"type\": \"value\",\n", "            \"splitLine\": {\n", "                \"show\": true\n", "            },\n", "            \"axisLine\": {\n", "                \"lineStyle\": {\n", "                    \"width\": 1\n", "                }\n", "            },\n", "            \"axisLabel\": {\n", "                \"interval\": \"auto\",\n", "                \"formatter\": \"{value} \",\n", "                \"rotate\": 0,\n", "                \"margin\": 8,\n", "                \"textStyle\": {\n", "                    \"fontSize\": 12\n", "                }\n", "            },\n", "            \"scale\": true,\n", "            \"splitArea\": {\n", "                \"show\": true\n", "            }\n", "        }\n", "    ],\n", "    \"color\": [\n", "        \"#c23531\",\n", "        \"#2f4554\",\n", "        \"#61a0a8\",\n", "        \"#d48265\",\n", "        \"#749f83\",\n", "        \"#ca8622\",\n", "        \"#bda29a\",\n", "        \"#6e7074\",\n", "        \"#546570\",\n", "        \"#c4ccd3\",\n", "        \"#f05b72\",\n", "        \"#ef5b9c\",\n", "        \"#f47920\",\n", "        \"#905a3d\",\n", "        \"#fab27b\",\n", "        \"#2a5caa\",\n", "        \"#444693\",\n", "        \"#726930\",\n", "        \"#b2d235\",\n", "        \"#6d8346\",\n", "        \"#ac6767\",\n", "        \"#1d953f\",\n", "        \"#6950a1\",\n", "        \"#918597\",\n", "        \"#f6f5ec\"\n", "    ],\n", "    \"dataZoom\": [\n", "        {\n", "            \"show\": true,\n", "            \"type\": \"slider\",\n", "            \"start\": 50,\n", "            \"end\": 100,\n", "            \"orient\": \"horizontal\"\n", "        }\n", "    ]\n", "};\n", "myChart_953c3ee2d583424aa17f1564657036bf.setOption(option_953c3ee2d583424aa17f1564657036bf);\n", "\n", "    });\n", "</script>\n"], "text/plain": ["<pyecharts.charts.kline.Kline at 0x12bbc15b518>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["len=500\n", "symbol='MA8888.ZC'\n", "barsize=BarSize.min5\n", "hist=ds.get_history_data(symbol, len, [BarData.datetime,BarData.open,BarData.high,BarData.low,BarData.close], barsize)\n", "# hist=ds.get_bar_series(symbol, [BarData.datetime,BarData.open,BarData.high,BarData.low,BarData.close], barsize)\n", "\n", "dt_str = []\n", "len=hist.shape[1]\n", "if len == 0:\n", "    print(len)\n", "\n", "for i in range(len):\n", "    dt_str.append(datetime.datetime.fromtimestamp(int(hist[0][i])).strftime('%Y-%m-%d'))\n", "\n", "bars = []\n", "for i in range(len):\n", "    bar = []\n", "    bar.append(hist[1][i])\n", "    bar.append(hist[4][i])\n", "    bar.append(hist[3][i])\n", "    bar.append(hist[2][i])\n", "    bars.append(bar)\n", "    \n", "atr = ta.ATR(hist[2], hist[3], hist[4], 20)\n", "\n", "kline = Kline(\"K 线图\", width=1000, height=400)\n", "kline.add(\"{} {} KLine (bar count:{})(ATR:{})\".format(symbol, barsize, len, atr[-1]), dt_str, bars, mark_point=[\"max\"], is_datazoom_show=True)\n", "# kline.show_config()\n", "kline"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 2}