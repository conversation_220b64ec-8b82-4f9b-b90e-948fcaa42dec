# 要添加一个新单元，输入 '# %%'
# 要添加一个新的标记单元，输入 '# %% [markdown]'
#

import pandas as pd
import numpy as np
import json
import copy
import datetime
import matplotlib.pyplot as plt

import torch
import torch.nn.functional as F
from torch import nn
from torch.utils.data import DataLoader
from torch.optim import lr_scheduler
from sklearn.model_selection import train_test_split
'''
# !pip install sklearn -i https://pypi.douban.com/simple/

'''

MAIN_FUT_CODES = ['A', 'AG', 'AL', 'AP', 'AU', 'B', 'BU', 'C', 'CF', 'CJ', 'CS', 'CU', 'CY', 'EB', 'EG', 'FG', 'HC', 'I', 'IC', 'IF', 'IH', 'J', 'JD', 'JM', 'L', 'LH', 'M', 'MA', 'NI', 'NR', 'OI', 'P', 'PB', 'PF', 'PG', 'PK', 'PP', 'RB', 'RM', 'RR', 'RU', 'SA', 'SC', 'SF', 'SM', 'SN', 'SP', 'SR', 'SS', 'TA', 'UR', 'V', 'Y', 'ZN']
TRAD_FUT_CODES = ['M', 'Y', 'A', 'P', 'JM', 'I', 'V', 'EG', 'EB', 'SR', 'CF', 'FG', 'TA', 'MA', 'OI', 'RM', 'RS', 'SF', 'SM', 'AP', 'UR', 'SA', 'RB', 'HC', 'AG', 'SP', 'BU', 'SS', 'RU', 'LH', 'PK']
'''
AICM 因子库，模型可以根据需要，选择特征数据
'''
FACOTR_NUM = 112
ALL_FACTOR_NAMES = [
    # 价量因子
    "OPEN", "HIGH", "LOW", "CLOSE", "VOLUME", "TYPICAL_PRICE", "NEW",
    "NEW_CHANGE_PERCENT", "SHORT_TERM_HIGH", "LONG_TERM_HIGH", "SHORT_TERM_LOW",
    "LONG_TERM_LOW",

    # 技术指标类因子
    "AD", "DX", "ADX", "ADXR", "APO", "AROON_UP", "AROON_DOWN", "ATR",
    "BOLL_UP", "BOLL_MID", "BOLL_DOWN", "CCI", "CMO",

    "MA_FAST", "MA_SLOW", "EMA_FAST", "EMA_SLOW", "DEMA_FAST", "DEMA_SLOW",
    "KAMA_FAST", "KAMA_SLOW", "MAMA_FAST", "MAMA_SLOW", "T3_FAST", "T3_SLOW",
    "TEMA_FAST", "TEMA_SLOW", "TRIMA_FAST", "TRIMA_SLOW", "TRIX_FAST",
    "TRIX_SLOW",

    "MACD", "MACD_DIFF", "MACD_DEA", "MFI", "MOM", "NATR", "OBV", "ROC", "RSI",
    "SAR", "TRANGE", "TSF", "ULTOSC", "WILLR",
    "KDJ_K", "KDJ_D",

    # 自定义指标
    "LR_SLOPE_FAST", "LR_SLOPE_MIDD", "LR_SLOPE_SLOW",
    "LR_SLOPE_FAST_THRESHOLD", "LR_SLOPE_SLOW_THRESHOLD",

    "STDDEV_FAST", "STDDEV_SLOW", "STDDEV_THRESHOLD",

    "MOMENTUM_FAST", "MOMENTUM_MIDD", "MOMENTUM_SLOW", "MOMENTUM",
    "MOMENTUM_THRESHOLD",

    "SQUEEZE", "SQUEEZE_SIGNAL", "SQUEEZE_ZERO_BARS", "SQUEEZE_BAND_UPL",
    "SQUEEZE_BAND_DWL", "SQUEEZE_MDL", "SQUEEZE_KC_UPL", "SQUEEZE_KC_DWL",
    "SQUEEZE_GAP", "SQUEEZE_GAP_FAST", "SQUEEZE_GAP_SLOW",
    "SQUEEZE_GAP_THRESHOLD", "SQUEEZE_NARROW_BARS",

    "BAND_UPL", "BAND_MDL", "BAND_DWL", "BAND_POSITION", "BAND_WIDTH",
    "BAND_EXPAND", "BAND_GRADIENT", "BAND_GRADIENT_THRESHOLD", "BAND_GAP",
    "BAND_BK_BARS", "BAR_STICK_LENGTH",

    "TL_FAST", "TL_SLOW", "TL_THRESHOLD",

    "TREND_VALUE", "TREND_BARS", "TREND_INBARS", "TREND_INPOSR", "TREND_HIGHEST", "TREND_LOWEST", "TREND_HLR",
    "TREND_LEVEL",

    "HYO_TENKAN_SEN", "HYO_KIJUN_SEN", "HYO_CROSS_BARS", "TATR",
    "TATR_THRESHOLD"    
]

# 使用两个值的因子
TWO_VAL_FACTOR_NAMES = [
    "RSI", "LR_SLOPE_FAST", "LR_SLOPE_MIDD", "LR_SLOPE_SLOW", 
    "SQUEEZE_GAP", "SQUEEZE_GAP_FAST", "SQUEEZE_GAP_SLOW",
    "BAND_GRADIENT",
    "TL_FAST", "TL_SLOW",
    "TREND_VALUE", "TREND_BARS", "TREND_INBARS", "TREND_INPOSR"
]

CONTEXT_NAMES = [
    "AD_PS_RATIO", "COST_RNG", "CURRENT_TIME", "DRAWDOWN_RNG", 
    "FAST_AG_RSI", "FAST_AG_RSI_PREV", "FAST_QH_RSI", "FAST_QH_RSI_PREV", 
    "LONG_RANGE", "PF_PS_RATIO", "PF_YIELD_HL", "PF_YIELD_TREND", 
    "PNL", "POS_DAYS", "POS_LONG_BARS", "POS_SHORT_BARS", "SHORT_RANGE",
    "SLOW_AG_RSI", "SLOW_AG_RSI_PREV", "SLOW_QH_RSI", "SLOW_QH_RSI_PREV", "STDDEV_RNG"
]

FACTOR_NAMES = [[ # Slow period factor
    "RSI",
    # 自定义指标
    "LR_SLOPE_FAST", "LR_SLOPE_MIDD", "LR_SLOPE_SLOW",
    "LR_SLOPE_FAST_THRESHOLD", "LR_SLOPE_SLOW_THRESHOLD",

    "SQUEEZE_ZERO_BARS", 
    "SQUEEZE_GAP", "SQUEEZE_GAP_FAST", "SQUEEZE_GAP_SLOW",
    "SQUEEZE_GAP_THRESHOLD", "SQUEEZE_NARROW_BARS",

    "BAND_POSITION", "BAND_WIDTH",
    "BAND_EXPAND", "BAND_GRADIENT", "BAND_GRADIENT_THRESHOLD", "BAND_GAP",

    "TL_FAST", "TL_SLOW", "TL_THRESHOLD",

    "TREND_VALUE", "TREND_BARS", "TREND_INBARS", "TREND_INPOSR", "TREND_HLR",
    "TREND_LEVEL"
],
[ # Fast period factor
    "RSI",
    # 自定义指标
    "LR_SLOPE_FAST", "LR_SLOPE_MIDD", "LR_SLOPE_SLOW",
    "LR_SLOPE_FAST_THRESHOLD", "LR_SLOPE_SLOW_THRESHOLD",

    "SQUEEZE_ZERO_BARS", 
    "SQUEEZE_GAP", "SQUEEZE_GAP_FAST", "SQUEEZE_GAP_SLOW",
    "SQUEEZE_GAP_THRESHOLD", "SQUEEZE_NARROW_BARS",

    "BAND_POSITION", "BAND_WIDTH",
    "BAND_EXPAND", "BAND_GRADIENT", "BAND_GRADIENT_THRESHOLD", "BAND_GAP",

    "TL_FAST", "TL_SLOW", "TL_THRESHOLD",

    "TREND_VALUE", "TREND_BARS", "TREND_INBARS", "TREND_INPOSR", "TREND_HLR",
    "TREND_LEVEL"
]]

POS_CONTEXT = [
  "COST_RNG", "DRAWDOWN_RNG", "STDDEV_RNG", "PNL", "POS_DAYS",
  "POS_SHORT_BARS", "POS_LONG_BARS", "SHORT_RANGE", "LONG_RANGE",
  "PF_YIELD_TREND", "PF_YIELD_HL", "AD_PS_RATIO", "PF_PS_RATIO",
  "FAST_AG_RSI", "FAST_AG_RSI_PREV", "SLOW_AG_RSI", "SLOW_AG_RSI_PREV",
  "FAST_QH_RSI", "FAST_QH_RSI_PREV", "SLOW_QH_RSI", "SLOW_QH_RSI_PREV",
  "CURRENT_TIME"
]

OPEN_POS_CONTEXT = [
  "STDDEV_RNG", "SHORT_RANGE", "LONG_RANGE",
  "FAST_QH_RSI", "FAST_QH_RSI_PREV", "SLOW_QH_RSI", "SLOW_QH_RSI_PREV"
]

def factor_select(n, i):
    if ALL_FACTOR_NAMES[n] in FACTOR_NAMES[i]:
        if ALL_FACTOR_NAMES[n] in TWO_VAL_FACTOR_NAMES:
            return 2
        else:
            return 1
    return 0

def context_select(n, drop_cols):
    if POS_CONTEXT[n] in OPEN_POS_CONTEXT and POS_CONTEXT[n] not in drop_cols:
        return 1
    return 0
       

# 创建模型
class AicmMLP(nn.Module):
    '''多层感知机模型'''
    def __init__(self, input_num, bn=True, dropout=True):
        super(AicmMLP, self).__init__()
        self.bn = bn
        self.dropout = dropout
        self.lin_1 = nn.Linear(input_num, 128)
        self.bn_1 = nn.BatchNorm1d(128)
        self.lin_2 = nn.Linear(128, 96)
        self.bn_2 = nn.BatchNorm1d(96)
        self.lin_3 = nn.Linear(96, 96)
        self.bn_3 = nn.BatchNorm1d(96)
        self.lin_4 = nn.Linear(96, 1, bias=True)
        self.drop = nn.Dropout(0.4)
        self.activate = nn.ReLU()
        self.sigmoid = nn.Sigmoid()

    def forward(self, input):
        '''
        注意：
        模型不能这样写：self.bn_1(F.dropout(F.relu(self.lin_1(input))))
        模型层嵌套写法的问题，dropout在模型的train时执行，在eval时不执行
        Dropout：放在全连接层防止过拟合，一般放在激活函数层之后
        BatchNorm：归一化放在激活层前后好像都有，最初放在了
        激活层池化层后面，而现在普遍放在激活层前。
        '''
        # input layer
        x = self.lin_1(input)
        # print(x.shape)
        if self.bn:
            x = self.bn_1(x)
        x = self.activate(x)
        if self.dropout:
            x = self.drop(x)
        # hidden layer1
        x = self.lin_2(x)
        if self.bn:
            x = self.bn_2(x)
        x = self.activate(x)
        if self.dropout:
            x = self.drop(x)
        # hidden layer2
        x = self.lin_3(x)
        if self.bn:
            x = self.bn_3(x)
        x = self.activate(x)
        if self.dropout:
            x = self.drop(x)
        # out layer
        x = self.lin_4(x)
        x = self.sigmoid(x)
        return x

class FactorDataset(torch.utils.data.Dataset):
    def __init__(self, factors, labels) -> None:
        super().__init__()
        self.factors = factors
        self.labels = labels

    def __getitem__(self, index):
        factor = self.factors[index]
        label = self.labels[index]
        return factor, label

    def __len__(self):
        return len(self.factors)

class AicmTrainModels():
    def __init__(self) -> None:
        self.fut_codes = []
        self.mean = None
        self.std = None
        self.batch = 64
        self.epochs = 1000
        self.lr = 0.001
        self.input_num = 0
        self.train_dl = None
        self.test_dl = None
        self.train_loss = []
        self.train_acc = []
        self.test_loss = []
        self.test_acc = []        
        self.data_path = "e:/lab/RoboQuant/pylab/data"
        self.model_path = "e:/lab/RoboQuant/pylab/model"
        self.model_name = ""
        self.drop_columns = {
            # "base": ['instrument', 'datetime', 'direct', "STDDEV_RNG", "SHORT_RANGE", "LONG_RANGE", "FAST_QH_RSI", "FAST_QH_RSI_PREV", "SLOW_QH_RSI", "SLOW_QH_RSI_PREV"],
            # "maintrend": ['instrument', 'datetime', 'direct', "SHORT_RANGE", "LONG_RANGE", "FAST_QH_RSI", "FAST_QH_RSI_PREV", "SLOW_QH_RSI_PREV"],
            "context": ['instrument', 'datetime', 'direct', "SHORT_RANGE", "LONG_RANGE", "FAST_QH_RSI_PREV", "SLOW_QH_RSI_PREV"],
        }
        self.only_long_period = {
            "base": False,
            "context": False,
            "maintrend": True
        }
        self.only_trading_instrument = True
        self.is_onehot = True
        self.device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
        self.bn = True
        self.dropout = True

    def dump_factor_select_items(self, factors_name, drop_columns, ls, only_long_period):
        f_sel = {}
        f_sel['slow'] = [factor_select(n, 0) for n in range(len(ALL_FACTOR_NAMES))]
        if only_long_period:
            f_sel['fast'] = [0 for n in range(len(ALL_FACTOR_NAMES))]
        else:
            f_sel['fast'] = [factor_select(n, 1) for n in range(len(ALL_FACTOR_NAMES))]
        f_sel['context'] = [context_select(n, drop_columns) for n in range(len(POS_CONTEXT))]

        f_sel['codes'] = self.fut_codes

        f_sel['mean'] = self.mean.tolist()
        f_sel['std'] = self.std.tolist()
        f_sel['input_dim'] = 1
        f_sel['code_encoding'] = 1

        with open('%s/%s_%s_%s.json' % (self.model_path, self.model_name, factors_name, ls), 'w') as factor_sel_file:
            json.dump(f_sel, factor_sel_file)
        # print(f_sel)

        return 
    def load_data(self, ls, portfolios):
        data = pd.DataFrame()
        for pf in portfolios:
            data = data.append(pd.read_csv('%s/factors_%s_xy.%s.csv'%(self.data_path, ls, pf)))
            # print("portfolio: %s %s count: %d" % (pf, ls, len(data)))

        if len(data) == 0:
            print("load features data failed!")
            return data

        data.set_index('ord_id', inplace=True) # 注：独热编码前必须先索引
        data['CODE'] = data.apply(lambda x: x['instrument'][0:-7], axis=1)
        # 清除不常交易的合约
        if self.only_trading_instrument:
            cnt = len(data)
            for code in data.CODE.unique():
                if code not in TRAD_FUT_CODES:
                    data.drop(data[data.CODE == code].index, inplace=True)
            print("\nRemain: %d remove: %d" % (len(data), cnt - len(data)))

        # one-hot encode
        if self.is_onehot:
            data = data.join(pd.get_dummies(data.CODE))
            self.fut_codes = data.columns.to_list()[-1*len(data.CODE.unique()):]
            lb = data.pop('label')
            data.insert(loc=data.shape[1], column='label', value=lb)

        data.drop(columns=['CODE'], inplace=True)

        # print(data.head())
        # print(data.info())
        # print(data.label.value_counts())
        print("\nToday add %s count: " % ls, (data['datetime'] >= datetime.datetime.now().strftime("%Y%m%d 00:00:00")).sum())
        data.to_csv('%s/factors_%s_xy.csv'%(self.data_path, ls))
        return data

    def process_dataset(self, data):
        Y_data = data.label.values.reshape(-1, 1)
        Y_data = Y_data.astype(np.float32)

        # [c for c in data.columns if c != 'label']
        X_data = data[[c for c in data.columns if c != 'label']].values
        X_data = X_data.astype(np.float32)
        self.input_num = X_data.shape[1]

        train_x, test_x, train_y, test_y = train_test_split(X_data, Y_data)
        print(train_x.shape, train_y.shape, test_x.shape, test_y.shape)

        self.mean = train_x.mean(axis=0)
        self.std = train_x.std(axis=0)
        self.std[self.std==0] = 1 # todo: std有可能等于0
        train_x = (train_x - self.mean) / self.std
        test_x = (test_x - self.mean) / self.std

        train_ds = FactorDataset(train_x, train_y)
        self.train_dl = DataLoader(train_ds, batch_size=self.batch, shuffle=True)

        test_ds = FactorDataset(test_x, test_y)
        self.test_dl = DataLoader(test_ds, batch_size=self.batch * 2)

    # model.train()在训练之前调用代表训练模式
    # model.eval() 推理之前进行调用代表推理模式
    # 不同的模式仅会在使用nn.BatchNorm2d ，nn.Dropout等层时以确保这些不同阶段的行为正确。

    def get_model(self):
        model = AicmMLP(self.input_num, self.bn, self.dropout)
        model.to(self.device)
        optim = torch.optim.Adam(model.parameters(), lr=self.lr)
        # 学习速率衰减设置
        exp_lr_scheduler = lr_scheduler.StepLR(optim, step_size=30, gamma=0.5) # 按步数
        # exp_lr_scheduler = lr_scheduler.MultiStepLR(opt, milestones=[50, 100, 150], gamma=0.1) # 按里程碑
        # exp_lr_scheduler = lr_scheduler.ExponentialLR(opt, gamma=0.1) # 按系数每步

        return model, optim, exp_lr_scheduler

    # 定义计算正确率函数
    def accuracy(self, out, yb):
        preds = (out>0.5).type(torch.IntTensor)
        return (preds == yb).float().mean()

    def fit(self, epoch, model, optim, exp_lr_scheduler, trainloader, testloader):
        # 定义损失函数
        loss_fn = nn.BCELoss()

        # for p in optim.param_groups:
        #     print("lr: ", p['lr'])

        correct = 0
        total = 0
        running_loss = 0
        model.train() # 训练模式 dropout起作用
        for x, y in trainloader:
            if x.shape[0] == 1: # 单批次中的数据个数必须大于1
                continue
            x, y = x.to(self.device), y.to(self.device)
            y_pred = model(x)
            loss = loss_fn(y_pred, y)
            optim.zero_grad()
            loss.backward()
            optim.step()
            with torch.no_grad():
                correct += ((y_pred>0.5).type(torch.IntTensor).to(self.device) == y).sum().item()
                total += y.size(0)
                running_loss += loss.item()

        exp_lr_scheduler.step()

        epoch_loss = running_loss / len(trainloader.dataset) 
        epoch_acc = correct / total

        test_correct = 0
        test_total = 0
        test_running_loss = 0
        model.eval() # 预测模式 dropout不起作用
        with torch.no_grad():
            for x, y in testloader:
                x, y = x.to(self.device), y.to(self.device)
                y_pred = model(x)
                loss = loss_fn(y_pred, y)
                test_correct += ((y_pred>0.5).type(torch.IntTensor).to(self.device) == y).sum().item()
                test_total += y.size(0)
                test_running_loss += loss.item()

        epoch_test_loss = test_running_loss / len(testloader.dataset) 
        epoch_test_acc = test_correct / test_total

        if epoch % 100 == 0:
            print('epoch: ', epoch,
                'loss: ', round(epoch_loss, 3),
                'accuracy:', round(epoch_acc, 3),
                'test_loss: ', round(epoch_test_loss, 3),
                'test_accuracy:', round(epoch_test_acc, 3),
            )
        return epoch_loss, epoch_acc, epoch_test_loss, epoch_test_acc
        
    def train_model(self, model_name):
        model, optim, exp_lr = self.get_model()
        # 保存训练最优参数
        best_model_wts = copy.deepcopy(model.state_dict())
        best_acc = 0.0
        best_epoch = 0
        self.train_loss = []
        self.train_acc = []
        self.test_loss = []
        self.test_acc = []
        for epoch in range(1, self.epochs+1):
            epoch_loss, epoch_acc, epoch_test_loss, epoch_test_acc = self.fit(
                                    epoch,
                                    model, optim,
                                    exp_lr,
                                    self.train_dl,
                                    self.test_dl)
            self.train_loss.append(epoch_loss)
            self.train_acc.append(epoch_acc)
            self.test_loss.append(epoch_test_loss)
            self.test_acc.append(epoch_test_acc)

            if epoch_test_acc > best_acc:
                best_epoch = epoch
                best_acc = epoch_test_acc
                best_model_wts = copy.deepcopy(model.state_dict())

        model.load_state_dict(best_model_wts)
        model.eval() # 如果要使用，要调用eval()表明运行模式
        sm = torch.jit.script(model)
        print("Best model epoch: %d accuracy: %.3f" % (best_epoch, best_acc))

        sm.save('%s/%s.model' % (self.model_path, model_name))

    def plot_show(self, name, model_name, is_show=False):
        plt.figure()
        if name == 'loss':
            plt.plot(range(1, self.epochs+1), self.train_loss, label='train %s' % name)
            plt.plot(range(1, self.epochs+1), self.test_loss, label='test %s' % name)
        else:
            plt.plot(range(1, self.epochs+1), self.train_acc, label='train %s' % name)
            plt.plot(range(1, self.epochs+1), self.test_acc, label='test %s' % name)
        plt.legend()
        plt.title('%s %s' % (model_name, name))
        plt.savefig('%s/%s_%s.png' % (self.model_path, model_name, name))
        if is_show:
            plt.show()


    def get_data(train_ds, test_ds, bs):
        return (
            DataLoader(train_ds, batch_size=bs, shuffle=True),
            DataLoader(test_ds, batch_size=bs * 2),
        )


if __name__ == "__main__":
    am = AicmTrainModels()
    am.model_name = "LRS"
    # am.bn = False
    # am.dropout = False
    am.epochs = 400
    am.only_trading_instrument = False
    # am.is_onehot = False
    lss = ['long', 'short']
    # portfolios = ['00171106132928000', '00170623114649000'] # 5RNG
    # portfolios = ['00200910081133001'] # 7RNG
    portfolios = ['00200910081133001', '00171106132928000', '00170623114649000'] #
    for ls in lss:
        data = am.load_data(ls, portfolios)
        if len(data) == 0:
            continue
        for factors_name, columns in am.drop_columns.items():
            md_name = "%s_%s_%s" % (am.model_name, factors_name, ls)
            print("\nModel: %s" % md_name)
            ds = data.drop(columns, axis=1)
            if am.only_long_period[factors_name]:
                ds = ds.drop([c for c in ds.columns if 'sf_' in c], axis = 1)
            ds.dropna(axis=0, inplace=True)

            am.process_dataset(ds)
            am.dump_factor_select_items(factors_name, columns, ls, am.only_long_period[factors_name])
            am.train_model(md_name)
            am.plot_show("loss", md_name)
            am.plot_show("accuracy", md_name)

