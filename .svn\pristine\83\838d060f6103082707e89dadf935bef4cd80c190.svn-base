{"cells": [{"cell_type": "markdown", "source": ["# 导入AICM交易特征数据"], "metadata": {}}, {"cell_type": "code", "execution_count": 1, "source": ["import sys\r\n", "import time\r\n", "import datetime\r\n", "import json\r\n", "import sqlite3\r\n", "import pandas as pd\r\n", "import numpy as np\r\n", "sys.path.append(\"d:/QuantLab\")\r\n", "from qtunnel import DB,Mode,Cursor,Transaction,create_db,registered_dbs"], "outputs": [], "metadata": {}}, {"cell_type": "code", "execution_count": 20, "source": ["factor_num = 112\r\n", "all_factor_names = [\r\n", "    # 价量因子\r\n", "    \"OP<PERSON>\", \"HIGH\", \"<PERSON>OW\", \"C<PERSON><PERSON><PERSON>\", \"VOLUME\", \"TYPICAL_PRICE\", \"NEW\",\r\n", "    \"NEW_CHANGE_PERCENT\", \"SHORT_TERM_HIGH\", \"LONG_TERM_HIGH\", \"SHORT_TERM_LOW\",\r\n", "    \"LONG_TERM_LOW\",\r\n", "\r\n", "    # 技术指标类因子\r\n", "    \"AD\", \"DX\", \"ADX\", \"ADXR\", \"APO\", \"AROON_UP\", \"AROON_DOWN\", \"ATR\",\r\n", "    \"BOLL_UP\", \"B<PERSON>L_MID\", \"BOLL_DOWN\", \"CCI\", \"CMO\",\r\n", "\r\n", "    \"MA_FAST\", \"MA_SLOW\", \"EMA_FAST\", \"EMA_SLOW\", \"DEMA_FAST\", \"DEMA_SLOW\",\r\n", "    \"KAMA_FAST\", \"KAMA_SLOW\", \"MAMA_FAST\", \"MAMA_SLOW\", \"T3_FAST\", \"T3_SLOW\",\r\n", "    \"TEMA_FAST\", \"TEMA_SLOW\", \"TRIMA_FAST\", \"TRIMA_SLOW\", \"TRIX_FAST\",\r\n", "    \"TRIX_SLOW\",\r\n", "\r\n", "    \"MACD\", \"MACD_DIFF\", \"MACD_DEA\", \"MFI\", \"MOM\", \"NATR\", \"OBV\", \"ROC\", \"RSI\",\r\n", "    \"SAR\", \"TRANGE\", \"TSF\", \"ULTOSC\", \"WILLR\",\r\n", "    \"KD<PERSON>_<PERSON>\", \"KDJ_<PERSON>\",\r\n", "\r\n", "    # 自定义指标\r\n", "    \"LR_SLOPE_FAST\", \"LR_SLOPE_MIDD\", \"LR_SLOPE_SLOW\",\r\n", "    \"LR_SLOPE_FAST_THRESHOLD\", \"LR_SLOPE_SLOW_THRESHOLD\",\r\n", "\r\n", "    \"STDDEV_FAST\", \"STDDEV_SLOW\", \"STDDEV_THRESHOLD\",\r\n", "\r\n", "    \"MOMENTUM_FAST\", \"MOMENTUM_MIDD\", \"MOMENTUM_SLOW\", \"MOMENTUM\",\r\n", "    \"MOMENTUM_THRESHOLD\",\r\n", "\r\n", "    \"SQUEEZE\", \"SQUEEZE_SIGNAL\", \"SQUEEZE_ZERO_BARS\", \"SQUEEZE_BAND_UPL\",\r\n", "    \"SQUEEZE_BAND_DWL\", \"SQUEEZE_MDL\", \"SQUEEZE_KC_UPL\", \"SQUEEZE_KC_DWL\",\r\n", "    \"SQUEEZE_GAP\", \"SQUEEZE_GAP_FAST\", \"SQUEEZE_GAP_SLOW\",\r\n", "    \"SQUEEZE_GAP_THRESHOLD\", \"SQUEEZE_NARROW_BARS\",\r\n", "\r\n", "    \"BAND_UPL\", \"BAND_MDL\", \"BAND_DWL\", \"BAND_POSITION\", \"BAND_WIDTH\",\r\n", "    \"BAND_EXPAND\", \"<PERSON>ND_GRADIENT\", \"BAND_GRADIENT_THRESHOLD\", \"BAND_GAP\",\r\n", "    \"BAND_BK_BARS\", \"BAR_STICK_LENGTH\",\r\n", "\r\n", "    \"TL_FAST\", \"TL_SLOW\", \"TL_THRESHOLD\",\r\n", "\r\n", "    \"TREND_VALUE\", \"TREND_BARS\", \"TREND_INBARS\", \"TREND_INPOSR\", \"TREND_HIGHEST\", \"TREND_LOWEST\", \"TREND_HLR\",\r\n", "    \"TREND_LEVEL\",\r\n", "\r\n", "    \"HYO_TENKAN_SEN\", \"HYO_KIJUN_SEN\", \"HYO_CROSS_BARS\", \"TATR\",\r\n", "    \"TATR_THRESHOLD\"    \r\n", "]\r\n", "\r\n", "# 使用两个值的因子\r\n", "two_val_factor_name = [\r\n", "    \"RSI\", \"LR_SLOPE_FAST\", \"LR_SLOPE_MIDD\", \"LR_SLOPE_SLOW\", \r\n", "    \"SQUEEZE_GAP\", \"SQUEEZE_GAP_FAST\", \"SQUEEZE_GAP_SLOW\",\r\n", "    \"BAND_GRADIENT\",\r\n", "    \"TL_FAST\", \"TL_SLOW\",\r\n", "    \"TREND_VALUE\", \"TREND_BARS\", \"TREND_INBARS\", \"TREND_INPOSR\"\r\n", "]\r\n", "\r\n", "context_name = [\r\n", "    \"AD_PS_RATIO\", \"COST_RNG\", \"CURRENT_TIME\", \"DRAWDOWN_RNG\", \r\n", "    \"FAST_AG_RSI\", \"FAST_AG_RSI_PREV\", \"FAST_QH_RSI\", \"FAST_QH_RSI_PREV\", \r\n", "    \"LONG_RANGE\", \"PF_PS_RATIO\", \"PF_YIELD_HL\", \"PF_YIELD_TREND\", \r\n", "    \"PNL\", \"POS_DAYS\", \"POS_LONG_BARS\", \"POS_SHORT_BARS\", \"SHORT_RANGE\",\r\n", "    \"SLOW_AG_RSI\", \"SLOW_AG_RSI_PREV\", \"SLOW_QH_RSI\", \"SLOW_QH_RSI_PREV\", \"STDDEV_RNG\"\r\n", "]\r\n", "\r\n", "factor_names = [[ # Slow period factor\r\n", "    \"RSI\",\r\n", "    # 自定义指标\r\n", "    \"LR_SLOPE_FAST\", \"LR_SLOPE_MIDD\", \"LR_SLOPE_SLOW\",\r\n", "    \"LR_SLOPE_FAST_THRESHOLD\", \"LR_SLOPE_SLOW_THRESHOLD\",\r\n", "\r\n", "    \"SQUEEZE_ZERO_BARS\", \r\n", "    \"SQUEEZE_GAP\", \"SQUEEZE_GAP_FAST\", \"SQUEEZE_GAP_SLOW\",\r\n", "    \"SQUEEZE_GAP_THRESHOLD\", \"SQUEEZE_NARROW_BARS\",\r\n", "\r\n", "    \"BAND_POSITION\", \"BAND_WIDTH\",\r\n", "    \"BAND_EXPAND\", \"<PERSON>ND_GRADIENT\", \"BAND_GRADIENT_THRESHOLD\", \"BAND_GAP\",\r\n", "\r\n", "    \"TL_FAST\", \"TL_SLOW\", \"TL_THRESHOLD\",\r\n", "\r\n", "    \"TREND_VALUE\", \"TREND_BARS\", \"TREND_INBARS\", \"TREND_INPOSR\", \"TREND_HLR\",\r\n", "    \"TREND_LEVEL\"\r\n", "],\r\n", "[ # Fast period factor\r\n", "    \"RSI\",\r\n", "    # 自定义指标\r\n", "    \"LR_SLOPE_FAST\", \"LR_SLOPE_MIDD\", \"LR_SLOPE_SLOW\",\r\n", "    \"LR_SLOPE_FAST_THRESHOLD\", \"LR_SLOPE_SLOW_THRESHOLD\",\r\n", "\r\n", "    \"SQUEEZE_ZERO_BARS\", \r\n", "    \"SQUEEZE_GAP\", \"SQUEEZE_GAP_FAST\", \"SQUEEZE_GAP_SLOW\",\r\n", "    \"SQUEEZE_GAP_THRESHOLD\", \"SQUEEZE_NARROW_BARS\",\r\n", "\r\n", "    \"BAND_POSITION\", \"BAND_WIDTH\",\r\n", "    \"BAND_EXPAND\", \"<PERSON>ND_GRADIENT\", \"BAND_GRADIENT_THRESHOLD\", \"BAND_GAP\",\r\n", "\r\n", "    \"TL_FAST\", \"TL_SLOW\", \"TL_THRESHOLD\",\r\n", "\r\n", "    \"TREND_VALUE\", \"TREND_BARS\", \"TREND_INBARS\", \"TREND_INPOSR\", \"TREND_HLR\",\r\n", "    \"TREND_LEVEL\"\r\n", "]]\r\n", "\r\n", "pos_context = [\r\n", "  \"COST_RNG\", \"DRAWDOWN_RNG\", \"STDDEV_RNG\", \"PNL\", \"POS_DAYS\",\r\n", "  \"POS_SHORT_BARS\", \"POS_LONG_BARS\", \"SHORT_RANGE\", \"LONG_RANGE\",\r\n", "  \"PF_YIELD_TREND\", \"PF_YIELD_HL\", \"AD_PS_RATIO\", \"PF_PS_RATIO\",\r\n", "  \"FAST_AG_RSI\", \"FAST_AG_RSI_PREV\", \"SLOW_AG_RSI\", \"SLOW_AG_RSI_PREV\",\r\n", "  \"FAST_QH_RSI\", \"FAST_QH_RSI_PREV\", \"SLOW_QH_RSI\", \"SLOW_QH_RSI_PREV\",\r\n", "  \"CURRENT_TIME\"\r\n", "]\r\n", "\r\n", "open_pos_context = [\r\n", "  \"STDDEV_RNG\",\r\n", "  \"FAST_QH_RSI\", \"FAST_QH_RSI_PREV\", \"SLOW_QH_RSI\", \"SLOW_QH_RSI_PREV\"\r\n", "]\r\n", "\r\n", "def factor_select(n, i):\r\n", "    if all_factor_names[n] in factor_names[i]:\r\n", "        if all_factor_names[n] in two_val_factor_name:\r\n", "            return 2\r\n", "        else:\r\n", "            return 1\r\n", "    return 0\r\n", "\r\n", "def context_select(n):\r\n", "    if pos_context[n] in open_pos_context:\r\n", "        return 1\r\n", "    return 0\r\n", "        "], "outputs": [], "metadata": {}}, {"cell_type": "code", "execution_count": 21, "source": ["f_sel = {}\r\n", "f_sel['slow'] = [factor_select(n, 0) for n in range(len(all_factor_names))]\r\n", "f_sel['fast'] = [factor_select(n, 1) for n in range(len(all_factor_names))]\r\n", "f_sel['context'] = [context_select(n) for n in range(len(pos_context))]\r\n", "with open('e:/lab/RoboQuant/pylab/model/factor_sel.json', 'w') as factor_sel_file:\r\n", "    json.dump(f_sel, factor_sel_file)\r\n", "json.dumps(f_sel)"], "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'{\"slow\": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 2, 2, 2, 1, 1, 0, 0, 0, 1, 1, 1, 2, 1, 1, 0, 0, 2, 2, 1, 2, 2, 2, 2, 0, 0, 1, 1, 0, 0, 0, 0, 0], \"fast\": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 2, 2, 2, 1, 1, 0, 0, 0, 1, 1, 1, 2, 1, 1, 0, 0, 2, 2, 1, 2, 2, 2, 2, 0, 0, 1, 1, 0, 0, 0, 0, 0], \"context\": [0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0]}'"]}, "metadata": {}, "execution_count": 21}], "metadata": {}}, {"cell_type": "markdown", "source": ["ff = lambda n: 2 if all_factor_names[n] in two_val_factor_name else 1\r\n", "[ff(n)  for n in range(len(all_factor_names))]"], "metadata": {}}, {"cell_type": "code", "execution_count": 4, "source": ["portfolio_id = '*****************'\r\n", "db_path = \"d:/QuantLab\"\r\n", "# db_path = 'd:/RobQuant/data'\r\n", "# db_path = 'e:/lab/RoboQuant/bin/x64/Release/data'\r\n", "# 从数据库加载因子数据\r\n", "def load_factor_from_db():\r\n", "    db=create_db(\"leveldb\", \"%s/store/kv.db\" % db_path, Mode.read)\r\n", "    cursor = db.new_cursor()\r\n", "    # cursor.seek_to_first()\r\n", "    data = {}\r\n", "    while cursor.valid():\r\n", "        # print(cursor.key())\r\n", "        # print(cursor.key(), cursor.value())\r\n", "        # fss:lf:\r\n", "        if cursor.key()[0:4] == b'fss:':\r\n", "            key = cursor.key().decode()\r\n", "            ordid = key[7:]\r\n", "            if ordid not in data:\r\n", "                data[ordid] = {}\r\n", "            item = data[ordid]\r\n", "            if key[0:7] == 'fss:lf:':\r\n", "                item['lf'] = cursor.value().decode()\r\n", "            if key[0:7] == 'fss:sf:':\r\n", "                item['sf'] = cursor.value().decode()\r\n", "            if key[0:7] == 'fss:ct:':\r\n", "                item['ct'] = cursor.value().decode()\r\n", "            # data.append(cursor.value())\r\n", "        cursor.next()\r\n", "    del cursor\r\n", "    db.close()\r\n", "    del db\r\n", "    return data\r\n", "\r\n", "def unpack_factor_data(data):\r\n", "    return json.loads(data)\r\n", "\r\n", "def get_factor_names(factor_name, is_open_ord=True):\r\n", "    ds = load_factor_from_db()\r\n", "\r\n", "    col_names = ['ord_id']\r\n", "    for name in factor_name:\r\n", "        if name in two_val_factor_name:\r\n", "            col_names.append(\"lf_{}_1\".format(name))\r\n", "            col_names.append(\"lf_{}_2\".format(name))\r\n", "        else:\r\n", "            col_names.append(\"lf_%s\" % name)\r\n", "\r\n", "    for name in factor_name:\r\n", "        if name in two_val_factor_name:\r\n", "            col_names.append(\"sf_{}_1\".format(name))\r\n", "            col_names.append(\"sf_{}_2\".format(name))\r\n", "        else:\r\n", "            col_names.append(\"sf_%s\" % name)\r\n", "\r\n", "    if is_open_ord:\r\n", "        col_names.extend(open_pos_context)\r\n", "    else:\r\n", "        col_names.extend(pos_context)\r\n", "\r\n", "    return col_names\r\n", "\r\n", "def get_order_factors(factor_name, is_open_ord=True):\r\n", "    ds = load_factor_from_db()\r\n", "\r\n", "    factor_data = []\r\n", "    for key, value in ds.items():\r\n", "        item = []\r\n", "        item.append(key)\r\n", "\r\n", "        data = json.loads(value['lf'])\r\n", "        for name in factor_name:\r\n", "            if name in two_val_factor_name:\r\n", "                item.extend(data[name][1:])\r\n", "            else:\r\n", "                item.append(data[name][2])\r\n", "\r\n", "        data = json.loads(value['sf'])\r\n", "        for name in factor_name:\r\n", "            if name in two_val_factor_name:\r\n", "                item.extend(data[name][1:])\r\n", "            else:\r\n", "                item.append(data[name][2])\r\n", "\r\n", "        if 'ct' in value:\r\n", "            data = json.loads(value['ct'])\r\n", "            if is_open_ord and data['POS_SHORT_BARS'] == 0.0:\r\n", "                for key in open_pos_context:\r\n", "                    if key in data.keys():\r\n", "                        item.append(data[key])\r\n", "                    else:\r\n", "                        item.append(0.0)\r\n", "            elif not is_open_ord:\r\n", "                for key in pos_context:\r\n", "                    if key in data.keys():\r\n", "                        item.append(data[key])\r\n", "                    else:\r\n", "                        item.append(0.0)\r\n", "\r\n", "        factor_data.append(item)\r\n", "    return pd.DataFrame(factor_data, columns=get_factor_names(factor_name, is_open_ord))\r\n", "\r\n", "\r\n", "\"\"\"\r\n", "从orders数据库导出订单标签\r\n", "\"\"\"\r\n", "def load_orders():\r\n", "    conn = sqlite3.connect('%s/data/ot_store.db'%db_path)\r\n", "    cur = conn.cursor()\r\n", "\r\n", "    # cur.execute(\"\"\"SELECT * FROM T_Filled_Order WHERE account_id = \"%s\" ORDER BY id DESC\"\"\"%('*****************'))\r\n", "    cur.execute(\"SELECT * FROM T_Filled_Order ORDER BY id ASC\")\r\n", "    orders = cur.fetchall()\r\n", "\r\n", "    data = []\r\n", "    for item in orders:\r\n", "        record = []\r\n", "        ord = json.loads(item[1])\r\n", "        if ord['account_id'] != portfolio_id:\r\n", "            continue\r\n", "        record.append(ord['order_id'])\r\n", "        record.append(ord['account_id'])\r\n", "        record.append(ord['order_book_id'])\r\n", "        record.append(time.strftime(\"%Y%m%d %H:%M:%S\", time.localtime(ord['trading_dt'])))\r\n", "        msg = ord['message'].split(',')\r\n", "        if len(msg) <= 2:\r\n", "            record.append(msg[0])\r\n", "            record.append(msg[1])\r\n", "            record.append(0)\r\n", "            record.append(0)\r\n", "        elif len(msg) > 7:\r\n", "            record.append(msg[0])\r\n", "            record.append(msg[1])\r\n", "            record.append(msg[3])\r\n", "            record.append(msg[7])\r\n", "        else:\r\n", "            record.append(msg[0])\r\n", "            record.append(msg[1])\r\n", "            record.append(0)\r\n", "            record.append(0)\r\n", "        data.append(record)\r\n", "\r\n", "    cur.close()\r\n", "    conn.close()\r\n", "\r\n", "    return pd.DataFrame(data, columns=['ord_id', 'account_id', 'label', 'datetime', 'oprater', 'direct', 'cost_atr', 'pnl'])\r\n", "\r\n", "def get_orders_label(ord_df):\r\n", "    data = []\r\n", "    for name, group in ord_df.groupby(['account_id', 'label']):\r\n", "        for i in range(len(group)):\r\n", "            if i + 1 >= len(group):\r\n", "                break\r\n", "            if group.iat[i, 4] != \"open\":\r\n", "                continue\r\n", "            item = []\r\n", "            item.append(group.iat[i, 0])\r\n", "            item.append(group.iat[i, 2])\r\n", "            item.append(group.iat[i, 3])\r\n", "            item.append(group.iat[i, 5])\r\n", "            i = i + 1\r\n", "            if group.iat[i, 4] != \"open\":\r\n", "                if float(group.iat[i, 7]) > 0:\r\n", "                    item.append(1)\r\n", "                else:\r\n", "                    item.append(0)\r\n", "                data.append(item)\r\n", "            else:\r\n", "                print(\"warning:\", group.iat[i,:])\r\n", "    return pd.DataFrame(data, columns=['ord_id', 'instrument', 'datetime', 'direct', 'label'])"], "outputs": [], "metadata": {}}, {"cell_type": "code", "execution_count": 5, "source": ["open_df = get_order_factors(factor_name=factor_names[1], is_open_ord=True)\r\n", "open_df\r\n", "# open_df.to_csv(\"e:/lab/RoboQuant/pylab/data/open_factor.csv\")"], "outputs": [{"output_type": "execute_result", "data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ord_id</th>\n", "      <th>lf_RSI_1</th>\n", "      <th>lf_RSI_2</th>\n", "      <th>lf_LR_SLOPE_FAST_1</th>\n", "      <th>lf_LR_SLOPE_FAST_2</th>\n", "      <th>lf_LR_SLOPE_MIDD_1</th>\n", "      <th>lf_LR_SLOPE_MIDD_2</th>\n", "      <th>lf_LR_SLOPE_SLOW_1</th>\n", "      <th>lf_LR_SLOPE_SLOW_2</th>\n", "      <th>lf_LR_SLOPE_FAST_THRESHOLD</th>\n", "      <th>...</th>\n", "      <th>sf_TREND_INPOSR_2</th>\n", "      <th>sf_TREND_HLR</th>\n", "      <th>sf_TREND_LEVEL</th>\n", "      <th>FAST_QH_RSI</th>\n", "      <th>FAST_QH_RSI_PREV</th>\n", "      <th>LONG_RANGE</th>\n", "      <th>SHORT_RANGE</th>\n", "      <th>SLOW_QH_RSI</th>\n", "      <th>SLOW_QH_RSI_PREV</th>\n", "      <th>STDDEV_RNG</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>00210819133005001</td>\n", "      <td>40.570963</td>\n", "      <td>39.781466</td>\n", "      <td>-121.3</td>\n", "      <td>-107.8</td>\n", "      <td>-16.121212</td>\n", "      <td>-33.266667</td>\n", "      <td>-5.622308</td>\n", "      <td>-11.488462</td>\n", "      <td>64.718001</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>4.750000</td>\n", "      <td>3.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>00210819135636007</td>\n", "      <td>43.717702</td>\n", "      <td>41.523428</td>\n", "      <td>-26.6</td>\n", "      <td>-27.8</td>\n", "      <td>-8.630303</td>\n", "      <td>-8.006061</td>\n", "      <td>-10.763077</td>\n", "      <td>-14.075385</td>\n", "      <td>35.183265</td>\n", "      <td>...</td>\n", "      <td>1.300000</td>\n", "      <td>6.000000</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>00210819135637009</td>\n", "      <td>43.717702</td>\n", "      <td>41.523428</td>\n", "      <td>-26.6</td>\n", "      <td>-27.8</td>\n", "      <td>-8.630303</td>\n", "      <td>-8.006061</td>\n", "      <td>-10.763077</td>\n", "      <td>-14.075385</td>\n", "      <td>35.183265</td>\n", "      <td>...</td>\n", "      <td>1.300000</td>\n", "      <td>6.000000</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>00210819135855011</td>\n", "      <td>43.717702</td>\n", "      <td>42.051084</td>\n", "      <td>-26.6</td>\n", "      <td>-25.6</td>\n", "      <td>-8.630303</td>\n", "      <td>-7.406061</td>\n", "      <td>-10.763077</td>\n", "      <td>-13.973846</td>\n", "      <td>35.163861</td>\n", "      <td>...</td>\n", "      <td>0.200000</td>\n", "      <td>6.000000</td>\n", "      <td>4.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>00210819210132007</td>\n", "      <td>62.029417</td>\n", "      <td>61.350418</td>\n", "      <td>-5.7</td>\n", "      <td>-92.8</td>\n", "      <td>123.563636</td>\n", "      <td>100.181818</td>\n", "      <td>70.156154</td>\n", "      <td>67.841538</td>\n", "      <td>113.471268</td>\n", "      <td>...</td>\n", "      <td>0.394737</td>\n", "      <td>11.842105</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3552</th>\n", "      <td>00210917143922599</td>\n", "      <td>46.989491</td>\n", "      <td>46.716832</td>\n", "      <td>2.9</td>\n", "      <td>65.3</td>\n", "      <td>-100.266667</td>\n", "      <td>-81.181818</td>\n", "      <td>-15.990769</td>\n", "      <td>-26.118462</td>\n", "      <td>115.926286</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>6.304348</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3553</th>\n", "      <td>00210917144042601</td>\n", "      <td>56.239098</td>\n", "      <td>55.059427</td>\n", "      <td>-51.8</td>\n", "      <td>-100.9</td>\n", "      <td>58.739394</td>\n", "      <td>43.321212</td>\n", "      <td>27.765385</td>\n", "      <td>28.394615</td>\n", "      <td>61.383238</td>\n", "      <td>...</td>\n", "      <td>0.363636</td>\n", "      <td>17.545455</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3554</th>\n", "      <td>00210917144210607</td>\n", "      <td>53.430926</td>\n", "      <td>50.450557</td>\n", "      <td>20.8</td>\n", "      <td>-12.2</td>\n", "      <td>19.151515</td>\n", "      <td>10.860606</td>\n", "      <td>2.044615</td>\n", "      <td>3.330769</td>\n", "      <td>31.537663</td>\n", "      <td>...</td>\n", "      <td>0.000000</td>\n", "      <td>8.285714</td>\n", "      <td>-3.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3555</th>\n", "      <td>00210917144220609</td>\n", "      <td>34.408926</td>\n", "      <td>37.126332</td>\n", "      <td>-53.1</td>\n", "      <td>-7.8</td>\n", "      <td>-58.406061</td>\n", "      <td>-55.836364</td>\n", "      <td>-18.777692</td>\n", "      <td>-19.611538</td>\n", "      <td>41.141318</td>\n", "      <td>...</td>\n", "      <td>1.636364</td>\n", "      <td>13.909091</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3556</th>\n", "      <td>00210917144703613</td>\n", "      <td>42.646335</td>\n", "      <td>44.014354</td>\n", "      <td>-6.9</td>\n", "      <td>6.6</td>\n", "      <td>-2.139394</td>\n", "      <td>0.581818</td>\n", "      <td>-6.969231</td>\n", "      <td>-7.773846</td>\n", "      <td>16.807664</td>\n", "      <td>...</td>\n", "      <td>1.933702</td>\n", "      <td>15.745856</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3557 rows × 90 columns</p>\n", "</div>"], "text/plain": ["                 ord_id   lf_RSI_1   lf_RSI_2  lf_LR_SLOPE_FAST_1  \\\n", "0     00210819133005001  40.570963  39.781466              -121.3   \n", "1     00210819135636007  43.717702  41.523428               -26.6   \n", "2     00210819135637009  43.717702  41.523428               -26.6   \n", "3     00210819135855011  43.717702  42.051084               -26.6   \n", "4     00210819210132007  62.029417  61.350418                -5.7   \n", "...                 ...        ...        ...                 ...   \n", "3552  00210917143922599  46.989491  46.716832                 2.9   \n", "3553  00210917144042601  56.239098  55.059427               -51.8   \n", "3554  00210917144210607  53.430926  50.450557                20.8   \n", "3555  00210917144220609  34.408926  37.126332               -53.1   \n", "3556  00210917144703613  42.646335  44.014354                -6.9   \n", "\n", "      lf_LR_SLOPE_FAST_2  lf_LR_SLOPE_MIDD_1  lf_LR_SLOPE_MIDD_2  \\\n", "0                 -107.8          -16.121212          -33.266667   \n", "1                  -27.8           -8.630303           -8.006061   \n", "2                  -27.8           -8.630303           -8.006061   \n", "3                  -25.6           -8.630303           -7.406061   \n", "4                  -92.8          123.563636          100.181818   \n", "...                  ...                 ...                 ...   \n", "3552                65.3         -100.266667          -81.181818   \n", "3553              -100.9           58.739394           43.321212   \n", "3554               -12.2           19.151515           10.860606   \n", "3555                -7.8          -58.406061          -55.836364   \n", "3556                 6.6           -2.139394            0.581818   \n", "\n", "      lf_LR_SLOPE_SLOW_1  lf_LR_SLOPE_SLOW_2  lf_LR_SLOPE_FAST_THRESHOLD  ...  \\\n", "0              -5.622308          -11.488462                   64.718001  ...   \n", "1             -10.763077          -14.075385                   35.183265  ...   \n", "2             -10.763077          -14.075385                   35.183265  ...   \n", "3             -10.763077          -13.973846                   35.163861  ...   \n", "4              70.156154           67.841538                  113.471268  ...   \n", "...                  ...                 ...                         ...  ...   \n", "3552          -15.990769          -26.118462                  115.926286  ...   \n", "3553           27.765385           28.394615                   61.383238  ...   \n", "3554            2.044615            3.330769                   31.537663  ...   \n", "3555          -18.777692          -19.611538                   41.141318  ...   \n", "3556           -6.969231           -7.773846                   16.807664  ...   \n", "\n", "      sf_TREND_INPOSR_2  sf_TREND_HLR  sf_TREND_LEVEL  FAST_QH_RSI  \\\n", "0              0.000000      4.750000             3.0          NaN   \n", "1              1.300000      6.000000             0.0          NaN   \n", "2              1.300000      6.000000             0.0          NaN   \n", "3              0.200000      6.000000             4.0          NaN   \n", "4              0.394737     11.842105             0.0          NaN   \n", "...                 ...           ...             ...          ...   \n", "3552           0.000000      6.304348             0.0          NaN   \n", "3553           0.363636     17.545455             0.0          NaN   \n", "3554           0.000000      8.285714            -3.0          NaN   \n", "3555           1.636364     13.909091             0.0          NaN   \n", "3556           1.933702     15.745856             0.0          NaN   \n", "\n", "      FAST_QH_RSI_PREV  LONG_RANGE  SHORT_RANGE  SLOW_QH_RSI  \\\n", "0                  NaN         NaN          NaN          NaN   \n", "1                  NaN         NaN          NaN          NaN   \n", "2                  NaN         NaN          NaN          NaN   \n", "3                  NaN         NaN          NaN          NaN   \n", "4                  NaN         NaN          NaN          NaN   \n", "...                ...         ...          ...          ...   \n", "3552               NaN         NaN          NaN          NaN   \n", "3553               NaN         NaN          NaN          NaN   \n", "3554               NaN         NaN          NaN          NaN   \n", "3555               NaN         NaN          NaN          NaN   \n", "3556               NaN         NaN          NaN          NaN   \n", "\n", "      SLOW_QH_RSI_PREV  STDDEV_RNG  \n", "0                  NaN         NaN  \n", "1                  NaN         NaN  \n", "2                  NaN         NaN  \n", "3                  NaN         NaN  \n", "4                  NaN         NaN  \n", "...                ...         ...  \n", "3552               NaN         NaN  \n", "3553               NaN         NaN  \n", "3554               NaN         NaN  \n", "3555               NaN         NaN  \n", "3556               NaN         NaN  \n", "\n", "[3557 rows x 90 columns]"]}, "metadata": {}, "execution_count": 5}], "metadata": {}}, {"cell_type": "code", "execution_count": 6, "source": ["close_df = get_order_factors(factor_names[1], is_open_ord=False)\r\n", "close_df\r\n", "# close_df.to_csv(\"e:/lab/RoboQuant/pylab/data/close_factor.csv\")"], "outputs": [{"output_type": "execute_result", "data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ord_id</th>\n", "      <th>lf_RSI_1</th>\n", "      <th>lf_RSI_2</th>\n", "      <th>lf_LR_SLOPE_FAST_1</th>\n", "      <th>lf_LR_SLOPE_FAST_2</th>\n", "      <th>lf_LR_SLOPE_MIDD_1</th>\n", "      <th>lf_LR_SLOPE_MIDD_2</th>\n", "      <th>lf_LR_SLOPE_SLOW_1</th>\n", "      <th>lf_LR_SLOPE_SLOW_2</th>\n", "      <th>lf_LR_SLOPE_FAST_THRESHOLD</th>\n", "      <th>...</th>\n", "      <th>PNL</th>\n", "      <th>POS_DAYS</th>\n", "      <th>POS_LONG_BARS</th>\n", "      <th>POS_SHORT_BARS</th>\n", "      <th>SHORT_RANGE</th>\n", "      <th>SLOW_AG_RSI</th>\n", "      <th>SLOW_AG_RSI_PREV</th>\n", "      <th>SLOW_QH_RSI</th>\n", "      <th>SLOW_QH_RSI_PREV</th>\n", "      <th>STDDEV_RNG</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>00210819133005001</td>\n", "      <td>40.570963</td>\n", "      <td>39.781466</td>\n", "      <td>-121.3</td>\n", "      <td>-107.8</td>\n", "      <td>-16.121212</td>\n", "      <td>-33.266667</td>\n", "      <td>-5.622308</td>\n", "      <td>-11.488462</td>\n", "      <td>64.718001</td>\n", "      <td>...</td>\n", "      <td>-1360.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>13.0</td>\n", "      <td>16.0</td>\n", "      <td>-3.328824</td>\n", "      <td>0.000000</td>\n", "      <td>-0.581372</td>\n", "      <td>0.000000</td>\n", "      <td>1.208815</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>00210819135636007</td>\n", "      <td>43.717702</td>\n", "      <td>41.523428</td>\n", "      <td>-26.6</td>\n", "      <td>-27.8</td>\n", "      <td>-8.630303</td>\n", "      <td>-8.006061</td>\n", "      <td>-10.763077</td>\n", "      <td>-14.075385</td>\n", "      <td>35.183265</td>\n", "      <td>...</td>\n", "      <td>400.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>6.0</td>\n", "      <td>10.0</td>\n", "      <td>-3.294673</td>\n", "      <td>-3.294673</td>\n", "      <td>-1.100247</td>\n", "      <td>-1.114566</td>\n", "      <td>1.080474</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>00210819135637009</td>\n", "      <td>43.717702</td>\n", "      <td>41.523428</td>\n", "      <td>-26.6</td>\n", "      <td>-27.8</td>\n", "      <td>-8.630303</td>\n", "      <td>-8.006061</td>\n", "      <td>-10.763077</td>\n", "      <td>-14.075385</td>\n", "      <td>35.183265</td>\n", "      <td>...</td>\n", "      <td>440.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>5.0</td>\n", "      <td>10.0</td>\n", "      <td>-3.294673</td>\n", "      <td>-3.294673</td>\n", "      <td>-1.100247</td>\n", "      <td>-1.114566</td>\n", "      <td>1.080474</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>00210819135855011</td>\n", "      <td>43.717702</td>\n", "      <td>42.051084</td>\n", "      <td>-26.6</td>\n", "      <td>-25.6</td>\n", "      <td>-8.630303</td>\n", "      <td>-7.406061</td>\n", "      <td>-10.763077</td>\n", "      <td>-13.973846</td>\n", "      <td>35.163861</td>\n", "      <td>...</td>\n", "      <td>480.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>4.0</td>\n", "      <td>10.0</td>\n", "      <td>-3.294673</td>\n", "      <td>-3.294673</td>\n", "      <td>-1.084081</td>\n", "      <td>-1.114566</td>\n", "      <td>1.081786</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>00210819210132007</td>\n", "      <td>62.029417</td>\n", "      <td>61.350418</td>\n", "      <td>-5.7</td>\n", "      <td>-92.8</td>\n", "      <td>123.563636</td>\n", "      <td>100.181818</td>\n", "      <td>70.156154</td>\n", "      <td>67.841538</td>\n", "      <td>113.471268</td>\n", "      <td>...</td>\n", "      <td>2250.0</td>\n", "      <td>9.0</td>\n", "      <td>7.0</td>\n", "      <td>268.0</td>\n", "      <td>38.0</td>\n", "      <td>-3.460083</td>\n", "      <td>0.000000</td>\n", "      <td>-1.675582</td>\n", "      <td>0.000000</td>\n", "      <td>1.209055</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3552</th>\n", "      <td>00210917143922599</td>\n", "      <td>46.989491</td>\n", "      <td>46.716832</td>\n", "      <td>2.9</td>\n", "      <td>65.3</td>\n", "      <td>-100.266667</td>\n", "      <td>-81.181818</td>\n", "      <td>-15.990769</td>\n", "      <td>-26.118462</td>\n", "      <td>115.926286</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3553</th>\n", "      <td>00210917144042601</td>\n", "      <td>56.239098</td>\n", "      <td>55.059427</td>\n", "      <td>-51.8</td>\n", "      <td>-100.9</td>\n", "      <td>58.739394</td>\n", "      <td>43.321212</td>\n", "      <td>27.765385</td>\n", "      <td>28.394615</td>\n", "      <td>61.383238</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3554</th>\n", "      <td>00210917144210607</td>\n", "      <td>53.430926</td>\n", "      <td>50.450557</td>\n", "      <td>20.8</td>\n", "      <td>-12.2</td>\n", "      <td>19.151515</td>\n", "      <td>10.860606</td>\n", "      <td>2.044615</td>\n", "      <td>3.330769</td>\n", "      <td>31.537663</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3555</th>\n", "      <td>00210917144220609</td>\n", "      <td>34.408926</td>\n", "      <td>37.126332</td>\n", "      <td>-53.1</td>\n", "      <td>-7.8</td>\n", "      <td>-58.406061</td>\n", "      <td>-55.836364</td>\n", "      <td>-18.777692</td>\n", "      <td>-19.611538</td>\n", "      <td>41.141318</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3556</th>\n", "      <td>00210917144703613</td>\n", "      <td>42.646335</td>\n", "      <td>44.014354</td>\n", "      <td>-6.9</td>\n", "      <td>6.6</td>\n", "      <td>-2.139394</td>\n", "      <td>0.581818</td>\n", "      <td>-6.969231</td>\n", "      <td>-7.773846</td>\n", "      <td>16.807664</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3557 rows × 104 columns</p>\n", "</div>"], "text/plain": ["                 ord_id   lf_RSI_1   lf_RSI_2  lf_LR_SLOPE_FAST_1  \\\n", "0     00210819133005001  40.570963  39.781466              -121.3   \n", "1     00210819135636007  43.717702  41.523428               -26.6   \n", "2     00210819135637009  43.717702  41.523428               -26.6   \n", "3     00210819135855011  43.717702  42.051084               -26.6   \n", "4     00210819210132007  62.029417  61.350418                -5.7   \n", "...                 ...        ...        ...                 ...   \n", "3552  00210917143922599  46.989491  46.716832                 2.9   \n", "3553  00210917144042601  56.239098  55.059427               -51.8   \n", "3554  00210917144210607  53.430926  50.450557                20.8   \n", "3555  00210917144220609  34.408926  37.126332               -53.1   \n", "3556  00210917144703613  42.646335  44.014354                -6.9   \n", "\n", "      lf_LR_SLOPE_FAST_2  lf_LR_SLOPE_MIDD_1  lf_LR_SLOPE_MIDD_2  \\\n", "0                 -107.8          -16.121212          -33.266667   \n", "1                  -27.8           -8.630303           -8.006061   \n", "2                  -27.8           -8.630303           -8.006061   \n", "3                  -25.6           -8.630303           -7.406061   \n", "4                  -92.8          123.563636          100.181818   \n", "...                  ...                 ...                 ...   \n", "3552                65.3         -100.266667          -81.181818   \n", "3553              -100.9           58.739394           43.321212   \n", "3554               -12.2           19.151515           10.860606   \n", "3555                -7.8          -58.406061          -55.836364   \n", "3556                 6.6           -2.139394            0.581818   \n", "\n", "      lf_LR_SLOPE_SLOW_1  lf_LR_SLOPE_SLOW_2  lf_LR_SLOPE_FAST_THRESHOLD  ...  \\\n", "0              -5.622308          -11.488462                   64.718001  ...   \n", "1             -10.763077          -14.075385                   35.183265  ...   \n", "2             -10.763077          -14.075385                   35.183265  ...   \n", "3             -10.763077          -13.973846                   35.163861  ...   \n", "4              70.156154           67.841538                  113.471268  ...   \n", "...                  ...                 ...                         ...  ...   \n", "3552          -15.990769          -26.118462                  115.926286  ...   \n", "3553           27.765385           28.394615                   61.383238  ...   \n", "3554            2.044615            3.330769                   31.537663  ...   \n", "3555          -18.777692          -19.611538                   41.141318  ...   \n", "3556           -6.969231           -7.773846                   16.807664  ...   \n", "\n", "         PNL  POS_DAYS  POS_LONG_BARS  POS_SHORT_BARS  SHORT_RANGE  \\\n", "0    -1360.0       0.0            1.0            13.0         16.0   \n", "1      400.0       0.0            0.0             6.0         10.0   \n", "2      440.0       0.0            0.0             5.0         10.0   \n", "3      480.0       0.0            0.0             4.0         10.0   \n", "4     2250.0       9.0            7.0           268.0         38.0   \n", "...      ...       ...            ...             ...          ...   \n", "3552     NaN       NaN            NaN             NaN          NaN   \n", "3553     NaN       NaN            NaN             NaN          NaN   \n", "3554     NaN       NaN            NaN             NaN          NaN   \n", "3555     NaN       NaN            NaN             NaN          NaN   \n", "3556     NaN       NaN            NaN             NaN          NaN   \n", "\n", "      SLOW_AG_RSI  SLOW_AG_RSI_PREV  SLOW_QH_RSI  SLOW_QH_RSI_PREV  STDDEV_RNG  \n", "0       -3.328824          0.000000    -0.581372          0.000000    1.208815  \n", "1       -3.294673         -3.294673    -1.100247         -1.114566    1.080474  \n", "2       -3.294673         -3.294673    -1.100247         -1.114566    1.080474  \n", "3       -3.294673         -3.294673    -1.084081         -1.114566    1.081786  \n", "4       -3.460083          0.000000    -1.675582          0.000000    1.209055  \n", "...           ...               ...          ...               ...         ...  \n", "3552          NaN               NaN          NaN               NaN         NaN  \n", "3553          NaN               NaN          NaN               NaN         NaN  \n", "3554          NaN               NaN          NaN               NaN         NaN  \n", "3555          NaN               NaN          NaN               NaN         NaN  \n", "3556          NaN               NaN          NaN               NaN         NaN  \n", "\n", "[3557 rows x 104 columns]"]}, "metadata": {}, "execution_count": 6}], "metadata": {}}, {"cell_type": "code", "execution_count": 7, "source": ["label_df = get_orders_label(load_orders())\r\n", "label_df.to_csv(\"e:/lab/RoboQuant/pylab/data/ord_label.csv\")\r\n", "label_df"], "outputs": [{"output_type": "execute_result", "data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ord_id</th>\n", "      <th>instrument</th>\n", "      <th>datetime</th>\n", "      <th>direct</th>\n", "      <th>label</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>00210820220644059</td>\n", "      <td>A2111.DC</td>\n", "      <td>20210820 22:06:44</td>\n", "      <td>S</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>00210913110854037</td>\n", "      <td>A2111.DC</td>\n", "      <td>20210913 11:08:54</td>\n", "      <td>L</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>00210913210530061</td>\n", "      <td>A2111.DC</td>\n", "      <td>20210913 21:05:30</td>\n", "      <td>S</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>00210913214812119</td>\n", "      <td>A2111.DC</td>\n", "      <td>20210913 21:48:12</td>\n", "      <td>S</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>00210914104351299</td>\n", "      <td>A2111.DC</td>\n", "      <td>20210914 10:43:51</td>\n", "      <td>S</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>551</th>\n", "      <td>00210916134057405</td>\n", "      <td>Y2201.DC</td>\n", "      <td>20210916 13:40:57</td>\n", "      <td>S</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>552</th>\n", "      <td>00210917104104275</td>\n", "      <td>Y2201.DC</td>\n", "      <td>20210917 10:41:04</td>\n", "      <td>S</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>553</th>\n", "      <td>00210917112844421</td>\n", "      <td>Y2201.DC</td>\n", "      <td>20210917 11:28:44</td>\n", "      <td>L</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>554</th>\n", "      <td>00210922090913267</td>\n", "      <td>Y2201.DC</td>\n", "      <td>20210922 09:09:13</td>\n", "      <td>L</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>555</th>\n", "      <td>00210922101346071</td>\n", "      <td>Y2201.DC</td>\n", "      <td>20210922 10:13:46</td>\n", "      <td>L</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>556 rows × 5 columns</p>\n", "</div>"], "text/plain": ["                ord_id instrument           datetime direct  label\n", "0    00210820220644059   A2111.DC  20210820 22:06:44      S      0\n", "1    00210913110854037   A2111.DC  20210913 11:08:54      L      0\n", "2    00210913210530061   A2111.DC  20210913 21:05:30      S      0\n", "3    00210913214812119   A2111.DC  20210913 21:48:12      S      1\n", "4    00210914104351299   A2111.DC  20210914 10:43:51      S      0\n", "..                 ...        ...                ...    ...    ...\n", "551  00210916134057405   Y2201.DC  20210916 13:40:57      S      1\n", "552  00210917104104275   Y2201.DC  20210917 10:41:04      S      1\n", "553  00210917112844421   Y2201.DC  20210917 11:28:44      L      0\n", "554  00210922090913267   Y2201.DC  20210922 09:09:13      L      0\n", "555  00210922101346071   Y2201.DC  20210922 10:13:46      L      1\n", "\n", "[556 rows x 5 columns]"]}, "metadata": {}, "execution_count": 7}], "metadata": {}}, {"cell_type": "code", "execution_count": 8, "source": ["factor_df = pd.merge(open_df, label_df,how='inner', on='ord_id') # right\r\n", "factor_df"], "outputs": [{"output_type": "execute_result", "data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ord_id</th>\n", "      <th>lf_RSI_1</th>\n", "      <th>lf_RSI_2</th>\n", "      <th>lf_LR_SLOPE_FAST_1</th>\n", "      <th>lf_LR_SLOPE_FAST_2</th>\n", "      <th>lf_LR_SLOPE_MIDD_1</th>\n", "      <th>lf_LR_SLOPE_MIDD_2</th>\n", "      <th>lf_LR_SLOPE_SLOW_1</th>\n", "      <th>lf_LR_SLOPE_SLOW_2</th>\n", "      <th>lf_LR_SLOPE_FAST_THRESHOLD</th>\n", "      <th>...</th>\n", "      <th>FAST_QH_RSI_PREV</th>\n", "      <th>LONG_RANGE</th>\n", "      <th>SHORT_RANGE</th>\n", "      <th>SLOW_QH_RSI</th>\n", "      <th>SLOW_QH_RSI_PREV</th>\n", "      <th>STDDEV_RNG</th>\n", "      <th>instrument</th>\n", "      <th>datetime</th>\n", "      <th>direct</th>\n", "      <th>label</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>00210922090006095</td>\n", "      <td>42.198003</td>\n", "      <td>43.185837</td>\n", "      <td>-6.7</td>\n", "      <td>6.1</td>\n", "      <td>-2.096970</td>\n", "      <td>0.400000</td>\n", "      <td>-5.757692</td>\n", "      <td>-6.750769</td>\n", "      <td>17.134648</td>\n", "      <td>...</td>\n", "      <td>-1.606082</td>\n", "      <td>41.0</td>\n", "      <td>3.74</td>\n", "      <td>0.766325</td>\n", "      <td>0.766325</td>\n", "      <td>1.232618</td>\n", "      <td>CS2111.DC</td>\n", "      <td>20210922 09:00:06</td>\n", "      <td>L</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>00210922090015109</td>\n", "      <td>49.171182</td>\n", "      <td>49.047868</td>\n", "      <td>16.6</td>\n", "      <td>-12.6</td>\n", "      <td>12.618182</td>\n", "      <td>6.933333</td>\n", "      <td>-1.192308</td>\n", "      <td>-0.221538</td>\n", "      <td>32.259578</td>\n", "      <td>...</td>\n", "      <td>-1.606082</td>\n", "      <td>80.0</td>\n", "      <td>8.00</td>\n", "      <td>0.766325</td>\n", "      <td>0.766325</td>\n", "      <td>1.095635</td>\n", "      <td>BU2112.SC</td>\n", "      <td>20210922 09:00:15</td>\n", "      <td>L</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>00210922090045119</td>\n", "      <td>77.789003</td>\n", "      <td>78.640329</td>\n", "      <td>200.4</td>\n", "      <td>302.2</td>\n", "      <td>142.030303</td>\n", "      <td>153.951515</td>\n", "      <td>137.015385</td>\n", "      <td>135.775385</td>\n", "      <td>144.101446</td>\n", "      <td>...</td>\n", "      <td>-1.606082</td>\n", "      <td>351.0</td>\n", "      <td>61.00</td>\n", "      <td>0.766325</td>\n", "      <td>0.766325</td>\n", "      <td>1.461996</td>\n", "      <td>SF2201.ZC</td>\n", "      <td>20210922 09:00:45</td>\n", "      <td>S</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>00210922090045123</td>\n", "      <td>58.708151</td>\n", "      <td>58.803915</td>\n", "      <td>-93.6</td>\n", "      <td>-53.0</td>\n", "      <td>43.793939</td>\n", "      <td>20.975758</td>\n", "      <td>41.997692</td>\n", "      <td>42.429231</td>\n", "      <td>61.884091</td>\n", "      <td>...</td>\n", "      <td>-1.606082</td>\n", "      <td>176.0</td>\n", "      <td>26.00</td>\n", "      <td>0.766325</td>\n", "      <td>0.766325</td>\n", "      <td>1.429672</td>\n", "      <td>L2201.DC</td>\n", "      <td>20210922 09:00:45</td>\n", "      <td>S</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>00210922090053127</td>\n", "      <td>58.140772</td>\n", "      <td>59.828301</td>\n", "      <td>-34.4</td>\n", "      <td>-40.1</td>\n", "      <td>10.654545</td>\n", "      <td>20.787879</td>\n", "      <td>48.699231</td>\n", "      <td>45.233846</td>\n", "      <td>85.413369</td>\n", "      <td>...</td>\n", "      <td>-1.606082</td>\n", "      <td>229.0</td>\n", "      <td>31.00</td>\n", "      <td>0.766325</td>\n", "      <td>0.766325</td>\n", "      <td>1.300617</td>\n", "      <td>V2201.DC</td>\n", "      <td>20210922 09:00:53</td>\n", "      <td>S</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>540</th>\n", "      <td>00210917142830581</td>\n", "      <td>24.969521</td>\n", "      <td>24.225190</td>\n", "      <td>-26.8</td>\n", "      <td>-23.1</td>\n", "      <td>-21.375758</td>\n", "      <td>-20.618182</td>\n", "      <td>-12.485769</td>\n", "      <td>-11.975385</td>\n", "      <td>19.989133</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>I2201.DC</td>\n", "      <td>20210917 14:28:30</td>\n", "      <td>L</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>541</th>\n", "      <td>00210917143922599</td>\n", "      <td>46.989491</td>\n", "      <td>46.716832</td>\n", "      <td>2.9</td>\n", "      <td>65.3</td>\n", "      <td>-100.266667</td>\n", "      <td>-81.181818</td>\n", "      <td>-15.990769</td>\n", "      <td>-26.118462</td>\n", "      <td>115.926286</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>RU2201.SC</td>\n", "      <td>20210917 14:39:22</td>\n", "      <td>S</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>542</th>\n", "      <td>00210917144042601</td>\n", "      <td>56.239098</td>\n", "      <td>55.059427</td>\n", "      <td>-51.8</td>\n", "      <td>-100.9</td>\n", "      <td>58.739394</td>\n", "      <td>43.321212</td>\n", "      <td>27.765385</td>\n", "      <td>28.394615</td>\n", "      <td>61.383238</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>PP2201.DC</td>\n", "      <td>20210917 14:40:42</td>\n", "      <td>S</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>543</th>\n", "      <td>00210917144220609</td>\n", "      <td>34.408926</td>\n", "      <td>37.126332</td>\n", "      <td>-53.1</td>\n", "      <td>-7.8</td>\n", "      <td>-58.406061</td>\n", "      <td>-55.836364</td>\n", "      <td>-18.777692</td>\n", "      <td>-19.611538</td>\n", "      <td>41.141318</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>FG2201.ZC</td>\n", "      <td>20210917 14:42:20</td>\n", "      <td>S</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>544</th>\n", "      <td>00210917144703613</td>\n", "      <td>42.646335</td>\n", "      <td>44.014354</td>\n", "      <td>-6.9</td>\n", "      <td>6.6</td>\n", "      <td>-2.139394</td>\n", "      <td>0.581818</td>\n", "      <td>-6.969231</td>\n", "      <td>-7.773846</td>\n", "      <td>16.807664</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>CS2111.DC</td>\n", "      <td>20210917 14:47:03</td>\n", "      <td>S</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>545 rows × 94 columns</p>\n", "</div>"], "text/plain": ["                ord_id   lf_RSI_1   lf_RSI_2  lf_LR_SLOPE_FAST_1  \\\n", "0    00210922090006095  42.198003  43.185837                -6.7   \n", "1    00210922090015109  49.171182  49.047868                16.6   \n", "2    00210922090045119  77.789003  78.640329               200.4   \n", "3    00210922090045123  58.708151  58.803915               -93.6   \n", "4    00210922090053127  58.140772  59.828301               -34.4   \n", "..                 ...        ...        ...                 ...   \n", "540  00210917142830581  24.969521  24.225190               -26.8   \n", "541  00210917143922599  46.989491  46.716832                 2.9   \n", "542  00210917144042601  56.239098  55.059427               -51.8   \n", "543  00210917144220609  34.408926  37.126332               -53.1   \n", "544  00210917144703613  42.646335  44.014354                -6.9   \n", "\n", "     lf_LR_SLOPE_FAST_2  lf_LR_SLOPE_MIDD_1  lf_LR_SLOPE_MIDD_2  \\\n", "0                   6.1           -2.096970            0.400000   \n", "1                 -12.6           12.618182            6.933333   \n", "2                 302.2          142.030303          153.951515   \n", "3                 -53.0           43.793939           20.975758   \n", "4                 -40.1           10.654545           20.787879   \n", "..                  ...                 ...                 ...   \n", "540               -23.1          -21.375758          -20.618182   \n", "541                65.3         -100.266667          -81.181818   \n", "542              -100.9           58.739394           43.321212   \n", "543                -7.8          -58.406061          -55.836364   \n", "544                 6.6           -2.139394            0.581818   \n", "\n", "     lf_LR_SLOPE_SLOW_1  lf_LR_SLOPE_SLOW_2  lf_LR_SLOPE_FAST_THRESHOLD  ...  \\\n", "0             -5.757692           -6.750769                   17.134648  ...   \n", "1             -1.192308           -0.221538                   32.259578  ...   \n", "2            137.015385          135.775385                  144.101446  ...   \n", "3             41.997692           42.429231                   61.884091  ...   \n", "4             48.699231           45.233846                   85.413369  ...   \n", "..                  ...                 ...                         ...  ...   \n", "540          -12.485769          -11.975385                   19.989133  ...   \n", "541          -15.990769          -26.118462                  115.926286  ...   \n", "542           27.765385           28.394615                   61.383238  ...   \n", "543          -18.777692          -19.611538                   41.141318  ...   \n", "544           -6.969231           -7.773846                   16.807664  ...   \n", "\n", "     FAST_QH_RSI_PREV  LONG_RANGE  SHORT_RANGE  SLOW_QH_RSI  SLOW_QH_RSI_PREV  \\\n", "0           -1.606082        41.0         3.74     0.766325          0.766325   \n", "1           -1.606082        80.0         8.00     0.766325          0.766325   \n", "2           -1.606082       351.0        61.00     0.766325          0.766325   \n", "3           -1.606082       176.0        26.00     0.766325          0.766325   \n", "4           -1.606082       229.0        31.00     0.766325          0.766325   \n", "..                ...         ...          ...          ...               ...   \n", "540               NaN         NaN          NaN          NaN               NaN   \n", "541               NaN         NaN          NaN          NaN               NaN   \n", "542               NaN         NaN          NaN          NaN               NaN   \n", "543               NaN         NaN          NaN          NaN               NaN   \n", "544               NaN         NaN          NaN          NaN               NaN   \n", "\n", "     STDDEV_RNG  instrument           datetime  direct  label  \n", "0      1.232618   CS2111.DC  20210922 09:00:06       L      1  \n", "1      1.095635   BU2112.SC  20210922 09:00:15       L      0  \n", "2      1.461996   SF2201.ZC  20210922 09:00:45       S      1  \n", "3      1.429672    L2201.DC  20210922 09:00:45       S      1  \n", "4      1.300617    V2201.DC  20210922 09:00:53       S      1  \n", "..          ...         ...                ...     ...    ...  \n", "540         NaN    I2201.DC  20210917 14:28:30       L      0  \n", "541         NaN   RU2201.SC  20210917 14:39:22       S      1  \n", "542         NaN   PP2201.DC  20210917 14:40:42       S      0  \n", "543         NaN   FG2201.ZC  20210917 14:42:20       S      1  \n", "544         NaN   CS2111.DC  20210917 14:47:03       S      1  \n", "\n", "[545 rows x 94 columns]"]}, "metadata": {}, "execution_count": 8}], "metadata": {}}, {"cell_type": "markdown", "source": ["# 删除列值全为0的列\r\n", "factor_df = factor_df.loc[:, (factor_df != 0).any(axis=0)]\r\n", "factor_df"], "metadata": {}}, {"cell_type": "code", "execution_count": 9, "source": ["factor_df = factor_df.drop(factor_df[factor_df.lf_TREND_HLR <= 0.01].index)"], "outputs": [], "metadata": {}}, {"cell_type": "code", "execution_count": 10, "source": ["factor_df.to_csv(\"e:/lab/RoboQuant/pylab/data/factors.csv\")"], "outputs": [], "metadata": {}}, {"cell_type": "code", "execution_count": 11, "source": ["[c for c in factor_df.columns if c != 'label']"], "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["['ord_id',\n", " 'lf_RSI_1',\n", " 'lf_RSI_2',\n", " 'lf_LR_SLOPE_FAST_1',\n", " 'lf_LR_SLOPE_FAST_2',\n", " 'lf_LR_SLOPE_MIDD_1',\n", " 'lf_LR_SLOPE_MIDD_2',\n", " 'lf_LR_SLOPE_SLOW_1',\n", " 'lf_LR_SLOPE_SLOW_2',\n", " 'lf_LR_SLOPE_FAST_THRESHOLD',\n", " 'lf_LR_SLOPE_SLOW_THRESHOLD',\n", " 'lf_SQUEEZE_ZERO_BARS',\n", " 'lf_SQUEEZE_GAP_1',\n", " 'lf_SQUEEZE_GAP_2',\n", " 'lf_SQUEEZE_GAP_FAST_1',\n", " 'lf_SQUEEZE_GAP_FAST_2',\n", " 'lf_SQUEEZE_GAP_SLOW_1',\n", " 'lf_SQUEEZE_GAP_SLOW_2',\n", " 'lf_SQUEEZE_GAP_THRESHOLD',\n", " 'lf_SQUEEZE_NARROW_BARS',\n", " 'lf_BAND_POSITION',\n", " 'lf_BAND_WIDTH',\n", " 'lf_BAND_EXPAND',\n", " 'lf_BAND_GRADIENT_1',\n", " 'lf_BAND_GRADIENT_2',\n", " 'lf_BAND_GRADIENT_THRESHOLD',\n", " 'lf_BAND_GAP',\n", " 'lf_TL_FAST_1',\n", " 'lf_TL_FAST_2',\n", " 'lf_TL_SLOW_1',\n", " 'lf_TL_SLOW_2',\n", " 'lf_TL_THRESHOLD',\n", " 'lf_TREND_VALUE_1',\n", " 'lf_TREND_VALUE_2',\n", " 'lf_TREND_BARS_1',\n", " 'lf_TREND_BARS_2',\n", " 'lf_TREND_INBARS_1',\n", " 'lf_TREND_INBARS_2',\n", " 'lf_TREND_INPOSR_1',\n", " 'lf_TREND_INPOSR_2',\n", " 'lf_TREND_HLR',\n", " 'lf_TREND_LEVEL',\n", " 'sf_RSI_1',\n", " 'sf_RSI_2',\n", " 'sf_LR_SLOPE_FAST_1',\n", " 'sf_LR_SLOPE_FAST_2',\n", " 'sf_LR_SLOPE_MIDD_1',\n", " 'sf_LR_SLOPE_MIDD_2',\n", " 'sf_LR_SLOPE_SLOW_1',\n", " 'sf_LR_SLOPE_SLOW_2',\n", " 'sf_LR_SLOPE_FAST_THRESHOLD',\n", " 'sf_LR_SLOPE_SLOW_THRESHOLD',\n", " 'sf_SQUEEZE_ZERO_BARS',\n", " 'sf_SQUEEZE_GAP_1',\n", " 'sf_SQUEEZE_GAP_2',\n", " 'sf_SQUEEZE_GAP_FAST_1',\n", " 'sf_SQUEEZE_GAP_FAST_2',\n", " 'sf_SQUEEZE_GAP_SLOW_1',\n", " 'sf_SQUEEZE_GAP_SLOW_2',\n", " 'sf_SQUEEZE_GAP_THRESHOLD',\n", " 'sf_SQUEEZE_NARROW_BARS',\n", " 'sf_BAND_POSITION',\n", " 'sf_BAND_WIDTH',\n", " 'sf_BAND_EXPAND',\n", " 'sf_BAND_GRADIENT_1',\n", " 'sf_BAND_GRADIENT_2',\n", " 'sf_BAND_GRADIENT_THRESHOLD',\n", " 'sf_BAND_GAP',\n", " 'sf_TL_FAST_1',\n", " 'sf_TL_FAST_2',\n", " 'sf_TL_SLOW_1',\n", " 'sf_TL_SLOW_2',\n", " 'sf_TL_THRESHOLD',\n", " 'sf_TREND_VALUE_1',\n", " 'sf_TREND_VALUE_2',\n", " 'sf_TREND_BARS_1',\n", " 'sf_TREND_BARS_2',\n", " 'sf_TREND_INBARS_1',\n", " 'sf_TREND_INBARS_2',\n", " 'sf_TREND_INPOSR_1',\n", " 'sf_TREND_INPOSR_2',\n", " 'sf_TREND_HLR',\n", " 'sf_TREND_LEVEL',\n", " 'FAST_QH_RSI',\n", " 'FAST_QH_RSI_PREV',\n", " 'LONG_RANGE',\n", " 'SHORT_RANGE',\n", " 'SLOW_QH_RSI',\n", " 'SLOW_QH_RSI_PREV',\n", " 'STDDEV_RNG',\n", " 'instrument',\n", " 'datetime',\n", " 'direct']"]}, "metadata": {}, "execution_count": 11}], "metadata": {}}, {"cell_type": "code", "execution_count": 12, "source": ["for name, group in factor_df.groupby(['direct']):\r\n", "    if name == 'L':\r\n", "        factor_long_df = group\r\n", "    if name == 'S':\r\n", "        factor_short_df = group\r\n", "\r\n", "factor_long_df.head()"], "outputs": [{"output_type": "execute_result", "data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ord_id</th>\n", "      <th>lf_RSI_1</th>\n", "      <th>lf_RSI_2</th>\n", "      <th>lf_LR_SLOPE_FAST_1</th>\n", "      <th>lf_LR_SLOPE_FAST_2</th>\n", "      <th>lf_LR_SLOPE_MIDD_1</th>\n", "      <th>lf_LR_SLOPE_MIDD_2</th>\n", "      <th>lf_LR_SLOPE_SLOW_1</th>\n", "      <th>lf_LR_SLOPE_SLOW_2</th>\n", "      <th>lf_LR_SLOPE_FAST_THRESHOLD</th>\n", "      <th>...</th>\n", "      <th>FAST_QH_RSI_PREV</th>\n", "      <th>LONG_RANGE</th>\n", "      <th>SHORT_RANGE</th>\n", "      <th>SLOW_QH_RSI</th>\n", "      <th>SLOW_QH_RSI_PREV</th>\n", "      <th>STDDEV_RNG</th>\n", "      <th>instrument</th>\n", "      <th>datetime</th>\n", "      <th>direct</th>\n", "      <th>label</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>00210922090006095</td>\n", "      <td>42.198003</td>\n", "      <td>43.185837</td>\n", "      <td>-6.7</td>\n", "      <td>6.1</td>\n", "      <td>-2.096970</td>\n", "      <td>0.400000</td>\n", "      <td>-5.757692</td>\n", "      <td>-6.750769</td>\n", "      <td>17.134648</td>\n", "      <td>...</td>\n", "      <td>-1.606082</td>\n", "      <td>41.0</td>\n", "      <td>3.74</td>\n", "      <td>0.766325</td>\n", "      <td>0.766325</td>\n", "      <td>1.232618</td>\n", "      <td>CS2111.DC</td>\n", "      <td>20210922 09:00:06</td>\n", "      <td>L</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>00210922090015109</td>\n", "      <td>49.171182</td>\n", "      <td>49.047868</td>\n", "      <td>16.6</td>\n", "      <td>-12.6</td>\n", "      <td>12.618182</td>\n", "      <td>6.933333</td>\n", "      <td>-1.192308</td>\n", "      <td>-0.221538</td>\n", "      <td>32.259578</td>\n", "      <td>...</td>\n", "      <td>-1.606082</td>\n", "      <td>80.0</td>\n", "      <td>8.00</td>\n", "      <td>0.766325</td>\n", "      <td>0.766325</td>\n", "      <td>1.095635</td>\n", "      <td>BU2112.SC</td>\n", "      <td>20210922 09:00:15</td>\n", "      <td>L</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>00210922090500167</td>\n", "      <td>51.181740</td>\n", "      <td>52.669278</td>\n", "      <td>44.6</td>\n", "      <td>58.4</td>\n", "      <td>32.715152</td>\n", "      <td>39.951515</td>\n", "      <td>-23.562308</td>\n", "      <td>-21.250000</td>\n", "      <td>52.785925</td>\n", "      <td>...</td>\n", "      <td>-1.606082</td>\n", "      <td>122.0</td>\n", "      <td>15.00</td>\n", "      <td>1.517871</td>\n", "      <td>0.766325</td>\n", "      <td>1.366270</td>\n", "      <td>AP2201.ZC</td>\n", "      <td>20210922 09:05:00</td>\n", "      <td>L</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>00210922090502169</td>\n", "      <td>69.478145</td>\n", "      <td>67.232473</td>\n", "      <td>67.0</td>\n", "      <td>46.6</td>\n", "      <td>27.812121</td>\n", "      <td>31.630303</td>\n", "      <td>9.746154</td>\n", "      <td>12.932308</td>\n", "      <td>29.977054</td>\n", "      <td>...</td>\n", "      <td>-1.606082</td>\n", "      <td>76.0</td>\n", "      <td>10.00</td>\n", "      <td>1.517871</td>\n", "      <td>0.766325</td>\n", "      <td>1.428389</td>\n", "      <td>UR2201.ZC</td>\n", "      <td>20210922 09:05:02</td>\n", "      <td>L</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>00210922090503177</td>\n", "      <td>54.342023</td>\n", "      <td>57.438281</td>\n", "      <td>-68.7</td>\n", "      <td>-88.4</td>\n", "      <td>55.327273</td>\n", "      <td>46.290909</td>\n", "      <td>27.682308</td>\n", "      <td>30.622308</td>\n", "      <td>63.619696</td>\n", "      <td>...</td>\n", "      <td>-1.606082</td>\n", "      <td>170.0</td>\n", "      <td>22.00</td>\n", "      <td>1.517871</td>\n", "      <td>0.766325</td>\n", "      <td>1.449321</td>\n", "      <td>PP2201.DC</td>\n", "      <td>20210922 09:05:03</td>\n", "      <td>L</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 94 columns</p>\n", "</div>"], "text/plain": ["              ord_id   lf_RSI_1   lf_RSI_2  lf_LR_SLOPE_FAST_1  \\\n", "0  00210922090006095  42.198003  43.185837                -6.7   \n", "1  00210922090015109  49.171182  49.047868                16.6   \n", "6  00210922090500167  51.181740  52.669278                44.6   \n", "7  00210922090502169  69.478145  67.232473                67.0   \n", "9  00210922090503177  54.342023  57.438281               -68.7   \n", "\n", "   lf_LR_SLOPE_FAST_2  lf_LR_SLOPE_MIDD_1  lf_LR_SLOPE_MIDD_2  \\\n", "0                 6.1           -2.096970            0.400000   \n", "1               -12.6           12.618182            6.933333   \n", "6                58.4           32.715152           39.951515   \n", "7                46.6           27.812121           31.630303   \n", "9               -88.4           55.327273           46.290909   \n", "\n", "   lf_LR_SLOPE_SLOW_1  lf_LR_SLOPE_SLOW_2  lf_LR_SLOPE_FAST_THRESHOLD  ...  \\\n", "0           -5.757692           -6.750769                   17.134648  ...   \n", "1           -1.192308           -0.221538                   32.259578  ...   \n", "6          -23.562308          -21.250000                   52.785925  ...   \n", "7            9.746154           12.932308                   29.977054  ...   \n", "9           27.682308           30.622308                   63.619696  ...   \n", "\n", "   FAST_QH_RSI_PREV  LONG_RANGE  SHORT_RANGE  SLOW_QH_RSI  SLOW_QH_RSI_PREV  \\\n", "0         -1.606082        41.0         3.74     0.766325          0.766325   \n", "1         -1.606082        80.0         8.00     0.766325          0.766325   \n", "6         -1.606082       122.0        15.00     1.517871          0.766325   \n", "7         -1.606082        76.0        10.00     1.517871          0.766325   \n", "9         -1.606082       170.0        22.00     1.517871          0.766325   \n", "\n", "   STDDEV_RNG  instrument           datetime  direct  label  \n", "0    1.232618   CS2111.DC  20210922 09:00:06       L      1  \n", "1    1.095635   BU2112.SC  20210922 09:00:15       L      0  \n", "6    1.366270   AP2201.ZC  20210922 09:05:00       L      0  \n", "7    1.428389   UR2201.ZC  20210922 09:05:02       L      1  \n", "9    1.449321   PP2201.DC  20210922 09:05:03       L      1  \n", "\n", "[5 rows x 94 columns]"]}, "metadata": {}, "execution_count": 12}], "metadata": {}}, {"cell_type": "code", "execution_count": 13, "source": ["factor_long_df.drop(['ord_id', 'instrument', 'datetime', 'direct'], axis=1, inplace=True)\r\n", "factor_long_df"], "outputs": [{"output_type": "execute_result", "data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>lf_RSI_1</th>\n", "      <th>lf_RSI_2</th>\n", "      <th>lf_LR_SLOPE_FAST_1</th>\n", "      <th>lf_LR_SLOPE_FAST_2</th>\n", "      <th>lf_LR_SLOPE_MIDD_1</th>\n", "      <th>lf_LR_SLOPE_MIDD_2</th>\n", "      <th>lf_LR_SLOPE_SLOW_1</th>\n", "      <th>lf_LR_SLOPE_SLOW_2</th>\n", "      <th>lf_LR_SLOPE_FAST_THRESHOLD</th>\n", "      <th>lf_LR_SLOPE_SLOW_THRESHOLD</th>\n", "      <th>...</th>\n", "      <th>sf_TREND_HLR</th>\n", "      <th>sf_TREND_LEVEL</th>\n", "      <th>FAST_QH_RSI</th>\n", "      <th>FAST_QH_RSI_PREV</th>\n", "      <th>LONG_RANGE</th>\n", "      <th>SHORT_RANGE</th>\n", "      <th>SLOW_QH_RSI</th>\n", "      <th>SLOW_QH_RSI_PREV</th>\n", "      <th>STDDEV_RNG</th>\n", "      <th>label</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>42.198003</td>\n", "      <td>43.185837</td>\n", "      <td>-6.7</td>\n", "      <td>6.1</td>\n", "      <td>-2.096970</td>\n", "      <td>0.400000</td>\n", "      <td>-5.757692</td>\n", "      <td>-6.750769</td>\n", "      <td>17.134648</td>\n", "      <td>6.741661</td>\n", "      <td>...</td>\n", "      <td>5.347594</td>\n", "      <td>0.0</td>\n", "      <td>-1.606082</td>\n", "      <td>-1.606082</td>\n", "      <td>41.0</td>\n", "      <td>3.74</td>\n", "      <td>0.766325</td>\n", "      <td>0.766325</td>\n", "      <td>1.232618</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>49.171182</td>\n", "      <td>49.047868</td>\n", "      <td>16.6</td>\n", "      <td>-12.6</td>\n", "      <td>12.618182</td>\n", "      <td>6.933333</td>\n", "      <td>-1.192308</td>\n", "      <td>-0.221538</td>\n", "      <td>32.259578</td>\n", "      <td>12.499770</td>\n", "      <td>...</td>\n", "      <td>13.500000</td>\n", "      <td>0.0</td>\n", "      <td>-1.606082</td>\n", "      <td>-1.606082</td>\n", "      <td>80.0</td>\n", "      <td>8.00</td>\n", "      <td>0.766325</td>\n", "      <td>0.766325</td>\n", "      <td>1.095635</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>51.181740</td>\n", "      <td>52.669278</td>\n", "      <td>44.6</td>\n", "      <td>58.4</td>\n", "      <td>32.715152</td>\n", "      <td>39.951515</td>\n", "      <td>-23.562308</td>\n", "      <td>-21.250000</td>\n", "      <td>52.785925</td>\n", "      <td>22.688217</td>\n", "      <td>...</td>\n", "      <td>7.666667</td>\n", "      <td>0.0</td>\n", "      <td>0.920621</td>\n", "      <td>-1.606082</td>\n", "      <td>122.0</td>\n", "      <td>15.00</td>\n", "      <td>1.517871</td>\n", "      <td>0.766325</td>\n", "      <td>1.366270</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>69.478145</td>\n", "      <td>67.232473</td>\n", "      <td>67.0</td>\n", "      <td>46.6</td>\n", "      <td>27.812121</td>\n", "      <td>31.630303</td>\n", "      <td>9.746154</td>\n", "      <td>12.932308</td>\n", "      <td>29.977054</td>\n", "      <td>7.870420</td>\n", "      <td>...</td>\n", "      <td>7.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.920621</td>\n", "      <td>-1.606082</td>\n", "      <td>76.0</td>\n", "      <td>10.00</td>\n", "      <td>1.517871</td>\n", "      <td>0.766325</td>\n", "      <td>1.428389</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>54.342023</td>\n", "      <td>57.438281</td>\n", "      <td>-68.7</td>\n", "      <td>-88.4</td>\n", "      <td>55.327273</td>\n", "      <td>46.290909</td>\n", "      <td>27.682308</td>\n", "      <td>30.622308</td>\n", "      <td>63.619696</td>\n", "      <td>29.322697</td>\n", "      <td>...</td>\n", "      <td>6.545455</td>\n", "      <td>2.0</td>\n", "      <td>0.920621</td>\n", "      <td>-1.606082</td>\n", "      <td>170.0</td>\n", "      <td>22.00</td>\n", "      <td>1.517871</td>\n", "      <td>0.766325</td>\n", "      <td>1.449321</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>536</th>\n", "      <td>56.627324</td>\n", "      <td>56.702467</td>\n", "      <td>-66.0</td>\n", "      <td>-111.4</td>\n", "      <td>54.715152</td>\n", "      <td>38.030303</td>\n", "      <td>40.166923</td>\n", "      <td>40.649231</td>\n", "      <td>60.537510</td>\n", "      <td>26.263298</td>\n", "      <td>...</td>\n", "      <td>17.200000</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>537</th>\n", "      <td>60.674337</td>\n", "      <td>61.735032</td>\n", "      <td>-2.7</td>\n", "      <td>-13.2</td>\n", "      <td>-3.127273</td>\n", "      <td>-10.096970</td>\n", "      <td>18.070769</td>\n", "      <td>16.630000</td>\n", "      <td>31.821490</td>\n", "      <td>7.490356</td>\n", "      <td>...</td>\n", "      <td>6.083333</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>538</th>\n", "      <td>57.700700</td>\n", "      <td>55.363646</td>\n", "      <td>35.3</td>\n", "      <td>-14.2</td>\n", "      <td>49.775758</td>\n", "      <td>33.236364</td>\n", "      <td>8.932308</td>\n", "      <td>14.486154</td>\n", "      <td>54.215475</td>\n", "      <td>26.132487</td>\n", "      <td>...</td>\n", "      <td>11.833333</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>539</th>\n", "      <td>52.955601</td>\n", "      <td>50.678947</td>\n", "      <td>5.8</td>\n", "      <td>-3.6</td>\n", "      <td>8.036364</td>\n", "      <td>-3.315152</td>\n", "      <td>16.413077</td>\n", "      <td>16.201538</td>\n", "      <td>61.862214</td>\n", "      <td>28.843515</td>\n", "      <td>...</td>\n", "      <td>5.500000</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>540</th>\n", "      <td>24.969521</td>\n", "      <td>24.225190</td>\n", "      <td>-26.8</td>\n", "      <td>-23.1</td>\n", "      <td>-21.375758</td>\n", "      <td>-20.618182</td>\n", "      <td>-12.485769</td>\n", "      <td>-11.975385</td>\n", "      <td>19.989133</td>\n", "      <td>8.589806</td>\n", "      <td>...</td>\n", "      <td>6.043956</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>280 rows × 90 columns</p>\n", "</div>"], "text/plain": ["      lf_RSI_1   lf_RSI_2  lf_LR_SLOPE_FAST_1  lf_LR_SLOPE_FAST_2  \\\n", "0    42.198003  43.185837                -6.7                 6.1   \n", "1    49.171182  49.047868                16.6               -12.6   \n", "6    51.181740  52.669278                44.6                58.4   \n", "7    69.478145  67.232473                67.0                46.6   \n", "9    54.342023  57.438281               -68.7               -88.4   \n", "..         ...        ...                 ...                 ...   \n", "536  56.627324  56.702467               -66.0              -111.4   \n", "537  60.674337  61.735032                -2.7               -13.2   \n", "538  57.700700  55.363646                35.3               -14.2   \n", "539  52.955601  50.678947                 5.8                -3.6   \n", "540  24.969521  24.225190               -26.8               -23.1   \n", "\n", "     lf_LR_SLOPE_MIDD_1  lf_LR_SLOPE_MIDD_2  lf_LR_SLOPE_SLOW_1  \\\n", "0             -2.096970            0.400000           -5.757692   \n", "1             12.618182            6.933333           -1.192308   \n", "6             32.715152           39.951515          -23.562308   \n", "7             27.812121           31.630303            9.746154   \n", "9             55.327273           46.290909           27.682308   \n", "..                  ...                 ...                 ...   \n", "536           54.715152           38.030303           40.166923   \n", "537           -3.127273          -10.096970           18.070769   \n", "538           49.775758           33.236364            8.932308   \n", "539            8.036364           -3.315152           16.413077   \n", "540          -21.375758          -20.618182          -12.485769   \n", "\n", "     lf_LR_SLOPE_SLOW_2  lf_LR_SLOPE_FAST_THRESHOLD  \\\n", "0             -6.750769                   17.134648   \n", "1             -0.221538                   32.259578   \n", "6            -21.250000                   52.785925   \n", "7             12.932308                   29.977054   \n", "9             30.622308                   63.619696   \n", "..                  ...                         ...   \n", "536           40.649231                   60.537510   \n", "537           16.630000                   31.821490   \n", "538           14.486154                   54.215475   \n", "539           16.201538                   61.862214   \n", "540          -11.975385                   19.989133   \n", "\n", "     lf_LR_SLOPE_SLOW_THRESHOLD  ...  sf_TREND_HLR  sf_TREND_LEVEL  \\\n", "0                      6.741661  ...      5.347594             0.0   \n", "1                     12.499770  ...     13.500000             0.0   \n", "6                     22.688217  ...      7.666667             0.0   \n", "7                      7.870420  ...      7.000000             0.0   \n", "9                     29.322697  ...      6.545455             2.0   \n", "..                          ...  ...           ...             ...   \n", "536                   26.263298  ...     17.200000             0.0   \n", "537                    7.490356  ...      6.083333             0.0   \n", "538                   26.132487  ...     11.833333             0.0   \n", "539                   28.843515  ...      5.500000             0.0   \n", "540                    8.589806  ...      6.043956             0.0   \n", "\n", "     FAST_QH_RSI  FAST_QH_RSI_PREV  LONG_RANGE  SHORT_RANGE  SLOW_QH_RSI  \\\n", "0      -1.606082         -1.606082        41.0         3.74     0.766325   \n", "1      -1.606082         -1.606082        80.0         8.00     0.766325   \n", "6       0.920621         -1.606082       122.0        15.00     1.517871   \n", "7       0.920621         -1.606082        76.0        10.00     1.517871   \n", "9       0.920621         -1.606082       170.0        22.00     1.517871   \n", "..           ...               ...         ...          ...          ...   \n", "536          NaN               NaN         NaN          NaN          NaN   \n", "537          NaN               NaN         NaN          NaN          NaN   \n", "538          NaN               NaN         NaN          NaN          NaN   \n", "539          NaN               NaN         NaN          NaN          NaN   \n", "540          NaN               NaN         NaN          NaN          NaN   \n", "\n", "     SLOW_QH_RSI_PREV  STDDEV_RNG  label  \n", "0            0.766325    1.232618      1  \n", "1            0.766325    1.095635      0  \n", "6            0.766325    1.366270      0  \n", "7            0.766325    1.428389      1  \n", "9            0.766325    1.449321      1  \n", "..                ...         ...    ...  \n", "536               NaN         NaN      0  \n", "537               NaN         NaN      1  \n", "538               NaN         NaN      0  \n", "539               NaN         NaN      1  \n", "540               NaN         NaN      0  \n", "\n", "[280 rows x 90 columns]"]}, "metadata": {}, "execution_count": 13}], "metadata": {}}, {"cell_type": "code", "execution_count": 14, "source": ["factor_short_df.drop(['ord_id', 'instrument', 'datetime', 'direct'], axis=1, inplace=True)\r\n", "factor_short_df"], "outputs": [{"output_type": "execute_result", "data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>lf_RSI_1</th>\n", "      <th>lf_RSI_2</th>\n", "      <th>lf_LR_SLOPE_FAST_1</th>\n", "      <th>lf_LR_SLOPE_FAST_2</th>\n", "      <th>lf_LR_SLOPE_MIDD_1</th>\n", "      <th>lf_LR_SLOPE_MIDD_2</th>\n", "      <th>lf_LR_SLOPE_SLOW_1</th>\n", "      <th>lf_LR_SLOPE_SLOW_2</th>\n", "      <th>lf_LR_SLOPE_FAST_THRESHOLD</th>\n", "      <th>lf_LR_SLOPE_SLOW_THRESHOLD</th>\n", "      <th>...</th>\n", "      <th>sf_TREND_HLR</th>\n", "      <th>sf_TREND_LEVEL</th>\n", "      <th>FAST_QH_RSI</th>\n", "      <th>FAST_QH_RSI_PREV</th>\n", "      <th>LONG_RANGE</th>\n", "      <th>SHORT_RANGE</th>\n", "      <th>SLOW_QH_RSI</th>\n", "      <th>SLOW_QH_RSI_PREV</th>\n", "      <th>STDDEV_RNG</th>\n", "      <th>label</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>77.789003</td>\n", "      <td>78.640329</td>\n", "      <td>200.4</td>\n", "      <td>302.2</td>\n", "      <td>142.030303</td>\n", "      <td>153.951515</td>\n", "      <td>137.015385</td>\n", "      <td>135.775385</td>\n", "      <td>144.101446</td>\n", "      <td>52.750708</td>\n", "      <td>...</td>\n", "      <td>18.459016</td>\n", "      <td>0.0</td>\n", "      <td>-1.606082</td>\n", "      <td>-1.606082</td>\n", "      <td>351.0</td>\n", "      <td>61.0</td>\n", "      <td>0.766325</td>\n", "      <td>0.766325</td>\n", "      <td>1.461996</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>58.708151</td>\n", "      <td>58.803915</td>\n", "      <td>-93.6</td>\n", "      <td>-53.0</td>\n", "      <td>43.793939</td>\n", "      <td>20.975758</td>\n", "      <td>41.997692</td>\n", "      <td>42.429231</td>\n", "      <td>61.884091</td>\n", "      <td>27.088602</td>\n", "      <td>...</td>\n", "      <td>8.846154</td>\n", "      <td>0.0</td>\n", "      <td>-1.606082</td>\n", "      <td>-1.606082</td>\n", "      <td>176.0</td>\n", "      <td>26.0</td>\n", "      <td>0.766325</td>\n", "      <td>0.766325</td>\n", "      <td>1.429672</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>58.140772</td>\n", "      <td>59.828301</td>\n", "      <td>-34.4</td>\n", "      <td>-40.1</td>\n", "      <td>10.654545</td>\n", "      <td>20.787879</td>\n", "      <td>48.699231</td>\n", "      <td>45.233846</td>\n", "      <td>85.413369</td>\n", "      <td>29.009940</td>\n", "      <td>...</td>\n", "      <td>12.580645</td>\n", "      <td>0.0</td>\n", "      <td>-1.606082</td>\n", "      <td>-1.606082</td>\n", "      <td>229.0</td>\n", "      <td>31.0</td>\n", "      <td>0.766325</td>\n", "      <td>0.766325</td>\n", "      <td>1.300617</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>66.567302</td>\n", "      <td>68.784082</td>\n", "      <td>0.3</td>\n", "      <td>43.5</td>\n", "      <td>5.521212</td>\n", "      <td>15.303030</td>\n", "      <td>19.176154</td>\n", "      <td>19.130000</td>\n", "      <td>33.102468</td>\n", "      <td>7.808299</td>\n", "      <td>...</td>\n", "      <td>20.692308</td>\n", "      <td>0.0</td>\n", "      <td>2.320337</td>\n", "      <td>-1.606082</td>\n", "      <td>91.0</td>\n", "      <td>13.0</td>\n", "      <td>1.672801</td>\n", "      <td>0.766325</td>\n", "      <td>1.297279</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>42.458217</td>\n", "      <td>41.638966</td>\n", "      <td>43.5</td>\n", "      <td>-40.5</td>\n", "      <td>-59.569697</td>\n", "      <td>-37.563636</td>\n", "      <td>-38.450769</td>\n", "      <td>-50.829231</td>\n", "      <td>115.702259</td>\n", "      <td>43.841593</td>\n", "      <td>...</td>\n", "      <td>17.692308</td>\n", "      <td>-3.0</td>\n", "      <td>0.920621</td>\n", "      <td>-1.606082</td>\n", "      <td>268.0</td>\n", "      <td>26.0</td>\n", "      <td>1.517871</td>\n", "      <td>0.766325</td>\n", "      <td>1.291485</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>535</th>\n", "      <td>56.106968</td>\n", "      <td>55.194119</td>\n", "      <td>0.8</td>\n", "      <td>-35.0</td>\n", "      <td>5.551515</td>\n", "      <td>4.460606</td>\n", "      <td>20.351538</td>\n", "      <td>17.688462</td>\n", "      <td>29.758552</td>\n", "      <td>8.711441</td>\n", "      <td>...</td>\n", "      <td>9.416667</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>541</th>\n", "      <td>46.989491</td>\n", "      <td>46.716832</td>\n", "      <td>2.9</td>\n", "      <td>65.3</td>\n", "      <td>-100.266667</td>\n", "      <td>-81.181818</td>\n", "      <td>-15.990769</td>\n", "      <td>-26.118462</td>\n", "      <td>115.926286</td>\n", "      <td>43.765376</td>\n", "      <td>...</td>\n", "      <td>6.304348</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>542</th>\n", "      <td>56.239098</td>\n", "      <td>55.059427</td>\n", "      <td>-51.8</td>\n", "      <td>-100.9</td>\n", "      <td>58.739394</td>\n", "      <td>43.321212</td>\n", "      <td>27.765385</td>\n", "      <td>28.394615</td>\n", "      <td>61.383238</td>\n", "      <td>27.970591</td>\n", "      <td>...</td>\n", "      <td>17.545455</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>543</th>\n", "      <td>34.408926</td>\n", "      <td>37.126332</td>\n", "      <td>-53.1</td>\n", "      <td>-7.8</td>\n", "      <td>-58.406061</td>\n", "      <td>-55.836364</td>\n", "      <td>-18.777692</td>\n", "      <td>-19.611538</td>\n", "      <td>41.141318</td>\n", "      <td>18.384350</td>\n", "      <td>...</td>\n", "      <td>13.909091</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>544</th>\n", "      <td>42.646335</td>\n", "      <td>44.014354</td>\n", "      <td>-6.9</td>\n", "      <td>6.6</td>\n", "      <td>-2.139394</td>\n", "      <td>0.581818</td>\n", "      <td>-6.969231</td>\n", "      <td>-7.773846</td>\n", "      <td>16.807664</td>\n", "      <td>6.619253</td>\n", "      <td>...</td>\n", "      <td>15.745856</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>264 rows × 90 columns</p>\n", "</div>"], "text/plain": ["      lf_RSI_1   lf_RSI_2  lf_LR_SLOPE_FAST_1  lf_LR_SLOPE_FAST_2  \\\n", "2    77.789003  78.640329               200.4               302.2   \n", "3    58.708151  58.803915               -93.6               -53.0   \n", "4    58.140772  59.828301               -34.4               -40.1   \n", "5    66.567302  68.784082                 0.3                43.5   \n", "8    42.458217  41.638966                43.5               -40.5   \n", "..         ...        ...                 ...                 ...   \n", "535  56.106968  55.194119                 0.8               -35.0   \n", "541  46.989491  46.716832                 2.9                65.3   \n", "542  56.239098  55.059427               -51.8              -100.9   \n", "543  34.408926  37.126332               -53.1                -7.8   \n", "544  42.646335  44.014354                -6.9                 6.6   \n", "\n", "     lf_LR_SLOPE_MIDD_1  lf_LR_SLOPE_MIDD_2  lf_LR_SLOPE_SLOW_1  \\\n", "2            142.030303          153.951515          137.015385   \n", "3             43.793939           20.975758           41.997692   \n", "4             10.654545           20.787879           48.699231   \n", "5              5.521212           15.303030           19.176154   \n", "8            -59.569697          -37.563636          -38.450769   \n", "..                  ...                 ...                 ...   \n", "535            5.551515            4.460606           20.351538   \n", "541         -100.266667          -81.181818          -15.990769   \n", "542           58.739394           43.321212           27.765385   \n", "543          -58.406061          -55.836364          -18.777692   \n", "544           -2.139394            0.581818           -6.969231   \n", "\n", "     lf_LR_SLOPE_SLOW_2  lf_LR_SLOPE_FAST_THRESHOLD  \\\n", "2            135.775385                  144.101446   \n", "3             42.429231                   61.884091   \n", "4             45.233846                   85.413369   \n", "5             19.130000                   33.102468   \n", "8            -50.829231                  115.702259   \n", "..                  ...                         ...   \n", "535           17.688462                   29.758552   \n", "541          -26.118462                  115.926286   \n", "542           28.394615                   61.383238   \n", "543          -19.611538                   41.141318   \n", "544           -7.773846                   16.807664   \n", "\n", "     lf_LR_SLOPE_SLOW_THRESHOLD  ...  sf_TREND_HLR  sf_TREND_LEVEL  \\\n", "2                     52.750708  ...     18.459016             0.0   \n", "3                     27.088602  ...      8.846154             0.0   \n", "4                     29.009940  ...     12.580645             0.0   \n", "5                      7.808299  ...     20.692308             0.0   \n", "8                     43.841593  ...     17.692308            -3.0   \n", "..                          ...  ...           ...             ...   \n", "535                    8.711441  ...      9.416667             0.0   \n", "541                   43.765376  ...      6.304348             0.0   \n", "542                   27.970591  ...     17.545455             0.0   \n", "543                   18.384350  ...     13.909091             0.0   \n", "544                    6.619253  ...     15.745856             0.0   \n", "\n", "     FAST_QH_RSI  FAST_QH_RSI_PREV  LONG_RANGE  SHORT_RANGE  SLOW_QH_RSI  \\\n", "2      -1.606082         -1.606082       351.0         61.0     0.766325   \n", "3      -1.606082         -1.606082       176.0         26.0     0.766325   \n", "4      -1.606082         -1.606082       229.0         31.0     0.766325   \n", "5       2.320337         -1.606082        91.0         13.0     1.672801   \n", "8       0.920621         -1.606082       268.0         26.0     1.517871   \n", "..           ...               ...         ...          ...          ...   \n", "535          NaN               NaN         NaN          NaN          NaN   \n", "541          NaN               NaN         NaN          NaN          NaN   \n", "542          NaN               NaN         NaN          NaN          NaN   \n", "543          NaN               NaN         NaN          NaN          NaN   \n", "544          NaN               NaN         NaN          NaN          NaN   \n", "\n", "     SLOW_QH_RSI_PREV  STDDEV_RNG  label  \n", "2            0.766325    1.461996      1  \n", "3            0.766325    1.429672      1  \n", "4            0.766325    1.300617      1  \n", "5            0.766325    1.297279      1  \n", "8            0.766325    1.291485      1  \n", "..                ...         ...    ...  \n", "535               NaN         NaN      0  \n", "541               NaN         NaN      1  \n", "542               NaN         NaN      0  \n", "543               NaN         NaN      1  \n", "544               NaN         NaN      1  \n", "\n", "[264 rows x 90 columns]"]}, "metadata": {}, "execution_count": 14}], "metadata": {}}, {"cell_type": "code", "execution_count": 15, "source": ["factor_long_df.to_csv(\"e:/lab/RoboQuant/pylab/data/factors_long_xy.csv\", index=0) #header=0\r\n", "factor_short_df.to_csv(\"e:/lab/RoboQuant/pylab/data/factors_short_xy.csv\", index=0)"], "outputs": [], "metadata": {}}, {"cell_type": "code", "execution_count": null, "source": [], "outputs": [], "metadata": {}}], "metadata": {"orig_nbformat": 4, "language_info": {"name": "python", "version": "3.7.6", "mimetype": "text/x-python", "codemirror_mode": {"name": "ipython", "version": 3}, "pygments_lexer": "ipython3", "nbconvert_exporter": "python", "file_extension": ".py"}, "kernelspec": {"name": "python3", "display_name": "Python 3.7.6 64-bit ('base': conda)"}, "interpreter": {"hash": "af09bc94d41e018aa4bb791c06386d7d2a0d085b02fa573368449120a8cb3c6e"}}, "nbformat": 4, "nbformat_minor": 2}