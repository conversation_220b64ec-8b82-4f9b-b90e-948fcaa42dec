from copy import copy
import os
import pandas as pd
import numpy as np
import datetime
import copy
import json

import random
from tqdm import tqdm

import matplotlib.pyplot as plt
import seaborn as sns

from sklearn.model_selection import KFold
from sklearn.model_selection import StratifiedKFold # 将全部训练集S分成k个不相交的子集
from sklearn.preprocessing import LabelEncoder # 标签编码LabelEncoder 作用： 利用LabelEncoder() 将转换成连续的数值型变量。即是对不连续的数字或者文本进行编号

import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader, SequentialSampler, RandomSampler
import torch.optim as optim
from torch.optim import lr_scheduler
# from fastai.layers import SigmoidRange

TRAD_FUT_CODES = ['M', 'Y', 'A', 'P', 'JM', 'I', 'V', 'EG', 'EB', 'SR', 'CF', 'FG', 'TA', 'MA', 'OI',\
    'RM', 'RS', 'SF', 'SM', 'AP', 'UR', 'SA', 'RB', 'HC', 'AG', 'SP', 'BU', 'SS', 'RU', 'LH', 'PK', 'C', 'CY']


class PreprocessingPipeline:
    
    def __init__(self, n_splits, shuffle, random_state, only_trading_code, data_path, portfolios, interface_params):
        
        self.n_splits = n_splits
        self.shuffle = shuffle
        self.random_state = random_state
        self.only_trading_code = only_trading_code
        self.data_path = data_path
        self.portfolios = portfolios
        self.interface_params = interface_params
        self.train_lb_df = pd.DataFrame()
        self.train_ft_df = pd.DataFrame()
        self.valid_lb_df = pd.DataFrame()
        self.valid_ft_df = pd.DataFrame()
        self.ft_mean = pd.DataFrame()
        self.ft_std = pd.DataFrame()
        self.codes = []
        self.ft_lens = {}

    def _load_data(self, direct):

        lb_df = pd.DataFrame()
        lf_df = pd.DataFrame()
        sf_df = pd.DataFrame()
        ct_df = pd.DataFrame()

        for pf in self.portfolios:
            if os.path.isfile('%s/factors_%s_lf.%s.csv'%(self.data_path, direct, pf)):
                lf_df = lf_df.append(pd.read_csv('%s/factors_%s_lf.%s.csv'%(self.data_path, direct, pf)))
            if os.path.isfile('%s/factors_%s_sf.%s.csv'%(self.data_path, direct, pf)):
                sf_df = sf_df.append(pd.read_csv('%s/factors_%s_sf.%s.csv'%(self.data_path, direct, pf)))
            if os.path.isfile('%s/factors_%s_ct.%s.csv'%(self.data_path, direct, pf)):
                ct_df = ct_df.append(pd.read_csv('%s/factors_%s_ct.%s.csv'%(self.data_path, direct, pf)))
            if os.path.isfile('%s/orders_%s_label.%s.csv'%(self.data_path, direct, pf)):
                lb_df = lb_df.append(pd.read_csv('%s/orders_%s_label.%s.csv'%(self.data_path, direct, pf)), ignore_index=True)

        # 清除不需要的数据
        if self.only_trading_code:
            lb_df = lb_df[lb_df['CODE'].isin(TRAD_FUT_CODES)]
        # lb_df的排序和重新索引步骤不能少，否则FOLD标记有问题
        lb_df.drop_duplicates(subset=['ord_id'], keep=False, inplace=True)
        sf_df.drop_duplicates(subset=['ord_id'], keep=False, inplace=True)
        lf_df.drop_duplicates(subset=['ord_id'], keep=False, inplace=True)
        ct_df.drop_duplicates(subset=['ord_id'], keep=False, inplace=True)
        lb_df.set_index("ord_id", inplace=True)
        lf_df.set_index("ord_id", inplace=True)
        sf_df.set_index("ord_id", inplace=True)
        ct_df.set_index("ord_id", inplace=True)        
        lf_df = lf_df[lf_df.index.isin(lb_df.index)]
        sf_df = sf_df[sf_df.index.isin(lb_df.index)]
        ct_df = ct_df[ct_df.index.isin(lb_df.index)]
        lb_df = lb_df[lb_df.index.isin(lf_df.index)]

        # 汇总后保存
        lf_df.to_csv("%s/factors_%s_lf.csv"%(self.data_path, direct), index=0)
        sf_df.to_csv("%s/factors_%s_sf.csv"%(self.data_path, direct), index=0)
        ct_df.to_csv("%s/factors_%s_ct.csv"%(self.data_path, direct), index=0)
        lb_df.to_csv("%s/orders_%s_label.csv"%(self.data_path, direct), index=0)

        print(f'Feature total: {len(lf_df)}')
        # print(self.lb_df.label.value_counts())
        print(f"Today add {direct} count: {(lb_df['datetime'] >= datetime.datetime.now().strftime('%Y%m%d 00:00:00')).sum()}")

        ft_df = pd.merge(lf_df, sf_df, how='inner', left_index=True, right_index=True)
        ft_df = pd.merge(ft_df, ct_df, how='inner', left_index=True, right_index=True)

        self.codes = sorted(lb_df.CODE.unique().tolist())
        self.ft_lens["lf_len"] = lf_df.shape[1]
        self.ft_lens["sf_len"] = sf_df.shape[1]
        self.ft_lens["ct_len"] = ct_df.shape[1]
        self.ft_mean = ft_df.mean()
        self.ft_std = ft_df.std()
        # print(self.ft_mean.shape)
        # print(self.ft_std.shape)

        # 划分测试集与验证集
        cv = KFold(5, shuffle=True, random_state=1)
        train_ids, valid_ids = next(cv.split(lb_df.index))
        self.train_lb_df = lb_df.iloc[train_ids,:]
        self.valid_lb_df = lb_df.iloc[valid_ids,:]
        # print(self.train_lb_df, self.valid_lb_df)

        self.train_ft_df = ft_df[ft_df.index.isin(self.train_lb_df.index)]
        self.train_lb_df.sort_index(inplace=True)
        self.train_ft_df.sort_index(inplace=True)

        self.valid_ft_df = ft_df[ft_df.index.isin(self.valid_lb_df.index)]
        self.valid_lb_df.sort_index(inplace=True)
        self.valid_ft_df.sort_index(inplace=True)

        assert len(self.train_lb_df) == len(self.train_ft_df) and self.train_lb_df.index[-1] == self.train_ft_df.index[-1],\
            f'dataset processing error: {len(self.train_lb_df)} == {len(self.train_ft_df)} and {self.train_lb_df.index[-1]} == {self.train_ft_df.index[-1]}'
        assert len(self.valid_lb_df) == len(self.valid_ft_df) and self.valid_lb_df.index[-1] == self.valid_ft_df.index[-1],\
            f'dataset processing error: {len(self.valid_lb_df)} == {len(self.valid_ft_df)} and {self.valid_lb_df.index[-1]} == {self.valid_ft_df.index[-1]}'

        # 归一化
        # data_val = self.train_ft_df.values
        # self.train_ft_df = pd.DataFrame((data_val - data_val.mean(axis=0)) /data_val.std(axis=0))
        # data_val = self.valid_ft_df.values
        # self.valid_ft_df = pd.DataFrame((data_val - data_val.mean(axis=0)) /data_val.std(axis=0))
        self.train_ft_df = (self.train_ft_df - self.ft_mean) / self.ft_std
        self.valid_ft_df = (self.valid_ft_df - self.ft_mean) / self.ft_std

    def _label_encode(self):

        # Encoding instrument_id for embeddings
        le = LabelEncoder()
        self.train_lb_df['code_encoded'] = le.fit_transform(self.train_lb_df['CODE'].values)
        self.valid_lb_df['code_encoded'] = le.fit_transform(self.valid_lb_df['CODE'].values)
    
    def _get_folds(self):
        """此函数已不再使用"""
        skf = StratifiedKFold(n_splits=self.n_splits, shuffle=self.shuffle, random_state=self.random_state)
        for fold, (_, val_idx) in enumerate(skf.split(X=self.lb_df, y=self.lb_df['label']), 1):
            self.lb_df.loc[val_idx, 'fold'] = fold
        self.lb_df['fold'] = self.lb_df['fold'].astype(np.uint8)

    def _dump_input_param_json(self, direct, model_name, model_path):
        f_sel = {}
        f_sel['codes'] = self.codes

        f_sel['mean'] = self.ft_mean.values.tolist()
        f_sel['std'] = self.ft_std.values.tolist()

        f_sel['lf_len'] = self.ft_lens["lf_len"]
        f_sel['sf_len'] = self.ft_lens["sf_len"]
        f_sel['ct_len'] = self.ft_lens["ct_len"]

        # todo: using_file
        with open(f'{self.data_path}/using_factor.json', 'r') as using_file:
            using_factor = json.load(using_file)
            f_sel.update(using_factor)
        f_sel.update(self.interface_params)
        with open(f'{model_path}/{model_name}_{direct}.json', 'w') as factor_sel_file:
            json.dump(f_sel, factor_sel_file)
                        
    def transform(self, direct, model_name, model_path):
        self._load_data(direct)
        self._label_encode()
        self._dump_input_param_json(direct, model_name, model_path)
        
        return self.train_ft_df, self.train_lb_df, self.valid_ft_df, self.valid_lb_df

    def get_num_embeddings(self):
        return len(self.codes)

class Factor1DDataset(Dataset):

    def __init__(self, ft_df, lb_df):
        self.lb_df = lb_df
        self.ft_df = ft_df.values

    def __len__(self):
        return len(self.lb_df)

    def __getitem__(self, idx):

        sequences = torch.as_tensor(np.array(self.ft_df[idx]), dtype=torch.float)
        code_encoded = torch.as_tensor(self.lb_df.iloc[idx]['code_encoded'], dtype=torch.long)
        target = self.lb_df.iloc[idx]['label']
        target = torch.as_tensor(target, dtype=torch.float)
        return code_encoded, sequences, target

class Factor2DDataset(Dataset):

    def __init__(self, lb_df, lf_df, sf_df, ct_df, flip_probability=0.):
        """
        关键点：要保证几个表基于ord_id的顺序一致
        """

        self.lb_df = lb_df
        self.lb_df.sort_values(by='ord_id', inplace=True)
        self.lb_df.reset_index(drop=True, inplace=True)
        self.lf_df = lf_df[lf_df.index.isin(self.lb_df['ord_id'])]
        self.sf_df = sf_df[sf_df.index.isin(self.lb_df['ord_id'])]
        self.ct_df = ct_df[ct_df.index.isin(self.lb_df['ord_id'])]

        self.lf_df = self.lf_df.sort_index().values
        self.sf_df = self.sf_df.sort_index().values
        self.ct_df = self.ct_df.sort_index().values

        assert len(self.lb_df) == len(self.lf_df) and\
            len(self.lb_df) == len(self.sf_df) and\
            len(self.lb_df) == len(self.ct_df),\
         f'dataset processing error: {len(self.lb_df)} {len(self.lf_df)} {len(self.sf_df)} {len(self.ct_df)}'
        
        
        self.lf_df = (self.lf_df - self.lf_df.mean(axis=0)) /self.lf_df.std(axis=0)
        self.sf_df = (self.sf_df - self.sf_df.mean(axis=0)) /self.sf_df.std(axis=0)
        self.ct_df = (self.ct_df - self.ct_df.mean(axis=0)) /self.ct_df.std(axis=0)

        self.transforms = {
            'flip': flip_probability,
        }

    def __len__(self):
        return len(self.lb_df)

    def __getitem__(self, idx):

        """
        Get the idxth element in the dataset
        """

        item = []
        item.append(self.lf_df[idx].tolist())
        item.append(self.sf_df[idx].tolist())
        item.append(self.ct_df[idx].tolist() + [0]*(len(self.lf_df[idx])-len(self.ct_df[idx])))

        sequences = torch.as_tensor(np.array(item), dtype=torch.float)

        # Flip sequences on zeroth dimension
        if np.random.rand() < self.transforms['flip']:
            sequences = torch.flip(sequences, dims=[0])

        code_encoded = torch.as_tensor(self.lb_df.iloc[idx]['code_encoded'], dtype=torch.long)
        target = self.lb_df.iloc[idx]['label']
        target = torch.as_tensor(target, dtype=torch.float)
        return code_encoded, sequences, target


def visualize_learning_curve(training_losses, validation_losses, title, path=None):
    
    """
    Visualize learning curves of the models

    Parameters
    ----------
    training_losses [array-like of shape (n_epochs)]: Array of training losses computed after every epoch
    validation_losses [array-like of shape (n_epochs)]: Array of validation losses computed after every epoch
    title (str): Title of the plot
    path (str or None): Path of the output file (if path is None, plot is displayed with selected backend)
    """

    fig, ax = plt.subplots(figsize=(32, 8), dpi=100)

    sns.lineplot(
        x=np.arange(1, len(training_losses) + 1),
        y=training_losses,
        ax=ax,
        label='train_loss'
    )
    sns.lineplot(
        x=np.arange(1, len(validation_losses) + 1),
        y=validation_losses,
        ax=ax,
        label='val_loss'
    )

    ax.set_xlabel('Epochs/Steps', size=15, labelpad=12.5)
    ax.set_ylabel('Loss', size=15, labelpad=12.5)
    ax.tick_params(axis='x', labelsize=12.5, pad=10)
    ax.tick_params(axis='y', labelsize=12.5, pad=10)
    ax.legend(prop={'size': 18})
    ax.set_title(title, size=20, pad=15)

    if path is None:
        plt.show()
    else:
        plt.savefig(path)


def set_seed(seed, deterministic_cudnn=False):

    """
    Set random seed for reproducible results
    
    Parameters
    ----------
    seed (int): Random seed
    deterministic_cudnn (bool): Whether to set deterministic cuDNN or not
    """

    if deterministic_cudnn:
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False

    os.environ['PYTHONHASHSEED'] = str(seed)
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)


def rmspe_metric(y_true, y_pred):

    """
    Calculate root mean squared percentage error between ground-truth and predictions
    
    Parameters
    ----------
    y_true [array-like of shape (n_samples)]: Ground-truth
    y_pred [array-like of shape (n_samples)]: Predictions
    
    Returns
    -------
    rmspe (float): Root mean squared percentage error
    """

    rmspe = np.sqrt(np.mean(np.square((y_true - y_pred) / y_true)))
    return rmspe


def rmspe_loss(y_true, y_pred):

    """
    Calculate root mean squared percentage error between ground-truth and predictions
    
    Parameters
    ----------
    y_true [torch.tensor of shape (n_samples)]: Ground-truth
    y_pred [torch.tensor of shape (n_samples)]: Predictions
    
    Returns
    -------
    rmspe (torch.FloatTensor): Root mean squared percentage error
    """

    rmspe = torch.sqrt(torch.mean(torch.square((y_true - y_pred) / y_true)))
    return rmspe

# 定义计算正确率函数
def accuracy(y_true, y_pred):
    # preds = (out>0.5).type(torch.IntTensor)
    # return (preds == yb).float().mean()
    return (y_true == y_pred).astype(float).mean()


# -------------------------------------------------------------------------
# 
# 创建模型
class MLPModel(nn.Module):
    '''多层感知机模型'''
    def __init__(self, code_num, input_num, bn=True, dropout=True):

        super(MLPModel, self).__init__()
        
        self.code_embeddings = nn.Embedding(num_embeddings=code_num, embedding_dim=1)
        self.bn = bn
        self.dropout = dropout
        self.lin_1 = nn.Linear(input_num + 1, 96)
        self.bn_1 = nn.BatchNorm1d(96)
        self.lin_2 = nn.Linear(96, 96)
        self.bn_2 = nn.BatchNorm1d(96)
        self.lin_3 = nn.Linear(96, 96)
        self.bn_3 = nn.BatchNorm1d(96)
        self.lin_4 = nn.Linear(96, 1, bias=True)
        self.drop = nn.Dropout(0.4)
        self.activate = nn.ReLU()
        self.sigmoid = nn.Sigmoid()

    def forward(self, code_ids, input):
        '''
        注意：
        模型不能这样写：self.bn_1(F.dropout(F.relu(self.lin_1(input))))
        模型层嵌套写法的问题，dropout在模型的train时执行，在eval时不执行
        Dropout：放在全连接层防止过拟合，一般放在激活函数层之后
        BatchNorm：归一化放在激活层前后好像都有，最初放在了
        激活层池化层后面，而现在普遍放在激活层前。
        '''
        # input layer
        # 加入code向量
        embedded_code_ids = self.code_embeddings(code_ids)
        input = torch.cat([input, embedded_code_ids], dim=1)
        x = self.lin_1(input)
        # print(x.shape)
        if self.bn:
            x = self.bn_1(x)
        x = self.activate(x)
        if self.dropout:
            x = self.drop(x)
        # hidden layer1
        x = self.lin_2(x)
        if self.bn:
            x = self.bn_2(x)
        x = self.activate(x)
        if self.dropout:
            x = self.drop(x)
        # hidden layer2
        x = self.lin_3(x)
        if self.bn:
            x = self.bn_3(x)
        x = self.activate(x)
        if self.dropout:
            x = self.drop(x)
        # out layer
        x = self.lin_4(x)
        x = self.sigmoid(x)
        return x.view(-1)

#--------------------------------------------------------------------------    

class Conv1dBlock(nn.Module):

    def __init__(self, in_channels, out_channels, kernel_size=(5,), stride=(1,), padding=(2,), skip_connection=False):

        super(Conv1dBlock, self).__init__()

        self.skip_connection = skip_connection
        self.conv_block = nn.Sequential(
            nn.Conv1d(in_channels, out_channels, kernel_size=kernel_size, stride=stride, padding=padding, padding_mode='replicate', bias=True),
            nn.BatchNorm1d(out_channels),
            nn.ReLU(inplace=True),
            nn.Conv1d(out_channels, out_channels, kernel_size=kernel_size, stride=stride, padding=padding, padding_mode='replicate', bias=True),
            nn.BatchNorm1d(out_channels),
        )
        self.downsample = nn.Sequential(
            nn.Conv1d(in_channels, out_channels, kernel_size=(1,), stride=(1,), bias=False),
            nn.BatchNorm1d(out_channels)
        )
        self.relu = nn.ReLU(inplace=True)

    def forward(self, x):

        output = self.conv_block(x)
        if self.skip_connection:
            x = self.downsample(x)
            output += x
        output = self.relu(output)

        return output

class CNN1DModel(nn.Module):

    def __init__(self, num_embeddings, in_channels):

        super(CNN1DModel, self).__init__()

        self.stock_embeddings = nn.Embedding(num_embeddings=num_embeddings, embedding_dim=10)
        self.conv_block1 = Conv1dBlock(in_channels=in_channels, out_channels=32, skip_connection=True)
        self.conv_block2 = Conv1dBlock(in_channels=32, out_channels=64, skip_connection=True)
        self.conv_block3 = Conv1dBlock(in_channels=64, out_channels=128, skip_connection=True)
        self.conv_block4 = Conv1dBlock(in_channels=128, out_channels=64, skip_connection=True)
        self.conv_block5 = Conv1dBlock(in_channels=64, out_channels=32, skip_connection=True)
        self.conv_block6 = Conv1dBlock(in_channels=32, out_channels=16, skip_connection=True)
        self.conv_block7 = Conv1dBlock(in_channels=16, out_channels=8, skip_connection=True)
        self.conv_block8 = Conv1dBlock(in_channels=8, out_channels=1, skip_connection=True)
        self.pooling = nn.AvgPool1d(kernel_size=(3,), stride=(1,), padding=(1,))
        self.linear = nn.Linear(3 + 10, 32, bias=True)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.25)
        self.head = nn.Sequential(
            nn.Linear(32, 1, bias=True),
            nn.Sigmoid()
        )

    def forward(self, stock_ids, sequences):

        x = torch.transpose(sequences, 1, 2)
        x = self.conv_block1(x)
        x = self.pooling(x)
        x = self.conv_block2(x)
        x = self.pooling(x)
        x = self.conv_block3(x)
        x = self.pooling(x)
        x = self.conv_block4(x)
        x = self.pooling(x)
        x = self.conv_block5(x)
        x = self.pooling(x)
        x = self.conv_block6(x)
        x = self.pooling(x)
        x = self.conv_block7(x)
        x = self.pooling(x)
        x = self.conv_block8(x)
        x = self.pooling(x)
        x = x.view(x.size(0), -1)
        embedded_stock_ids = self.stock_embeddings(stock_ids)
        x = torch.cat([x, self.dropout(embedded_stock_ids)], dim=1)
        x = self.relu(self.linear(x))
        output = self.head(x)
        
        return output.view(-1)

class CNN1DModel2(nn.Module):

    def __init__(self, num_embeddings, in_channels):

        super(CNN1DModel2, self).__init__()

        self.stock_embeddings = nn.Embedding(num_embeddings=num_embeddings, embedding_dim=10)
        # self.conv_block1 = Conv1dBlock(in_channels=in_channels, out_channels=32, skip_connection=True)
        self.conv_block2 = Conv1dBlock(in_channels=in_channels, out_channels=64, skip_connection=True)
        self.conv_block3 = Conv1dBlock(in_channels=64, out_channels=128, skip_connection=True)
        self.conv_block4 = Conv1dBlock(in_channels=128, out_channels=64, skip_connection=True)
        self.conv_block5 = Conv1dBlock(in_channels=64, out_channels=32, skip_connection=True)
        self.conv_block6 = Conv1dBlock(in_channels=32, out_channels=16, skip_connection=True)
        self.conv_block7 = Conv1dBlock(in_channels=16, out_channels=8, skip_connection=True)
        self.conv_block8 = Conv1dBlock(in_channels=8, out_channels=1, skip_connection=True)
        self.pooling = nn.AvgPool1d(kernel_size=(3,), stride=(1,), padding=(1,))
        self.linear = nn.Linear(3 + 10, 32, bias=True)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.25)
        self.head = nn.Sequential(
            nn.Linear(32, 1, bias=True),
            nn.Sigmoid()
        )

    def forward(self, stock_ids, sequences):

        x = torch.transpose(sequences, 1, 2)
        # x = self.conv_block1(x)
        # x = self.pooling(x)
        x = self.conv_block2(x)
        x = self.pooling(x)
        x = self.conv_block3(x)
        x = self.pooling(x)
        x = self.conv_block4(x)
        x = self.pooling(x)
        x = self.conv_block5(x)
        x = self.pooling(x)
        x = self.conv_block6(x)
        x = self.pooling(x)
        x = self.conv_block7(x)
        x = self.pooling(x)
        x = self.conv_block8(x)
        x = self.pooling(x)
        x = x.view(x.size(0), -1)
        embedded_stock_ids = self.stock_embeddings(stock_ids)
        x = torch.cat([x, self.dropout(embedded_stock_ids)], dim=1)
        x = self.relu(self.linear(x))
        output = self.head(x)
        
        return output.view(-1)

#--------------------------------------------------------------------------
class MLPTrainer:
    def __init__(self, model_name, model_path, model_parameters, training_parameters):
        self.model_name = model_name
        self.model_path = model_path
        self.model_params = model_parameters
        self.training_params = training_parameters

        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        self.train_loss = []
        self.train_acc = []
        self.test_loss = []
        self.test_acc = []

    def get_model(self, code_num, input_num):
        model = MLPModel(code_num, input_num, self.model_params['batch_norm'], self.model_params['drop_out'])
        model.to(self.device)
        optim = torch.optim.Adam(model.parameters(), lr=self.training_params['learning_rate'])
        # 学习速率衰减设置
        exp_lr_scheduler = lr_scheduler.StepLR(optim, step_size=30, gamma=0.5) # 按步数
        # exp_lr_scheduler = lr_scheduler.MultiStepLR(opt, milestones=[50, 100, 150], gamma=0.1) # 按里程碑
        # exp_lr_scheduler = lr_scheduler.ExponentialLR(opt, gamma=0.1) # 按系数每步

        return model, optim, exp_lr_scheduler

    # 定义计算正确率函数
    def accuracy(self, out, yb):
        preds = (out>0.5).type(torch.IntTensor)
        return (preds == yb).float().mean()

    def fit(self, epoch, model, optim, exp_lr_scheduler, trainloader, testloader):
        # 定义损失函数
        loss_fn = nn.BCELoss()

        # for p in optim.param_groups:
        #     print("lr: ", p['lr'])

        correct = 0
        total = 0
        running_loss = 0
        model.train() # 训练模式 dropout起作用
        for code_encoded, x, y in trainloader:
            if x.shape[0] == 1: # 单批次中的数据个数必须大于1
                continue
            code_encoded, x, y = code_encoded.to(self.device), x.to(self.device), y.to(self.device)
            y_pred = model(code_encoded, x)
            loss = loss_fn(y_pred, y)
            optim.zero_grad()
            loss.backward()
            optim.step()
            with torch.no_grad():
                correct += ((y_pred>0.5).type(torch.IntTensor).to(self.device) == y).sum().item()
                total += y.size(0)
                running_loss += loss.item()

        exp_lr_scheduler.step()

        epoch_loss = running_loss / len(trainloader.dataset) 
        epoch_acc = correct / total

        test_correct = 0
        test_total = 0
        test_running_loss = 0
        model.eval() # 预测模式 dropout不起作用
        with torch.no_grad():
            for code_encoded, x, y in testloader:
                code_encoded, x, y = code_encoded.to(self.device), x.to(self.device), y.to(self.device)
                y_pred = model(code_encoded, x)
                loss = loss_fn(y_pred, y)
                test_correct += ((y_pred>0.5).type(torch.IntTensor).to(self.device) == y).sum().item()
                test_total += y.size(0)
                test_running_loss += loss.item()

        epoch_test_loss = test_running_loss / len(testloader.dataset) 
        epoch_test_acc = test_correct / test_total

        if epoch % 50 == 0:
            print('epoch: ', epoch,
                'loss: ', round(epoch_loss, 3),
                'accuracy:', round(epoch_acc, 3),
                'test_loss: ', round(epoch_test_loss, 3),
                'test_accuracy:', round(epoch_test_acc, 3),
            )
        return epoch_loss, epoch_acc, epoch_test_loss, epoch_test_acc
        
    def train_model(self, train_ft_df, train_lb_df, valid_ft_df, valid_lb_df, direct):
        train_ds = Factor1DDataset(ft_df=train_ft_df, lb_df=train_lb_df)
        train_dl = DataLoader(train_ds, batch_size=self.training_params['batch_size'], shuffle=True)

        test_ds = Factor1DDataset(ft_df=valid_ft_df, lb_df=valid_lb_df)
        test_dl = DataLoader(test_ds, batch_size=self.training_params['batch_size'] * 2)
        input_num= train_ft_df.shape[1]
        # todo: ...
        model, optim, exp_lr = self.get_model(len(train_lb_df.CODE.unique().tolist()), input_num)
        # 保存训练最优参数
        best_model_wts = copy.deepcopy(model.state_dict())
        best_acc = 0.0
        best_epoch = 0
        self.train_loss = []
        self.train_acc = []
        self.test_loss = []
        self.test_acc = []
        for epoch in range(1, self.training_params['epochs']+1):
            epoch_loss, epoch_acc, epoch_test_loss, epoch_test_acc = self.fit(
                                    epoch,
                                    model, optim,
                                    exp_lr,
                                    train_dl,
                                    test_dl)
            self.train_loss.append(epoch_loss)
            self.train_acc.append(epoch_acc)
            self.test_loss.append(epoch_test_loss)
            self.test_acc.append(epoch_test_acc)

            if epoch_test_acc > best_acc:
                best_epoch = epoch
                best_acc = epoch_test_acc
                best_model_wts = copy.deepcopy(model.state_dict())

        model.load_state_dict(best_model_wts)
        model = model.cpu()
        model.eval() # 如果要使用，要调用eval()表明运行模式
        sm = torch.jit.script(model)
        sm.save('%s/%s_%s.model' % (self.model_path, self.model_name, direct))
        print("Best model epoch: %d accuracy: %.3f" % (best_epoch, best_acc))

    def plot_show(self, name, is_show=False):
        plt.figure()
        if name == 'loss':
            plt.plot(range(1, self.training_params['epochs']+1), self.train_loss, label='train %s' % name)
            plt.plot(range(1, self.training_params['epochs']+1), self.test_loss, label='test %s' % name)
        else:
            plt.plot(range(1, self.training_params['epochs']+1), self.train_acc, label='train %s' % name)
            plt.plot(range(1, self.training_params['epochs']+1), self.test_acc, label='test %s' % name)
        plt.legend()
        plt.title('%s %s' % (self.model_name, name))
        plt.savefig('%s/%s_%s.png' % (self.model_path, self.model_name, name))
        if is_show:
            plt.show()

    def inference(self, lb_df, lf_df, sf_df, ct_df, direct):

        print(f'\n{"-" * 27}\nRunning Model for Inference\n{"-" * 27}')
        lb_df[f'{self.model_name}_predictions'] = 0

        for fold in sorted(lb_df['fold'].unique()):

            _, val_idx = lb_df.loc[lb_df['fold'] != fold].index, lb_df.loc[lb_df['fold'] == fold].index
            val_dataset = Factor1DDataset(lb_df=lb_df.loc[val_idx, :], lf_df=lf_df, sf_df=sf_df, ct_df=ct_df)
            val_loader = DataLoader(
                val_dataset,
                batch_size=self.training_params['batch_size'],
                sampler=SequentialSampler(val_dataset),
                pin_memory=True,
                drop_last=False,
                # num_workers=self.training_params['num_workers'], 此参数在笔记本电脑不能使用
            )

            set_seed(self.training_params['random_state'], deterministic_cudnn=self.training_params['deterministic_cudnn'])
            # model = self.get_model()
            input_num= lf_df.shape[1] + sf_df.shape[1] + ct_df.shape[1]
            model, _, _ = self.get_model(len(lb_df.CODE.unique().tolist()), input_num)
            model = (torch.jit.load(f'{self.model_path}/{self.model_name}_{direct}.model'))
            model.to(self.device)
            model.eval()

            val_predictions = []
            with torch.no_grad():
                for stock_id, sequences, target in val_loader:
                    stock_id, sequences, target = stock_id.to(self.device), sequences.to(self.device), target.to(self.device)
                    output = model(stock_id, sequences)
                    output = (output>0.5).type(torch.IntTensor)
                    output = output.detach().cpu().numpy().squeeze().tolist()
                    
                    if isinstance(output, int):
                        val_predictions.append(output)
                    else:
                        val_predictions += output

            lb_df.loc[val_idx, f'{self.model_name}_predictions'] = val_predictions
            fold_score = accuracy(lb_df.loc[val_idx, 'label'].to_numpy(), np.array(val_predictions))
            print(f'Fold {fold} - Accuracy: {fold_score:.6}')

            del _, val_idx, val_dataset, val_loader, val_predictions, model

        print(f'{"-" * 30}')
        for stock_id in lb_df['CODE'].unique():
            df_stock = lb_df.loc[lb_df['CODE'] == stock_id, :]
            stock_oof_score = accuracy(df_stock['label'].to_numpy(), df_stock[f'{self.model_name}_predictions'].to_numpy())
            print(f'Instrument code: {stock_id} - Accuracy: {stock_oof_score:.6}')

        oof_score = accuracy(lb_df['label'], lb_df[f'{self.model_name}_predictions'])
        print(f'{"-" * 30}\nOOF Accuracy: {oof_score:.6}\n{"-" * 30}')


class ConvTrainer:

    def __init__(self, model_name, model_path, model_parameters, training_parameters):

        self.model_name = model_name
        self.model_path = model_path
        self.model_params = model_parameters
        self.training_params = training_parameters

    def get_model(self):

        model = None

        if self.model_name == 'cnn1d':
            model = CNN1DModel2(**self.model_params)

        return model

    def train_fn(self, train_loader, model, criterion, optimizer, device):

        # print('\n')
        model.train()
        # progress_bar = tqdm(train_loader)
        losses = []
        correct = 0
        total = 0

        if self.training_params['amp']:
            scaler = torch.cuda.amp.GradScaler()
        else:
            scaler = None

        # for stock_id_encoded, sequences, target in progress_bar:
        for stock_id_encoded, sequences, target in train_loader:

            stock_id_encoded, sequences, target = stock_id_encoded.to(device), sequences.to(device), target.to(device)
            if scaler is not None:
                with torch.cuda.amp.autocast():
                    optimizer.zero_grad()
                    output = model(stock_id_encoded, sequences)
                    loss = criterion(output, target)
                scaler.scale(loss).backward()
                scaler.step(optimizer)
                scaler.update()
                with torch.no_grad():
                    correct += ((output>0.5).type(torch.IntTensor).to(device) == target).sum().item()
                    total += target.size(0)
            else:
                optimizer.zero_grad()
                output = model(stock_id_encoded, sequences)
                loss = criterion(output, target)
                loss.backward()
                optimizer.step()
                with torch.no_grad():
                    correct += ((output>0.5).type(torch.IntTensor).to(device) == target).sum().item()
                    total += target.size(0)

            losses.append(loss.item())
            average_loss = np.mean(losses)
            # progress_bar.set_description(f'train_rmspe: {average_loss:.6f}')

        train_loss = np.mean(losses)
        train_acc = correct / total
        return train_loss, train_acc

    def val_fn(self, val_loader, model, criterion, device):

        model.eval()
        # progress_bar = tqdm(val_loader)
        losses = []
        correct = 0
        total = 0

        with torch.no_grad():
            
            # for stock_id_encoded, sequences, target in progress_bar:
            for stock_id_encoded, sequences, target in val_loader:
                
                stock_id_encoded, sequences, target = stock_id_encoded.to(device), sequences.to(device), target.to(device)
                output = model(stock_id_encoded, sequences)
                loss = criterion(output, target)
                losses.append(loss.item())
                average_loss = np.mean(losses)
                correct += ((output>0.5).type(torch.IntTensor).to(device) == target).sum().item()
                total += target.size(0)
                # progress_bar.set_description(f'valid_rmspe: {average_loss:.6f}')

        val_loss = np.mean(losses)
        val_acc = correct / total
        return val_loss, val_acc

    def train_and_validate(self, lb_df, lf_df, sf_df, ct_df, direct):

        print(f'\n{"-" * 26}\nRunning Model for Training\n{"-" * 26}\n')

        best_model_wts = None
        best_acc = 0.0
        best_fold = 0
        best_epoch = 0
        for fold in sorted(lb_df['fold'].unique()):

            print(f'\nFold {fold}\n{"-" * 6}')

            trn_idx, val_idx = lb_df.loc[lb_df['fold'] != fold].index, lb_df.loc[lb_df['fold'] == fold].index
            train_dataset = Factor2DDataset(lb_df=lb_df.loc[trn_idx, :], lf_df=lf_df, sf_df=sf_df, ct_df=ct_df, flip_probability=0.)
            train_loader = DataLoader(
                train_dataset,
                batch_size=self.training_params['batch_size'],
                sampler=RandomSampler(train_dataset),
                pin_memory=True,
                drop_last=False,
                # num_workers=self.training_params['num_workers'],
            )
            
            val_dataset = Factor2DDataset(lb_df=lb_df.loc[val_idx, :], lf_df=lf_df, sf_df=sf_df, ct_df=ct_df, flip_probability=0.)
            val_loader = DataLoader(
                val_dataset,
                batch_size=self.training_params['batch_size'],
                sampler=SequentialSampler(val_dataset),
                pin_memory=True,
                drop_last=False,
                # num_workers=self.training_params['num_workers'],
            )

            set_seed(self.training_params['random_state'], deterministic_cudnn=self.training_params['deterministic_cudnn'])
            device = torch.device('cuda') if torch.cuda.is_available() else torch.device('cpu')
            model = self.get_model()
            model = model.to(device)

            optimizer = optim.Adam(
                model.parameters(),
                lr=self.training_params['learning_rate'],
                betas=self.training_params['betas'],
                weight_decay=self.training_params['weight_decay']
            )
            scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                optimizer,
                mode='min',
                patience=self.training_params['reduce_lr_patience'],
                factor=self.training_params['reduce_lr_factor'],
                min_lr=self.training_params['reduce_lr_min'],
                verbose=True
            )

            early_stopping = False
            summary = {
                'train_loss': [],
                'val_loss': [],
                'train_acc': [],
                'val_acc': []
            }
            
            criterion = nn.BCELoss() # rmspe_loss

            for epoch in range(1, self.training_params['epochs'] + 1):

                if early_stopping:
                    break

                train_loss, train_acc = self.train_fn(train_loader, model, criterion, optimizer, device)
                val_loss, val_acc = self.val_fn(val_loader, model, criterion, device)
                if epoch%10 == 0:
                    print(f'Epoch {epoch} - Training & Validation Loss: [{train_loss:.6f} -  {val_loss:.6f}] Accuracy: [{train_acc:.6f} - {val_acc:.6f}]')
                scheduler.step(val_loss)

                best_val_loss = np.min(summary['val_loss']) if len(summary['val_loss']) > 0 else np.inf
                if val_loss < best_val_loss:
                    model_path = f'{self.model_path}/{self.model_name}_fold{fold}_{direct}.pt'
                    torch.save(model.state_dict(), model_path)
                    # print(f'Saving model to {model_path} (validation loss decreased from {best_val_loss:.6f} to {val_loss:.6f})')

                if val_acc > best_acc:
                    best_fold = fold
                    best_epoch = epoch
                    best_acc = val_acc
                    best_model_wts = copy.deepcopy(model.state_dict())


                summary['train_loss'].append(train_loss)
                summary['val_loss'].append(val_loss)
                summary['train_acc'].append(train_acc)
                summary['val_acc'].append(val_acc)

                best_iteration = np.argmin(summary['val_loss']) + 1
                if len(summary['val_loss']) - best_iteration >= self.training_params['early_stopping_patience']:
                    print(f'Early stopping (validation loss didn\'t increase for {self.training_params["early_stopping_patience"]} epochs/steps)')
                    print(f'Best validation loss is {np.min(summary["val_loss"]):.6f}')
                    visualize_learning_curve(
                        training_losses=summary['train_loss'],
                        validation_losses=summary['val_loss'],
                        title=f'{self.model_name} - Fold {fold} Learning Curve',
                        path=f'{self.model_path}/{self.model_name}_fold{fold}_learning_curve.png'
                    )
                    early_stopping = True

        # 保持最优模型
        model.load_state_dict(best_model_wts)
        model.eval() # 如果要使用，要调用eval()表明运行模式
        sm = torch.jit.script(model)
        print(f'\n{"-" * 60}\nBest model Fold: {best_fold:d} epoch: {best_epoch:d} accuracy: {best_acc:.3f}\n{"-" * 60}')
        sm.save(f'{self.model_path}/{ self.model_name}_{direct}.model')

    def inference(self, lb_df, lf_df, sf_df, ct_df, direct):

        print(f'\n{"-" * 27}\nRunning Model for Inference\n{"-" * 27}')
        lb_df[f'{self.model_name}_predictions'] = 0

        for fold in sorted(lb_df['fold'].unique()):

            _, val_idx = lb_df.loc[lb_df['fold'] != fold].index, lb_df.loc[lb_df['fold'] == fold].index
            val_dataset = Factor2DDataset(lb_df=lb_df.loc[val_idx, :], lf_df=lf_df, sf_df=sf_df, ct_df=ct_df, flip_probability=0.)
            val_loader = DataLoader(
                val_dataset,
                batch_size=self.training_params['batch_size'],
                sampler=SequentialSampler(val_dataset),
                pin_memory=True,
                drop_last=False,
                # num_workers=self.training_params['num_workers'], 此参数在笔记本电脑不能使用
            )

            set_seed(self.training_params['random_state'], deterministic_cudnn=self.training_params['deterministic_cudnn'])
            device = torch.device('cuda') if torch.cuda.is_available() else torch.device('cpu')
            model = self.get_model()
            model.load_state_dict(torch.load(f'{self.model_path}/{self.model_name}_fold{fold}_{direct}.pt'))
            model.to(device)
            model.eval()

            val_predictions = []
            with torch.no_grad():
                for stock_id, sequences, target in val_loader:
                    stock_id, sequences, target = stock_id.to(device), sequences.to(device), target.to(device)
                    output = model(stock_id, sequences)
                    output = (output>0.5).type(torch.IntTensor)
                    output = output.detach().cpu().numpy().squeeze().tolist()
                    
                    if isinstance(output, int):
                        val_predictions.append(output)
                    else:
                        val_predictions += output

            lb_df.loc[val_idx, f'{self.model_name}_predictions'] = val_predictions
            fold_score = accuracy(lb_df.loc[val_idx, 'label'].to_numpy(), np.array(val_predictions))
            print(f'Fold {fold} - Accuracy: {fold_score:.6}')

            del _, val_idx, val_dataset, val_loader, val_predictions, model

        print(f'{"-" * 30}')
        for stock_id in lb_df['CODE'].unique():
            df_stock = lb_df.loc[lb_df['CODE'] == stock_id, :]
            stock_oof_score = accuracy(df_stock['label'].to_numpy(), df_stock[f'{self.model_name}_predictions'].to_numpy())
            print(f'Instrument code: {stock_id} - Accuracy: {stock_oof_score:.6}')

        oof_score = accuracy(lb_df['label'], lb_df[f'{self.model_name}_predictions'])
        print(f'{"-" * 30}\nOOF Accuracy: {oof_score:.6}\n{"-" * 30}')


def train_cnn1d():
    cnn1d_parameters = {
        'model_name': 'cnn1d',
        'model_path': 'e:/lab/RoboQuant/pylab/model',
        'model_parameters': {
            'num_embeddings': 38,
            'in_channels': 41,
        },
        'training_parameters': {
            'amp': False,
            'learning_rate': 0.0005,
            'betas': (0.9, 0.999),
            'weight_decay': 0,
            'epochs': 100,
            'batch_size': 64,
            'reduce_lr_patience': 5,
            'reduce_lr_factor': 0.25,
            'reduce_lr_min': 0.000001,
            'early_stopping_patience': 20,
            'num_workers': 3,
            'random_state': 42,
            'deterministic_cudnn': False,
        }
    }

    preprocessing_parameters = {
        'n_splits': 5,
        'shuffle': True,
        'random_state': 42,
        'only_trading_code': True,
        'data_path': 'e:/lab/RoboQuant/pylab/data',
        'portfolios': ['00200910081133001', '00171106132928000', '00170623114649000'],
        'interface_params': {
            'input_dim': 1, # 1: expression call 2: API call
            'code_encoding': 2, # 0:unsing, 1:onehot, 2:embedding
        }
    }

    ppp = PreprocessingPipeline(**preprocessing_parameters)
    directs = ['long', 'short']
    for direct in directs:
        print(f'\n{"*" * 30}\n{direct}\n{"*" * 30}')
        lb_df, lf_df, sf_df, ct_df = ppp.transform(direct, cnn1d_parameters['model_name'], cnn1d_parameters['model_path'])
        cnn1d_parameters['model_parameters']['num_embeddings'] = ppp.get_num_embeddings()

        trainer = ConvTrainer(**cnn1d_parameters)
        trainer.train_and_validate(lb_df, lf_df, sf_df, ct_df, direct)
        trainer.inference(lb_df, lf_df, sf_df, ct_df, direct)


def train_mlp():
    mlp_parameters = {
        'model_name': "MLP_5R",
        'model_path': 'e:/lab/RoboQuant/pylab/model',
        'model_parameters': {
            'batch_norm': True,
            'drop_out': True,
        },
        'training_parameters': {
            'amp': False,
            'learning_rate': 0.001,
            'weight_decay': 0,
            'epochs': 300,
            'batch_size': 128,
            'reduce_lr_patience': 5,
            'reduce_lr_factor': 0.25,
            'reduce_lr_min': 0.000001,
            'early_stopping_patience': 20,
            'random_state': 42,
            'deterministic_cudnn': False,
            'valid_fold': 1,
        },
    }


    preprocessing_parameters = {
        'n_splits': 5,
        'shuffle': True,
        'random_state': 42,
        'only_trading_code': True,
        'data_path': 'e:/lab/RoboQuant/pylab/data',
        'portfolios': ['00211229152555000', '00171106132928000'], # 5R
        # 'portfolios': ['00200910081133001', '00170623114649000'], # 7R
        'interface_params': {
            'input_dim': 1,
            'code_encoding': 2, # 0:unsing, 1:onehot, 2:embedding
        }
    }

    ppp = PreprocessingPipeline(**preprocessing_parameters)
    directs = ['long', 'short']
    for direct in directs:
        print(f'\n{"*" * 30}\n{direct}\n{"*" * 30}')
        train_ft_df, train_lb_df, valid_ft_df, valid_lb_df = ppp.transform(direct, mlp_parameters['model_name'], mlp_parameters['model_path'])
  
        am = MLPTrainer(**mlp_parameters)

        am.train_model(train_ft_df, train_lb_df, valid_ft_df, valid_lb_df, direct)
        am.plot_show("loss")
        am.plot_show("accuracy")
        # am.inference(lb_df, lf_df, sf_df, ct_df, direct)

if __name__ == '__main__':
    train_mlp()
    """
    item = input("select training item:\n 1: train MLP Model\n 2: train CONV1 Model\n 3: train MLP and CONV1 Model\n")
    if item == '1':
        train_mlp()
    elif item == '2':
        train_cnn1d()
    elif item == '3':
        train_mlp()
        train_cnn1d()
    """