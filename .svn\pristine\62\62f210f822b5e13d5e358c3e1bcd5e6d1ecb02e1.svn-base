{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Echo State Network as a tunable frequency generator\n", "\n", "This is a simplified implementation of <PERSON>'s task of learning a simple non-autonomous system, [a frequency generator controlled by an external signal](http://www.scholarpedia.org/article/Echo_state_network). Plots at the end.\n", "\n", "[See the ESN implementation](https://github.com/cknd/pyESN).\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from matplotlib import pyplot as plt\n", "%matplotlib inline\n", "from pyESN import ESN\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task\n", "\n", "The network will learn to generate a wave signal whose frequency is determined by some slowly changing control input.\n", "\n", "#### 1) Generate some sample data:\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["rng = np.random.RandomState(42)\n", "def frequency_generator(N,min_period,max_period,n_changepoints):\n", "    \"\"\"returns a random step function with N changepoints\n", "       and a sine wave signal that changes its frequency at\n", "       each such step, in the limits given by min_ and max_period.\"\"\"\n", "    # vector of random indices < N, padded with 0 and N at the ends:\n", "    changepoints = np.insert(np.sort(rng.randint(0,N,n_changepoints)),[0,n_changepoints],[0,N])\n", "    # list of interval boundaries between which the control sequence should be constant:\n", "    const_intervals = list(zip(changepoints,np.roll(changepoints,-1)))[:-1]\n", "    # populate a control sequence\n", "    frequency_control = np.zeros((N,1))\n", "    for (t0,t1) in const_intervals:\n", "        frequency_control[t0:t1] = rng.rand()\n", "    periods = frequency_control * (max_period - min_period) + max_period\n", "    # run time through a sine, while changing the period length\n", "    frequency_output = np.zeros((N,1))\n", "    z = 0\n", "    for i in range(N):\n", "        z = z + 2 * np.pi / periods[i]\n", "        frequency_output[i] = (np.sin(z) + 1)/2\n", "    return np.hstack([np.ones((N,1)),1-frequency_control]),frequency_output\n", "\n", "\n", "N = 15000 # signal length\n", "min_period = 2\n", "max_period = 10\n", "n_changepoints = int(N/200)\n", "frequency_control,frequency_output = frequency_generator(N,min_period,max_period,n_changepoints)\n", "\n", "traintest_cutoff = int(np.ceil(0.7*N))\n", "\n", "train_ctrl,train_output = frequency_control[:traintest_cutoff],frequency_output[:traintest_cutoff]\n", "test_ctrl, test_output  = frequency_control[traintest_cutoff:],frequency_output[traintest_cutoff:]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2) Instantiate, train & test the network\n", "Parameters are mostly the same as in <PERSON>'s original Matlab code. "]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["harvesting states...\n", "fitting...\n", "training error:\n", "0.00367775097729\n", "test error:\n", "0.390371124559\n"]}], "source": ["esn = ESN(n_inputs = 2,\n", "          n_outputs = 1,\n", "          n_reservoir = 200,\n", "          spectral_radius = 0.25,\n", "          sparsity = 0.95,\n", "          noise = 0.001,\n", "          input_shift = [0,0],\n", "          input_scaling = [0.01, 3],\n", "          teacher_scaling = 1.12,\n", "          teacher_shift = -0.7,\n", "          out_activation = np.tanh,\n", "          inverse_out_activation = np.arctanh,\n", "          random_state = rng,\n", "          silent = False)\n", "\n", "pred_train = esn.fit(train_ctrl,train_output)\n", "\n", "print(\"test error:\")\n", "pred_test = esn.predict(test_ctrl)\n", "print(np.sqrt(np.mean((pred_test - test_output)**2)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3) Plots\n", "First, a look at the control signal, the target signal and the output of the model both during training and during testing."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Created with matplotlib (http://matplotlib.org/) -->\n", "<svg height=\"125pt\" version=\"1.1\" viewBox=\"0 0 605 125\" width=\"605pt\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", " <defs>\n", "  <style type=\"text/css\">\n", "*{stroke-linecap:butt;stroke-linejoin:round;stroke-miterlimit:100000;}\n", "  </style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M -0 125.89625 \n", "L 605.028125 125.89625 \n", "L 605.028125 0 \n", "L -0 0 \n", "L -0 125.89625 \n", "z\n", "\" style=\"fill:none;\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 27.103125 105.018125 \n", "L 585.103125 105.018125 \n", "L 585.103125 21.318125 \n", "L 27.103125 21.318125 \n", "z\n", "\" style=\"fill:#ffffff;\"/>\n", "   </g>\n", "   <g id=\"line2d_1\">\n", "    <path clip-path=\"url(#pdeef12bd97)\" d=\"M 27.103125 34.465477 \n", "L 57.235125 34.465477 \n", "L 57.514125 41.96293 \n", "L 60.862125 41.96293 \n", "L 61.141125 31.447728 \n", "L 132.844125 31.447728 \n", "L 133.123125 50.984916 \n", "L 238.864125 50.984916 \n", "L 239.143125 55.403366 \n", "L 286.573125 55.403366 \n", "L 286.852125 47.21972 \n", "L 379.759125 47.21972 \n", "L 380.038125 86.097566 \n", "L 529.303125 86.097566 \n", "L 529.582125 53.17667 \n", "L 565.294125 53.17667 \n", "L 565.573125 47.888307 \n", "L 584.824125 47.888307 \n", "L 584.824125 47.888307 \n", "\" style=\"fill:none;stroke:#0000ff;stroke-linecap:square;\"/>\n", "   </g>\n", "   <g id=\"line2d_2\">\n", "    <path clip-path=\"url(#pdeef12bd97)\" d=\"M 27.103125 65.962904 \n", "L 27.382125 46.247897 \n", "L 27.661125 32.193421 \n", "L 27.940125 28.501292 \n", "L 28.219125 36.406684 \n", "L 29.335125 97.874467 \n", "L 29.614125 93.965846 \n", "L 30.172125 59.993622 \n", "L 30.730125 29.914133 \n", "L 31.009125 29.65797 \n", "L 31.288125 40.612369 \n", "L 32.125125 93.540337 \n", "L 32.404125 97.950273 \n", "L 32.683125 90.724111 \n", "L 33.799125 28.609101 \n", "L 34.078125 31.796408 \n", "L 34.357125 45.478879 \n", "L 34.915125 84.040104 \n", "L 35.194125 96.018498 \n", "L 35.473125 97.007054 \n", "L 35.752125 86.675058 \n", "L 36.589125 33.438483 \n", "L 36.868125 28.316558 \n", "L 37.147125 34.853954 \n", "L 38.263125 97.534229 \n", "L 38.542125 95.072442 \n", "L 38.821125 81.937314 \n", "L 39.658125 30.764718 \n", "L 39.937125 29.045074 \n", "L 40.216125 38.741032 \n", "L 41.053125 92.215968 \n", "L 41.332125 98.043123 \n", "L 41.611125 92.203118 \n", "L 42.169125 56.586093 \n", "L 42.448125 38.724474 \n", "L 42.727125 29.040288 \n", "L 43.006125 30.773307 \n", "L 43.285125 43.34376 \n", "L 44.122125 95.081808 \n", "L 44.401125 97.530271 \n", "L 44.680125 88.483144 \n", "L 45.517125 34.840411 \n", "L 45.796125 28.315715 \n", "L 46.075125 33.450622 \n", "L 46.633125 68.501936 \n", "L 46.912125 86.692197 \n", "L 47.191125 97.012661 \n", "L 47.470125 96.010698 \n", "L 47.749125 84.021507 \n", "L 48.586125 31.786277 \n", "L 48.865125 28.612226 \n", "L 49.144125 36.998583 \n", "L 49.981125 90.73833 \n", "L 50.260125 97.951958 \n", "L 50.539125 93.528924 \n", "L 51.097125 59.089573 \n", "L 51.376125 40.594674 \n", "L 51.655125 29.651548 \n", "L 51.934125 29.921134 \n", "L 52.213125 41.313243 \n", "L 53.050125 93.976729 \n", "L 53.329125 97.872179 \n", "L 53.608125 90.157658 \n", "L 54.724125 28.498768 \n", "L 55.003125 32.204092 \n", "L 55.282125 46.268194 \n", "L 55.840125 84.761169 \n", "L 56.119125 96.312516 \n", "L 56.398125 96.775663 \n", "L 56.677125 85.995669 \n", "L 57.514125 33.763111 \n", "L 57.793125 28.303603 \n", "L 58.072125 32.879471 \n", "L 58.630125 64.359419 \n", "L 59.188125 94.566463 \n", "L 59.467125 97.892917 \n", "L 59.746125 91.224215 \n", "L 60.304125 57.903884 \n", "L 60.862125 30.208423 \n", "L 61.141125 29.588671 \n", "L 61.420125 40.942197 \n", "L 62.257125 94.681815 \n", "L 62.536125 97.576739 \n", "L 62.815125 88.202734 \n", "L 63.652125 33.479301 \n", "L 63.931125 28.344009 \n", "L 64.210125 35.625799 \n", "L 65.326125 97.989091 \n", "L 65.605125 92.887057 \n", "L 66.163125 56.490388 \n", "L 66.442125 38.173553 \n", "L 66.721125 28.768924 \n", "L 67.000125 31.629866 \n", "L 67.279125 45.736267 \n", "L 67.837125 85.349744 \n", "L 68.116125 96.732018 \n", "L 68.395125 96.146562 \n", "L 68.674125 83.802129 \n", "L 69.511125 30.843042 \n", "L 69.790125 29.145974 \n", "L 70.069125 39.580035 \n", "L 70.906125 93.865788 \n", "L 71.185125 97.824979 \n", "L 71.464125 89.426727 \n", "L 72.301125 34.466925 \n", "L 72.580125 28.29321 \n", "L 72.859125 34.55469 \n", "L 73.975125 97.841838 \n", "L 74.254125 93.792406 \n", "L 74.812125 58.272274 \n", "L 75.091125 39.466833 \n", "L 75.370125 29.112457 \n", "L 75.649125 30.901161 \n", "L 75.928125 44.195155 \n", "L 76.486125 83.926082 \n", "L 76.765125 96.196333 \n", "L 77.044125 96.689862 \n", "L 77.323125 85.230692 \n", "L 78.160125 31.564438 \n", "L 78.439125 28.794611 \n", "L 78.718125 38.281196 \n", "L 79.555125 92.967353 \n", "L 79.834125 97.980182 \n", "L 80.113125 90.580228 \n", "L 81.229125 28.336033 \n", "L 81.508125 33.560393 \n", "L 82.066125 70.053285 \n", "L 82.345125 88.309706 \n", "L 82.624125 97.601504 \n", "L 82.903125 94.615544 \n", "L 83.182125 80.416515 \n", "L 83.740125 40.823739 \n", "L 84.019125 29.547413 \n", "L 84.298125 30.259077 \n", "L 84.577125 42.704976 \n", "L 85.414125 95.571984 \n", "L 85.693125 97.143172 \n", "L 85.972125 86.600027 \n", "L 86.809125 32.370675 \n", "L 87.088125 28.535523 \n", "L 87.367125 37.049165 \n", "L 88.204125 91.988922 \n", "L 88.483125 98.041932 \n", "L 88.762125 91.660142 \n", "L 89.878125 28.472363 \n", "L 90.157125 32.645579 \n", "L 90.715125 68.27325 \n", "L 90.994125 87.024102 \n", "L 91.273125 97.268733 \n", "L 91.552125 95.354261 \n", "L 91.831125 81.963319 \n", "L 92.389125 42.240628 \n", "L 92.668125 30.072625 \n", "L 92.947125 29.705338 \n", "L 93.226125 41.26973 \n", "L 94.063125 94.860646 \n", "L 94.342125 97.505277 \n", "L 94.621125 87.90646 \n", "L 95.458125 33.259588 \n", "L 95.737125 28.369407 \n", "L 96.016125 35.887252 \n", "L 97.132125 98.010063 \n", "L 97.411125 92.663568 \n", "L 97.969125 56.075795 \n", "L 98.248125 37.880458 \n", "L 98.527125 28.701834 \n", "L 98.806125 31.812703 \n", "L 99.085125 46.103837 \n", "L 99.643125 85.674456 \n", "L 99.922125 96.844419 \n", "L 100.201125 96.006574 \n", "L 100.480125 83.459667 \n", "L 101.317125 30.686681 \n", "L 101.596125 29.24143 \n", "L 101.875125 39.893271 \n", "L 102.712125 94.064229 \n", "L 102.991125 97.775202 \n", "L 103.270125 89.146482 \n", "L 104.107125 34.22879 \n", "L 104.386125 28.296708 \n", "L 104.665125 34.798574 \n", "L 105.781125 97.884661 \n", "L 106.060125 93.587814 \n", "L 106.618125 57.853914 \n", "L 106.897125 39.15834 \n", "L 107.176125 29.02383 \n", "L 107.455125 31.064001 \n", "L 107.734125 44.551398 \n", "L 108.292125 84.264392 \n", "L 108.571125 96.329701 \n", "L 108.850125 96.570732 \n", "L 109.129125 84.901542 \n", "L 109.966125 31.387935 \n", "L 110.245125 28.868599 \n", "L 110.524125 38.579294 \n", "L 111.361125 93.184872 \n", "L 111.640125 97.952225 \n", "L 111.919125 90.316765 \n", "L 112.756125 35.27568 \n", "L 113.035125 28.317622 \n", "L 113.314125 33.786054 \n", "L 113.872125 70.467364 \n", "L 114.151125 88.600947 \n", "L 114.430125 97.666061 \n", "L 114.709125 94.430398 \n", "L 114.988125 80.047683 \n", "L 115.546125 40.500676 \n", "L 115.825125 29.437486 \n", "L 116.104125 30.401482 \n", "L 116.383125 43.048936 \n", "L 117.220125 95.725959 \n", "L 117.499125 97.04522 \n", "L 117.778125 86.285074 \n", "L 118.615125 32.174502 \n", "L 118.894125 28.587845 \n", "L 119.173125 37.331326 \n", "L 120.010125 92.224934 \n", "L 120.289125 98.03587 \n", "L 120.568125 91.414167 \n", "L 121.684125 28.432092 \n", "L 121.963125 32.852411 \n", "L 122.521125 68.691227 \n", "L 122.800125 87.330835 \n", "L 123.079125 97.354852 \n", "L 123.358125 95.189058 \n", "L 123.637125 81.6057 \n", "L 124.195125 41.903863 \n", "L 124.474125 29.941693 \n", "L 124.753125 29.826926 \n", "L 125.032125 41.600484 \n", "L 125.869125 95.034816 \n", "L 126.148125 97.428765 \n", "L 126.427125 87.606548 \n", "L 127.264125 33.044272 \n", "L 127.543125 28.399921 \n", "L 127.822125 36.152716 \n", "L 128.659125 91.186993 \n", "L 128.938125 98.025912 \n", "L 129.217125 92.435743 \n", "L 129.775125 55.662246 \n", "L 130.054125 37.591082 \n", "L 130.333125 28.639812 \n", "L 130.612125 32.000151 \n", "L 130.891125 46.473918 \n", "L 131.449125 85.995858 \n", "L 131.728125 96.951868 \n", "L 132.007125 95.861757 \n", "L 132.286125 83.114221 \n", "L 132.844125 43.364134 \n", "L 133.123125 32.048444 \n", "L 133.402125 28.309054 \n", "L 133.681125 33.056343 \n", "L 134.239125 61.603157 \n", "L 134.797125 91.581222 \n", "L 135.076125 97.792317 \n", "L 135.355125 95.573915 \n", "L 135.634125 85.466102 \n", "L 136.471125 38.101638 \n", "L 136.750125 29.558749 \n", "L 137.029125 29.198292 \n", "L 137.308125 37.108024 \n", "L 138.424125 95.005609 \n", "L 138.703125 97.936805 \n", "L 138.982125 92.403327 \n", "L 139.540125 63.063811 \n", "L 140.098125 33.819696 \n", "L 140.377125 28.383789 \n", "L 140.656125 31.416367 \n", "L 140.935125 42.179128 \n", "L 141.772125 89.366403 \n", "L 142.051125 97.184573 \n", "L 142.330125 96.721206 \n", "L 142.609125 88.089112 \n", "L 143.725125 30.68581 \n", "L 144.004125 28.569528 \n", "L 144.283125 34.876512 \n", "L 144.841125 64.941483 \n", "L 145.399125 93.384618 \n", "L 145.678125 98.032877 \n", "L 145.957125 94.193074 \n", "L 146.236125 82.800034 \n", "L 147.073125 35.898103 \n", "L 147.352125 28.822572 \n", "L 147.631125 30.1087 \n", "L 147.910125 39.443372 \n", "L 149.026125 96.22082 \n", "L 149.305125 97.517337 \n", "L 149.584125 90.451304 \n", "L 150.142125 59.729788 \n", "L 150.700125 32.152825 \n", "L 150.979125 28.302867 \n", "L 151.258125 32.941095 \n", "L 151.816125 61.373681 \n", "L 152.374125 91.447387 \n", "L 152.653125 97.764062 \n", "L 152.932125 95.65812 \n", "L 153.211125 85.642266 \n", "L 154.327125 29.620808 \n", "L 154.606125 29.147027 \n", "L 154.885125 36.955915 \n", "L 156.001125 94.911143 \n", "L 156.280125 97.953976 \n", "L 156.559125 92.527954 \n", "L 157.117125 63.293553 \n", "L 157.675125 33.944441 \n", "L 157.954125 28.401099 \n", "L 158.233125 31.322028 \n", "L 158.512125 41.996108 \n", "L 159.349125 89.21419 \n", "L 159.628125 97.133172 \n", "L 159.907125 96.783132 \n", "L 160.186125 88.249287 \n", "L 161.302125 30.770144 \n", "L 161.581125 28.541412 \n", "L 161.860125 34.74279 \n", "L 162.418125 64.712001 \n", "L 162.976125 93.26925 \n", "L 163.255125 98.026552 \n", "L 163.534125 94.297331 \n", "L 163.813125 82.989492 \n", "L 164.650125 36.041906 \n", "L 164.929125 28.863197 \n", "L 165.208125 30.036257 \n", "L 165.487125 39.275498 \n", "L 166.603125 96.146811 \n", "L 166.882125 97.556335 \n", "L 167.161125 90.593814 \n", "L 167.719125 59.958485 \n", "L 168.277125 32.258552 \n", "L 168.556125 28.298194 \n", "L 168.835125 32.827159 \n", "L 169.393125 61.144283 \n", "L 169.951125 91.312324 \n", "L 170.230125 97.734306 \n", "L 170.509125 95.740915 \n", "L 170.788125 85.817455 \n", "L 171.904125 29.684323 \n", "L 172.183125 29.097238 \n", "L 172.462125 36.804943 \n", "L 173.578125 94.8153 \n", "L 173.857125 97.969637 \n", "L 174.136125 92.651308 \n", "L 174.694125 63.52329 \n", "L 175.252125 34.070453 \n", "L 175.531125 28.419918 \n", "L 175.810125 31.229071 \n", "L 176.089125 41.814006 \n", "L 176.926125 89.060846 \n", "L 177.205125 97.080297 \n", "L 177.484125 96.843598 \n", "L 177.763125 88.408374 \n", "L 178.879125 30.855884 \n", "L 179.158125 28.514798 \n", "L 179.437125 34.610301 \n", "L 179.995125 64.482451 \n", "L 180.553125 93.152576 \n", "L 180.832125 98.018714 \n", "L 181.111125 94.400237 \n", "L 181.390125 83.178089 \n", "L 182.227125 36.186886 \n", "L 182.506125 28.90531 \n", "L 182.785125 29.965252 \n", "L 183.064125 39.10866 \n", "L 184.180125 96.07137 \n", "L 184.459125 97.59384 \n", "L 184.738125 90.735134 \n", "L 185.296125 60.18732 \n", "L 185.854125 32.36562 \n", "L 186.133125 28.295034 \n", "L 186.412125 32.71454 \n", "L 186.970125 60.914974 \n", "L 187.528125 91.176041 \n", "L 187.807125 97.70305 \n", "L 188.086125 95.822296 \n", "L 188.365125 85.991661 \n", "L 189.481125 29.749291 \n", "L 189.760125 29.048927 \n", "L 190.039125 36.655116 \n", "L 191.155125 94.718083 \n", "L 191.434125 97.983789 \n", "L 191.713125 92.773382 \n", "L 192.271125 63.753011 \n", "L 192.829125 34.197729 \n", "L 193.108125 28.440244 \n", "L 193.387125 31.1375 \n", "L 193.666125 41.632831 \n", "L 194.503125 88.906379 \n", "L 194.782125 97.025951 \n", "L 195.061125 96.902603 \n", "L 195.340125 88.566366 \n", "L 196.456125 30.943027 \n", "L 196.735125 28.489688 \n", "L 197.014125 34.479052 \n", "L 197.572125 64.252845 \n", "L 198.130125 93.034601 \n", "L 198.409125 98.009363 \n", "L 198.688125 94.501787 \n", "L 198.967125 83.365817 \n", "L 199.804125 36.333036 \n", "L 200.083125 28.948911 \n", "L 200.362125 29.895687 \n", "L 200.641125 38.942867 \n", "L 201.757125 95.994502 \n", "L 202.036125 97.629851 \n", "L 202.315125 90.875257 \n", "L 202.873125 60.416285 \n", "L 203.431125 32.474025 \n", "L 203.710125 28.293387 \n", "L 203.989125 32.603243 \n", "L 204.547125 60.685762 \n", "L 205.105125 91.038541 \n", "L 205.384125 97.670296 \n", "L 205.663125 95.902261 \n", "L 205.942125 86.164876 \n", "L 207.058125 29.815709 \n", "L 207.337125 29.002097 \n", "L 207.616125 36.506439 \n", "L 208.732125 94.619498 \n", "L 209.011125 97.996429 \n", "L 209.290125 92.894172 \n", "L 209.848125 63.982707 \n", "L 210.406125 34.326261 \n", "L 210.685125 28.462078 \n", "L 210.964125 31.04732 \n", "L 211.243125 41.452591 \n", "L 212.080125 88.750795 \n", "L 212.359125 96.970135 \n", "L 212.638125 96.960144 \n", "L 212.917125 88.723255 \n", "L 214.033125 31.031568 \n", "L 214.312125 28.466083 \n", "L 214.591125 34.349048 \n", "L 215.149125 64.023192 \n", "L 215.707125 92.915329 \n", "L 215.986125 97.998501 \n", "L 216.265125 94.601978 \n", "L 216.544125 83.552669 \n", "L 217.381125 36.480352 \n", "L 217.660125 28.993996 \n", "L 217.939125 29.827567 \n", "L 218.218125 38.778124 \n", "L 219.334125 95.916209 \n", "L 219.613125 97.664367 \n", "L 219.892125 91.014179 \n", "L 220.450125 60.645369 \n", "L 221.008125 32.583762 \n", "L 221.287125 28.293253 \n", "L 221.566125 32.493271 \n", "L 222.124125 60.456657 \n", "L 222.682125 90.899832 \n", "L 222.961125 97.636044 \n", "L 223.240125 95.980804 \n", "L 223.519125 86.337093 \n", "L 224.635125 29.883575 \n", "L 224.914125 28.95675 \n", "L 225.193125 36.358919 \n", "L 226.309125 94.519547 \n", "L 226.588125 98.007558 \n", "L 226.867125 93.013671 \n", "L 227.425125 64.212367 \n", "L 227.983125 34.456046 \n", "L 228.262125 28.485418 \n", "L 228.541125 30.958533 \n", "L 228.820125 41.273293 \n", "L 229.657125 88.594101 \n", "L 229.936125 96.912852 \n", "L 230.215125 97.016219 \n", "L 230.494125 88.879035 \n", "L 231.610125 31.121503 \n", "L 231.889125 28.443984 \n", "L 232.168125 34.220294 \n", "L 232.726125 63.793501 \n", "L 233.284125 92.794767 \n", "L 233.563125 97.986126 \n", "L 233.842125 94.700805 \n", "L 234.121125 83.738637 \n", "L 234.958125 36.628825 \n", "L 235.237125 29.040565 \n", "L 235.516125 29.760893 \n", "L 235.795125 38.614441 \n", "L 236.911125 95.836494 \n", "L 237.190125 97.697385 \n", "L 237.469125 91.151892 \n", "L 238.027125 60.874563 \n", "L 238.585125 32.694826 \n", "L 238.864125 28.294633 \n", "L 239.143125 32.074477 \n", "L 239.422125 42.861365 \n", "L 240.259125 88.579639 \n", "L 240.538125 96.73113 \n", "L 240.817125 97.319099 \n", "L 241.096125 90.211047 \n", "L 241.654125 60.687505 \n", "L 242.212125 33.274047 \n", "L 242.491125 28.359581 \n", "L 242.770125 31.289321 \n", "L 243.049125 41.403043 \n", "L 244.165125 96.188277 \n", "L 244.444125 97.642718 \n", "L 244.723125 91.328209 \n", "L 245.281125 62.514392 \n", "L 245.839125 34.256658 \n", "L 246.118125 28.520238 \n", "L 246.397125 30.59182 \n", "L 246.676125 40.004568 \n", "L 247.792125 95.554632 \n", "L 248.071125 97.871545 \n", "L 248.350125 92.367941 \n", "L 248.908125 64.343076 \n", "L 249.466125 35.318765 \n", "L 249.745125 28.776164 \n", "L 250.024125 29.983892 \n", "L 250.303125 38.669784 \n", "L 251.419125 94.831937 \n", "L 251.698125 98.004951 \n", "L 251.977125 93.327385 \n", "L 252.535125 66.16853 \n", "L 253.093125 36.457448 \n", "L 253.372125 29.126656 \n", "L 253.651125 29.467208 \n", "L 253.930125 37.402361 \n", "L 255.046125 94.022177 \n", "L 255.325125 98.042568 \n", "L 255.604125 94.203902 \n", "L 255.883125 83.371233 \n", "L 256.720125 37.669574 \n", "L 256.999125 29.570748 \n", "L 257.278125 29.043189 \n", "L 257.557125 36.205784 \n", "L 258.115125 65.775735 \n", "L 258.673125 93.127581 \n", "L 258.952125 97.984294 \n", "L 259.231125 94.995082 \n", "L 259.510125 84.833573 \n", "L 260.626125 30.107221 \n", "L 260.905125 28.713001 \n", "L 261.184125 35.083343 \n", "L 261.742125 63.94916 \n", "L 262.300125 92.150607 \n", "L 262.579125 97.830289 \n", "L 262.858125 95.698751 \n", "L 263.137125 86.236341 \n", "L 264.253125 30.734598 \n", "L 264.532125 28.477551 \n", "L 264.811125 34.038125 \n", "L 265.369125 62.120438 \n", "L 265.927125 91.093942 \n", "L 266.206125 97.580975 \n", "L 266.485125 96.312972 \n", "L 266.764125 87.57568 \n", "L 267.880125 31.451156 \n", "L 268.159125 28.337487 \n", "L 268.438125 33.073004 \n", "L 268.996125 60.294596 \n", "L 269.554125 89.960492 \n", "L 269.833125 97.237039 \n", "L 270.112125 96.836057 \n", "L 270.391125 88.847908 \n", "L 271.507125 32.254924 \n", "L 271.786125 28.293195 \n", "L 272.065125 32.190633 \n", "L 272.344125 43.068939 \n", "L 273.181125 88.753373 \n", "L 273.460125 96.799426 \n", "L 273.739125 97.266568 \n", "L 274.018125 90.049526 \n", "L 274.576125 60.43356 \n", "L 275.134125 33.143691 \n", "L 275.413125 28.344795 \n", "L 275.692125 31.393438 \n", "L 275.971125 41.6026 \n", "L 277.087125 96.26934 \n", "L 277.366125 97.603321 \n", "L 277.645125 91.17723 \n", "L 278.203125 62.259798 \n", "L 278.761125 34.115014 \n", "L 279.040125 28.492147 \n", "L 279.319125 30.683612 \n", "L 279.598125 40.195557 \n", "L 280.714125 95.648238 \n", "L 280.993125 97.845391 \n", "L 281.272125 92.22792 \n", "L 281.830125 64.088534 \n", "L 282.388125 35.166223 \n", "L 282.667125 28.734844 \n", "L 282.946125 30.063106 \n", "L 283.225125 38.851681 \n", "L 284.341125 94.937828 \n", "L 284.620125 97.99211 \n", "L 284.899125 93.198706 \n", "L 285.457125 65.914739 \n", "L 286.015125 36.294426 \n", "L 286.294125 29.07222 \n", "L 286.573125 29.533627 \n", "L 286.852125 38.467627 \n", "L 287.968125 96.558631 \n", "L 288.247125 97.17623 \n", "L 288.526125 88.929672 \n", "L 289.642125 30.25372 \n", "L 289.921125 28.8525 \n", "L 290.200125 36.395591 \n", "L 291.316125 95.542468 \n", "L 291.595125 97.724587 \n", "L 291.874125 90.899621 \n", "L 292.432125 59.228107 \n", "L 292.990125 31.396755 \n", "L 293.269125 28.437978 \n", "L 293.548125 34.531557 \n", "L 294.106125 65.577159 \n", "L 294.664125 94.274782 \n", "L 294.943125 98.004468 \n", "L 295.222125 92.654118 \n", "L 295.780125 62.294757 \n", "L 296.338125 32.786629 \n", "L 296.617125 28.293282 \n", "L 296.896125 32.890006 \n", "L 297.454125 62.504131 \n", "L 298.012125 92.765421 \n", "L 298.291125 98.013697 \n", "L 298.570125 94.179532 \n", "L 298.849125 82.262294 \n", "L 299.686125 34.412543 \n", "L 299.965125 28.419537 \n", "L 300.244125 31.483693 \n", "L 300.523125 42.806345 \n", "L 301.360125 91.026113 \n", "L 301.639125 97.752204 \n", "L 301.918125 95.464012 \n", "L 302.197125 84.757949 \n", "L 303.034125 36.261865 \n", "L 303.313125 28.81576 \n", "L 303.592125 30.323543 \n", "L 303.871125 40.392212 \n", "L 304.708125 89.070371 \n", "L 304.987125 97.22202 \n", "L 305.266125 96.497578 \n", "L 305.545125 87.085868 \n", "L 306.661125 29.478874 \n", "L 306.940125 29.418569 \n", "L 307.219125 38.15503 \n", "L 308.335125 96.427264 \n", "L 308.614125 97.272201 \n", "L 308.893125 89.227965 \n", "L 310.009125 30.403728 \n", "L 310.288125 28.775803 \n", "L 310.567125 36.112181 \n", "L 311.683125 95.374111 \n", "L 311.962125 97.781861 \n", "L 312.241125 91.167598 \n", "L 312.799125 59.671233 \n", "L 313.357125 31.583134 \n", "L 313.636125 28.400238 \n", "L 313.915125 34.279534 \n", "L 314.473125 65.13237 \n", "L 315.031125 94.070743 \n", "L 315.310125 98.022601 \n", "L 315.589125 92.889697 \n", "L 316.147125 62.740345 \n", "L 316.705125 33.007932 \n", "L 316.984125 28.294792 \n", "L 317.263125 32.671329 \n", "L 317.821125 62.058609 \n", "L 318.379125 92.527285 \n", "L 318.658125 97.992548 \n", "L 318.937125 94.380882 \n", "L 319.216125 82.633662 \n", "L 320.053125 34.66705 \n", "L 320.332125 28.460284 \n", "L 320.611125 31.300061 \n", "L 320.890125 42.446196 \n", "L 321.727125 90.75573 \n", "L 322.006125 97.691938 \n", "L 322.285125 95.629569 \n", "L 322.564125 85.106178 \n", "L 323.401125 36.547599 \n", "L 323.680125 28.895429 \n", "L 323.959125 30.176382 \n", "L 324.238125 40.056578 \n", "L 325.075125 88.769841 \n", "L 325.354125 97.123104 \n", "L 325.633125 96.626057 \n", "L 325.912125 87.408254 \n", "L 327.028125 29.596846 \n", "L 327.307125 29.309022 \n", "L 327.586125 37.846519 \n", "L 328.702125 96.290467 \n", "L 328.981125 97.362602 \n", "L 329.260125 89.522002 \n", "L 330.376125 30.559085 \n", "L 330.655125 28.704722 \n", "L 330.934125 35.833188 \n", "L 332.050125 95.200495 \n", "L 332.329125 97.833483 \n", "L 332.608125 91.431002 \n", "L 333.166125 60.114929 \n", "L 333.724125 31.774671 \n", "L 334.003125 28.368175 \n", "L 334.282125 34.032229 \n", "L 334.840125 64.68726 \n", "L 335.398125 93.861657 \n", "L 335.677125 98.035042 \n", "L 335.956125 93.120422 \n", "L 336.514125 63.186003 \n", "L 337.072125 33.234159 \n", "L 337.351125 28.301996 \n", "L 337.630125 32.457632 \n", "L 338.188125 61.613269 \n", "L 338.746125 92.284355 \n", "L 339.025125 97.965712 \n", "L 339.304125 94.577135 \n", "L 339.583125 83.001851 \n", "L 340.420125 34.926211 \n", "L 340.699125 28.5067 \n", "L 340.978125 31.121632 \n", "L 341.257125 42.089431 \n", "L 342.094125 90.480842 \n", "L 342.373125 97.626033 \n", "L 342.652125 95.789826 \n", "L 342.931125 85.450826 \n", "L 343.768125 36.83768 \n", "L 344.047125 28.980695 \n", "L 344.326125 30.034608 \n", "L 344.605125 39.724718 \n", "L 345.721125 97.018642 \n", "L 346.000125 96.749071 \n", "L 346.279125 87.726681 \n", "L 347.395125 29.7203 \n", "L 347.674125 29.205005 \n", "L 347.953125 37.542142 \n", "L 349.069125 96.14826 \n", "L 349.348125 97.447419 \n", "L 349.627125 89.811736 \n", "L 350.743125 30.719768 \n", "L 351.022125 28.639269 \n", "L 351.301125 35.55866 \n", "L 351.859125 67.30728 \n", "L 352.417125 95.021648 \n", "L 352.696125 97.879444 \n", "L 352.975125 91.689791 \n", "L 353.533125 60.559124 \n", "L 354.091125 31.971335 \n", "L 354.370125 28.341795 \n", "L 354.649125 33.789682 \n", "L 355.207125 64.241902 \n", "L 355.765125 93.64756 \n", "L 356.044125 98.04179 \n", "L 356.323125 93.346256 \n", "L 356.881125 63.631658 \n", "L 357.439125 33.465275 \n", "L 357.718125 28.314894 \n", "L 357.997125 32.248951 \n", "L 358.276125 44.24204 \n", "L 359.113125 92.03667 \n", "L 359.392125 97.933194 \n", "L 359.671125 94.76826 \n", "L 359.950125 83.366802 \n", "L 360.787125 35.189984 \n", "L 361.066125 28.558775 \n", "L 361.345125 30.948437 \n", "L 361.624125 41.736108 \n", "L 362.461125 90.201494 \n", "L 362.740125 97.554501 \n", "L 363.019125 95.944755 \n", "L 363.298125 85.791834 \n", "L 364.135125 37.132061 \n", "L 364.414125 29.071544 \n", "L 364.693125 29.898245 \n", "L 364.972125 39.396687 \n", "L 366.088125 96.908653 \n", "L 366.367125 96.866602 \n", "L 366.646125 88.041098 \n", "L 367.762125 29.849216 \n", "L 368.041125 29.106534 \n", "L 368.320125 37.241951 \n", "L 369.436125 96.000668 \n", "L 369.715125 97.526639 \n", "L 369.994125 90.097119 \n", "L 371.110125 30.88575 \n", "L 371.389125 28.579455 \n", "L 371.668125 35.28864 \n", "L 372.226125 66.864436 \n", "L 372.784125 94.8376 \n", "L 373.063125 97.919737 \n", "L 373.342125 91.943923 \n", "L 373.900125 61.003745 \n", "L 374.458125 32.173092 \n", "L 374.737125 28.321102 \n", "L 375.016125 33.551932 \n", "L 375.574125 63.796369 \n", "L 376.132125 93.428485 \n", "L 376.411125 98.042842 \n", "L 376.690125 93.567161 \n", "L 377.248125 64.077238 \n", "L 377.806125 33.701242 \n", "L 378.085125 28.333484 \n", "L 378.364125 32.045318 \n", "L 378.643125 43.869261 \n", "L 379.480125 91.78427 \n", "L 379.759125 97.894998 \n", "L 380.038125 96.630189 \n", "L 380.317125 90.645177 \n", "L 380.875125 68.438305 \n", "L 381.712125 34.170067 \n", "L 381.991125 29.068011 \n", "L 382.270125 28.776163 \n", "L 382.549125 33.335691 \n", "L 383.107125 53.842566 \n", "L 383.944125 89.661049 \n", "L 384.223125 96.15919 \n", "L 384.502125 98.003568 \n", "L 384.781125 94.934012 \n", "L 385.060125 87.383518 \n", "L 386.455125 31.752711 \n", "L 386.734125 28.38189 \n", "L 387.013125 29.91806 \n", "L 387.292125 36.144529 \n", "L 387.850125 58.617382 \n", "L 388.408125 83.567418 \n", "L 388.687125 92.563569 \n", "L 388.966125 97.41316 \n", "L 389.245125 97.432101 \n", "L 389.524125 92.617721 \n", "L 390.082125 71.791482 \n", "L 390.919125 36.208399 \n", "L 391.198125 29.948628 \n", "L 391.477125 28.374843 \n", "L 391.756125 31.709045 \n", "L 392.035125 39.480906 \n", "L 393.430125 94.892249 \n", "L 393.709125 97.99862 \n", "L 393.988125 96.191754 \n", "L 394.267125 89.726532 \n", "L 394.825125 66.997456 \n", "L 395.383125 42.183895 \n", "L 395.662125 33.388058 \n", "L 395.941125 28.793036 \n", "L 396.220125 29.04701 \n", "L 396.499125 34.114154 \n", "L 397.057125 55.250714 \n", "L 397.894125 90.582947 \n", "L 398.173125 96.60163 \n", "L 398.452125 97.904139 \n", "L 398.731125 94.30674 \n", "L 399.010125 86.316887 \n", "L 400.405125 31.149064 \n", "L 400.684125 28.308493 \n", "L 400.963125 30.385269 \n", "L 401.242125 37.086437 \n", "L 401.800125 60.061869 \n", "L 402.358125 84.728182 \n", "L 402.637125 93.319886 \n", "L 402.916125 97.658343 \n", "L 403.195125 97.131564 \n", "L 403.474125 91.813857 \n", "L 404.032125 70.376153 \n", "L 404.869125 35.310109 \n", "L 405.148125 29.535127 \n", "L 405.427125 28.504459 \n", "L 405.706125 32.363494 \n", "L 406.264125 51.96027 \n", "L 407.101125 88.33839 \n", "L 407.380125 95.468222 \n", "L 407.659125 98.041759 \n", "L 407.938125 95.695975 \n", "L 408.217125 88.76177 \n", "L 408.775125 65.549957 \n", "L 409.333125 41.041601 \n", "L 409.612125 32.657761 \n", "L 409.891125 28.577752 \n", "L 410.170125 29.377108 \n", "L 410.449125 34.943068 \n", "L 411.007125 56.672609 \n", "L 411.844125 91.457239 \n", "L 412.123125 96.986014 \n", "L 412.402125 97.744393 \n", "L 412.681125 93.625397 \n", "L 413.239125 73.685456 \n", "L 414.076125 37.500362 \n", "L 414.355125 30.601016 \n", "L 414.634125 28.29563 \n", "L 414.913125 30.909404 \n", "L 415.192125 38.073635 \n", "L 415.750125 61.511751 \n", "L 416.308125 85.851508 \n", "L 416.587125 94.023846 \n", "L 416.866125 97.843635 \n", "L 417.145125 96.77205 \n", "L 417.424125 90.960252 \n", "L 417.982125 68.948308 \n", "L 418.819125 34.460194 \n", "L 419.098125 29.180027 \n", "L 419.377125 28.694267 \n", "L 419.656125 33.071435 \n", "L 420.214125 53.345885 \n", "L 421.051125 89.322241 \n", "L 421.330125 95.988106 \n", "L 421.609125 98.024341 \n", "L 421.888125 95.143712 \n", "L 422.167125 87.752565 \n", "L 423.004125 51.171927 \n", "L 423.562125 31.980444 \n", "L 423.841125 28.422533 \n", "L 424.120125 29.765882 \n", "L 424.399125 35.820995 \n", "L 424.957125 58.105784 \n", "L 425.794125 92.282409 \n", "L 426.073125 97.311674 \n", "L 426.352125 97.524606 \n", "L 426.631125 92.891166 \n", "L 427.189125 72.29101 \n", "L 428.026125 36.539011 \n", "L 428.305125 30.109521 \n", "L 428.584125 28.343321 \n", "L 428.863125 31.489555 \n", "L 429.142125 39.104409 \n", "L 430.537125 94.674225 \n", "L 430.816125 97.968713 \n", "L 431.095125 96.354184 \n", "L 431.374125 90.058386 \n", "L 431.932125 67.510425 \n", "L 432.490125 42.598747 \n", "L 432.769125 33.660129 \n", "L 433.048125 28.883948 \n", "L 433.327125 28.943938 \n", "L 433.606125 33.831637 \n", "L 434.164125 54.748556 \n", "L 435.001125 90.260676 \n", "L 435.280125 96.450999 \n", "L 435.559125 97.946397 \n", "L 435.838125 94.535925 \n", "L 436.117125 86.70067 \n", "L 437.512125 31.357284 \n", "L 437.791125 28.327649 \n", "L 438.070125 30.212658 \n", "L 438.349125 36.746409 \n", "L 438.907125 59.54775 \n", "L 439.465125 84.319832 \n", "L 439.744125 93.057022 \n", "L 440.023125 97.578045 \n", "L 440.302125 97.24516 \n", "L 440.581125 92.105322 \n", "L 441.139125 70.880723 \n", "L 441.976125 35.6239 \n", "L 442.255125 29.675431 \n", "L 442.534125 28.451485 \n", "L 442.813125 32.124715 \n", "L 443.092125 40.176969 \n", "L 444.487125 95.269896 \n", "L 444.766125 98.033362 \n", "L 445.045125 95.878692 \n", "L 445.324125 89.109826 \n", "L 445.882125 66.065003 \n", "L 446.440125 41.443273 \n", "L 446.719125 32.911303 \n", "L 446.998125 28.647401 \n", "L 447.277125 29.253038 \n", "L 447.556125 34.642782 \n", "L 448.114125 56.165847 \n", "L 448.951125 91.152066 \n", "L 449.230125 96.856098 \n", "L 449.509125 97.808061 \n", "L 449.788125 93.873668 \n", "L 450.346125 74.176768 \n", "L 451.183125 37.852851 \n", "L 451.462125 30.789362 \n", "L 451.741125 28.293264 \n", "L 452.020125 30.71666 \n", "L 452.299125 37.717703 \n", "L 452.857125 60.996002 \n", "L 453.415125 85.45669 \n", "L 453.694125 93.779734 \n", "L 453.973125 97.784665 \n", "L 454.252125 96.90654 \n", "L 454.531125 91.26923 \n", "L 455.089125 69.457043 \n", "L 455.926125 34.756619 \n", "L 456.205125 29.299499 \n", "L 456.484125 28.619933 \n", "L 456.763125 32.81378 \n", "L 457.321125 52.851358 \n", "L 458.158125 88.977695 \n", "L 458.437125 95.809822 \n", "L 458.716125 98.037468 \n", "L 458.995125 95.346398 \n", "L 459.274125 88.116219 \n", "L 460.111125 51.658237 \n", "L 460.669125 32.215018 \n", "L 460.948125 28.470799 \n", "L 461.227125 29.62103 \n", "L 461.506125 35.503459 \n", "L 462.064125 57.595297 \n", "L 462.901125 91.994862 \n", "L 463.180125 97.202699 \n", "L 463.459125 97.609574 \n", "L 463.738125 93.158092 \n", "L 464.296125 72.788537 \n", "L 465.133125 36.875464 \n", "L 465.412125 30.277665 \n", "L 465.691125 28.319438 \n", "L 465.970125 31.277014 \n", "L 466.249125 38.733191 \n", "L 467.644125 94.449291 \n", "L 467.923125 97.931173 \n", "L 468.202125 96.509335 \n", "L 468.481125 90.384341 \n", "L 469.039125 68.022443 \n", "L 469.876125 33.938673 \n", "L 470.155125 28.98238 \n", "L 470.434125 28.848373 \n", "L 470.713125 33.555555 \n", "L 471.271125 54.248244 \n", "L 472.108125 89.932462 \n", "L 472.387125 96.293068 \n", "L 472.666125 97.981025 \n", "L 472.945125 94.758229 \n", "L 473.224125 87.07929 \n", "L 474.619125 31.572482 \n", "L 474.898125 28.354447 \n", "L 475.177125 30.047276 \n", "L 475.456125 36.412176 \n", "L 476.014125 59.034425 \n", "L 476.572125 83.906841 \n", "L 476.851125 92.787602 \n", "L 477.130125 97.4902 \n", "L 477.409125 97.35128 \n", "L 477.688125 92.39044 \n", "L 478.246125 71.383601 \n", "L 479.083125 35.943733 \n", "L 479.362125 29.823081 \n", "L 479.641125 28.406126 \n", "L 479.920125 31.892745 \n", "L 480.199125 39.79111 \n", "L 481.594125 95.064528 \n", "L 481.873125 98.017317 \n", "L 482.152125 96.054233 \n", "L 482.431125 89.452191 \n", "L 482.989125 66.579413 \n", "L 483.547125 41.84971 \n", "L 483.826125 33.171483 \n", "L 484.105125 28.724623 \n", "L 484.384125 29.136408 \n", "L 484.663125 34.348752 \n", "L 485.221125 55.66062 \n", "L 486.058125 90.840753 \n", "L 486.337125 96.718792 \n", "L 486.616125 97.86413 \n", "L 486.895125 94.115203 \n", "L 487.453125 74.665665 \n", "L 488.290125 38.210893 \n", "L 488.569125 30.98481 \n", "L 488.848125 28.298549 \n", "L 489.127125 30.531035 \n", "L 489.406125 37.367353 \n", "L 489.964125 60.48073 \n", "L 490.522125 85.056983 \n", "L 490.801125 93.528908 \n", "L 491.080125 97.718101 \n", "L 491.359125 97.033629 \n", "L 491.638125 91.572044 \n", "L 492.196125 69.964399 \n", "L 493.033125 35.059276 \n", "L 493.312125 29.4264 \n", "L 493.591125 28.553177 \n", "L 493.870125 32.562785 \n", "L 494.428125 52.359094 \n", "L 495.265125 88.627488 \n", "L 495.544125 95.624378 \n", "L 495.823125 98.042946 \n", "L 496.102125 95.542026 \n", "L 496.381125 88.4744 \n", "L 496.939125 65.130459 \n", "L 497.497125 40.718328 \n", "L 497.776125 32.456382 \n", "L 498.055125 28.526676 \n", "L 498.334125 29.483538 \n", "L 498.613125 35.191992 \n", "L 499.171125 57.086033 \n", "L 500.008125 91.700992 \n", "L 500.287125 97.086257 \n", "L 500.566125 97.686987 \n", "L 500.845125 93.41844 \n", "L 501.403125 73.283954 \n", "L 502.240125 37.217684 \n", "L 502.519125 30.453024 \n", "L 502.798125 28.3032 \n", "L 503.077125 31.071468 \n", "L 503.356125 38.367333 \n", "L 504.193125 74.876186 \n", "L 504.751125 94.217494 \n", "L 505.030125 97.886008 \n", "L 505.309125 96.657171 \n", "L 505.588125 90.704325 \n", "L 506.146125 68.533395 \n", "L 506.983125 34.223629 \n", "L 507.262125 29.088311 \n", "L 507.541125 28.760336 \n", "L 507.820125 33.28597 \n", "L 508.378125 53.74989 \n", "L 509.215125 89.598377 \n", "L 509.494125 96.127869 \n", "L 509.773125 98.008017 \n", "L 510.052125 94.973603 \n", "L 510.331125 87.452666 \n", "L 511.726125 31.794611 \n", "L 512.005125 28.388882 \n", "L 512.284125 29.88916 \n", "L 512.563125 36.083812 \n", "L 513.121125 58.522007 \n", "L 513.679125 83.489301 \n", "L 513.958125 92.511684 \n", "L 514.237125 97.394825 \n", "L 514.516125 97.449903 \n", "L 514.795125 92.669147 \n", "L 515.353125 71.884677 \n", "L 516.190125 36.269537 \n", "L 516.469125 29.978047 \n", "L 516.748125 28.368393 \n", "L 517.027125 31.667635 \n", "L 517.306125 39.410378 \n", "L 518.701125 94.852163 \n", "L 518.980125 97.993628 \n", "L 519.259125 96.222561 \n", "L 519.538125 89.788791 \n", "L 520.096125 67.093075 \n", "L 520.654125 42.260824 \n", "L 520.933125 33.438244 \n", "L 521.212125 28.8094 \n", "L 521.491125 29.027243 \n", "L 521.770125 34.061044 \n", "L 522.328125 55.157041 \n", "L 523.165125 90.52337 \n", "L 523.444125 96.574127 \n", "L 523.723125 97.912588 \n", "L 524.002125 94.34995 \n", "L 524.281125 86.388762 \n", "L 525.676125 31.187318 \n", "L 525.955125 28.311482 \n", "L 526.234125 30.35257 \n", "L 526.513125 37.022663 \n", "L 527.071125 59.966048 \n", "L 527.629125 84.652474 \n", "L 527.908125 93.271422 \n", "L 528.187125 97.643959 \n", "L 528.466125 97.153289 \n", "L 528.745125 91.868627 \n", "L 529.303125 70.470264 \n", "L 529.861125 38.973662 \n", "L 530.140125 30.012404 \n", "L 530.419125 28.816749 \n", "L 530.698125 35.666737 \n", "L 531.256125 65.57749 \n", "L 531.814125 93.363166 \n", "L 532.093125 98.021493 \n", "L 532.372125 94.516601 \n", "L 532.651125 83.669392 \n", "L 533.488125 37.244522 \n", "L 533.767125 29.325741 \n", "L 534.046125 29.333389 \n", "L 534.325125 37.265676 \n", "L 535.441125 94.530438 \n", "L 535.720125 98.020365 \n", "L 535.999125 93.347337 \n", "L 536.557125 65.545957 \n", "L 537.115125 35.647312 \n", "L 537.394125 28.811306 \n", "L 537.673125 30.022219 \n", "L 537.952125 38.996437 \n", "L 539.068125 95.538104 \n", "L 539.347125 97.84187 \n", "L 539.626125 92.024488 \n", "L 540.184125 63.059355 \n", "L 540.742125 34.190158 \n", "L 541.021125 28.471718 \n", "L 541.300125 30.879734 \n", "L 541.579125 40.85021 \n", "L 542.695125 96.381033 \n", "L 542.974125 97.486915 \n", "L 543.253125 90.554784 \n", "L 543.811125 60.573307 \n", "L 544.369125 32.880478 \n", "L 544.648125 28.308705 \n", "L 544.927125 31.901568 \n", "L 545.206125 42.817562 \n", "L 546.043125 89.215733 \n", "L 546.322125 97.054938 \n", "L 546.601125 96.957307 \n", "L 546.880125 88.945706 \n", "L 547.996125 31.724935 \n", "L 548.275125 28.323096 \n", "L 548.554125 33.082523 \n", "L 549.112125 60.975826 \n", "L 549.670125 90.802752 \n", "L 549.949125 97.556388 \n", "L 550.228125 96.255741 \n", "L 550.507125 87.205442 \n", "L 551.623125 30.729412 \n", "L 551.902125 28.514819 \n", "L 552.181125 34.416587 \n", "L 552.739125 63.462824 \n", "L 553.297125 92.249134 \n", "L 553.576125 97.882831 \n", "L 553.855125 95.385787 \n", "L 554.134125 85.342849 \n", "L 555.250125 29.898973 \n", "L 555.529125 28.882897 \n", "L 555.808125 35.896972 \n", "L 556.366125 65.948322 \n", "L 556.924125 93.547518 \n", "L 557.203125 98.032607 \n", "L 557.482125 94.351874 \n", "L 557.761125 83.367405 \n", "L 558.598125 36.997251 \n", "L 558.877125 29.237846 \n", "L 559.156125 29.425458 \n", "L 559.435125 37.516144 \n", "L 560.551125 94.691297 \n", "L 560.830125 98.004952 \n", "L 561.109125 93.159261 \n", "L 561.667125 65.17483 \n", "L 562.225125 35.42047 \n", "L 562.504125 28.749395 \n", "L 562.783125 30.139739 \n", "L 563.062125 39.265862 \n", "L 564.178125 95.674651 \n", "L 564.457125 97.800007 \n", "L 564.736125 91.81402 \n", "L 565.294125 62.687506 \n", "L 565.852125 33.09497 \n", "L 566.131125 28.298748 \n", "L 566.410125 32.480218 \n", "L 566.968125 61.43561 \n", "L 567.526125 92.060413 \n", "L 567.805125 97.92754 \n", "L 568.084125 94.845288 \n", "L 568.363125 83.607233 \n", "L 569.200125 35.573093 \n", "L 569.479125 28.658695 \n", "L 569.758125 30.629313 \n", "L 570.037125 40.97758 \n", "L 570.874125 89.354739 \n", "L 571.153125 97.288555 \n", "L 571.432125 96.437508 \n", "L 571.711125 87.020715 \n", "L 572.827125 29.574145 \n", "L 573.106125 29.302191 \n", "L 573.385125 37.749576 \n", "L 574.501125 96.100327 \n", "L 574.780125 97.494185 \n", "L 575.059125 90.050238 \n", "L 576.175125 31.030363 \n", "L 576.454125 28.520215 \n", "L 576.733125 34.930738 \n", "L 577.291125 66.039994 \n", "L 577.849125 94.381984 \n", "L 578.128125 97.99831 \n", "L 578.407125 92.647036 \n", "L 578.965125 62.506903 \n", "L 579.523125 33.003908 \n", "L 579.802125 28.295973 \n", "L 580.081125 32.566442 \n", "L 580.639125 61.616037 \n", "L 581.197125 92.161187 \n", "L 581.476125 97.941767 \n", "L 581.755125 94.769306 \n", "L 582.034125 83.460604 \n", "L 582.871125 35.463012 \n", "L 583.150125 28.633073 \n", "L 583.429125 30.694747 \n", "L 583.708125 41.117223 \n", "L 584.545125 89.473683 \n", "L 584.824125 97.325468 \n", "L 584.824125 97.325468 \n", "\" style=\"fill:none;stroke:#008000;stroke-linecap:square;\"/>\n", "   </g>\n", "   <g id=\"line2d_3\">\n", "    <path clip-path=\"url(#pdeef12bd97)\" d=\"M 27.103125 65.875435 \n", "L 27.382125 45.751908 \n", "L 27.661125 32.319738 \n", "L 27.940125 28.334534 \n", "L 28.219125 36.169352 \n", "L 29.335125 97.763987 \n", "L 29.614125 93.928348 \n", "L 29.893125 80.115399 \n", "L 30.451125 41.213917 \n", "L 30.730125 30.402582 \n", "L 31.009125 29.744764 \n", "L 31.288125 40.701833 \n", "L 32.125125 93.77013 \n", "L 32.404125 97.575679 \n", "L 32.683125 90.808913 \n", "L 33.799125 28.548667 \n", "L 34.078125 31.748098 \n", "L 34.636125 65.184304 \n", "L 35.194125 95.980324 \n", "L 35.473125 96.686373 \n", "L 35.752125 87.021556 \n", "L 36.589125 33.526027 \n", "L 36.868125 28.426423 \n", "L 37.147125 34.825774 \n", "L 38.263125 97.215625 \n", "L 38.542125 94.863573 \n", "L 38.821125 82.139085 \n", "L 39.379125 43.267366 \n", "L 39.658125 31.002759 \n", "L 39.937125 29.08445 \n", "L 40.216125 38.789155 \n", "L 41.053125 92.518661 \n", "L 41.332125 97.843126 \n", "L 41.611125 92.454284 \n", "L 42.727125 29.450502 \n", "L 43.006125 30.813876 \n", "L 43.285125 43.106714 \n", "L 44.122125 95.149743 \n", "L 44.401125 97.243437 \n", "L 44.680125 88.931414 \n", "L 45.517125 34.658185 \n", "L 45.796125 28.173623 \n", "L 46.075125 33.208028 \n", "L 46.633125 68.63179 \n", "L 46.912125 86.873855 \n", "L 47.191125 96.876973 \n", "L 47.470125 95.976489 \n", "L 47.749125 84.321699 \n", "L 48.586125 31.626802 \n", "L 48.865125 28.604128 \n", "L 49.144125 36.898336 \n", "L 49.981125 90.796636 \n", "L 50.260125 97.645234 \n", "L 50.539125 93.365472 \n", "L 50.818125 79.51676 \n", "L 51.376125 40.542324 \n", "L 51.655125 30.105349 \n", "L 51.934125 29.992589 \n", "L 52.213125 41.530597 \n", "L 53.050125 93.93311 \n", "L 53.329125 97.655982 \n", "L 53.608125 90.288084 \n", "L 54.724125 28.917124 \n", "L 55.003125 32.462727 \n", "L 55.282125 46.358965 \n", "L 55.840125 84.556145 \n", "L 56.119125 96.464619 \n", "L 56.398125 96.450218 \n", "L 56.677125 86.104801 \n", "L 57.514125 33.117574 \n", "L 57.793125 28.897024 \n", "L 58.072125 32.641071 \n", "L 58.630125 64.56594 \n", "L 59.188125 94.692959 \n", "L 59.467125 97.534315 \n", "L 59.746125 91.464269 \n", "L 60.862125 30.12937 \n", "L 61.141125 30.020446 \n", "L 61.420125 40.246863 \n", "L 62.257125 94.730114 \n", "L 62.536125 97.534108 \n", "L 62.815125 88.564671 \n", "L 63.652125 33.852347 \n", "L 63.931125 28.442778 \n", "L 64.210125 35.198645 \n", "L 65.047125 91.345489 \n", "L 65.326125 97.524545 \n", "L 65.605125 92.920729 \n", "L 66.163125 56.367128 \n", "L 66.442125 38.272362 \n", "L 66.721125 28.659141 \n", "L 67.000125 31.742145 \n", "L 67.279125 45.554619 \n", "L 67.837125 85.637009 \n", "L 68.116125 96.488948 \n", "L 68.395125 96.02888 \n", "L 68.674125 84.191035 \n", "L 69.511125 31.040264 \n", "L 69.790125 29.836511 \n", "L 70.069125 38.967607 \n", "L 70.906125 93.829235 \n", "L 71.185125 97.56688 \n", "L 71.464125 89.717395 \n", "L 72.301125 34.400235 \n", "L 72.580125 28.261001 \n", "L 72.859125 34.102118 \n", "L 73.975125 97.645718 \n", "L 74.254125 93.912919 \n", "L 74.533125 79.205903 \n", "L 75.091125 39.477967 \n", "L 75.370125 28.991861 \n", "L 75.649125 30.793554 \n", "L 75.928125 44.043828 \n", "L 76.486125 83.97252 \n", "L 76.765125 96.021425 \n", "L 77.044125 96.560946 \n", "L 77.323125 85.76642 \n", "L 78.160125 31.503947 \n", "L 78.439125 28.5543 \n", "L 78.718125 38.237553 \n", "L 79.555125 93.065577 \n", "L 79.834125 97.779965 \n", "L 80.113125 90.893426 \n", "L 80.950125 35.689624 \n", "L 81.229125 28.323064 \n", "L 81.508125 33.436269 \n", "L 82.066125 69.985619 \n", "L 82.345125 88.433219 \n", "L 82.624125 97.274002 \n", "L 82.903125 94.775156 \n", "L 83.182125 80.83167 \n", "L 83.740125 40.703839 \n", "L 84.019125 29.602756 \n", "L 84.298125 30.372816 \n", "L 84.577125 42.469717 \n", "L 85.414125 95.608481 \n", "L 85.693125 96.977164 \n", "L 85.972125 87.202403 \n", "L 86.809125 32.352432 \n", "L 87.088125 28.637801 \n", "L 87.367125 37.056805 \n", "L 88.204125 91.955109 \n", "L 88.483125 97.826155 \n", "L 88.762125 92.04592 \n", "L 89.878125 28.61611 \n", "L 90.157125 32.320631 \n", "L 90.715125 68.372075 \n", "L 90.994125 86.764064 \n", "L 91.273125 97.043089 \n", "L 91.552125 95.361283 \n", "L 91.831125 82.475571 \n", "L 92.389125 42.204919 \n", "L 92.668125 30.334431 \n", "L 92.947125 29.987194 \n", "L 93.226125 40.849068 \n", "L 94.063125 94.75184 \n", "L 94.342125 97.172139 \n", "L 94.621125 88.298232 \n", "L 95.458125 33.557607 \n", "L 95.737125 28.824474 \n", "L 96.016125 35.371309 \n", "L 96.853125 91.300348 \n", "L 97.132125 97.797946 \n", "L 97.411125 93.045855 \n", "L 97.969125 56.282085 \n", "L 98.248125 37.76721 \n", "L 98.527125 28.728808 \n", "L 98.806125 31.799625 \n", "L 99.085125 45.887433 \n", "L 99.643125 85.707695 \n", "L 99.922125 96.695523 \n", "L 100.201125 95.969771 \n", "L 100.480125 83.903239 \n", "L 101.317125 30.599795 \n", "L 101.596125 29.711171 \n", "L 101.875125 40.180178 \n", "L 102.712125 93.93917 \n", "L 102.991125 97.488133 \n", "L 103.270125 89.522917 \n", "L 104.107125 34.372996 \n", "L 104.386125 28.086447 \n", "L 104.665125 34.707187 \n", "L 105.781125 97.686936 \n", "L 106.060125 93.862457 \n", "L 106.618125 57.774962 \n", "L 106.897125 39.014281 \n", "L 107.176125 29.084581 \n", "L 107.455125 30.634547 \n", "L 107.734125 44.669108 \n", "L 108.292125 84.508389 \n", "L 108.571125 96.110579 \n", "L 108.850125 96.848546 \n", "L 109.129125 85.120312 \n", "L 109.966125 31.081412 \n", "L 110.245125 28.477184 \n", "L 110.524125 38.363721 \n", "L 111.361125 93.373791 \n", "L 111.640125 97.771661 \n", "L 111.919125 90.804835 \n", "L 112.756125 34.711669 \n", "L 113.035125 28.575387 \n", "L 113.314125 33.698557 \n", "L 114.430125 97.477135 \n", "L 114.709125 94.41653 \n", "L 114.988125 80.024823 \n", "L 115.546125 40.72999 \n", "L 115.825125 29.642432 \n", "L 116.104125 30.132563 \n", "L 116.383125 43.080096 \n", "L 117.220125 95.70279 \n", "L 117.499125 96.607754 \n", "L 117.778125 86.976645 \n", "L 118.615125 31.896451 \n", "L 118.894125 28.601501 \n", "L 119.173125 37.249597 \n", "L 120.010125 92.423235 \n", "L 120.289125 97.760626 \n", "L 120.568125 91.828559 \n", "L 121.684125 28.728381 \n", "L 121.963125 32.555034 \n", "L 122.521125 68.608399 \n", "L 122.800125 87.487216 \n", "L 123.079125 97.371393 \n", "L 123.358125 95.172125 \n", "L 123.637125 81.728032 \n", "L 124.195125 41.562395 \n", "L 124.474125 29.919651 \n", "L 124.753125 29.854554 \n", "L 125.032125 41.531165 \n", "L 125.869125 95.16463 \n", "L 126.148125 97.198514 \n", "L 126.427125 87.643154 \n", "L 127.264125 33.033694 \n", "L 127.543125 28.588064 \n", "L 127.822125 36.057159 \n", "L 128.659125 91.452525 \n", "L 128.938125 97.865517 \n", "L 129.217125 92.574133 \n", "L 129.775125 55.287917 \n", "L 130.054125 37.717469 \n", "L 130.333125 28.945393 \n", "L 130.612125 31.770256 \n", "L 131.170125 66.92071 \n", "L 131.449125 86.059635 \n", "L 131.728125 96.759054 \n", "L 132.007125 96.026915 \n", "L 132.286125 83.557069 \n", "L 132.844125 43.788545 \n", "L 133.123125 31.783703 \n", "L 133.402125 29.04079 \n", "L 133.681125 33.3045 \n", "L 134.239125 61.447644 \n", "L 134.797125 91.791168 \n", "L 135.076125 97.618986 \n", "L 135.355125 95.362114 \n", "L 135.634125 85.81744 \n", "L 136.471125 38.088155 \n", "L 136.750125 29.191072 \n", "L 137.029125 29.294207 \n", "L 137.308125 36.995933 \n", "L 138.424125 95.123626 \n", "L 138.703125 97.678239 \n", "L 138.982125 92.396683 \n", "L 139.540125 62.679771 \n", "L 140.098125 33.599982 \n", "L 140.377125 28.232861 \n", "L 140.656125 31.24885 \n", "L 141.214125 57.657742 \n", "L 141.772125 89.542706 \n", "L 142.051125 97.045029 \n", "L 142.330125 96.62601 \n", "L 142.609125 88.313099 \n", "L 143.725125 30.323226 \n", "L 144.004125 28.88831 \n", "L 144.283125 34.268898 \n", "L 145.399125 93.731971 \n", "L 145.678125 97.944656 \n", "L 145.957125 94.054673 \n", "L 146.236125 82.538686 \n", "L 147.073125 35.603674 \n", "L 147.352125 28.808875 \n", "L 147.631125 29.782868 \n", "L 147.910125 39.654472 \n", "L 149.026125 96.365309 \n", "L 149.305125 97.266191 \n", "L 149.584125 90.78813 \n", "L 150.700125 31.852967 \n", "L 150.979125 28.058273 \n", "L 151.258125 32.544465 \n", "L 151.816125 60.893305 \n", "L 152.374125 91.972035 \n", "L 152.653125 97.535543 \n", "L 152.932125 95.450087 \n", "L 153.211125 85.887376 \n", "L 154.048125 38.185847 \n", "L 154.327125 29.708611 \n", "L 154.606125 29.284596 \n", "L 154.885125 36.89294 \n", "L 156.001125 95.200547 \n", "L 156.280125 97.844263 \n", "L 156.559125 92.822707 \n", "L 157.117125 62.780848 \n", "L 157.675125 33.964086 \n", "L 157.954125 28.422329 \n", "L 158.233125 31.381851 \n", "L 158.512125 41.859105 \n", "L 159.349125 89.109569 \n", "L 159.628125 97.198984 \n", "L 159.907125 96.626585 \n", "L 160.186125 88.565829 \n", "L 161.302125 30.932657 \n", "L 161.581125 28.685312 \n", "L 161.860125 34.937258 \n", "L 162.418125 64.545912 \n", "L 162.976125 93.499179 \n", "L 163.255125 98.218415 \n", "L 163.534125 94.473098 \n", "L 163.813125 83.516839 \n", "L 164.650125 35.732598 \n", "L 164.929125 28.688355 \n", "L 165.208125 29.828316 \n", "L 165.487125 39.317191 \n", "L 166.603125 96.062656 \n", "L 166.882125 97.479518 \n", "L 167.161125 90.619873 \n", "L 167.719125 59.802516 \n", "L 168.277125 32.237761 \n", "L 168.556125 28.230501 \n", "L 168.835125 33.054461 \n", "L 169.114125 44.765001 \n", "L 169.951125 91.47807 \n", "L 170.230125 97.64213 \n", "L 170.509125 95.57943 \n", "L 170.788125 86.017467 \n", "L 171.904125 29.571241 \n", "L 172.183125 28.770097 \n", "L 172.462125 36.967809 \n", "L 173.578125 95.226676 \n", "L 173.857125 97.69604 \n", "L 174.136125 93.016904 \n", "L 174.694125 63.33466 \n", "L 175.252125 33.931774 \n", "L 175.531125 28.437687 \n", "L 175.810125 31.050192 \n", "L 176.089125 41.922364 \n", "L 176.926125 89.426966 \n", "L 177.205125 97.042626 \n", "L 177.484125 96.894413 \n", "L 177.763125 88.795963 \n", "L 178.879125 30.79719 \n", "L 179.158125 28.601992 \n", "L 179.437125 34.376482 \n", "L 179.995125 64.470772 \n", "L 180.553125 93.253091 \n", "L 180.832125 97.711705 \n", "L 181.111125 94.592706 \n", "L 181.390125 83.276589 \n", "L 182.227125 36.193332 \n", "L 182.506125 28.827133 \n", "L 182.785125 30.244093 \n", "L 183.064125 39.137919 \n", "L 184.180125 96.187998 \n", "L 184.459125 97.565614 \n", "L 184.738125 90.799567 \n", "L 185.296125 59.879643 \n", "L 185.854125 32.357901 \n", "L 186.133125 28.577948 \n", "L 186.412125 32.819577 \n", "L 186.970125 60.128813 \n", "L 187.528125 91.357486 \n", "L 187.807125 97.45396 \n", "L 188.086125 95.687096 \n", "L 188.365125 85.956528 \n", "L 189.202125 38.30444 \n", "L 189.481125 29.642536 \n", "L 189.760125 29.181327 \n", "L 190.039125 36.695052 \n", "L 191.155125 94.875636 \n", "L 191.434125 97.897771 \n", "L 191.713125 92.836491 \n", "L 192.271125 63.800432 \n", "L 192.829125 34.385944 \n", "L 193.108125 28.454261 \n", "L 193.387125 30.992453 \n", "L 193.666125 41.576479 \n", "L 194.503125 89.133543 \n", "L 194.782125 97.087239 \n", "L 195.061125 97.012232 \n", "L 195.340125 89.053937 \n", "L 196.456125 30.913072 \n", "L 196.735125 28.58343 \n", "L 197.014125 34.549201 \n", "L 197.572125 63.853038 \n", "L 198.130125 93.414679 \n", "L 198.409125 97.984649 \n", "L 198.688125 94.349381 \n", "L 198.967125 83.820356 \n", "L 199.804125 36.321683 \n", "L 200.083125 28.82289 \n", "L 200.362125 29.860037 \n", "L 200.641125 38.761304 \n", "L 201.757125 96.136449 \n", "L 202.036125 97.346587 \n", "L 202.315125 91.034874 \n", "L 203.431125 32.471816 \n", "L 203.710125 28.043694 \n", "L 203.989125 32.885869 \n", "L 204.547125 60.723014 \n", "L 205.105125 91.476531 \n", "L 205.384125 97.591802 \n", "L 205.663125 95.956856 \n", "L 205.942125 86.424836 \n", "L 207.058125 29.9203 \n", "L 207.337125 28.837999 \n", "L 207.616125 36.510802 \n", "L 208.732125 94.835943 \n", "L 209.011125 97.886272 \n", "L 209.290125 92.901856 \n", "L 209.569125 80.887321 \n", "L 210.406125 34.117618 \n", "L 210.685125 28.456787 \n", "L 210.964125 30.807568 \n", "L 211.243125 41.720413 \n", "L 212.080125 88.796174 \n", "L 212.359125 97.02909 \n", "L 212.638125 96.896404 \n", "L 212.917125 88.844332 \n", "L 214.033125 30.761414 \n", "L 214.312125 28.492825 \n", "L 214.591125 34.558653 \n", "L 215.149125 64.099066 \n", "L 215.707125 93.262762 \n", "L 215.986125 97.861659 \n", "L 216.265125 94.521797 \n", "L 216.544125 83.824335 \n", "L 217.381125 36.021748 \n", "L 217.660125 28.745946 \n", "L 217.939125 30.184817 \n", "L 218.218125 38.740204 \n", "L 219.334125 95.996673 \n", "L 219.613125 97.448009 \n", "L 219.892125 91.11138 \n", "L 220.450125 60.577237 \n", "L 221.008125 32.268925 \n", "L 221.287125 28.499026 \n", "L 221.566125 32.768169 \n", "L 222.124125 59.843489 \n", "L 222.682125 91.334979 \n", "L 222.961125 97.376521 \n", "L 223.240125 96.009702 \n", "L 223.519125 86.573266 \n", "L 224.635125 29.500543 \n", "L 224.914125 28.972356 \n", "L 225.193125 35.831104 \n", "L 226.309125 94.78006 \n", "L 226.588125 97.927139 \n", "L 226.867125 92.908685 \n", "L 227.425125 64.088024 \n", "L 227.983125 34.614547 \n", "L 228.262125 28.674952 \n", "L 228.541125 30.989585 \n", "L 228.820125 41.573307 \n", "L 229.657125 88.857374 \n", "L 229.936125 96.890237 \n", "L 230.215125 96.877571 \n", "L 230.494125 88.970384 \n", "L 231.610125 31.381869 \n", "L 231.889125 28.350463 \n", "L 232.168125 34.025081 \n", "L 232.726125 63.717818 \n", "L 233.284125 93.065265 \n", "L 233.563125 97.859698 \n", "L 233.842125 94.865138 \n", "L 234.121125 83.827313 \n", "L 234.958125 36.666392 \n", "L 235.237125 28.998941 \n", "L 235.516125 29.693063 \n", "L 235.795125 38.814444 \n", "L 236.911125 95.817326 \n", "L 237.190125 97.397824 \n", "L 237.469125 91.377944 \n", "L 238.027125 60.704484 \n", "L 238.585125 32.625305 \n", "L 238.864125 27.992302 \n", "L 239.143125 31.959884 \n", "L 239.701125 58.259707 \n", "L 240.259125 88.943804 \n", "L 240.538125 97.027471 \n", "L 240.817125 97.304835 \n", "L 241.096125 90.510583 \n", "L 241.654125 60.519082 \n", "L 242.212125 32.831932 \n", "L 242.491125 28.07298 \n", "L 242.770125 31.317658 \n", "L 243.049125 41.218773 \n", "L 243.886125 87.187406 \n", "L 244.165125 96.240722 \n", "L 244.444125 97.453006 \n", "L 244.723125 91.757903 \n", "L 245.281125 62.092451 \n", "L 245.839125 34.385468 \n", "L 246.118125 28.471994 \n", "L 246.397125 30.746992 \n", "L 246.676125 39.873406 \n", "L 247.792125 95.646792 \n", "L 248.071125 97.982396 \n", "L 248.350125 92.613834 \n", "L 248.908125 64.070225 \n", "L 249.466125 35.335951 \n", "L 249.745125 29.07457 \n", "L 250.024125 30.001301 \n", "L 250.303125 39.053242 \n", "L 251.419125 95.009131 \n", "L 251.698125 97.724462 \n", "L 251.977125 93.406086 \n", "L 252.256125 82.110312 \n", "L 253.093125 36.448257 \n", "L 253.372125 29.245154 \n", "L 253.651125 29.585859 \n", "L 253.930125 37.331409 \n", "L 255.046125 94.199835 \n", "L 255.325125 97.935158 \n", "L 255.604125 94.423201 \n", "L 255.883125 83.365378 \n", "L 256.720125 37.528898 \n", "L 256.999125 29.556443 \n", "L 257.278125 29.205187 \n", "L 257.557125 35.57101 \n", "L 258.115125 65.630975 \n", "L 258.673125 93.589822 \n", "L 258.952125 97.90331 \n", "L 259.231125 94.928034 \n", "L 259.510125 84.965366 \n", "L 260.626125 30.067317 \n", "L 260.905125 28.987194 \n", "L 261.184125 34.956284 \n", "L 262.579125 97.649392 \n", "L 262.858125 95.693594 \n", "L 263.137125 86.769307 \n", "L 263.974125 40.521017 \n", "L 264.253125 31.01416 \n", "L 264.532125 28.918277 \n", "L 264.811125 33.966845 \n", "L 265.369125 61.822784 \n", "L 265.927125 91.377393 \n", "L 266.206125 97.583787 \n", "L 266.485125 96.139628 \n", "L 266.764125 87.761826 \n", "L 267.880125 31.193361 \n", "L 268.159125 28.290709 \n", "L 268.438125 33.14239 \n", "L 268.996125 60.325395 \n", "L 269.554125 90.277367 \n", "L 269.833125 97.236014 \n", "L 270.112125 96.683116 \n", "L 270.391125 89.122879 \n", "L 271.507125 32.204097 \n", "L 271.786125 28.29531 \n", "L 272.065125 32.194018 \n", "L 272.344125 43.000156 \n", "L 273.181125 88.936411 \n", "L 273.460125 96.583597 \n", "L 273.739125 97.051953 \n", "L 274.018125 90.434207 \n", "L 275.134125 33.065487 \n", "L 275.413125 28.292423 \n", "L 275.692125 31.566523 \n", "L 275.971125 41.870624 \n", "L 277.087125 96.464582 \n", "L 277.366125 97.291904 \n", "L 277.645125 91.53168 \n", "L 278.203125 62.27356 \n", "L 278.761125 34.183448 \n", "L 279.040125 28.233 \n", "L 279.319125 30.978033 \n", "L 279.598125 40.194304 \n", "L 280.714125 96.004182 \n", "L 280.993125 97.608875 \n", "L 281.272125 92.534274 \n", "L 281.830125 64.049781 \n", "L 282.388125 34.946844 \n", "L 282.667125 28.701452 \n", "L 282.946125 30.312884 \n", "L 283.225125 38.747132 \n", "L 284.341125 95.359074 \n", "L 284.620125 97.915149 \n", "L 284.899125 93.284412 \n", "L 285.178125 81.754732 \n", "L 286.015125 36.496319 \n", "L 286.294125 29.061189 \n", "L 286.573125 29.640643 \n", "L 286.852125 38.62994 \n", "L 287.968125 96.717552 \n", "L 288.247125 97.038999 \n", "L 288.526125 88.922206 \n", "L 289.642125 30.159269 \n", "L 289.921125 28.859762 \n", "L 290.200125 36.292892 \n", "L 291.316125 95.555805 \n", "L 291.595125 97.703441 \n", "L 291.874125 91.3445 \n", "L 292.990125 31.300732 \n", "L 293.269125 28.24373 \n", "L 293.548125 34.343141 \n", "L 294.664125 94.442618 \n", "L 294.943125 97.529787 \n", "L 295.222125 92.582927 \n", "L 295.780125 61.987153 \n", "L 296.338125 32.577021 \n", "L 296.617125 28.458224 \n", "L 296.896125 32.801137 \n", "L 297.454125 62.445212 \n", "L 298.012125 93.191025 \n", "L 298.291125 97.901652 \n", "L 298.570125 94.299631 \n", "L 298.849125 82.330232 \n", "L 299.686125 33.902522 \n", "L 299.965125 28.631411 \n", "L 300.244125 31.548807 \n", "L 300.523125 42.595883 \n", "L 301.360125 91.30344 \n", "L 301.639125 97.546405 \n", "L 301.918125 95.564873 \n", "L 302.197125 84.813944 \n", "L 303.034125 36.208907 \n", "L 303.313125 28.652396 \n", "L 303.592125 30.455564 \n", "L 303.871125 40.579421 \n", "L 304.708125 89.659114 \n", "L 304.987125 97.014448 \n", "L 305.266125 96.469652 \n", "L 305.545125 87.215777 \n", "L 306.382125 38.463803 \n", "L 306.661125 29.881308 \n", "L 306.940125 29.339233 \n", "L 307.219125 38.06631 \n", "L 308.335125 96.44043 \n", "L 308.614125 97.134912 \n", "L 308.893125 89.319627 \n", "L 310.009125 30.211106 \n", "L 310.288125 28.974316 \n", "L 310.567125 36.110137 \n", "L 311.683125 95.354664 \n", "L 311.962125 97.436817 \n", "L 312.241125 91.344428 \n", "L 312.799125 59.52792 \n", "L 313.357125 31.349181 \n", "L 313.636125 28.728454 \n", "L 313.915125 34.547497 \n", "L 314.473125 64.970965 \n", "L 315.031125 94.549033 \n", "L 315.310125 97.760934 \n", "L 315.589125 93.160968 \n", "L 316.147125 62.792895 \n", "L 316.705125 32.783092 \n", "L 316.984125 28.297489 \n", "L 317.263125 32.582164 \n", "L 317.542125 44.801642 \n", "L 318.379125 92.969217 \n", "L 318.658125 97.930559 \n", "L 318.937125 94.715296 \n", "L 319.495125 66.292189 \n", "L 320.053125 34.380609 \n", "L 320.332125 28.684228 \n", "L 320.611125 31.241357 \n", "L 320.890125 42.341052 \n", "L 321.727125 90.968026 \n", "L 322.006125 97.829997 \n", "L 322.285125 95.557777 \n", "L 322.564125 85.221404 \n", "L 323.401125 36.346434 \n", "L 323.680125 29.101327 \n", "L 323.959125 29.866542 \n", "L 324.238125 40.123123 \n", "L 325.354125 97.037346 \n", "L 325.633125 96.438828 \n", "L 325.912125 87.666665 \n", "L 327.028125 29.390812 \n", "L 327.307125 29.549296 \n", "L 327.586125 37.748242 \n", "L 328.702125 96.418086 \n", "L 328.981125 96.904693 \n", "L 329.260125 89.910707 \n", "L 330.376125 30.449496 \n", "L 330.655125 28.888979 \n", "L 330.934125 35.920032 \n", "L 331.492125 67.206909 \n", "L 332.050125 95.371999 \n", "L 332.329125 97.904306 \n", "L 332.608125 91.195909 \n", "L 333.166125 60.334915 \n", "L 333.724125 31.68428 \n", "L 334.003125 28.653585 \n", "L 334.282125 34.213304 \n", "L 334.840125 64.236213 \n", "L 335.398125 93.948737 \n", "L 335.677125 97.742439 \n", "L 335.956125 93.122331 \n", "L 336.514125 62.961279 \n", "L 337.072125 32.961765 \n", "L 337.351125 28.137982 \n", "L 337.630125 32.305612 \n", "L 338.188125 61.533433 \n", "L 338.746125 92.372272 \n", "L 339.025125 97.788568 \n", "L 339.304125 94.437508 \n", "L 339.583125 83.155275 \n", "L 340.420125 34.725895 \n", "L 340.699125 28.564186 \n", "L 340.978125 31.808395 \n", "L 341.257125 42.17675 \n", "L 342.094125 90.781759 \n", "L 342.373125 97.592811 \n", "L 342.652125 95.621429 \n", "L 342.931125 85.251386 \n", "L 343.768125 36.923055 \n", "L 344.047125 28.853622 \n", "L 344.326125 29.926526 \n", "L 344.605125 39.497164 \n", "L 345.442125 88.605869 \n", "L 345.721125 96.853691 \n", "L 346.000125 96.675286 \n", "L 346.279125 87.882147 \n", "L 347.395125 29.737185 \n", "L 347.674125 29.160977 \n", "L 347.953125 37.599416 \n", "L 349.069125 96.105634 \n", "L 349.348125 97.048615 \n", "L 349.627125 89.72775 \n", "L 350.743125 30.408367 \n", "L 351.022125 28.744164 \n", "L 351.301125 35.605882 \n", "L 352.417125 95.120224 \n", "L 352.696125 97.52723 \n", "L 352.975125 91.919823 \n", "L 353.533125 60.78315 \n", "L 354.091125 31.906028 \n", "L 354.370125 28.106275 \n", "L 354.649125 33.746164 \n", "L 355.207125 64.172472 \n", "L 355.765125 94.046112 \n", "L 356.044125 97.914297 \n", "L 356.323125 93.482788 \n", "L 356.881125 63.129643 \n", "L 357.439125 33.840816 \n", "L 357.718125 28.200321 \n", "L 357.997125 32.392248 \n", "L 358.276125 44.301236 \n", "L 359.113125 92.132312 \n", "L 359.392125 97.732482 \n", "L 359.671125 94.766354 \n", "L 359.950125 83.872981 \n", "L 360.787125 35.599655 \n", "L 361.066125 28.719457 \n", "L 361.345125 30.574068 \n", "L 361.903125 58.076902 \n", "L 362.461125 90.74042 \n", "L 362.740125 97.43604 \n", "L 363.019125 95.730905 \n", "L 363.298125 85.640456 \n", "L 364.135125 37.299843 \n", "L 364.414125 29.216996 \n", "L 364.693125 29.839553 \n", "L 364.972125 39.08003 \n", "L 366.088125 96.869121 \n", "L 366.367125 96.747292 \n", "L 366.646125 88.284065 \n", "L 367.762125 29.811331 \n", "L 368.041125 29.015563 \n", "L 368.320125 37.491687 \n", "L 369.436125 96.197656 \n", "L 369.715125 97.202998 \n", "L 369.994125 90.441512 \n", "L 371.110125 31.005141 \n", "L 371.389125 28.497812 \n", "L 371.668125 35.233769 \n", "L 372.784125 95.019483 \n", "L 373.063125 97.609999 \n", "L 373.342125 91.866442 \n", "L 373.900125 61.120326 \n", "L 374.458125 31.915257 \n", "L 374.737125 28.264235 \n", "L 375.016125 33.39933 \n", "L 375.574125 63.58905 \n", "L 376.132125 93.728067 \n", "L 376.411125 97.598487 \n", "L 376.690125 93.794283 \n", "L 377.248125 64.122067 \n", "L 377.806125 33.675192 \n", "L 378.085125 28.218357 \n", "L 378.364125 31.737651 \n", "L 378.922125 60.95727 \n", "L 379.480125 92.128788 \n", "L 379.759125 97.722846 \n", "L 380.038125 96.793205 \n", "L 380.317125 90.714282 \n", "L 380.596125 81.825666 \n", "L 381.433125 43.598355 \n", "L 381.712125 34.276222 \n", "L 381.991125 29.170677 \n", "L 382.270125 29.19328 \n", "L 382.549125 33.083851 \n", "L 383.107125 53.904349 \n", "L 383.944125 89.91087 \n", "L 384.223125 96.159852 \n", "L 384.502125 97.789999 \n", "L 384.781125 95.313972 \n", "L 385.060125 87.709515 \n", "L 386.455125 31.875258 \n", "L 386.734125 27.910547 \n", "L 387.013125 29.726858 \n", "L 387.292125 36.221387 \n", "L 387.850125 58.782488 \n", "L 388.687125 92.746712 \n", "L 388.966125 97.347802 \n", "L 389.245125 97.323954 \n", "L 389.524125 92.791859 \n", "L 389.803125 83.86049 \n", "L 390.919125 36.503994 \n", "L 391.198125 29.841563 \n", "L 391.477125 28.457434 \n", "L 391.756125 31.664477 \n", "L 392.035125 39.299112 \n", "L 393.430125 95.013253 \n", "L 393.709125 97.827384 \n", "L 393.988125 96.088908 \n", "L 394.267125 89.856705 \n", "L 394.825125 66.177503 \n", "L 395.662125 33.348795 \n", "L 395.941125 29.076376 \n", "L 396.220125 29.327876 \n", "L 396.499125 34.11489 \n", "L 397.057125 55.432072 \n", "L 397.894125 90.704249 \n", "L 398.173125 96.569416 \n", "L 398.452125 97.769418 \n", "L 398.731125 94.645482 \n", "L 399.010125 86.969807 \n", "L 400.405125 30.940519 \n", "L 400.684125 28.247803 \n", "L 400.963125 30.217679 \n", "L 401.242125 37.228538 \n", "L 401.800125 59.912084 \n", "L 402.358125 84.930319 \n", "L 402.637125 93.451802 \n", "L 402.916125 97.643552 \n", "L 403.195125 97.000892 \n", "L 403.474125 91.953515 \n", "L 403.753125 82.918512 \n", "L 404.869125 35.592081 \n", "L 405.148125 29.847973 \n", "L 405.427125 28.545464 \n", "L 405.706125 32.340057 \n", "L 406.264125 51.81955 \n", "L 407.101125 88.661929 \n", "L 407.380125 95.415467 \n", "L 407.659125 97.791414 \n", "L 407.938125 95.777163 \n", "L 408.217125 88.841282 \n", "L 408.775125 65.134422 \n", "L 409.612125 32.699839 \n", "L 409.891125 28.802474 \n", "L 410.170125 29.352825 \n", "L 410.449125 35.218441 \n", "L 411.007125 56.80786 \n", "L 411.844125 91.798003 \n", "L 412.123125 96.916067 \n", "L 412.402125 97.451199 \n", "L 412.681125 93.931172 \n", "L 412.960125 85.475377 \n", "L 414.076125 37.414547 \n", "L 414.355125 30.729315 \n", "L 414.634125 28.196787 \n", "L 414.913125 31.188836 \n", "L 415.192125 38.099216 \n", "L 416.029125 74.286407 \n", "L 416.587125 94.213931 \n", "L 416.866125 97.696121 \n", "L 417.145125 96.704968 \n", "L 417.424125 91.317439 \n", "L 417.982125 68.375075 \n", "L 418.819125 34.01699 \n", "L 419.098125 29.21747 \n", "L 419.377125 28.547548 \n", "L 419.656125 32.880323 \n", "L 420.214125 53.658135 \n", "L 421.051125 89.597986 \n", "L 421.330125 96.055 \n", "L 421.609125 97.889367 \n", "L 421.888125 95.206864 \n", "L 422.167125 88.133293 \n", "L 422.725125 63.461591 \n", "L 423.283125 39.960615 \n", "L 423.562125 32.067271 \n", "L 423.841125 28.252815 \n", "L 424.120125 29.929582 \n", "L 424.399125 36.034363 \n", "L 424.957125 58.087296 \n", "L 425.794125 92.627216 \n", "L 426.073125 97.232182 \n", "L 426.352125 97.574038 \n", "L 426.631125 93.08414 \n", "L 426.910125 84.336167 \n", "L 428.026125 36.424765 \n", "L 428.305125 29.927776 \n", "L 428.584125 28.398161 \n", "L 428.863125 31.443791 \n", "L 429.142125 39.067017 \n", "L 430.537125 95.051151 \n", "L 430.816125 97.76464 \n", "L 431.095125 96.409618 \n", "L 431.374125 89.990897 \n", "L 431.932125 67.334603 \n", "L 432.490125 42.961295 \n", "L 432.769125 33.840063 \n", "L 433.048125 28.649299 \n", "L 433.327125 28.621046 \n", "L 433.606125 33.442052 \n", "L 434.164125 54.636332 \n", "L 435.001125 90.40343 \n", "L 435.280125 96.601049 \n", "L 435.559125 97.568326 \n", "L 435.838125 94.573791 \n", "L 436.117125 86.805193 \n", "L 437.512125 31.401091 \n", "L 437.791125 28.474464 \n", "L 438.070125 30.136485 \n", "L 438.349125 36.766378 \n", "L 439.186125 72.037462 \n", "L 439.744125 93.346871 \n", "L 440.023125 97.448093 \n", "L 440.302125 97.04896 \n", "L 440.581125 92.520811 \n", "L 441.139125 70.423847 \n", "L 441.976125 35.810365 \n", "L 442.255125 29.593205 \n", "L 442.534125 28.50728 \n", "L 442.813125 32.066192 \n", "L 443.092125 40.121437 \n", "L 444.487125 95.375018 \n", "L 444.766125 97.785223 \n", "L 445.045125 96.005017 \n", "L 445.324125 89.622911 \n", "L 445.882125 65.966581 \n", "L 446.440125 41.625319 \n", "L 446.719125 33.378304 \n", "L 446.998125 28.641581 \n", "L 447.277125 29.219759 \n", "L 447.556125 34.558286 \n", "L 448.114125 56.636308 \n", "L 448.951125 91.463191 \n", "L 449.230125 96.832654 \n", "L 449.509125 97.495905 \n", "L 449.788125 94.163231 \n", "L 450.067125 85.750746 \n", "L 451.183125 38.154716 \n", "L 451.462125 30.728544 \n", "L 451.741125 28.508838 \n", "L 452.020125 30.389815 \n", "L 452.299125 37.775676 \n", "L 453.136125 73.853555 \n", "L 453.694125 93.83605 \n", "L 453.973125 97.533688 \n", "L 454.252125 96.820644 \n", "L 454.531125 91.602128 \n", "L 455.089125 68.817483 \n", "L 455.647125 44.33286 \n", "L 456.205125 29.249354 \n", "L 456.484125 28.759385 \n", "L 456.763125 32.897981 \n", "L 457.042125 41.206596 \n", "L 458.437125 96.017257 \n", "L 458.716125 98.03839 \n", "L 458.995125 95.306754 \n", "L 459.274125 88.42411 \n", "L 460.111125 51.882412 \n", "L 460.669125 32.425505 \n", "L 460.948125 28.659799 \n", "L 461.227125 29.999476 \n", "L 461.506125 35.588245 \n", "L 462.064125 57.572773 \n", "L 462.901125 92.22486 \n", "L 463.180125 97.017552 \n", "L 463.459125 97.571875 \n", "L 463.738125 93.546437 \n", "L 464.017125 84.924693 \n", "L 465.133125 37.061306 \n", "L 465.412125 30.587452 \n", "L 465.691125 28.494987 \n", "L 465.970125 31.45048 \n", "L 466.249125 38.830862 \n", "L 466.807125 62.222223 \n", "L 467.365125 86.710233 \n", "L 467.644125 94.602785 \n", "L 467.923125 97.791578 \n", "L 468.202125 96.355401 \n", "L 468.481125 90.530735 \n", "L 469.039125 67.767985 \n", "L 469.876125 33.974771 \n", "L 470.155125 29.231372 \n", "L 470.434125 28.694661 \n", "L 470.713125 33.198081 \n", "L 471.271125 54.44412 \n", "L 472.108125 90.163982 \n", "L 472.387125 96.364995 \n", "L 472.666125 97.802967 \n", "L 472.945125 94.877479 \n", "L 473.224125 87.319847 \n", "L 474.619125 31.352593 \n", "L 474.898125 28.472022 \n", "L 475.177125 30.125566 \n", "L 475.456125 36.250207 \n", "L 476.014125 59.089878 \n", "L 476.851125 93.072774 \n", "L 477.130125 97.553233 \n", "L 477.409125 97.271885 \n", "L 477.688125 92.399222 \n", "L 477.967125 83.487553 \n", "L 479.083125 35.936209 \n", "L 479.362125 29.849994 \n", "L 479.641125 28.645107 \n", "L 479.920125 31.85387 \n", "L 480.478125 51.279558 \n", "L 481.315125 87.886574 \n", "L 481.594125 95.129049 \n", "L 481.873125 97.870045 \n", "L 482.152125 95.972208 \n", "L 482.431125 89.576163 \n", "L 483.268125 53.420467 \n", "L 483.826125 33.038947 \n", "L 484.105125 28.742378 \n", "L 484.384125 29.336684 \n", "L 484.663125 34.340647 \n", "L 485.221125 55.923725 \n", "L 486.058125 91.227446 \n", "L 486.337125 96.773001 \n", "L 486.616125 97.638674 \n", "L 486.895125 94.365239 \n", "L 487.174125 86.414778 \n", "L 488.569125 30.823224 \n", "L 488.848125 28.413422 \n", "L 489.127125 30.53325 \n", "L 489.406125 37.347121 \n", "L 489.964125 60.602203 \n", "L 490.522125 85.328045 \n", "L 490.801125 93.582243 \n", "L 491.080125 97.61198 \n", "L 491.359125 96.896658 \n", "L 491.638125 91.851867 \n", "L 492.196125 69.965366 \n", "L 493.033125 35.218514 \n", "L 493.312125 29.747224 \n", "L 493.591125 28.784869 \n", "L 493.870125 32.320054 \n", "L 494.428125 52.293819 \n", "L 495.265125 89.071178 \n", "L 495.544125 95.635866 \n", "L 495.823125 97.847169 \n", "L 496.102125 95.698779 \n", "L 496.381125 88.588646 \n", "L 497.776125 32.375606 \n", "L 498.055125 28.578478 \n", "L 498.334125 29.61706 \n", "L 498.613125 34.926744 \n", "L 499.171125 57.266294 \n", "L 500.008125 91.956534 \n", "L 500.287125 97.101874 \n", "L 500.566125 97.473051 \n", "L 500.845125 93.527981 \n", "L 501.124125 85.06115 \n", "L 502.240125 37.249998 \n", "L 502.519125 30.401784 \n", "L 502.798125 28.151994 \n", "L 503.077125 30.718702 \n", "L 503.635125 48.818388 \n", "L 504.472125 86.329266 \n", "L 504.751125 94.302246 \n", "L 505.030125 97.7623 \n", "L 505.309125 96.490754 \n", "L 505.588125 91.23124 \n", "L 506.146125 68.326569 \n", "L 506.983125 34.44001 \n", "L 507.262125 28.954433 \n", "L 507.541125 28.824653 \n", "L 507.820125 33.409191 \n", "L 508.378125 53.732871 \n", "L 509.215125 90.028888 \n", "L 509.494125 96.29434 \n", "L 509.773125 97.719302 \n", "L 510.052125 95.487754 \n", "L 510.331125 87.622868 \n", "L 511.726125 31.561717 \n", "L 512.005125 28.28775 \n", "L 512.284125 29.752215 \n", "L 512.563125 36.31208 \n", "L 513.121125 58.87201 \n", "L 513.958125 92.617672 \n", "L 514.237125 97.317131 \n", "L 514.516125 97.181219 \n", "L 514.795125 92.946539 \n", "L 515.353125 71.644661 \n", "L 516.190125 36.197624 \n", "L 516.469125 29.874015 \n", "L 516.748125 28.120074 \n", "L 517.027125 31.720957 \n", "L 517.585125 50.604587 \n", "L 518.422125 87.468747 \n", "L 518.701125 95.310041 \n", "L 518.980125 98.174955 \n", "L 519.259125 96.108836 \n", "L 519.538125 89.83928 \n", "L 520.096125 66.511047 \n", "L 520.933125 33.050213 \n", "L 521.212125 28.690875 \n", "L 521.491125 28.946035 \n", "L 521.770125 33.620249 \n", "L 522.328125 55.239204 \n", "L 523.165125 90.765161 \n", "L 523.444125 96.485034 \n", "L 523.723125 97.607478 \n", "L 524.002125 94.269356 \n", "L 524.281125 86.623433 \n", "L 525.676125 31.264773 \n", "L 525.955125 28.141619 \n", "L 526.234125 30.093366 \n", "L 526.513125 37.20433 \n", "L 527.071125 59.750503 \n", "L 527.908125 93.522281 \n", "L 528.187125 97.565049 \n", "L 528.466125 96.957086 \n", "L 528.745125 92.021255 \n", "L 529.303125 70.203188 \n", "L 530.140125 30.021616 \n", "L 530.419125 28.663851 \n", "L 530.698125 35.620488 \n", "L 531.256125 65.264848 \n", "L 531.814125 93.957759 \n", "L 532.093125 97.984309 \n", "L 532.372125 94.754141 \n", "L 532.651125 84.217424 \n", "L 533.488125 37.120369 \n", "L 533.767125 28.990117 \n", "L 534.046125 29.080407 \n", "L 534.325125 37.408005 \n", "L 535.441125 94.685019 \n", "L 535.720125 97.853843 \n", "L 535.999125 93.35386 \n", "L 536.557125 65.229199 \n", "L 537.115125 35.495224 \n", "L 537.394125 28.714301 \n", "L 537.673125 30.541754 \n", "L 537.952125 38.795516 \n", "L 539.068125 95.758435 \n", "L 539.347125 97.730662 \n", "L 539.626125 92.031581 \n", "L 540.184125 62.951129 \n", "L 540.742125 34.142808 \n", "L 541.021125 28.390779 \n", "L 541.300125 30.779131 \n", "L 541.579125 40.742832 \n", "L 542.695125 96.386078 \n", "L 542.974125 97.385943 \n", "L 543.253125 90.866305 \n", "L 543.811125 60.134256 \n", "L 544.369125 32.699631 \n", "L 544.648125 28.742774 \n", "L 544.927125 31.898778 \n", "L 545.485125 58.312871 \n", "L 546.043125 89.485957 \n", "L 546.322125 96.975054 \n", "L 546.601125 96.953724 \n", "L 546.880125 89.138564 \n", "L 547.996125 31.405421 \n", "L 548.275125 28.284641 \n", "L 548.554125 33.350838 \n", "L 549.112125 60.908346 \n", "L 549.670125 91.449035 \n", "L 549.949125 97.506002 \n", "L 550.228125 96.314184 \n", "L 550.507125 87.489856 \n", "L 551.623125 30.646212 \n", "L 551.902125 28.520918 \n", "L 552.181125 34.764086 \n", "L 552.739125 63.512439 \n", "L 553.297125 92.649897 \n", "L 553.576125 97.956616 \n", "L 553.855125 95.249647 \n", "L 554.134125 85.611792 \n", "L 554.971125 38.304478 \n", "L 555.250125 29.988009 \n", "L 555.529125 29.040641 \n", "L 555.808125 35.580893 \n", "L 556.924125 93.979124 \n", "L 557.203125 97.83479 \n", "L 557.482125 94.177108 \n", "L 557.761125 83.725278 \n", "L 558.598125 37.036043 \n", "L 558.877125 29.177977 \n", "L 559.156125 29.313675 \n", "L 559.435125 37.64979 \n", "L 560.551125 94.733857 \n", "L 560.830125 97.786561 \n", "L 561.109125 93.408165 \n", "L 561.388125 81.497721 \n", "L 562.225125 35.40231 \n", "L 562.504125 28.734204 \n", "L 562.783125 30.317346 \n", "L 563.062125 38.794577 \n", "L 564.178125 95.675204 \n", "L 564.457125 97.693708 \n", "L 564.736125 91.953001 \n", "L 565.294125 62.559907 \n", "L 565.852125 32.785757 \n", "L 566.131125 28.2244 \n", "L 566.410125 32.197095 \n", "L 566.968125 61.205443 \n", "L 567.526125 92.379193 \n", "L 567.805125 97.851138 \n", "L 568.084125 95.185239 \n", "L 568.363125 83.967396 \n", "L 569.200125 35.353642 \n", "L 569.479125 28.805648 \n", "L 569.758125 30.166852 \n", "L 570.037125 40.684799 \n", "L 570.874125 89.852409 \n", "L 571.153125 97.191041 \n", "L 571.432125 96.210761 \n", "L 571.711125 87.141443 \n", "L 572.827125 29.244112 \n", "L 573.106125 29.18349 \n", "L 573.385125 37.976273 \n", "L 574.501125 96.029969 \n", "L 574.780125 97.114133 \n", "L 575.059125 90.157143 \n", "L 576.175125 30.773216 \n", "L 576.454125 28.817294 \n", "L 576.733125 35.049403 \n", "L 577.291125 65.560624 \n", "L 577.849125 94.665975 \n", "L 578.128125 97.804741 \n", "L 578.407125 92.68459 \n", "L 578.965125 61.919027 \n", "L 579.523125 32.660656 \n", "L 579.802125 28.334919 \n", "L 580.081125 32.625703 \n", "L 580.639125 61.577778 \n", "L 581.197125 92.473407 \n", "L 581.476125 97.647785 \n", "L 581.755125 94.774781 \n", "L 582.034125 83.490829 \n", "L 582.871125 35.316251 \n", "L 583.150125 28.820239 \n", "L 583.429125 30.839152 \n", "L 583.708125 41.362449 \n", "L 584.545125 89.772333 \n", "L 584.824125 97.240178 \n", "L 584.824125 97.240178 \n", "\" style=\"fill:none;stroke:#ff0000;stroke-linecap:square;\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 27.103125 21.318125 \n", "L 585.103125 21.318125 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 27.103125 105.018125 \n", "L 27.103125 21.318125 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 27.103125 105.018125 \n", "L 585.103125 105.018125 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 585.103125 105.018125 \n", "L 585.103125 21.318125 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_4\">\n", "      <defs>\n", "       <path d=\"M 0 0 \n", "L 0 -4 \n", "\" id=\"m56e0345fc3\" style=\"stroke:#000000;stroke-width:0.5;\"/>\n", "      </defs>\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"27.103125\" xlink:href=\"#m56e0345fc3\" y=\"105.018125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_5\">\n", "      <defs>\n", "       <path d=\"M 0 0 \n", "L 0 4 \n", "\" id=\"mbd28f27cfa\" style=\"stroke:#000000;stroke-width:0.5;\"/>\n", "      </defs>\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"27.103125\" xlink:href=\"#mbd28f27cfa\" y=\"21.318125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <defs>\n", "       <path d=\"M 31.78125 66.40625 \n", "Q 24.171875 66.40625 20.328125 58.90625 \n", "Q 16.5 51.421875 16.5 36.375 \n", "Q 16.5 21.390625 20.328125 13.890625 \n", "Q 24.171875 6.390625 31.78125 6.390625 \n", "Q 39.453125 6.390625 43.28125 13.890625 \n", "Q 47.125 21.390625 47.125 36.375 \n", "Q 47.125 51.421875 43.28125 58.90625 \n", "Q 39.453125 66.40625 31.78125 66.40625 \n", "M 31.78125 74.21875 \n", "Q 44.046875 74.21875 50.515625 64.515625 \n", "Q 56.984375 54.828125 56.984375 36.375 \n", "Q 56.984375 17.96875 50.515625 8.265625 \n", "Q 44.046875 -1.421875 31.78125 -1.421875 \n", "Q 19.53125 -1.421875 13.0625 8.265625 \n", "Q 6.59375 17.96875 6.59375 36.375 \n", "Q 6.59375 54.828125 13.0625 64.515625 \n", "Q 19.53125 74.21875 31.78125 74.21875 \n", "\" id=\"BitstreamVeraSans-Roman-30\"/>\n", "      </defs>\n", "      <g transform=\"translate(23.921875 116.6165625)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"166.603125\" xlink:href=\"#m56e0345fc3\" y=\"105.018125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"166.603125\" xlink:href=\"#mbd28f27cfa\" y=\"21.318125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 500 -->\n", "      <defs>\n", "       <path d=\"M 10.796875 72.90625 \n", "L 49.515625 72.90625 \n", "L 49.515625 64.59375 \n", "L 19.828125 64.59375 \n", "L 19.828125 46.734375 \n", "Q 21.96875 47.46875 24.109375 47.828125 \n", "Q 26.265625 48.1875 28.421875 48.1875 \n", "Q 40.625 48.1875 47.75 41.5 \n", "Q 54.890625 34.8125 54.890625 23.390625 \n", "Q 54.890625 11.625 47.5625 5.09375 \n", "Q 40.234375 -1.421875 26.90625 -1.421875 \n", "Q 22.3125 -1.421875 17.546875 -0.640625 \n", "Q 12.796875 0.140625 7.71875 1.703125 \n", "L 7.71875 11.625 \n", "Q 12.109375 9.234375 16.796875 8.0625 \n", "Q 21.484375 6.890625 26.703125 6.890625 \n", "Q 35.15625 6.890625 40.078125 11.328125 \n", "Q 45.015625 15.765625 45.015625 23.390625 \n", "Q 45.015625 31 40.078125 35.4375 \n", "Q 35.15625 39.890625 26.703125 39.890625 \n", "Q 22.75 39.890625 18.8125 39.015625 \n", "Q 14.890625 38.140625 10.796875 36.28125 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-35\"/>\n", "      </defs>\n", "      <g transform=\"translate(157.059375 116.6165625)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-35\"/>\n", "       <use x=\"63.623046875\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"127.24609375\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"306.103125\" xlink:href=\"#m56e0345fc3\" y=\"105.018125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"306.103125\" xlink:href=\"#mbd28f27cfa\" y=\"21.318125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 1000 -->\n", "      <defs>\n", "       <path d=\"M 12.40625 8.296875 \n", "L 28.515625 8.296875 \n", "L 28.515625 63.921875 \n", "L 10.984375 60.40625 \n", "L 10.984375 69.390625 \n", "L 28.421875 72.90625 \n", "L 38.28125 72.90625 \n", "L 38.28125 8.296875 \n", "L 54.390625 8.296875 \n", "L 54.390625 0 \n", "L 12.40625 0 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-31\"/>\n", "      </defs>\n", "      <g transform=\"translate(293.378125 116.6165625)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-31\"/>\n", "       <use x=\"63.623046875\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"127.24609375\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"190.869140625\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"445.603125\" xlink:href=\"#m56e0345fc3\" y=\"105.018125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"445.603125\" xlink:href=\"#mbd28f27cfa\" y=\"21.318125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 1500 -->\n", "      <g transform=\"translate(432.878125 116.6165625)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-31\"/>\n", "       <use x=\"63.623046875\" xlink:href=\"#BitstreamVeraSans-Roman-35\"/>\n", "       <use x=\"127.24609375\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"190.869140625\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"585.103125\" xlink:href=\"#m56e0345fc3\" y=\"105.018125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"585.103125\" xlink:href=\"#mbd28f27cfa\" y=\"21.318125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 2000 -->\n", "      <defs>\n", "       <path d=\"M 19.1875 8.296875 \n", "L 53.609375 8.296875 \n", "L 53.609375 0 \n", "L 7.328125 0 \n", "L 7.328125 8.296875 \n", "Q 12.9375 14.109375 22.625 23.890625 \n", "Q 32.328125 33.6875 34.8125 36.53125 \n", "Q 39.546875 41.84375 41.421875 45.53125 \n", "Q 43.3125 49.21875 43.3125 52.78125 \n", "Q 43.3125 58.59375 39.234375 62.25 \n", "Q 35.15625 65.921875 28.609375 65.921875 \n", "Q 23.96875 65.921875 18.8125 64.3125 \n", "Q 13.671875 62.703125 7.8125 59.421875 \n", "L 7.8125 69.390625 \n", "Q 13.765625 71.78125 18.9375 73 \n", "Q 24.125 74.21875 28.421875 74.21875 \n", "Q 39.75 74.21875 46.484375 68.546875 \n", "Q 53.21875 62.890625 53.21875 53.421875 \n", "Q 53.21875 48.921875 51.53125 44.890625 \n", "Q 49.859375 40.875 45.40625 35.40625 \n", "Q 44.1875 33.984375 37.640625 27.21875 \n", "Q 31.109375 20.453125 19.1875 8.296875 \n", "\" id=\"BitstreamVeraSans-Roman-32\"/>\n", "      </defs>\n", "      <g transform=\"translate(572.378125 116.6165625)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-32\"/>\n", "       <use x=\"63.623046875\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"127.24609375\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"190.869140625\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_14\">\n", "      <defs>\n", "       <path d=\"M 0 0 \n", "L 4 0 \n", "\" id=\"m86367d38d1\" style=\"stroke:#000000;stroke-width:0.5;\"/>\n", "      </defs>\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"27.103125\" xlink:href=\"#m86367d38d1\" y=\"98.043125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_15\">\n", "      <defs>\n", "       <path d=\"M 0 0 \n", "L -4 0 \n", "\" id=\"mc7b6199803\" style=\"stroke:#000000;stroke-width:0.5;\"/>\n", "      </defs>\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"585.103125\" xlink:href=\"#mc7b6199803\" y=\"98.043125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 0.0 -->\n", "      <defs>\n", "       <path d=\"M 10.6875 12.40625 \n", "L 21 12.40625 \n", "L 21 0 \n", "L 10.6875 0 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-2e\"/>\n", "      </defs>\n", "      <g transform=\"translate(7.2 100.8025)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"63.623046875\" xlink:href=\"#BitstreamVeraSans-Roman-2e\"/>\n", "       <use x=\"95.41015625\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"27.103125\" xlink:href=\"#m86367d38d1\" y=\"84.093125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"585.103125\" xlink:href=\"#mc7b6199803\" y=\"84.093125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(7.2 86.8525)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"63.623046875\" xlink:href=\"#BitstreamVeraSans-Roman-2e\"/>\n", "       <use x=\"95.41015625\" xlink:href=\"#BitstreamVeraSans-Roman-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"27.103125\" xlink:href=\"#m86367d38d1\" y=\"70.143125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"585.103125\" xlink:href=\"#mc7b6199803\" y=\"70.143125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.4 -->\n", "      <defs>\n", "       <path d=\"M 37.796875 64.3125 \n", "L 12.890625 25.390625 \n", "L 37.796875 25.390625 \n", "z\n", "M 35.203125 72.90625 \n", "L 47.609375 72.90625 \n", "L 47.609375 25.390625 \n", "L 58.015625 25.390625 \n", "L 58.015625 17.1875 \n", "L 47.609375 17.1875 \n", "L 47.609375 0 \n", "L 37.796875 0 \n", "L 37.796875 17.1875 \n", "L 4.890625 17.1875 \n", "L 4.890625 26.703125 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-34\"/>\n", "      </defs>\n", "      <g transform=\"translate(7.2 72.9025)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"63.623046875\" xlink:href=\"#BitstreamVeraSans-Roman-2e\"/>\n", "       <use x=\"95.41015625\" xlink:href=\"#BitstreamVeraSans-Roman-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"27.103125\" xlink:href=\"#m86367d38d1\" y=\"56.193125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"585.103125\" xlink:href=\"#mc7b6199803\" y=\"56.193125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.6 -->\n", "      <defs>\n", "       <path d=\"M 33.015625 40.375 \n", "Q 26.375 40.375 22.484375 35.828125 \n", "Q 18.609375 31.296875 18.609375 23.390625 \n", "Q 18.609375 15.53125 22.484375 10.953125 \n", "Q 26.375 6.390625 33.015625 6.390625 \n", "Q 39.65625 6.390625 43.53125 10.953125 \n", "Q 47.40625 15.53125 47.40625 23.390625 \n", "Q 47.40625 31.296875 43.53125 35.828125 \n", "Q 39.65625 40.375 33.015625 40.375 \n", "M 52.59375 71.296875 \n", "L 52.59375 62.3125 \n", "Q 48.875 64.0625 45.09375 64.984375 \n", "Q 41.3125 65.921875 37.59375 65.921875 \n", "Q 27.828125 65.921875 22.671875 59.328125 \n", "Q 17.53125 52.734375 16.796875 39.40625 \n", "Q 19.671875 43.65625 24.015625 45.921875 \n", "Q 28.375 48.1875 33.59375 48.1875 \n", "Q 44.578125 48.1875 50.953125 41.515625 \n", "Q 57.328125 34.859375 57.328125 23.390625 \n", "Q 57.328125 12.15625 50.6875 5.359375 \n", "Q 44.046875 -1.421875 33.015625 -1.421875 \n", "Q 20.359375 -1.421875 13.671875 8.265625 \n", "Q 6.984375 17.96875 6.984375 36.375 \n", "Q 6.984375 53.65625 15.1875 63.9375 \n", "Q 23.390625 74.21875 37.203125 74.21875 \n", "Q 40.921875 74.21875 44.703125 73.484375 \n", "Q 48.484375 72.75 52.59375 71.296875 \n", "\" id=\"BitstreamVeraSans-Roman-36\"/>\n", "      </defs>\n", "      <g transform=\"translate(7.2 58.9525)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"63.623046875\" xlink:href=\"#BitstreamVeraSans-Roman-2e\"/>\n", "       <use x=\"95.41015625\" xlink:href=\"#BitstreamVeraSans-Roman-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"27.103125\" xlink:href=\"#m86367d38d1\" y=\"42.243125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"585.103125\" xlink:href=\"#mc7b6199803\" y=\"42.243125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.8 -->\n", "      <defs>\n", "       <path d=\"M 31.78125 34.625 \n", "Q 24.75 34.625 20.71875 30.859375 \n", "Q 16.703125 27.09375 16.703125 20.515625 \n", "Q 16.703125 13.921875 20.71875 10.15625 \n", "Q 24.75 6.390625 31.78125 6.390625 \n", "Q 38.8125 6.390625 42.859375 10.171875 \n", "Q 46.921875 13.96875 46.921875 20.515625 \n", "Q 46.921875 27.09375 42.890625 30.859375 \n", "Q 38.875 34.625 31.78125 34.625 \n", "M 21.921875 38.8125 \n", "Q 15.578125 40.375 12.03125 44.71875 \n", "Q 8.5 49.078125 8.5 55.328125 \n", "Q 8.5 64.0625 14.71875 69.140625 \n", "Q 20.953125 74.21875 31.78125 74.21875 \n", "Q 42.671875 74.21875 48.875 69.140625 \n", "Q 55.078125 64.0625 55.078125 55.328125 \n", "Q 55.078125 49.078125 51.53125 44.71875 \n", "Q 48 40.375 41.703125 38.8125 \n", "Q 48.828125 37.15625 52.796875 32.3125 \n", "Q 56.78125 27.484375 56.78125 20.515625 \n", "Q 56.78125 9.90625 50.3125 4.234375 \n", "Q 43.84375 -1.421875 31.78125 -1.421875 \n", "Q 19.734375 -1.421875 13.25 4.234375 \n", "Q 6.78125 9.90625 6.78125 20.515625 \n", "Q 6.78125 27.484375 10.78125 32.3125 \n", "Q 14.796875 37.15625 21.921875 38.8125 \n", "M 18.3125 54.390625 \n", "Q 18.3125 48.734375 21.84375 45.5625 \n", "Q 25.390625 42.390625 31.78125 42.390625 \n", "Q 38.140625 42.390625 41.71875 45.5625 \n", "Q 45.3125 48.734375 45.3125 54.390625 \n", "Q 45.3125 60.0625 41.71875 63.234375 \n", "Q 38.140625 66.40625 31.78125 66.40625 \n", "Q 25.390625 66.40625 21.84375 63.234375 \n", "Q 18.3125 60.0625 18.3125 54.390625 \n", "\" id=\"BitstreamVeraSans-Roman-38\"/>\n", "      </defs>\n", "      <g transform=\"translate(7.2 45.0025)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"63.623046875\" xlink:href=\"#BitstreamVeraSans-Roman-2e\"/>\n", "       <use x=\"95.41015625\" xlink:href=\"#BitstreamVeraSans-Roman-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"27.103125\" xlink:href=\"#m86367d38d1\" y=\"28.293125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_25\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"585.103125\" xlink:href=\"#mc7b6199803\" y=\"28.293125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 31.0525)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-31\"/>\n", "       <use x=\"63.623046875\" xlink:href=\"#BitstreamVeraSans-Roman-2e\"/>\n", "       <use x=\"95.41015625\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"text_12\">\n", "    <!-- training (excerpt) -->\n", "    <defs>\n", "     <path d=\"M 9.421875 54.6875 \n", "L 18.40625 54.6875 \n", "L 18.40625 0 \n", "L 9.421875 0 \n", "z\n", "M 9.421875 75.984375 \n", "L 18.40625 75.984375 \n", "L 18.40625 64.59375 \n", "L 9.421875 64.59375 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-69\"/>\n", "     <path id=\"BitstreamVeraSans-Roman-20\"/>\n", "     <path d=\"M 8.015625 75.875 \n", "L 15.828125 75.875 \n", "Q 23.140625 64.359375 26.78125 53.3125 \n", "Q 30.421875 42.28125 30.421875 31.390625 \n", "Q 30.421875 20.453125 26.78125 9.375 \n", "Q 23.140625 -1.703125 15.828125 -13.1875 \n", "L 8.015625 -13.1875 \n", "Q 14.5 -2 17.703125 9.0625 \n", "Q 20.90625 20.125 20.90625 31.390625 \n", "Q 20.90625 42.671875 17.703125 53.65625 \n", "Q 14.5 64.65625 8.015625 75.875 \n", "\" id=\"BitstreamVeraSans-Roman-29\"/>\n", "     <path d=\"M 34.28125 27.484375 \n", "Q 23.390625 27.484375 19.1875 25 \n", "Q 14.984375 22.515625 14.984375 16.5 \n", "Q 14.984375 11.71875 18.140625 8.90625 \n", "Q 21.296875 6.109375 26.703125 6.109375 \n", "Q 34.1875 6.109375 38.703125 11.40625 \n", "Q 43.21875 16.703125 43.21875 25.484375 \n", "L 43.21875 27.484375 \n", "z\n", "M 52.203125 31.203125 \n", "L 52.203125 0 \n", "L 43.21875 0 \n", "L 43.21875 8.296875 \n", "Q 40.140625 3.328125 35.546875 0.953125 \n", "Q 30.953125 -1.421875 24.3125 -1.421875 \n", "Q 15.921875 -1.421875 10.953125 3.296875 \n", "Q 6 8.015625 6 15.921875 \n", "Q 6 25.140625 12.171875 29.828125 \n", "Q 18.359375 34.515625 30.609375 34.515625 \n", "L 43.21875 34.515625 \n", "L 43.21875 35.40625 \n", "Q 43.21875 41.609375 39.140625 45 \n", "Q 35.0625 48.390625 27.6875 48.390625 \n", "Q 23 48.390625 18.546875 47.265625 \n", "Q 14.109375 46.140625 10.015625 43.890625 \n", "L 10.015625 52.203125 \n", "Q 14.9375 54.109375 19.578125 55.046875 \n", "Q 24.21875 56 28.609375 56 \n", "Q 40.484375 56 46.34375 49.84375 \n", "Q 52.203125 43.703125 52.203125 31.203125 \n", "\" id=\"BitstreamVeraSans-Roman-61\"/>\n", "     <path d=\"M 18.109375 8.203125 \n", "L 18.109375 -20.796875 \n", "L 9.078125 -20.796875 \n", "L 9.078125 54.6875 \n", "L 18.109375 54.6875 \n", "L 18.109375 46.390625 \n", "Q 20.953125 51.265625 25.265625 53.625 \n", "Q 29.59375 56 35.59375 56 \n", "Q 45.5625 56 51.78125 48.09375 \n", "Q 58.015625 40.1875 58.015625 27.296875 \n", "Q 58.015625 14.40625 51.78125 6.484375 \n", "Q 45.5625 -1.421875 35.59375 -1.421875 \n", "Q 29.59375 -1.421875 25.265625 0.953125 \n", "Q 20.953125 3.328125 18.109375 8.203125 \n", "M 48.6875 27.296875 \n", "Q 48.6875 37.203125 44.609375 42.84375 \n", "Q 40.53125 48.484375 33.40625 48.484375 \n", "Q 26.265625 48.484375 22.1875 42.84375 \n", "Q 18.109375 37.203125 18.109375 27.296875 \n", "Q 18.109375 17.390625 22.1875 11.75 \n", "Q 26.265625 6.109375 33.40625 6.109375 \n", "Q 40.53125 6.109375 44.609375 11.75 \n", "Q 48.6875 17.390625 48.6875 27.296875 \n", "\" id=\"BitstreamVeraSans-Roman-70\"/>\n", "     <path d=\"M 48.78125 52.59375 \n", "L 48.78125 44.1875 \n", "Q 44.96875 46.296875 41.140625 47.34375 \n", "Q 37.3125 48.390625 33.40625 48.390625 \n", "Q 24.65625 48.390625 19.8125 42.84375 \n", "Q 14.984375 37.3125 14.984375 27.296875 \n", "Q 14.984375 17.28125 19.8125 11.734375 \n", "Q 24.65625 6.203125 33.40625 6.203125 \n", "Q 37.3125 6.203125 41.140625 7.25 \n", "Q 44.96875 8.296875 48.78125 10.40625 \n", "L 48.78125 2.09375 \n", "Q 45.015625 0.34375 40.984375 -0.53125 \n", "Q 36.96875 -1.421875 32.421875 -1.421875 \n", "Q 20.0625 -1.421875 12.78125 6.34375 \n", "Q 5.515625 14.109375 5.515625 27.296875 \n", "Q 5.515625 40.671875 12.859375 48.328125 \n", "Q 20.21875 56 33.015625 56 \n", "Q 37.15625 56 41.109375 55.140625 \n", "Q 45.0625 54.296875 48.78125 52.59375 \n", "\" id=\"BitstreamVeraSans-Roman-63\"/>\n", "     <path d=\"M 45.40625 27.984375 \n", "Q 45.40625 37.75 41.375 43.109375 \n", "Q 37.359375 48.484375 30.078125 48.484375 \n", "Q 22.859375 48.484375 18.828125 43.109375 \n", "Q 14.796875 37.75 14.796875 27.984375 \n", "Q 14.796875 18.265625 18.828125 12.890625 \n", "Q 22.859375 7.515625 30.078125 7.515625 \n", "Q 37.359375 7.515625 41.375 12.890625 \n", "Q 45.40625 18.265625 45.40625 27.984375 \n", "M 54.390625 6.78125 \n", "Q 54.390625 -7.171875 48.1875 -13.984375 \n", "Q 42 -20.796875 29.203125 -20.796875 \n", "Q 24.46875 -20.796875 20.265625 -20.09375 \n", "Q 16.0625 -19.390625 12.109375 -17.921875 \n", "L 12.109375 -9.1875 \n", "Q 16.0625 -11.328125 19.921875 -12.34375 \n", "Q 23.78125 -13.375 27.78125 -13.375 \n", "Q 36.625 -13.375 41.015625 -8.765625 \n", "Q 45.40625 -4.15625 45.40625 5.171875 \n", "L 45.40625 9.625 \n", "Q 42.625 4.78125 38.28125 2.390625 \n", "Q 33.9375 0 27.875 0 \n", "Q 17.828125 0 11.671875 7.65625 \n", "Q 5.515625 15.328125 5.515625 27.984375 \n", "Q 5.515625 40.671875 11.671875 48.328125 \n", "Q 17.828125 56 27.875 56 \n", "Q 33.9375 56 38.28125 53.609375 \n", "Q 42.625 51.21875 45.40625 46.390625 \n", "L 45.40625 54.6875 \n", "L 54.390625 54.6875 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-67\"/>\n", "     <path d=\"M 56.203125 29.59375 \n", "L 56.203125 25.203125 \n", "L 14.890625 25.203125 \n", "Q 15.484375 15.921875 20.484375 11.0625 \n", "Q 25.484375 6.203125 34.421875 6.203125 \n", "Q 39.59375 6.203125 44.453125 7.46875 \n", "Q 49.3125 8.734375 54.109375 11.28125 \n", "L 54.109375 2.78125 \n", "Q 49.265625 0.734375 44.1875 -0.34375 \n", "Q 39.109375 -1.421875 33.890625 -1.421875 \n", "Q 20.796875 -1.421875 13.15625 6.1875 \n", "Q 5.515625 13.8125 5.515625 26.8125 \n", "Q 5.515625 40.234375 12.765625 48.109375 \n", "Q 20.015625 56 32.328125 56 \n", "Q 43.359375 56 49.78125 48.890625 \n", "Q 56.203125 41.796875 56.203125 29.59375 \n", "M 47.21875 32.234375 \n", "Q 47.125 39.59375 43.09375 43.984375 \n", "Q 39.0625 48.390625 32.421875 48.390625 \n", "Q 24.90625 48.390625 20.390625 44.140625 \n", "Q 15.875 39.890625 15.1875 32.171875 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-65\"/>\n", "     <path d=\"M 31 75.875 \n", "Q 24.46875 64.65625 21.28125 53.65625 \n", "Q 18.109375 42.671875 18.109375 31.390625 \n", "Q 18.109375 20.125 21.3125 9.0625 \n", "Q 24.515625 -2 31 -13.1875 \n", "L 23.1875 -13.1875 \n", "Q 15.875 -1.703125 12.234375 9.375 \n", "Q 8.59375 20.453125 8.59375 31.390625 \n", "Q 8.59375 42.28125 12.203125 53.3125 \n", "Q 15.828125 64.359375 23.1875 75.875 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-28\"/>\n", "     <path d=\"M 54.890625 54.6875 \n", "L 35.109375 28.078125 \n", "L 55.90625 0 \n", "L 45.3125 0 \n", "L 29.390625 21.484375 \n", "L 13.484375 0 \n", "L 2.875 0 \n", "L 24.125 28.609375 \n", "L 4.6875 54.6875 \n", "L 15.28125 54.6875 \n", "L 29.78125 35.203125 \n", "L 44.28125 54.6875 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-78\"/>\n", "     <path d=\"M 54.890625 33.015625 \n", "L 54.890625 0 \n", "L 45.90625 0 \n", "L 45.90625 32.71875 \n", "Q 45.90625 40.484375 42.875 44.328125 \n", "Q 39.84375 48.1875 33.796875 48.1875 \n", "Q 26.515625 48.1875 22.3125 43.546875 \n", "Q 18.109375 38.921875 18.109375 30.90625 \n", "L 18.109375 0 \n", "L 9.078125 0 \n", "L 9.078125 54.6875 \n", "L 18.109375 54.6875 \n", "L 18.109375 46.1875 \n", "Q 21.34375 51.125 25.703125 53.5625 \n", "Q 30.078125 56 35.796875 56 \n", "Q 45.21875 56 50.046875 50.171875 \n", "Q 54.890625 44.34375 54.890625 33.015625 \n", "\" id=\"BitstreamVeraSans-Roman-6e\"/>\n", "     <path d=\"M 41.109375 46.296875 \n", "Q 39.59375 47.171875 37.8125 47.578125 \n", "Q 36.03125 48 33.890625 48 \n", "Q 26.265625 48 22.1875 43.046875 \n", "Q 18.109375 38.09375 18.109375 28.8125 \n", "L 18.109375 0 \n", "L 9.078125 0 \n", "L 9.078125 54.6875 \n", "L 18.109375 54.6875 \n", "L 18.109375 46.1875 \n", "Q 20.953125 51.171875 25.484375 53.578125 \n", "Q 30.03125 56 36.53125 56 \n", "Q 37.453125 56 38.578125 55.875 \n", "Q 39.703125 55.765625 41.0625 55.515625 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-72\"/>\n", "     <path d=\"M 18.3125 70.21875 \n", "L 18.3125 54.6875 \n", "L 36.8125 54.6875 \n", "L 36.8125 47.703125 \n", "L 18.3125 47.703125 \n", "L 18.3125 18.015625 \n", "Q 18.3125 11.328125 20.140625 9.421875 \n", "Q 21.96875 7.515625 27.59375 7.515625 \n", "L 36.8125 7.515625 \n", "L 36.8125 0 \n", "L 27.59375 0 \n", "Q 17.1875 0 13.234375 3.875 \n", "Q 9.28125 7.765625 9.28125 18.015625 \n", "L 9.28125 47.703125 \n", "L 2.6875 47.703125 \n", "L 2.6875 54.6875 \n", "L 9.28125 54.6875 \n", "L 9.28125 70.21875 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-74\"/>\n", "    </defs>\n", "    <g transform=\"translate(253.4109375 16.318125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#BitstreamVeraSans-Roman-74\"/>\n", "     <use x=\"39.208984375\" xlink:href=\"#BitstreamVeraSans-Roman-72\"/>\n", "     <use x=\"80.322265625\" xlink:href=\"#BitstreamVeraSans-Roman-61\"/>\n", "     <use x=\"141.6015625\" xlink:href=\"#BitstreamVeraSans-Roman-69\"/>\n", "     <use x=\"169.384765625\" xlink:href=\"#BitstreamVeraSans-Roman-6e\"/>\n", "     <use x=\"232.763671875\" xlink:href=\"#BitstreamVeraSans-Roman-69\"/>\n", "     <use x=\"260.546875\" xlink:href=\"#BitstreamVeraSans-Roman-6e\"/>\n", "     <use x=\"323.92578125\" xlink:href=\"#BitstreamVeraSans-Roman-67\"/>\n", "     <use x=\"387.40234375\" xlink:href=\"#BitstreamVeraSans-Roman-20\"/>\n", "     <use x=\"419.189453125\" xlink:href=\"#BitstreamVeraSans-Roman-28\"/>\n", "     <use x=\"458.203125\" xlink:href=\"#BitstreamVeraSans-Roman-65\"/>\n", "     <use x=\"519.7109375\" xlink:href=\"#BitstreamVeraSans-Roman-78\"/>\n", "     <use x=\"578.875\" xlink:href=\"#BitstreamVeraSans-Roman-63\"/>\n", "     <use x=\"633.85546875\" xlink:href=\"#BitstreamVeraSans-Roman-65\"/>\n", "     <use x=\"695.37890625\" xlink:href=\"#BitstreamVeraSans-Roman-72\"/>\n", "     <use x=\"736.4921875\" xlink:href=\"#BitstreamVeraSans-Roman-70\"/>\n", "     <use x=\"799.96875\" xlink:href=\"#BitstreamVeraSans-Roman-74\"/>\n", "     <use x=\"839.177734375\" xlink:href=\"#BitstreamVeraSans-Roman-29\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 532.442622 57.429981 \n", "L 581.633125 57.429981 \n", "L 581.633125 24.788125 \n", "L 532.442622 24.788125 \n", "z\n", "\" style=\"fill:#ffffff;stroke:#000000;stroke-linejoin:miter;\"/>\n", "    </g>\n", "    <g id=\"line2d_26\">\n", "     <path d=\"M 537.300622 30.408441 \n", "L 547.016622 30.408441 \n", "\" style=\"fill:none;stroke:#0000ff;stroke-linecap:square;\"/>\n", "    </g>\n", "    <g id=\"line2d_27\"/>\n", "    <g id=\"text_13\">\n", "     <!-- control -->\n", "     <defs>\n", "      <path d=\"M 30.609375 48.390625 \n", "Q 23.390625 48.390625 19.1875 42.75 \n", "Q 14.984375 37.109375 14.984375 27.296875 \n", "Q 14.984375 17.484375 19.15625 11.84375 \n", "Q 23.34375 6.203125 30.609375 6.203125 \n", "Q 37.796875 6.203125 41.984375 11.859375 \n", "Q 46.1875 17.53125 46.1875 27.296875 \n", "Q 46.1875 37.015625 41.984375 42.703125 \n", "Q 37.796875 48.390625 30.609375 48.390625 \n", "M 30.609375 56 \n", "Q 42.328125 56 49.015625 48.375 \n", "Q 55.71875 40.765625 55.71875 27.296875 \n", "Q 55.71875 13.875 49.015625 6.21875 \n", "Q 42.328125 -1.421875 30.609375 -1.421875 \n", "Q 18.84375 -1.421875 12.171875 6.21875 \n", "Q 5.515625 13.875 5.515625 27.296875 \n", "Q 5.515625 40.765625 12.171875 48.375 \n", "Q 18.84375 56 30.609375 56 \n", "\" id=\"BitstreamVeraSans-Roman-6f\"/>\n", "      <path d=\"M 9.421875 75.984375 \n", "L 18.40625 75.984375 \n", "L 18.40625 0 \n", "L 9.421875 0 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-6c\"/>\n", "     </defs>\n", "     <g transform=\"translate(554.650621875 32.837440625)scale(0.06939999999999999 -0.06939999999999999)\">\n", "      <use xlink:href=\"#BitstreamVeraSans-Roman-63\"/>\n", "      <use x=\"54.98046875\" xlink:href=\"#BitstreamVeraSans-Roman-6f\"/>\n", "      <use x=\"116.162109375\" xlink:href=\"#BitstreamVeraSans-Roman-6e\"/>\n", "      <use x=\"179.541015625\" xlink:href=\"#BitstreamVeraSans-Roman-74\"/>\n", "      <use x=\"218.75\" xlink:href=\"#BitstreamVeraSans-Roman-72\"/>\n", "      <use x=\"259.83203125\" xlink:href=\"#BitstreamVeraSans-Roman-6f\"/>\n", "      <use x=\"321.013671875\" xlink:href=\"#BitstreamVeraSans-Roman-6c\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_28\">\n", "     <path d=\"M 537.300622 40.595059 \n", "L 547.016622 40.595059 \n", "\" style=\"fill:none;stroke:#008000;stroke-linecap:square;\"/>\n", "    </g>\n", "    <g id=\"line2d_29\"/>\n", "    <g id=\"text_14\">\n", "     <!-- target -->\n", "     <g transform=\"translate(554.650621875 43.024059375)scale(0.06939999999999999 -0.06939999999999999)\">\n", "      <use xlink:href=\"#BitstreamVeraSans-Roman-74\"/>\n", "      <use x=\"39.208984375\" xlink:href=\"#BitstreamVeraSans-Roman-61\"/>\n", "      <use x=\"100.48828125\" xlink:href=\"#BitstreamVeraSans-Roman-72\"/>\n", "      <use x=\"141.5859375\" xlink:href=\"#BitstreamVeraSans-Roman-67\"/>\n", "      <use x=\"205.0625\" xlink:href=\"#BitstreamVeraSans-Roman-65\"/>\n", "      <use x=\"266.5859375\" xlink:href=\"#BitstreamVeraSans-Roman-74\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_30\">\n", "     <path d=\"M 537.300622 50.781678 \n", "L 547.016622 50.781678 \n", "\" style=\"fill:none;stroke:#ff0000;stroke-linecap:square;\"/>\n", "    </g>\n", "    <g id=\"line2d_31\"/>\n", "    <g id=\"text_15\">\n", "     <!-- model -->\n", "     <defs>\n", "      <path d=\"M 45.40625 46.390625 \n", "L 45.40625 75.984375 \n", "L 54.390625 75.984375 \n", "L 54.390625 0 \n", "L 45.40625 0 \n", "L 45.40625 8.203125 \n", "Q 42.578125 3.328125 38.25 0.953125 \n", "Q 33.9375 -1.421875 27.875 -1.421875 \n", "Q 17.96875 -1.421875 11.734375 6.484375 \n", "Q 5.515625 14.40625 5.515625 27.296875 \n", "Q 5.515625 40.1875 11.734375 48.09375 \n", "Q 17.96875 56 27.875 56 \n", "Q 33.9375 56 38.25 53.625 \n", "Q 42.578125 51.265625 45.40625 46.390625 \n", "M 14.796875 27.296875 \n", "Q 14.796875 17.390625 18.875 11.75 \n", "Q 22.953125 6.109375 30.078125 6.109375 \n", "Q 37.203125 6.109375 41.296875 11.75 \n", "Q 45.40625 17.390625 45.40625 27.296875 \n", "Q 45.40625 37.203125 41.296875 42.84375 \n", "Q 37.203125 48.484375 30.078125 48.484375 \n", "Q 22.953125 48.484375 18.875 42.84375 \n", "Q 14.796875 37.203125 14.796875 27.296875 \n", "\" id=\"BitstreamVeraSans-Roman-64\"/>\n", "      <path d=\"M 52 44.1875 \n", "Q 55.375 50.25 60.0625 53.125 \n", "Q 64.75 56 71.09375 56 \n", "Q 79.640625 56 84.28125 50.015625 \n", "Q 88.921875 44.046875 88.921875 33.015625 \n", "L 88.921875 0 \n", "L 79.890625 0 \n", "L 79.890625 32.71875 \n", "Q 79.890625 40.578125 77.09375 44.375 \n", "Q 74.3125 48.1875 68.609375 48.1875 \n", "Q 61.625 48.1875 57.5625 43.546875 \n", "Q 53.515625 38.921875 53.515625 30.90625 \n", "L 53.515625 0 \n", "L 44.484375 0 \n", "L 44.484375 32.71875 \n", "Q 44.484375 40.625 41.703125 44.40625 \n", "Q 38.921875 48.1875 33.109375 48.1875 \n", "Q 26.21875 48.1875 22.15625 43.53125 \n", "Q 18.109375 38.875 18.109375 30.90625 \n", "L 18.109375 0 \n", "L 9.078125 0 \n", "L 9.078125 54.6875 \n", "L 18.109375 54.6875 \n", "L 18.109375 46.1875 \n", "Q 21.1875 51.21875 25.484375 53.609375 \n", "Q 29.78125 56 35.6875 56 \n", "Q 41.65625 56 45.828125 52.96875 \n", "Q 50 49.953125 52 44.1875 \n", "\" id=\"BitstreamVeraSans-Roman-6d\"/>\n", "     </defs>\n", "     <g transform=\"translate(554.650621875 53.210678125)scale(0.06939999999999999 -0.06939999999999999)\">\n", "      <use xlink:href=\"#BitstreamVeraSans-Roman-6d\"/>\n", "      <use x=\"97.412109375\" xlink:href=\"#BitstreamVeraSans-Roman-6f\"/>\n", "      <use x=\"158.59375\" xlink:href=\"#BitstreamVeraSans-Roman-64\"/>\n", "      <use x=\"222.0703125\" xlink:href=\"#BitstreamVeraSans-Roman-65\"/>\n", "      <use x=\"283.59375\" xlink:href=\"#BitstreamVeraSans-Roman-6c\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pdeef12bd97\">\n", "   <rect height=\"83.7\" width=\"558.0\" x=\"27.103125\" y=\"21.318125\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<matplotlib.figure.Figure at 0x1095bd048>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Created with matplotlib (http://matplotlib.org/) -->\n", "<svg height=\"125pt\" version=\"1.1\" viewBox=\"0 0 605 125\" width=\"605pt\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", " <defs>\n", "  <style type=\"text/css\">\n", "*{stroke-linecap:butt;stroke-linejoin:round;stroke-miterlimit:100000;}\n", "  </style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M -0 125.89625 \n", "L 605.028125 125.89625 \n", "L 605.028125 0 \n", "L -0 0 \n", "L -0 125.89625 \n", "z\n", "\" style=\"fill:none;\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 27.103125 105.018125 \n", "L 585.103125 105.018125 \n", "L 585.103125 21.318125 \n", "L 27.103125 21.318125 \n", "z\n", "\" style=\"fill:#ffffff;\"/>\n", "   </g>\n", "   <g id=\"line2d_1\">\n", "    <path clip-path=\"url(#p286ae4acb9)\" d=\"M 27.103125 64.75374 \n", "L 42.169125 64.75374 \n", "L 42.448125 58.114111 \n", "L 49.981125 58.114111 \n", "L 50.260125 30.066109 \n", "L 62.257125 30.066109 \n", "L 62.536125 35.818552 \n", "L 96.016125 35.818552 \n", "L 96.295125 30.485311 \n", "L 170.788125 30.485311 \n", "L 171.067125 72.682751 \n", "L 188.644125 72.682751 \n", "L 188.923125 50.219455 \n", "L 193.108125 50.219455 \n", "L 193.387125 63.765931 \n", "L 197.293125 63.765931 \n", "L 197.572125 91.595887 \n", "L 245.560125 91.595887 \n", "L 245.839125 45.681258 \n", "L 267.601125 45.681258 \n", "L 267.880125 56.917334 \n", "L 276.250125 56.917334 \n", "L 276.529125 80.992817 \n", "L 343.768125 80.992817 \n", "L 344.047125 44.251797 \n", "L 435.280125 44.251797 \n", "L 435.559125 33.662474 \n", "L 531.535125 33.662474 \n", "L 531.814125 48.503289 \n", "L 584.824125 48.503289 \n", "L 584.824125 48.503289 \n", "\" style=\"fill:none;stroke:#0000ff;stroke-linecap:square;\"/>\n", "   </g>\n", "   <g id=\"line2d_2\">\n", "    <path clip-path=\"url(#p286ae4acb9)\" d=\"M 27.103125 83.964034 \n", "L 28.219125 31.577926 \n", "L 28.498125 28.29366 \n", "L 28.777125 31.743582 \n", "L 29.056125 41.261518 \n", "L 30.172125 94.137586 \n", "L 30.451125 98.022075 \n", "L 30.730125 95.176337 \n", "L 31.009125 86.14988 \n", "L 32.125125 32.869659 \n", "L 32.404125 28.391251 \n", "L 32.683125 30.628186 \n", "L 32.962125 39.148516 \n", "L 34.078125 92.746428 \n", "L 34.357125 97.811488 \n", "L 34.636125 96.186985 \n", "L 34.915125 88.186608 \n", "L 36.310125 28.714492 \n", "L 36.589125 29.723926 \n", "L 36.868125 37.191367 \n", "L 38.263125 97.376117 \n", "L 38.542125 96.983389 \n", "L 38.821125 90.061004 \n", "L 39.379125 62.976803 \n", "L 39.937125 36.033243 \n", "L 40.216125 29.261286 \n", "L 40.495125 29.03667 \n", "L 40.774125 35.402767 \n", "L 41.332125 61.9547 \n", "L 41.890125 89.398627 \n", "L 42.169125 96.718787 \n", "L 42.448125 97.404154 \n", "L 42.727125 90.721186 \n", "L 43.285125 62.279792 \n", "L 43.843125 34.562004 \n", "L 44.122125 28.638095 \n", "L 44.401125 30.145796 \n", "L 44.680125 38.760617 \n", "L 45.796125 94.209967 \n", "L 46.075125 98.042676 \n", "L 46.354125 94.369628 \n", "L 46.633125 83.981341 \n", "L 47.749125 30.261277 \n", "L 48.028125 28.590232 \n", "L 48.307125 34.361098 \n", "L 48.865125 61.92615 \n", "L 49.423125 90.502873 \n", "L 49.702125 97.334976 \n", "L 49.981125 96.813634 \n", "L 50.260125 85.331823 \n", "L 51.097125 31.212625 \n", "L 51.376125 29.014234 \n", "L 51.655125 39.363036 \n", "L 52.492125 94.187001 \n", "L 52.771125 97.69655 \n", "L 53.050125 88.521313 \n", "L 53.887125 33.207436 \n", "L 54.166125 28.400482 \n", "L 54.445125 36.366197 \n", "L 55.282125 91.953212 \n", "L 55.561125 98.038732 \n", "L 55.840125 91.313757 \n", "L 56.956125 28.331211 \n", "L 57.235125 33.789091 \n", "L 57.793125 71.11384 \n", "L 58.072125 89.268632 \n", "L 58.351125 97.83482 \n", "L 58.630125 93.665425 \n", "L 59.188125 57.362755 \n", "L 59.467125 38.566075 \n", "L 59.746125 28.807507 \n", "L 60.025125 31.672077 \n", "L 60.304125 46.107423 \n", "L 60.862125 86.175303 \n", "L 61.141125 97.088008 \n", "L 61.420125 95.539488 \n", "L 61.699125 82.098626 \n", "L 62.257125 41.845984 \n", "L 62.536125 30.227095 \n", "L 62.815125 29.324521 \n", "L 63.094125 39.431886 \n", "L 63.931125 92.25916 \n", "L 64.210125 98.042537 \n", "L 64.489125 92.480634 \n", "L 65.047125 57.660742 \n", "L 65.326125 39.730295 \n", "L 65.605125 29.4246 \n", "L 65.884125 30.096285 \n", "L 66.163125 41.52684 \n", "L 67.000125 93.689168 \n", "L 67.279125 97.949334 \n", "L 67.558125 90.894542 \n", "L 68.674125 28.833463 \n", "L 68.953125 31.075201 \n", "L 69.232125 43.757348 \n", "L 70.069125 94.928001 \n", "L 70.348125 97.638273 \n", "L 70.627125 89.134781 \n", "L 71.464125 35.951673 \n", "L 71.743125 28.457389 \n", "L 72.022125 32.255136 \n", "L 72.301125 46.10944 \n", "L 72.859125 84.154137 \n", "L 73.138125 95.9679 \n", "L 73.417125 97.111302 \n", "L 73.696125 87.212372 \n", "L 74.533125 34.312408 \n", "L 74.812125 28.298732 \n", "L 75.091125 33.628702 \n", "L 75.649125 68.257624 \n", "L 75.928125 86.291159 \n", "L 76.207125 96.802352 \n", "L 76.486125 96.371721 \n", "L 76.765125 85.139357 \n", "L 77.602125 32.853886 \n", "L 77.881125 28.358486 \n", "L 78.160125 35.187293 \n", "L 79.276125 97.426129 \n", "L 79.555125 95.424162 \n", "L 79.834125 82.928722 \n", "L 80.671125 31.585242 \n", "L 80.950125 28.636276 \n", "L 81.229125 36.921147 \n", "L 82.345125 97.835324 \n", "L 82.624125 94.274562 \n", "L 82.903125 80.594312 \n", "L 83.461125 42.521326 \n", "L 83.740125 30.514425 \n", "L 84.019125 29.130364 \n", "L 84.298125 38.819405 \n", "L 85.135125 91.784283 \n", "L 85.414125 98.027375 \n", "L 85.693125 92.930121 \n", "L 86.251125 58.497258 \n", "L 86.530125 40.363285 \n", "L 86.809125 29.64814 \n", "L 87.088125 29.837654 \n", "L 87.367125 40.870175 \n", "L 88.204125 93.271106 \n", "L 88.483125 98.001079 \n", "L 88.762125 91.39926 \n", "L 89.878125 28.991813 \n", "L 90.157125 30.753715 \n", "L 90.436125 43.060613 \n", "L 91.273125 94.569372 \n", "L 91.552125 97.756599 \n", "L 91.831125 89.691567 \n", "L 92.947125 28.549557 \n", "L 93.226125 31.872811 \n", "L 93.505125 45.376998 \n", "L 94.342125 95.670951 \n", "L 94.621125 97.295468 \n", "L 94.900125 87.81774 \n", "L 95.737125 34.795732 \n", "L 96.016125 28.324141 \n", "L 96.295125 33.82058 \n", "L 96.853125 70.97445 \n", "L 97.132125 89.106288 \n", "L 97.411125 97.795529 \n", "L 97.690125 93.878917 \n", "L 98.248125 58.001403 \n", "L 98.527125 39.101447 \n", "L 98.806125 28.962788 \n", "L 99.085125 31.276331 \n", "L 99.364125 45.199847 \n", "L 99.922125 85.220502 \n", "L 100.201125 96.748415 \n", "L 100.480125 96.05167 \n", "L 100.759125 83.383912 \n", "L 101.596125 30.412154 \n", "L 101.875125 29.487967 \n", "L 102.154125 40.824793 \n", "L 102.991125 94.905396 \n", "L 103.270125 97.445032 \n", "L 103.549125 87.506411 \n", "L 104.386125 32.637889 \n", "L 104.665125 28.497876 \n", "L 104.944125 36.979312 \n", "L 105.781125 92.310153 \n", "L 106.060125 98.025977 \n", "L 106.339125 91.052055 \n", "L 107.455125 28.329524 \n", "L 107.734125 33.754546 \n", "L 108.292125 70.854948 \n", "L 108.571125 89.024199 \n", "L 108.850125 97.780736 \n", "L 109.129125 93.936806 \n", "L 109.687125 58.122646 \n", "L 109.966125 39.190298 \n", "L 110.245125 28.986902 \n", "L 110.524125 31.226929 \n", "L 110.803125 45.094914 \n", "L 111.361125 85.125415 \n", "L 111.640125 96.715122 \n", "L 111.919125 96.092291 \n", "L 112.198125 83.483659 \n", "L 113.035125 30.454428 \n", "L 113.314125 29.456368 \n", "L 113.593125 40.730825 \n", "L 114.430125 94.854392 \n", "L 114.709125 97.467421 \n", "L 114.988125 87.594044 \n", "L 115.825125 32.697321 \n", "L 116.104125 28.484828 \n", "L 116.383125 36.898535 \n", "L 117.220125 92.242647 \n", "L 117.499125 98.029605 \n", "L 117.778125 91.125496 \n", "L 118.894125 28.335337 \n", "L 119.173125 33.688876 \n", "L 119.731125 70.735351 \n", "L 120.010125 88.94179 \n", "L 120.289125 97.765516 \n", "L 120.568125 93.994315 \n", "L 121.126125 58.243952 \n", "L 121.405125 39.279446 \n", "L 121.684125 29.011438 \n", "L 121.963125 31.177922 \n", "L 122.242125 44.990204 \n", "L 122.800125 85.030058 \n", "L 123.079125 96.681415 \n", "L 123.358125 96.132505 \n", "L 123.637125 83.583155 \n", "L 124.474125 30.497106 \n", "L 124.753125 29.425185 \n", "L 125.032125 40.637133 \n", "L 125.869125 94.802996 \n", "L 126.148125 97.489387 \n", "L 126.427125 87.681375 \n", "L 127.264125 32.757129 \n", "L 127.543125 28.472209 \n", "L 127.822125 36.818083 \n", "L 128.659125 92.174782 \n", "L 128.938125 98.032802 \n", "L 129.217125 91.198592 \n", "L 130.333125 28.341581 \n", "L 130.612125 33.62357 \n", "L 131.170125 70.615661 \n", "L 131.449125 88.859063 \n", "L 131.728125 97.749869 \n", "L 132.007125 94.051444 \n", "L 132.565125 58.365318 \n", "L 132.844125 39.368889 \n", "L 133.123125 29.036397 \n", "L 133.402125 31.12931 \n", "L 133.681125 44.885718 \n", "L 134.239125 84.93443 \n", "L 134.518125 96.647294 \n", "L 134.797125 96.172312 \n", "L 135.076125 83.682399 \n", "L 135.913125 30.540188 \n", "L 136.192125 29.394419 \n", "L 136.471125 40.54372 \n", "L 137.308125 94.75121 \n", "L 137.587125 97.510929 \n", "L 137.866125 87.768403 \n", "L 138.703125 32.817312 \n", "L 138.982125 28.460019 \n", "L 139.261125 36.737956 \n", "L 140.098125 92.106558 \n", "L 140.377125 98.035568 \n", "L 140.656125 91.271342 \n", "L 141.772125 28.348255 \n", "L 142.051125 33.558629 \n", "L 142.609125 70.495878 \n", "L 142.888125 88.776019 \n", "L 143.167125 97.733794 \n", "L 143.446125 94.108191 \n", "L 144.004125 58.486743 \n", "L 144.283125 39.458625 \n", "L 144.562125 29.061776 \n", "L 144.841125 31.081093 \n", "L 145.120125 44.781458 \n", "L 145.678125 84.838533 \n", "L 145.957125 96.612759 \n", "L 146.236125 96.211712 \n", "L 146.515125 83.781389 \n", "L 147.352125 30.583672 \n", "L 147.631125 29.36407 \n", "L 147.910125 40.450587 \n", "L 148.747125 94.699034 \n", "L 149.026125 97.532047 \n", "L 149.305125 87.855127 \n", "L 150.142125 32.877871 \n", "L 150.421125 28.448257 \n", "L 150.700125 36.658156 \n", "L 151.537125 92.037977 \n", "L 151.816125 98.037904 \n", "L 152.095125 91.343745 \n", "L 153.211125 28.355359 \n", "L 153.490125 33.494054 \n", "L 154.048125 70.376006 \n", "L 154.327125 88.692659 \n", "L 154.606125 97.717293 \n", "L 154.885125 94.164556 \n", "L 155.443125 58.608227 \n", "L 155.722125 39.548655 \n", "L 156.001125 29.087578 \n", "L 156.280125 31.033273 \n", "L 156.559125 44.677426 \n", "L 157.117125 84.742369 \n", "L 157.396125 96.577812 \n", "L 157.675125 96.250703 \n", "L 157.954125 83.880125 \n", "L 158.791125 30.627559 \n", "L 159.070125 29.334139 \n", "L 159.349125 40.357733 \n", "L 160.186125 94.646468 \n", "L 160.465125 97.552741 \n", "L 160.744125 87.941547 \n", "L 161.581125 32.938803 \n", "L 161.860125 28.436924 \n", "L 162.139125 36.578683 \n", "L 162.976125 91.969039 \n", "L 163.255125 98.03981 \n", "L 163.534125 91.4158 \n", "L 164.650125 28.362892 \n", "L 164.929125 33.429845 \n", "L 165.487125 70.256044 \n", "L 165.766125 88.608983 \n", "L 166.045125 97.700365 \n", "L 166.324125 94.220538 \n", "L 166.603125 79.436309 \n", "L 167.161125 39.638976 \n", "L 167.440125 29.1138 \n", "L 167.719125 30.98585 \n", "L 167.998125 44.573621 \n", "L 168.556125 84.645938 \n", "L 168.835125 96.542451 \n", "L 169.114125 96.289286 \n", "L 169.393125 83.978605 \n", "L 170.230125 30.671848 \n", "L 170.509125 29.304626 \n", "L 171.067125 52.858186 \n", "L 171.904125 91.514183 \n", "L 172.183125 97.309143 \n", "L 172.462125 97.270979 \n", "L 172.741125 91.406211 \n", "L 173.299125 67.029234 \n", "L 173.857125 40.126225 \n", "L 174.136125 31.507322 \n", "L 174.415125 28.297789 \n", "L 174.694125 31.045988 \n", "L 174.973125 39.282378 \n", "L 176.368125 97.014058 \n", "L 176.647125 97.52365 \n", "L 176.926125 92.163468 \n", "L 177.484125 68.343417 \n", "L 178.042125 41.137564 \n", "L 178.321125 32.085838 \n", "L 178.600125 28.344641 \n", "L 178.879125 30.553171 \n", "L 179.158125 38.334091 \n", "L 180.553125 96.670091 \n", "L 180.832125 97.726703 \n", "L 181.111125 92.878849 \n", "L 181.669125 69.650127 \n", "L 182.227125 42.180721 \n", "L 182.506125 32.709245 \n", "L 182.785125 28.441787 \n", "L 183.064125 30.107457 \n", "L 183.343125 37.421671 \n", "L 183.901125 63.245513 \n", "L 184.459125 89.018726 \n", "L 184.738125 96.277739 \n", "L 185.017125 97.879845 \n", "L 185.296125 93.551321 \n", "L 185.854125 70.947475 \n", "L 186.691125 33.376642 \n", "L 186.970125 28.589085 \n", "L 187.249125 29.709491 \n", "L 187.528125 36.546434 \n", "L 188.086125 61.920342 \n", "L 188.644125 88.110581 \n", "L 188.923125 96.762522 \n", "L 189.202125 97.122952 \n", "L 189.481125 89.10291 \n", "L 190.597125 31.060747 \n", "L 190.876125 28.470794 \n", "L 191.155125 34.444572 \n", "L 191.713125 64.435974 \n", "L 192.271125 93.252926 \n", "L 192.550125 98.029201 \n", "L 192.829125 94.201332 \n", "L 193.387125 68.322067 \n", "L 193.945125 39.526505 \n", "L 194.224125 30.771743 \n", "L 194.503125 28.37213 \n", "L 194.782125 32.798396 \n", "L 195.340125 57.486697 \n", "L 195.898125 86.414434 \n", "L 196.177125 95.363009 \n", "L 196.456125 97.995962 \n", "L 196.735125 93.79679 \n", "L 197.293125 69.375707 \n", "L 198.130125 35.567696 \n", "L 198.409125 29.786129 \n", "L 198.688125 28.379407 \n", "L 198.967125 31.531886 \n", "L 199.246125 38.830421 \n", "L 200.083125 74.127457 \n", "L 200.641125 93.379574 \n", "L 200.920125 97.602813 \n", "L 201.199125 97.313249 \n", "L 201.478125 92.548828 \n", "L 202.036125 72.597624 \n", "L 202.873125 37.710529 \n", "L 203.152125 30.892101 \n", "L 203.431125 28.303576 \n", "L 203.710125 30.28419 \n", "L 203.989125 36.574376 \n", "L 204.547125 58.329296 \n", "L 205.384125 91.59398 \n", "L 205.663125 96.924937 \n", "L 205.942125 97.831928 \n", "L 206.221125 94.196088 \n", "L 206.779125 75.734792 \n", "L 207.616125 40.082167 \n", "L 207.895125 32.28816 \n", "L 208.174125 28.541097 \n", "L 208.453125 29.332045 \n", "L 208.732125 34.557348 \n", "L 209.290125 55.080434 \n", "L 210.127125 89.552903 \n", "L 210.406125 95.943664 \n", "L 210.685125 98.039059 \n", "L 210.964125 95.564478 \n", "L 211.243125 88.844224 \n", "L 211.801125 66.630556 \n", "L 212.359125 42.661293 \n", "L 212.638125 33.961757 \n", "L 212.917125 29.089834 \n", "L 213.196125 28.684007 \n", "L 213.475125 32.797463 \n", "L 214.033125 51.904262 \n", "L 214.870125 87.274689 \n", "L 215.149125 94.667816 \n", "L 215.428125 97.932782 \n", "L 215.707125 96.6417 \n", "L 215.986125 90.963771 \n", "L 216.544125 69.901229 \n", "L 217.381125 35.897852 \n", "L 217.660125 29.944855 \n", "L 217.939125 28.345902 \n", "L 218.218125 31.31054 \n", "L 218.497125 38.450244 \n", "L 219.334125 73.618468 \n", "L 219.892125 93.108859 \n", "L 220.171125 97.514052 \n", "L 220.450125 97.418073 \n", "L 220.729125 92.8335 \n", "L 221.287125 73.111388 \n", "L 222.124125 38.079044 \n", "L 222.403125 31.098477 \n", "L 222.682125 28.320767 \n", "L 222.961125 30.109943 \n", "L 223.240125 36.231527 \n", "L 223.798125 57.800245 \n", "L 224.635125 91.280805 \n", "L 224.914125 96.786632 \n", "L 225.193125 97.886618 \n", "L 225.472125 94.436606 \n", "L 225.751125 86.888734 \n", "L 227.146125 32.54033 \n", "L 227.425125 28.608829 \n", "L 227.704125 29.206462 \n", "L 227.983125 34.254908 \n", "L 228.541125 54.561172 \n", "L 229.378125 89.200083 \n", "L 229.657125 95.757059 \n", "L 229.936125 98.043125 \n", "L 230.215125 95.758681 \n", "L 230.494125 89.203114 \n", "L 231.052125 67.162299 \n", "L 231.889125 34.257456 \n", "L 232.168125 29.207498 \n", "L 232.447125 28.608217 \n", "L 232.726125 32.538151 \n", "L 233.284125 51.399455 \n", "L 234.121125 86.885394 \n", "L 234.400125 94.434588 \n", "L 234.679125 97.886187 \n", "L 234.958125 96.787843 \n", "L 235.237125 91.283501 \n", "L 235.795125 70.425169 \n", "L 236.632125 36.234421 \n", "L 236.911125 30.111395 \n", "L 237.190125 28.320586 \n", "L 237.469125 31.096687 \n", "L 237.748125 38.075879 \n", "L 238.306125 60.553968 \n", "L 238.864125 84.357542 \n", "L 239.143125 92.831104 \n", "L 239.422125 97.417214 \n", "L 239.701125 97.514842 \n", "L 239.980125 93.111195 \n", "L 240.538125 73.622815 \n", "L 241.375125 38.453458 \n", "L 241.654125 31.312395 \n", "L 241.933125 28.346153 \n", "L 242.212125 29.94347 \n", "L 242.491125 35.895013 \n", "L 243.049125 57.272457 \n", "L 243.886125 90.961019 \n", "L 244.165125 96.640421 \n", "L 244.444125 97.933144 \n", "L 244.723125 94.669772 \n", "L 245.002125 87.277981 \n", "L 245.560125 64.398134 \n", "L 246.118125 33.590146 \n", "L 246.397125 28.31392 \n", "L 246.676125 32.385466 \n", "L 246.955125 44.712812 \n", "L 247.792125 92.773474 \n", "L 248.071125 98.024078 \n", "L 248.350125 93.92644 \n", "L 248.629125 81.579532 \n", "L 249.466125 33.535471 \n", "L 249.745125 28.310502 \n", "L 250.024125 32.434222 \n", "L 250.582125 62.093195 \n", "L 251.140125 92.828019 \n", "L 251.419125 98.027343 \n", "L 251.698125 93.877549 \n", "L 252.256125 64.191357 \n", "L 252.814125 33.481057 \n", "L 253.093125 28.30739 \n", "L 253.372125 32.483248 \n", "L 253.930125 62.196593 \n", "L 254.488125 92.882303 \n", "L 254.767125 98.030301 \n", "L 255.046125 93.828388 \n", "L 255.604125 64.087954 \n", "L 256.162125 33.426903 \n", "L 256.441125 28.304585 \n", "L 256.720125 32.532543 \n", "L 257.278125 62.3 \n", "L 257.836125 92.936325 \n", "L 258.115125 98.032953 \n", "L 258.394125 93.778958 \n", "L 258.952125 63.984544 \n", "L 259.510125 33.373012 \n", "L 259.789125 28.302087 \n", "L 260.068125 32.582108 \n", "L 260.626125 62.403415 \n", "L 261.184125 92.990086 \n", "L 261.463125 98.035297 \n", "L 261.742125 93.729258 \n", "L 262.300125 63.881126 \n", "L 262.858125 33.319382 \n", "L 263.137125 28.299895 \n", "L 263.416125 32.631943 \n", "L 263.974125 62.506836 \n", "L 264.532125 93.043584 \n", "L 264.811125 98.037336 \n", "L 265.090125 93.679289 \n", "L 265.648125 63.777701 \n", "L 266.206125 33.266015 \n", "L 266.485125 28.29801 \n", "L 266.764125 32.682046 \n", "L 267.322125 62.610263 \n", "L 267.880125 92.14864 \n", "L 268.159125 97.805092 \n", "L 268.438125 95.854951 \n", "L 268.717125 86.726486 \n", "L 269.833125 31.329288 \n", "L 270.112125 28.341471 \n", "L 270.391125 33.001904 \n", "L 270.949125 59.718764 \n", "L 271.507125 89.299312 \n", "L 271.786125 96.952051 \n", "L 272.065125 97.185534 \n", "L 272.344125 89.948484 \n", "L 272.902125 60.711643 \n", "L 273.460125 33.514194 \n", "L 273.739125 28.408132 \n", "L 274.018125 30.935681 \n", "L 274.297125 40.541767 \n", "L 275.413125 95.494239 \n", "L 275.692125 97.907165 \n", "L 275.971125 92.691083 \n", "L 277.366125 33.269355 \n", "L 277.645125 28.680358 \n", "L 277.924125 29.313158 \n", "L 278.203125 35.071943 \n", "L 278.761125 57.835606 \n", "L 279.319125 83.706611 \n", "L 279.598125 92.909651 \n", "L 279.877125 97.609522 \n", "L 280.156125 97.094619 \n", "L 280.435125 91.4429 \n", "L 280.993125 68.80013 \n", "L 281.551125 42.875504 \n", "L 281.830125 33.586093 \n", "L 282.109125 28.775701 \n", "L 282.388125 29.17267 \n", "L 282.667125 34.716894 \n", "L 283.225125 57.237059 \n", "L 284.062125 92.588427 \n", "L 284.341125 97.508975 \n", "L 284.620125 97.229972 \n", "L 284.899125 91.79366 \n", "L 285.457125 69.397804 \n", "L 286.294125 33.911778 \n", "L 286.573125 28.881445 \n", "L 286.852125 29.042463 \n", "L 287.131125 34.37045 \n", "L 287.689125 56.640305 \n", "L 288.526125 92.258305 \n", "L 288.805125 97.398042 \n", "L 289.084125 97.355023 \n", "L 289.363125 92.135762 \n", "L 289.921125 69.993593 \n", "L 290.758125 34.246311 \n", "L 291.037125 28.997559 \n", "L 291.316125 28.922577 \n", "L 291.595125 34.032716 \n", "L 292.153125 56.045525 \n", "L 292.990125 91.919385 \n", "L 293.269125 97.276756 \n", "L 293.548125 97.469735 \n", "L 293.827125 92.469103 \n", "L 294.385125 70.587318 \n", "L 295.222125 34.589592 \n", "L 295.501125 29.124008 \n", "L 295.780125 28.813048 \n", "L 296.059125 33.703794 \n", "L 296.617125 55.4529 \n", "L 297.454125 91.57177 \n", "L 297.733125 97.145154 \n", "L 298.012125 97.574072 \n", "L 298.291125 92.793582 \n", "L 298.849125 71.178799 \n", "L 299.686125 34.941516 \n", "L 299.965125 29.260753 \n", "L 300.244125 28.71391 \n", "L 300.523125 33.383783 \n", "L 301.081125 54.862608 \n", "L 301.918125 91.215564 \n", "L 302.197125 97.003276 \n", "L 302.476125 97.668003 \n", "L 302.755125 93.1091 \n", "L 303.313125 71.767857 \n", "L 304.150125 35.301977 \n", "L 304.429125 29.407754 \n", "L 304.708125 28.625192 \n", "L 304.987125 33.07278 \n", "L 305.545125 54.274828 \n", "L 306.382125 90.850875 \n", "L 306.661125 96.851164 \n", "L 306.940125 97.7515 \n", "L 307.219125 93.415564 \n", "L 307.777125 72.354314 \n", "L 308.614125 35.670866 \n", "L 308.893125 29.564965 \n", "L 309.172125 28.546922 \n", "L 309.451125 32.770879 \n", "L 310.009125 53.689738 \n", "L 310.846125 90.477814 \n", "L 311.125125 96.688866 \n", "L 311.404125 97.824538 \n", "L 311.683125 93.712879 \n", "L 312.241125 72.937993 \n", "L 313.078125 36.048071 \n", "L 313.357125 29.732339 \n", "L 313.636125 28.479123 \n", "L 313.915125 32.478172 \n", "L 314.473125 53.107515 \n", "L 315.310125 90.096493 \n", "L 315.589125 96.516429 \n", "L 315.868125 97.887094 \n", "L 316.147125 94.000956 \n", "L 316.705125 73.518717 \n", "L 317.542125 36.433479 \n", "L 317.821125 29.909825 \n", "L 318.100125 28.421815 \n", "L 318.379125 32.194747 \n", "L 318.658125 40.657362 \n", "L 320.053125 96.333906 \n", "L 320.332125 97.939149 \n", "L 320.611125 94.279708 \n", "L 320.890125 85.909658 \n", "L 322.285125 30.097371 \n", "L 322.564125 28.375016 \n", "L 322.843125 31.920689 \n", "L 323.122125 40.197541 \n", "L 324.517125 96.141352 \n", "L 324.796125 97.980688 \n", "L 325.075125 94.54905 \n", "L 325.354125 86.366023 \n", "L 326.749125 30.294918 \n", "L 327.028125 28.33874 \n", "L 327.307125 31.656082 \n", "L 327.586125 39.744667 \n", "L 328.981125 95.938826 \n", "L 329.260125 98.011699 \n", "L 329.539125 94.808902 \n", "L 329.818125 86.815371 \n", "L 331.213125 30.502408 \n", "L 331.492125 28.312998 \n", "L 331.771125 31.401006 \n", "L 332.050125 39.298878 \n", "L 333.445125 95.726389 \n", "L 333.724125 98.032171 \n", "L 334.003125 95.059184 \n", "L 334.282125 87.257568 \n", "L 335.677125 30.719777 \n", "L 335.956125 28.297797 \n", "L 336.235125 31.155538 \n", "L 336.514125 38.860308 \n", "L 337.909125 95.504104 \n", "L 338.188125 98.042098 \n", "L 338.467125 95.29982 \n", "L 338.746125 87.692479 \n", "L 340.141125 30.94696 \n", "L 340.420125 28.293143 \n", "L 340.699125 30.919751 \n", "L 340.978125 38.429089 \n", "L 342.373125 95.27204 \n", "L 342.652125 98.041478 \n", "L 342.931125 95.530739 \n", "L 343.210125 88.119972 \n", "L 343.768125 63.65864 \n", "L 344.326125 32.948963 \n", "L 344.605125 28.294372 \n", "L 344.884125 33.247669 \n", "L 345.442125 64.248249 \n", "L 346.000125 94.140496 \n", "L 346.279125 97.993232 \n", "L 346.558125 92.251481 \n", "L 347.116125 60.519552 \n", "L 347.674125 31.505368 \n", "L 347.953125 28.462304 \n", "L 348.232125 34.980863 \n", "L 348.790125 67.379773 \n", "L 349.348125 95.457042 \n", "L 349.627125 97.684262 \n", "L 349.906125 90.402119 \n", "L 351.022125 30.318543 \n", "L 351.301125 28.911684 \n", "L 351.580125 36.942642 \n", "L 352.696125 96.51174 \n", "L 352.975125 97.095383 \n", "L 353.254125 88.331902 \n", "L 354.370125 29.398112 \n", "L 354.649125 29.638868 \n", "L 354.928125 39.117097 \n", "L 356.044125 97.296037 \n", "L 356.323125 96.231371 \n", "L 356.602125 86.057619 \n", "L 357.439125 36.347059 \n", "L 357.718125 28.75154 \n", "L 357.997125 30.637957 \n", "L 358.276125 41.486594 \n", "L 359.113125 90.965681 \n", "L 359.392125 97.803573 \n", "L 359.671125 95.099231 \n", "L 359.950125 83.597714 \n", "L 360.787125 34.450465 \n", "L 361.066125 28.384069 \n", "L 361.345125 31.90085 \n", "L 361.624125 44.031918 \n", "L 362.461125 92.747639 \n", "L 362.740125 98.030232 \n", "L 363.019125 93.708145 \n", "L 363.577125 63.331031 \n", "L 364.135125 32.786757 \n", "L 364.414125 28.298681 \n", "L 364.693125 33.417306 \n", "L 365.251125 64.575664 \n", "L 365.809125 94.289722 \n", "L 366.088125 97.974177 \n", "L 366.367125 92.069395 \n", "L 366.925125 60.192997 \n", "L 367.483125 31.369427 \n", "L 367.762125 28.496067 \n", "L 368.041125 35.175027 \n", "L 368.599125 67.704809 \n", "L 369.157125 95.579424 \n", "L 369.436125 97.635861 \n", "L 369.715125 90.196269 \n", "L 370.831125 30.209969 \n", "L 371.110125 28.974626 \n", "L 371.389125 37.159759 \n", "L 372.505125 96.606285 \n", "L 372.784125 97.018028 \n", "L 373.063125 88.103958 \n", "L 374.179125 29.317786 \n", "L 374.458125 29.730479 \n", "L 374.737125 39.355405 \n", "L 375.574125 89.177697 \n", "L 375.853125 97.36198 \n", "L 376.132125 96.125689 \n", "L 376.411125 85.809429 \n", "L 377.248125 36.138837 \n", "L 377.527125 28.700113 \n", "L 377.806125 30.757494 \n", "L 378.085125 41.744162 \n", "L 378.922125 91.162302 \n", "L 379.201125 97.840378 \n", "L 379.480125 94.96608 \n", "L 379.759125 83.33129 \n", "L 380.596125 34.265842 \n", "L 380.875125 28.361959 \n", "L 381.154125 32.047345 \n", "L 381.433125 44.306656 \n", "L 382.270125 92.919888 \n", "L 382.549125 98.037602 \n", "L 382.828125 93.548605 \n", "L 383.386125 63.003409 \n", "L 383.944125 32.627231 \n", "L 384.223125 28.306067 \n", "L 384.502125 33.58957 \n", "L 385.060125 64.902953 \n", "L 385.618125 94.436201 \n", "L 385.897125 97.95205 \n", "L 386.176125 91.884759 \n", "L 386.734125 59.866703 \n", "L 387.292125 31.236291 \n", "L 387.571125 28.532889 \n", "L 387.850125 35.371662 \n", "L 388.408125 68.029443 \n", "L 388.966125 95.698945 \n", "L 389.245125 97.584418 \n", "L 389.524125 89.988034 \n", "L 390.640125 30.104304 \n", "L 390.919125 29.040586 \n", "L 391.198125 37.379171 \n", "L 392.314125 96.69788 \n", "L 392.593125 96.937686 \n", "L 392.872125 87.873813 \n", "L 393.988125 29.240448 \n", "L 394.267125 29.825041 \n", "L 394.546125 39.595816 \n", "L 395.383125 89.394801 \n", "L 395.662125 97.424905 \n", "L 395.941125 96.017099 \n", "L 396.220125 85.559241 \n", "L 397.057125 35.933001 \n", "L 397.336125 28.651729 \n", "L 397.615125 30.879892 \n", "L 397.894125 42.003621 \n", "L 398.731125 91.356453 \n", "L 399.010125 97.874124 \n", "L 399.289125 94.830123 \n", "L 399.568125 83.063088 \n", "L 400.405125 34.083771 \n", "L 400.684125 28.342921 \n", "L 400.963125 32.196585 \n", "L 401.242125 44.583059 \n", "L 402.079125 93.089511 \n", "L 402.358125 98.041894 \n", "L 402.637125 93.386383 \n", "L 403.195125 62.675801 \n", "L 403.753125 32.4704 \n", "L 404.032125 28.31653 \n", "L 404.311125 33.764443 \n", "L 404.869125 65.23009 \n", "L 405.427125 94.579921 \n", "L 405.706125 97.926854 \n", "L 405.985125 91.697588 \n", "L 406.543125 59.540701 \n", "L 407.101125 31.105974 \n", "L 407.380125 28.572768 \n", "L 407.659125 35.57075 \n", "L 408.775125 95.815596 \n", "L 409.054125 97.529938 \n", "L 409.333125 89.777432 \n", "L 410.449125 30.001556 \n", "L 410.728125 29.109557 \n", "L 411.007125 37.600859 \n", "L 412.123125 96.786516 \n", "L 412.402125 96.854364 \n", "L 412.681125 87.641488 \n", "L 413.797125 29.166103 \n", "L 414.076125 29.922545 \n", "L 414.355125 39.838306 \n", "L 415.192125 89.609591 \n", "L 415.471125 97.484807 \n", "L 415.750125 95.90561 \n", "L 416.029125 85.307077 \n", "L 416.866125 35.729568 \n", "L 417.145125 28.606391 \n", "L 417.424125 31.005139 \n", "L 417.703125 42.264947 \n", "L 418.540125 91.548116 \n", "L 418.819125 97.904806 \n", "L 419.098125 94.691372 \n", "L 419.377125 82.793129 \n", "L 420.214125 33.904266 \n", "L 420.493125 28.326957 \n", "L 420.772125 32.348559 \n", "L 421.051125 44.861102 \n", "L 421.888125 93.256493 \n", "L 422.167125 98.043108 \n", "L 422.446125 93.221495 \n", "L 423.004125 62.348236 \n", "L 423.562125 32.316279 \n", "L 423.841125 28.330068 \n", "L 424.120125 33.941911 \n", "L 424.678125 65.557045 \n", "L 425.236125 94.720868 \n", "L 425.515125 97.89859 \n", "L 425.794125 91.5079 \n", "L 426.352125 59.215019 \n", "L 426.910125 30.978486 \n", "L 427.189125 28.615699 \n", "L 427.468125 35.772274 \n", "L 428.584125 95.929366 \n", "L 428.863125 97.472425 \n", "L 429.142125 89.564482 \n", "L 430.258125 29.901736 \n", "L 430.537125 29.181534 \n", "L 430.816125 37.824804 \n", "L 431.932125 96.872185 \n", "L 432.211125 96.768068 \n", "L 432.490125 87.407004 \n", "L 433.606125 29.09476 \n", "L 433.885125 30.022984 \n", "L 434.164125 40.082856 \n", "L 435.001125 89.822047 \n", "L 435.280125 97.541681 \n", "L 435.559125 94.982351 \n", "L 435.838125 81.599786 \n", "L 436.675125 30.431843 \n", "L 436.954125 29.29138 \n", "L 437.233125 39.675824 \n", "L 438.070125 93.182826 \n", "L 438.349125 97.985306 \n", "L 438.628125 90.942941 \n", "L 439.744125 28.563742 \n", "L 440.023125 32.030766 \n", "L 440.302125 46.090754 \n", "L 440.860125 84.880249 \n", "L 441.139125 96.413526 \n", "L 441.418125 96.63668 \n", "L 441.697125 85.473793 \n", "L 442.534125 32.382878 \n", "L 442.813125 28.476938 \n", "L 443.092125 36.372979 \n", "L 444.208125 97.932884 \n", "L 444.487125 93.565225 \n", "L 445.045125 58.810469 \n", "L 445.324125 40.24698 \n", "L 445.603125 29.48129 \n", "L 445.882125 30.175899 \n", "L 446.161125 42.094501 \n", "L 446.998125 94.661243 \n", "L 447.277125 97.66269 \n", "L 447.556125 88.929048 \n", "L 448.393125 34.911856 \n", "L 448.672125 28.313767 \n", "L 448.951125 33.57317 \n", "L 449.509125 69.082206 \n", "L 449.788125 87.251626 \n", "L 450.067125 97.227811 \n", "L 450.346125 95.616853 \n", "L 450.625125 82.966801 \n", "L 451.462125 31.031253 \n", "L 451.741125 28.940408 \n", "L 452.020125 38.493871 \n", "L 452.857125 92.317728 \n", "L 453.136125 98.041122 \n", "L 453.415125 91.900683 \n", "L 454.531125 28.804929 \n", "L 454.810125 31.328957 \n", "L 455.089125 44.684706 \n", "L 455.926125 95.883324 \n", "L 456.205125 97.059309 \n", "L 456.484125 86.705475 \n", "L 457.321125 33.184534 \n", "L 457.600125 28.347487 \n", "L 457.879125 35.35646 \n", "L 458.995125 97.76487 \n", "L 459.274125 94.332912 \n", "L 459.553125 80.298658 \n", "L 460.111125 41.503772 \n", "L 460.390125 29.941204 \n", "L 460.669125 29.682473 \n", "L 460.948125 40.815598 \n", "L 461.785125 93.924661 \n", "L 462.064125 97.865514 \n", "L 462.343125 90.002275 \n", "L 463.459125 28.408265 \n", "L 463.738125 32.741167 \n", "L 464.296125 67.46525 \n", "L 464.575125 86.043259 \n", "L 464.854125 96.839121 \n", "L 465.133125 96.180073 \n", "L 465.412125 84.290322 \n", "L 466.249125 31.701261 \n", "L 466.528125 28.664627 \n", "L 466.807125 37.366122 \n", "L 467.644125 91.388596 \n", "L 467.923125 98.020331 \n", "L 468.202125 92.795307 \n", "L 468.760125 57.314171 \n", "L 469.039125 39.128781 \n", "L 469.318125 29.121604 \n", "L 469.597125 30.697091 \n", "L 469.876125 43.319261 \n", "L 470.713125 95.281254 \n", "L 470.992125 97.407488 \n", "L 471.271125 87.885452 \n", "L 472.108125 34.052056 \n", "L 472.387125 28.294527 \n", "L 472.666125 34.401036 \n", "L 473.782125 97.520856 \n", "L 474.061125 95.032137 \n", "L 474.340125 81.703246 \n", "L 475.177125 30.47411 \n", "L 475.456125 29.262606 \n", "L 475.735125 39.585797 \n", "L 476.572125 93.120515 \n", "L 476.851125 97.992115 \n", "L 477.130125 91.016554 \n", "L 478.246125 28.579123 \n", "L 478.525125 31.976006 \n", "L 478.804125 45.984483 \n", "L 479.362125 84.784641 \n", "L 479.641125 96.376464 \n", "L 479.920125 96.670773 \n", "L 480.199125 85.567443 \n", "L 481.036125 32.440393 \n", "L 481.315125 28.464641 \n", "L 481.594125 36.295053 \n", "L 482.710125 97.922979 \n", "L 482.989125 93.624847 \n", "L 483.547125 58.931543 \n", "L 483.826125 40.339072 \n", "L 484.105125 29.513071 \n", "L 484.384125 30.136557 \n", "L 484.663125 41.997419 \n", "L 485.500125 94.60864 \n", "L 485.779125 97.680451 \n", "L 486.058125 89.01113 \n", "L 486.895125 34.983539 \n", "L 487.174125 28.318177 \n", "L 487.453125 33.508807 \n", "L 488.011125 68.961934 \n", "L 488.290125 87.163237 \n", "L 488.569125 97.201377 \n", "L 488.848125 95.661365 \n", "L 489.127125 83.067117 \n", "L 489.964125 31.078837 \n", "L 490.243125 28.917221 \n", "L 490.522125 38.407801 \n", "L 491.359125 92.250571 \n", "L 491.638125 98.042216 \n", "L 491.917125 91.969656 \n", "L 493.033125 28.825964 \n", "L 493.312125 31.279366 \n", "L 493.591125 44.581359 \n", "L 494.428125 95.840856 \n", "L 494.707125 97.087876 \n", "L 494.986125 86.795358 \n", "L 495.823125 33.247027 \n", "L 496.102125 28.34089 \n", "L 496.381125 35.283018 \n", "L 497.497125 97.749278 \n", "L 497.776125 94.387481 \n", "L 498.055125 80.404824 \n", "L 498.613125 41.599513 \n", "L 498.892125 29.978469 \n", "L 499.171125 29.648584 \n", "L 499.450125 40.722085 \n", "L 500.287125 93.866958 \n", "L 500.566125 97.877599 \n", "L 500.845125 90.080037 \n", "L 501.961125 28.418384 \n", "L 502.240125 32.681732 \n", "L 502.798125 67.34415 \n", "L 503.077125 85.951027 \n", "L 503.356125 96.807135 \n", "L 503.635125 96.219213 \n", "L 503.914125 84.387274 \n", "L 504.751125 31.754056 \n", "L 505.030125 28.647077 \n", "L 505.309125 37.284198 \n", "L 506.146125 91.31674 \n", "L 506.425125 98.015708 \n", "L 506.704125 92.859489 \n", "L 507.262125 57.434479 \n", "L 507.541125 39.217317 \n", "L 507.820125 29.148247 \n", "L 508.099125 30.652778 \n", "L 508.378125 43.219066 \n", "L 509.215125 95.233474 \n", "L 509.494125 97.430465 \n", "L 509.773125 87.97137 \n", "L 510.610125 34.119391 \n", "L 510.889125 28.293647 \n", "L 511.168125 34.33224 \n", "L 511.726125 70.447718 \n", "L 512.005125 88.238078 \n", "L 512.284125 97.49961 \n", "L 512.563125 95.081533 \n", "L 512.842125 81.806479 \n", "L 513.679125 30.516778 \n", "L 513.958125 29.234246 \n", "L 514.237125 39.496059 \n", "L 515.074125 93.057839 \n", "L 515.353125 97.998498 \n", "L 515.632125 91.089825 \n", "L 516.748125 28.594927 \n", "L 517.027125 31.921628 \n", "L 517.306125 45.878422 \n", "L 517.864125 84.688768 \n", "L 518.143125 96.338996 \n", "L 518.422125 96.704456 \n", "L 518.701125 85.660819 \n", "L 519.538125 32.498284 \n", "L 519.817125 28.452768 \n", "L 520.096125 36.217456 \n", "L 521.212125 97.912647 \n", "L 521.491125 93.684096 \n", "L 522.049125 59.052669 \n", "L 522.328125 40.431444 \n", "L 522.607125 29.545263 \n", "L 522.886125 30.097618 \n", "L 523.165125 41.900597 \n", "L 524.002125 94.555652 \n", "L 524.281125 97.697789 \n", "L 524.560125 89.092895 \n", "L 525.397125 35.055567 \n", "L 525.676125 28.323014 \n", "L 525.955125 33.444807 \n", "L 526.513125 68.84159 \n", "L 526.792125 87.074555 \n", "L 527.071125 97.174525 \n", "L 527.350125 95.70548 \n", "L 527.629125 83.167189 \n", "L 528.466125 31.126813 \n", "L 528.745125 28.894453 \n", "L 529.024125 38.322035 \n", "L 529.861125 92.183059 \n", "L 530.140125 98.042884 \n", "L 530.419125 92.038276 \n", "L 530.977125 55.948201 \n", "L 531.256125 38.140617 \n", "L 531.535125 28.84742 \n", "L 531.814125 30.192495 \n", "L 532.093125 39.932843 \n", "L 533.209125 96.87426 \n", "L 533.488125 96.955079 \n", "L 533.767125 88.43407 \n", "L 534.883125 30.298163 \n", "L 535.162125 28.791981 \n", "L 535.441125 36.037628 \n", "L 536.557125 94.985809 \n", "L 536.836125 97.907452 \n", "L 537.115125 91.984802 \n", "L 537.673125 61.505808 \n", "L 538.231125 32.611907 \n", "L 538.510125 28.294012 \n", "L 538.789125 32.854724 \n", "L 539.347125 62.002723 \n", "L 539.905125 92.261984 \n", "L 540.184125 97.947743 \n", "L 540.463125 94.778952 \n", "L 540.742125 83.562354 \n", "L 541.579125 35.727904 \n", "L 541.858125 28.711662 \n", "L 542.137125 30.467698 \n", "L 542.416125 40.548943 \n", "L 543.253125 88.774301 \n", "L 543.532125 97.074896 \n", "L 543.811125 96.74316 \n", "L 544.090125 87.863549 \n", "L 545.206125 30.033967 \n", "L 545.485125 28.939222 \n", "L 545.764125 36.558821 \n", "L 546.880125 95.311827 \n", "L 547.159125 97.825855 \n", "L 547.438125 91.516365 \n", "L 547.996125 60.689591 \n", "L 548.554125 32.22621 \n", "L 548.833125 28.309426 \n", "L 549.112125 33.267326 \n", "L 549.670125 62.820189 \n", "L 550.228125 92.704825 \n", "L 550.507125 97.998612 \n", "L 550.786125 94.424899 \n", "L 551.065125 82.893517 \n", "L 551.902125 35.230832 \n", "L 552.181125 28.594845 \n", "L 552.460125 30.760876 \n", "L 552.739125 41.177476 \n", "L 553.576125 89.322336 \n", "L 553.855125 97.256894 \n", "L 554.134125 96.512786 \n", "L 554.413125 87.279453 \n", "L 555.529125 29.787985 \n", "L 555.808125 29.105279 \n", "L 556.087125 37.09464 \n", "L 557.203125 95.620176 \n", "L 557.482125 97.725208 \n", "L 557.761125 91.032346 \n", "L 558.319125 59.874737 \n", "L 558.877125 31.857521 \n", "L 559.156125 28.344002 \n", "L 559.435125 33.696363 \n", "L 559.993125 63.637846 \n", "L 560.551125 93.131429 \n", "L 560.830125 98.030335 \n", "L 561.109125 94.053664 \n", "L 561.388125 82.213837 \n", "L 562.225125 34.749117 \n", "L 562.504125 28.497032 \n", "L 562.783125 31.071868 \n", "L 563.062125 41.818096 \n", "L 563.899125 89.855995 \n", "L 564.178125 97.420154 \n", "L 564.457125 96.264082 \n", "L 564.736125 86.682103 \n", "L 565.852125 29.560352 \n", "L 566.131125 29.29006 \n", "L 566.410125 37.644791 \n", "L 567.526125 95.910686 \n", "L 567.805125 97.605565 \n", "L 568.084125 90.53301 \n", "L 568.642125 59.061694 \n", "L 569.200125 31.506043 \n", "L 569.479125 28.39772 \n", "L 569.758125 34.141601 \n", "L 570.316125 64.455245 \n", "L 570.874125 93.541563 \n", "L 571.153125 98.042894 \n", "L 571.432125 93.665451 \n", "L 571.990125 64.708778 \n", "L 572.548125 34.283023 \n", "L 572.827125 28.418278 \n", "L 573.106125 31.400503 \n", "L 573.385125 42.470453 \n", "L 574.222125 90.374984 \n", "L 574.501125 97.564586 \n", "L 574.780125 95.997186 \n", "L 575.059125 86.071828 \n", "L 575.896125 37.818381 \n", "L 576.175125 29.351192 \n", "L 576.454125 29.493463 \n", "L 576.733125 38.208973 \n", "L 577.849125 96.183199 \n", "L 578.128125 97.466993 \n", "L 578.407125 90.018631 \n", "L 579.523125 31.17197 \n", "L 579.802125 28.470551 \n", "L 580.081125 34.602795 \n", "L 580.639125 65.271937 \n", "L 581.197125 93.935001 \n", "L 581.476125 98.036283 \n", "L 581.755125 93.260475 \n", "L 582.313125 63.89155 \n", "L 582.871125 33.832807 \n", "L 583.150125 28.358624 \n", "L 583.429125 31.746599 \n", "L 583.708125 43.134187 \n", "L 584.545125 90.879018 \n", "L 584.824125 97.690111 \n", "L 584.824125 97.690111 \n", "\" style=\"fill:none;stroke:#008000;stroke-linecap:square;\"/>\n", "   </g>\n", "   <g id=\"line2d_3\">\n", "    <path clip-path=\"url(#p286ae4acb9)\" d=\"M 27.103125 17.411118 \n", "L 27.382125 10.487415 \n", "L 27.661125 13.205218 \n", "L 27.940125 21.856991 \n", "L 28.219125 39.735865 \n", "L 28.777125 84.704402 \n", "L 29.056125 94.048886 \n", "L 29.335125 95.662631 \n", "L 29.614125 91.747605 \n", "L 29.893125 81.429963 \n", "L 30.730125 28.136968 \n", "L 31.009125 22.018191 \n", "L 31.288125 22.921195 \n", "L 31.567125 30.830542 \n", "L 32.683125 90.71521 \n", "L 32.962125 94.00034 \n", "L 33.241125 92.227712 \n", "L 33.520125 85.153912 \n", "L 34.078125 54.858597 \n", "L 34.636125 29.639765 \n", "L 34.915125 26.740852 \n", "L 35.194125 29.966786 \n", "L 35.473125 39.677299 \n", "L 36.310125 84.154008 \n", "L 36.589125 91.246562 \n", "L 36.868125 92.925998 \n", "L 37.147125 89.932407 \n", "L 37.426125 81.933616 \n", "L 38.542125 29.245933 \n", "L 38.821125 27.036925 \n", "L 39.100125 30.884004 \n", "L 39.379125 41.045381 \n", "L 40.216125 84.921186 \n", "L 40.495125 91.534958 \n", "L 40.774125 92.941491 \n", "L 41.053125 89.9945 \n", "L 41.332125 82.015459 \n", "L 42.448125 29.429082 \n", "L 42.727125 27.033917 \n", "L 43.006125 31.210667 \n", "L 43.285125 42.664316 \n", "L 44.122125 86.855076 \n", "L 44.401125 92.249788 \n", "L 44.680125 92.197917 \n", "L 44.959125 87.097218 \n", "L 45.238125 75.893373 \n", "L 46.075125 32.073737 \n", "L 46.354125 27.324904 \n", "L 46.633125 29.62452 \n", "L 46.912125 38.870271 \n", "L 47.749125 84.231444 \n", "L 48.028125 91.379133 \n", "L 48.307125 92.599889 \n", "L 48.586125 88.850893 \n", "L 48.865125 79.301614 \n", "L 49.702125 33.885222 \n", "L 49.981125 28.097816 \n", "L 50.260125 31.497136 \n", "L 50.539125 45.036406 \n", "L 51.097125 83.659635 \n", "L 51.376125 91.741533 \n", "L 51.655125 90.802744 \n", "L 51.934125 81.258777 \n", "L 52.771125 30.234303 \n", "L 53.050125 30.051291 \n", "L 53.329125 40.87615 \n", "L 53.887125 79.808211 \n", "L 54.166125 90.09958 \n", "L 54.445125 91.324241 \n", "L 54.724125 84.507531 \n", "L 55.561125 34.393418 \n", "L 55.840125 30.966407 \n", "L 56.119125 38.488792 \n", "L 56.956125 87.993987 \n", "L 57.235125 91.686249 \n", "L 57.514125 87.324422 \n", "L 57.793125 74.382698 \n", "L 58.351125 38.792274 \n", "L 58.630125 31.868482 \n", "L 58.909125 35.383393 \n", "L 59.467125 68.811118 \n", "L 59.746125 84.532076 \n", "L 60.025125 91.230055 \n", "L 60.304125 89.503145 \n", "L 60.583125 79.285788 \n", "L 61.141125 41.591927 \n", "L 61.420125 31.58486 \n", "L 61.699125 32.147196 \n", "L 61.978125 43.871408 \n", "L 62.536125 81.444704 \n", "L 62.815125 91.126503 \n", "L 63.094125 92.050006 \n", "L 63.373125 84.966241 \n", "L 64.210125 33.952739 \n", "L 64.489125 28.966854 \n", "L 64.768125 33.81151 \n", "L 65.884125 91.641137 \n", "L 66.163125 90.807871 \n", "L 66.442125 82.062006 \n", "L 67.279125 32.473382 \n", "L 67.558125 29.514488 \n", "L 67.837125 36.410877 \n", "L 68.674125 86.632155 \n", "L 68.953125 92.192747 \n", "L 69.232125 89.913945 \n", "L 69.511125 79.608755 \n", "L 70.348125 30.321918 \n", "L 70.627125 29.327136 \n", "L 70.906125 38.818165 \n", "L 71.743125 88.799598 \n", "L 72.022125 92.396319 \n", "L 72.301125 88.322586 \n", "L 72.580125 76.055035 \n", "L 73.138125 38.798466 \n", "L 73.417125 29.632132 \n", "L 73.696125 30.327257 \n", "L 73.975125 41.199381 \n", "L 74.533125 78.53006 \n", "L 74.812125 89.583299 \n", "L 75.091125 92.028574 \n", "L 75.370125 86.879497 \n", "L 75.649125 72.982225 \n", "L 76.207125 36.342634 \n", "L 76.486125 29.464964 \n", "L 76.765125 32.436852 \n", "L 77.044125 45.494698 \n", "L 77.602125 82.222136 \n", "L 77.881125 90.889669 \n", "L 78.160125 91.411889 \n", "L 78.439125 84.499961 \n", "L 79.555125 29.822617 \n", "L 79.834125 34.41479 \n", "L 80.392125 67.89894 \n", "L 80.671125 83.679871 \n", "L 80.950125 91.293098 \n", "L 81.229125 91.056051 \n", "L 81.508125 83.132767 \n", "L 82.345125 33.34434 \n", "L 82.624125 29.394129 \n", "L 82.903125 35.374953 \n", "L 84.019125 91.673591 \n", "L 84.298125 90.335325 \n", "L 84.577125 80.786675 \n", "L 85.414125 31.964688 \n", "L 85.693125 29.844427 \n", "L 85.972125 37.453016 \n", "L 86.809125 86.638416 \n", "L 87.088125 91.865283 \n", "L 87.367125 89.382424 \n", "L 87.646125 78.851421 \n", "L 88.483125 31.631652 \n", "L 88.762125 30.750506 \n", "L 89.041125 39.649748 \n", "L 89.878125 88.318017 \n", "L 90.157125 91.88467 \n", "L 90.436125 87.981912 \n", "L 90.715125 75.805921 \n", "L 91.273125 39.629125 \n", "L 91.552125 30.844944 \n", "L 91.831125 32.043374 \n", "L 92.110125 43.007194 \n", "L 92.668125 79.008847 \n", "L 92.947125 89.550487 \n", "L 93.226125 91.82084 \n", "L 93.505125 86.620379 \n", "L 93.784125 73.245386 \n", "L 94.342125 38.461438 \n", "L 94.621125 31.218321 \n", "L 94.900125 33.738676 \n", "L 95.179125 46.349938 \n", "L 95.737125 81.735679 \n", "L 96.016125 90.54265 \n", "L 96.295125 90.552169 \n", "L 96.574125 82.18467 \n", "L 97.411125 33.364236 \n", "L 97.690125 31.364505 \n", "L 97.969125 40.011979 \n", "L 98.806125 89.001432 \n", "L 99.085125 91.906488 \n", "L 99.364125 86.484406 \n", "L 99.643125 72.073585 \n", "L 100.201125 34.988853 \n", "L 100.480125 29.408405 \n", "L 100.759125 34.840976 \n", "L 101.596125 86.278323 \n", "L 101.875125 91.73258 \n", "L 102.154125 88.938204 \n", "L 102.433125 77.41686 \n", "L 102.991125 39.198028 \n", "L 103.270125 30.013094 \n", "L 103.549125 31.675604 \n", "L 103.828125 44.33448 \n", "L 104.386125 82.161768 \n", "L 104.665125 90.883383 \n", "L 104.944125 90.852419 \n", "L 105.223125 82.556295 \n", "L 106.060125 32.229452 \n", "L 106.339125 29.986715 \n", "L 106.618125 38.649871 \n", "L 107.455125 89.437593 \n", "L 107.734125 92.047724 \n", "L 108.013125 86.405479 \n", "L 108.571125 50.753689 \n", "L 108.850125 35.01586 \n", "L 109.129125 29.944679 \n", "L 109.408125 35.889543 \n", "L 110.245125 86.954631 \n", "L 110.524125 91.925709 \n", "L 110.803125 88.816468 \n", "L 111.082125 76.798603 \n", "L 111.640125 39.414476 \n", "L 111.919125 30.969067 \n", "L 112.198125 33.77741 \n", "L 112.477125 46.962072 \n", "L 113.035125 83.382196 \n", "L 113.314125 91.11363 \n", "L 113.593125 90.400226 \n", "L 113.872125 80.95894 \n", "L 114.709125 31.081913 \n", "L 114.988125 30.978594 \n", "L 115.267125 41.61908 \n", "L 115.825125 80.027211 \n", "L 116.104125 90.446981 \n", "L 116.383125 91.77773 \n", "L 116.662125 84.64876 \n", "L 117.499125 32.119531 \n", "L 117.778125 28.686954 \n", "L 118.057125 35.940697 \n", "L 118.894125 87.879354 \n", "L 119.173125 91.928947 \n", "L 119.452125 87.599084 \n", "L 119.731125 74.353965 \n", "L 120.289125 37.011303 \n", "L 120.568125 29.96828 \n", "L 120.847125 33.587421 \n", "L 121.126125 48.164718 \n", "L 121.684125 84.958574 \n", "L 121.963125 91.513893 \n", "L 122.242125 89.674519 \n", "L 122.521125 79.075596 \n", "L 123.079125 41.652513 \n", "L 123.358125 31.435293 \n", "L 123.637125 31.964199 \n", "L 123.916125 43.586247 \n", "L 124.474125 81.162327 \n", "L 124.753125 90.562938 \n", "L 125.032125 91.262831 \n", "L 125.311125 83.536941 \n", "L 126.148125 32.485384 \n", "L 126.427125 29.85915 \n", "L 126.706125 38.063541 \n", "L 127.543125 88.513729 \n", "L 127.822125 92.070096 \n", "L 128.101125 87.32405 \n", "L 128.380125 73.370325 \n", "L 128.938125 36.166978 \n", "L 129.217125 29.903568 \n", "L 129.496125 34.52008 \n", "L 130.612125 91.636267 \n", "L 130.891125 89.655266 \n", "L 131.170125 78.986876 \n", "L 131.728125 41.073454 \n", "L 132.007125 30.834676 \n", "L 132.286125 31.889198 \n", "L 132.565125 43.919385 \n", "L 133.123125 82.102411 \n", "L 133.402125 91.09129 \n", "L 133.681125 91.054314 \n", "L 133.960125 82.653432 \n", "L 134.797125 32.289381 \n", "L 135.076125 30.155811 \n", "L 135.355125 38.656458 \n", "L 136.192125 88.986514 \n", "L 136.471125 91.802381 \n", "L 136.750125 86.602496 \n", "L 137.308125 52.829066 \n", "L 137.587125 36.833274 \n", "L 137.866125 30.732981 \n", "L 138.145125 35.211144 \n", "L 138.703125 70.816659 \n", "L 138.982125 86.402587 \n", "L 139.261125 92.074617 \n", "L 139.540125 89.402316 \n", "L 139.819125 77.836927 \n", "L 140.377125 40.170221 \n", "L 140.656125 30.708536 \n", "L 140.935125 32.293256 \n", "L 141.214125 44.755993 \n", "L 141.772125 82.263147 \n", "L 142.051125 90.897412 \n", "L 142.330125 90.836936 \n", "L 142.609125 82.473082 \n", "L 143.446125 32.797795 \n", "L 143.725125 30.545232 \n", "L 144.004125 39.039732 \n", "L 144.841125 88.921058 \n", "L 145.120125 91.691301 \n", "L 145.399125 86.492619 \n", "L 145.678125 72.422111 \n", "L 146.236125 36.07838 \n", "L 146.515125 30.146336 \n", "L 146.794125 35.050449 \n", "L 147.910125 91.951147 \n", "L 148.189125 89.37927 \n", "L 148.468125 78.010167 \n", "L 149.026125 40.234023 \n", "L 149.305125 30.87526 \n", "L 149.584125 32.60538 \n", "L 149.863125 45.013576 \n", "L 150.421125 82.122108 \n", "L 150.700125 90.801497 \n", "L 150.979125 90.72095 \n", "L 151.258125 82.456511 \n", "L 152.095125 33.973174 \n", "L 152.374125 32.059236 \n", "L 152.653125 40.824695 \n", "L 153.490125 88.588672 \n", "L 153.769125 91.299645 \n", "L 154.048125 86.101594 \n", "L 154.327125 72.456915 \n", "L 154.885125 37.322169 \n", "L 155.164125 31.668028 \n", "L 155.443125 36.491441 \n", "L 156.559125 91.275226 \n", "L 156.838125 89.123952 \n", "L 157.117125 78.896013 \n", "L 157.675125 42.6397 \n", "L 157.954125 32.435503 \n", "L 158.233125 32.686753 \n", "L 158.512125 43.354656 \n", "L 159.070125 80.337862 \n", "L 159.349125 90.094193 \n", "L 159.628125 91.151775 \n", "L 159.907125 83.970162 \n", "L 160.744125 34.329626 \n", "L 161.023125 30.651539 \n", "L 161.302125 37.935878 \n", "L 162.139125 88.017917 \n", "L 162.418125 91.919639 \n", "L 162.697125 87.808052 \n", "L 162.976125 74.779765 \n", "L 163.534125 37.788305 \n", "L 163.813125 30.421735 \n", "L 164.092125 33.558824 \n", "L 164.371125 46.914466 \n", "L 164.929125 83.382462 \n", "L 165.208125 91.221503 \n", "L 165.487125 90.490434 \n", "L 165.766125 81.433836 \n", "L 166.603125 31.257702 \n", "L 166.882125 30.419869 \n", "L 167.161125 40.571316 \n", "L 167.719125 79.219137 \n", "L 167.998125 90.000914 \n", "L 168.277125 91.58575 \n", "L 168.556125 84.774805 \n", "L 169.393125 33.574199 \n", "L 169.672125 29.57945 \n", "L 169.951125 36.211228 \n", "L 170.788125 87.378114 \n", "L 171.067125 92.220671 \n", "L 171.346125 92.974566 \n", "L 171.625125 89.890966 \n", "L 171.904125 82.399493 \n", "L 173.020125 30.832383 \n", "L 173.299125 27.032994 \n", "L 173.578125 28.540217 \n", "L 173.857125 35.283744 \n", "L 174.415125 62.311021 \n", "L 174.973125 85.901518 \n", "L 175.252125 91.177015 \n", "L 175.531125 91.973881 \n", "L 175.810125 88.914348 \n", "L 176.089125 81.681889 \n", "L 177.205125 34.994535 \n", "L 177.484125 31.127114 \n", "L 177.763125 31.813325 \n", "L 178.042125 36.993454 \n", "L 178.600125 59.66416 \n", "L 179.158125 83.346232 \n", "L 179.437125 89.722069 \n", "L 179.716125 91.898397 \n", "L 179.995125 90.159511 \n", "L 180.274125 84.684863 \n", "L 180.832125 61.83445 \n", "L 181.390125 37.882561 \n", "L 181.669125 31.683023 \n", "L 181.948125 30.645322 \n", "L 182.227125 34.557897 \n", "L 182.506125 42.885033 \n", "L 183.622125 87.773195 \n", "L 183.901125 91.179755 \n", "L 184.180125 90.77821 \n", "L 184.459125 86.645477 \n", "L 184.738125 78.565893 \n", "L 185.854125 34.587873 \n", "L 186.133125 31.511852 \n", "L 186.412125 33.302611 \n", "L 186.691125 39.714986 \n", "L 188.086125 90.51992 \n", "L 188.365125 91.443829 \n", "L 188.644125 88.69579 \n", "L 188.923125 81.126867 \n", "L 190.039125 33.296882 \n", "L 190.318125 33.315458 \n", "L 190.597125 40.205459 \n", "L 191.713125 90.154016 \n", "L 191.992125 91.415399 \n", "L 192.271125 87.261513 \n", "L 192.550125 76.910198 \n", "L 193.387125 35.339497 \n", "L 193.666125 31.061274 \n", "L 193.945125 32.642971 \n", "L 194.224125 39.9412 \n", "L 195.340125 88.397172 \n", "L 195.619125 91.816895 \n", "L 195.898125 90.747624 \n", "L 196.177125 85.20312 \n", "L 196.735125 60.891704 \n", "L 197.293125 36.126501 \n", "L 197.572125 32.305968 \n", "L 197.851125 32.129104 \n", "L 198.130125 35.384236 \n", "L 198.409125 41.853956 \n", "L 199.804125 88.045836 \n", "L 200.083125 91.178169 \n", "L 200.362125 91.285987 \n", "L 200.641125 88.578961 \n", "L 200.920125 83.063922 \n", "L 201.478125 63.260448 \n", "L 202.036125 40.775863 \n", "L 202.315125 33.509338 \n", "L 202.594125 30.411306 \n", "L 202.873125 30.899536 \n", "L 203.152125 35.071683 \n", "L 203.710125 53.884966 \n", "L 204.268125 76.386149 \n", "L 204.826125 89.91387 \n", "L 205.105125 91.745818 \n", "L 205.384125 90.641097 \n", "L 205.663125 86.7392 \n", "L 205.942125 79.915254 \n", "L 207.337125 32.494622 \n", "L 207.616125 30.966919 \n", "L 207.895125 33.240377 \n", "L 208.174125 39.463799 \n", "L 208.732125 60.986379 \n", "L 209.290125 81.796163 \n", "L 209.569125 88.359421 \n", "L 209.848125 91.598814 \n", "L 210.127125 91.497723 \n", "L 210.406125 88.489241 \n", "L 210.685125 82.253023 \n", "L 211.243125 59.765355 \n", "L 211.801125 36.704819 \n", "L 212.080125 30.391223 \n", "L 212.359125 28.0683 \n", "L 212.638125 29.763878 \n", "L 212.917125 35.811257 \n", "L 213.475125 59.368265 \n", "L 214.033125 82.861813 \n", "L 214.312125 89.571363 \n", "L 214.591125 92.452298 \n", "L 214.870125 92.133078 \n", "L 215.149125 88.739215 \n", "L 215.428125 81.9544 \n", "L 215.986125 58.485717 \n", "L 216.544125 35.872059 \n", "L 216.823125 29.981331 \n", "L 217.102125 28.24001 \n", "L 217.381125 30.653381 \n", "L 217.660125 37.366111 \n", "L 219.055125 89.38906 \n", "L 219.334125 92.04144 \n", "L 219.613125 91.622773 \n", "L 219.892125 88.152783 \n", "L 220.171125 81.635663 \n", "L 221.566125 31.400817 \n", "L 221.845125 29.385343 \n", "L 222.124125 31.344168 \n", "L 222.403125 37.384055 \n", "L 222.961125 59.731695 \n", "L 223.519125 81.806458 \n", "L 223.798125 88.727497 \n", "L 224.077125 91.998424 \n", "L 224.356125 91.892869 \n", "L 224.635125 88.82922 \n", "L 224.914125 82.777712 \n", "L 225.472125 60.842338 \n", "L 226.030125 37.801556 \n", "L 226.309125 31.078237 \n", "L 226.588125 28.707539 \n", "L 226.867125 30.585643 \n", "L 227.146125 36.920377 \n", "L 227.704125 59.597762 \n", "L 228.262125 82.153541 \n", "L 228.541125 89.001325 \n", "L 228.820125 92.160362 \n", "L 229.099125 91.837664 \n", "L 229.378125 88.523122 \n", "L 229.657125 82.196369 \n", "L 230.215125 60.082005 \n", "L 230.773125 37.404516 \n", "L 231.052125 31.102811 \n", "L 231.331125 28.940897 \n", "L 231.610125 30.726972 \n", "L 231.889125 36.650538 \n", "L 232.447125 58.668601 \n", "L 233.005125 80.903344 \n", "L 233.284125 87.839777 \n", "L 233.563125 91.518112 \n", "L 233.842125 91.912286 \n", "L 234.121125 89.386108 \n", "L 234.400125 83.75264 \n", "L 234.958125 63.271368 \n", "L 235.516125 41.122539 \n", "L 235.795125 34.324747 \n", "L 236.074125 31.224404 \n", "L 236.353125 31.65176 \n", "L 236.632125 35.840016 \n", "L 237.190125 54.668664 \n", "L 237.748125 76.295423 \n", "L 238.306125 89.639932 \n", "L 238.585125 91.759099 \n", "L 238.864125 90.890637 \n", "L 239.143125 87.293162 \n", "L 239.422125 80.807983 \n", "L 240.817125 36.272684 \n", "L 241.096125 33.841261 \n", "L 241.375125 34.88934 \n", "L 241.654125 39.125986 \n", "L 242.212125 56.57871 \n", "L 243.049125 83.642074 \n", "L 243.328125 88.890928 \n", "L 243.607125 91.249551 \n", "L 243.886125 90.80866 \n", "L 244.165125 87.856631 \n", "L 244.444125 82.123942 \n", "L 245.002125 63.154347 \n", "L 245.839125 32.66212 \n", "L 246.118125 31.218631 \n", "L 246.397125 37.658541 \n", "L 247.513125 90.356105 \n", "L 247.792125 91.647522 \n", "L 248.071125 86.856248 \n", "L 248.350125 75.211856 \n", "L 248.908125 41.878328 \n", "L 249.187125 31.999329 \n", "L 249.466125 30.01811 \n", "L 249.745125 36.133112 \n", "L 250.861125 90.371776 \n", "L 251.140125 91.749974 \n", "L 251.419125 87.057174 \n", "L 251.698125 75.729266 \n", "L 252.256125 42.848625 \n", "L 252.535125 32.845034 \n", "L 252.814125 30.907494 \n", "L 253.093125 36.727888 \n", "L 254.209125 90.422734 \n", "L 254.488125 91.557247 \n", "L 254.767125 86.522741 \n", "L 255.046125 74.677876 \n", "L 255.604125 42.433854 \n", "L 255.883125 33.021613 \n", "L 256.162125 31.213187 \n", "L 256.441125 37.023136 \n", "L 257.557125 90.593073 \n", "L 257.836125 91.916408 \n", "L 258.115125 87.240596 \n", "L 258.394125 75.317773 \n", "L 258.952125 41.334229 \n", "L 259.231125 31.530095 \n", "L 259.510125 30.136599 \n", "L 259.789125 37.007568 \n", "L 260.905125 91.283903 \n", "L 261.184125 91.789604 \n", "L 261.463125 85.978151 \n", "L 262.021125 55.057755 \n", "L 262.300125 39.079427 \n", "L 262.579125 30.150242 \n", "L 262.858125 29.809852 \n", "L 263.137125 37.741197 \n", "L 263.974125 85.05853 \n", "L 264.253125 91.637057 \n", "L 264.532125 91.530982 \n", "L 264.811125 84.98388 \n", "L 265.369125 52.781229 \n", "L 265.648125 37.157139 \n", "L 265.927125 29.496885 \n", "L 266.206125 30.326841 \n", "L 266.485125 39.101412 \n", "L 267.322125 86.696002 \n", "L 267.601125 92.017425 \n", "L 267.880125 91.63441 \n", "L 268.159125 86.218973 \n", "L 268.438125 74.696955 \n", "L 269.275125 32.079783 \n", "L 269.554125 28.208573 \n", "L 269.833125 31.33372 \n", "L 270.112125 41.294107 \n", "L 270.949125 85.037789 \n", "L 271.228125 91.273492 \n", "L 271.507125 92.081133 \n", "L 271.786125 87.803429 \n", "L 272.065125 77.770381 \n", "L 272.902125 35.269994 \n", "L 273.181125 30.30535 \n", "L 273.460125 32.082188 \n", "L 273.739125 40.096937 \n", "L 274.855125 89.688724 \n", "L 275.134125 91.833194 \n", "L 275.413125 89.204539 \n", "L 275.692125 81.349497 \n", "L 276.529125 41.889605 \n", "L 276.808125 35.288971 \n", "L 277.087125 33.11053 \n", "L 277.366125 34.662583 \n", "L 277.645125 40.346776 \n", "L 278.203125 61.386987 \n", "L 278.761125 82.316399 \n", "L 279.040125 88.586776 \n", "L 279.319125 91.387612 \n", "L 279.598125 90.87618 \n", "L 279.877125 87.168602 \n", "L 280.156125 79.956487 \n", "L 281.272125 35.627054 \n", "L 281.551125 31.455241 \n", "L 281.830125 31.480138 \n", "L 282.109125 35.859443 \n", "L 282.388125 44.758935 \n", "L 283.225125 80.597118 \n", "L 283.504125 87.772003 \n", "L 283.783125 91.145128 \n", "L 284.062125 91.076806 \n", "L 284.341125 87.886105 \n", "L 284.620125 81.281072 \n", "L 286.015125 32.018021 \n", "L 286.294125 30.668893 \n", "L 286.573125 33.49593 \n", "L 286.852125 40.732282 \n", "L 287.968125 85.753435 \n", "L 288.247125 90.98115 \n", "L 288.526125 92.238849 \n", "L 288.805125 90.059829 \n", "L 289.084125 84.544212 \n", "L 289.642125 62.383765 \n", "L 290.200125 38.382578 \n", "L 290.479125 31.683177 \n", "L 290.758125 29.285246 \n", "L 291.037125 31.577228 \n", "L 291.316125 38.400235 \n", "L 292.711125 90.525212 \n", "L 292.990125 92.184646 \n", "L 293.269125 90.447831 \n", "L 293.548125 85.27463 \n", "L 293.827125 76.2191 \n", "L 294.664125 39.300601 \n", "L 294.943125 32.450722 \n", "L 295.222125 30.123597 \n", "L 295.501125 32.438072 \n", "L 295.780125 38.925811 \n", "L 297.175125 88.640193 \n", "L 297.454125 91.151026 \n", "L 297.733125 90.264668 \n", "L 298.012125 86.470661 \n", "L 298.291125 79.50279 \n", "L 299.407125 38.530726 \n", "L 299.686125 33.647561 \n", "L 299.965125 32.93639 \n", "L 300.244125 36.202439 \n", "L 300.523125 43.532798 \n", "L 301.639125 85.255783 \n", "L 301.918125 90.10239 \n", "L 302.197125 91.499153 \n", "L 302.476125 89.697448 \n", "L 302.755125 84.577485 \n", "L 303.313125 64.171844 \n", "L 303.871125 41.655348 \n", "L 304.150125 35.170652 \n", "L 304.429125 32.917961 \n", "L 304.708125 34.769795 \n", "L 304.987125 40.758516 \n", "L 306.382125 88.470465 \n", "L 306.661125 91.188477 \n", "L 306.940125 90.659355 \n", "L 307.219125 86.965019 \n", "L 307.498125 79.69898 \n", "L 308.614125 37.143583 \n", "L 308.893125 32.932085 \n", "L 309.172125 32.793941 \n", "L 309.451125 37.274173 \n", "L 310.009125 57.032336 \n", "L 310.567125 79.865781 \n", "L 310.846125 87.432864 \n", "L 311.125125 91.279391 \n", "L 311.404125 91.630779 \n", "L 311.683125 88.619102 \n", "L 311.962125 82.279103 \n", "L 313.357125 34.08291 \n", "L 313.636125 32.525739 \n", "L 313.915125 34.902472 \n", "L 314.194125 41.092002 \n", "L 315.589125 89.319358 \n", "L 315.868125 91.480607 \n", "L 316.147125 90.492693 \n", "L 316.426125 86.256865 \n", "L 316.705125 78.721171 \n", "L 317.821125 36.696436 \n", "L 318.100125 32.449872 \n", "L 318.379125 32.604584 \n", "L 318.658125 37.160987 \n", "L 319.216125 57.431681 \n", "L 319.774125 79.726667 \n", "L 320.053125 87.106004 \n", "L 320.332125 90.899487 \n", "L 320.611125 91.294768 \n", "L 320.890125 88.403944 \n", "L 321.169125 82.022297 \n", "L 321.727125 60.707753 \n", "L 322.285125 39.377042 \n", "L 322.564125 33.510796 \n", "L 322.843125 31.835266 \n", "L 323.122125 34.772027 \n", "L 323.401125 41.589049 \n", "L 324.517125 84.817936 \n", "L 324.796125 90.086108 \n", "L 325.075125 91.649326 \n", "L 325.354125 89.943472 \n", "L 325.633125 84.897761 \n", "L 326.191125 64.255015 \n", "L 326.749125 42.287548 \n", "L 327.028125 35.903994 \n", "L 327.307125 33.355966 \n", "L 327.586125 34.722192 \n", "L 327.865125 40.387838 \n", "L 328.423125 62.251774 \n", "L 328.981125 82.609768 \n", "L 329.260125 88.589809 \n", "L 329.539125 91.022173 \n", "L 329.818125 90.234401 \n", "L 330.097125 86.332004 \n", "L 330.376125 79.133238 \n", "L 331.492125 37.869791 \n", "L 331.771125 33.546732 \n", "L 332.050125 33.252063 \n", "L 332.329125 37.006284 \n", "L 332.608125 44.710078 \n", "L 333.724125 86.980322 \n", "L 334.003125 91.282694 \n", "L 334.282125 92.06662 \n", "L 334.561125 89.455933 \n", "L 334.840125 83.409033 \n", "L 335.398125 60.127978 \n", "L 335.956125 37.863145 \n", "L 336.235125 32.164574 \n", "L 336.514125 30.585124 \n", "L 336.793125 33.318014 \n", "L 337.072125 40.711715 \n", "L 338.188125 84.75076 \n", "L 338.467125 89.939418 \n", "L 338.746125 91.528004 \n", "L 339.025125 89.894171 \n", "L 339.304125 84.573454 \n", "L 339.862125 62.433189 \n", "L 340.420125 37.649534 \n", "L 340.699125 30.490729 \n", "L 340.978125 28.430473 \n", "L 341.257125 31.073676 \n", "L 341.536125 38.176491 \n", "L 342.652125 85.492697 \n", "L 342.931125 90.924994 \n", "L 343.210125 92.319033 \n", "L 343.489125 90.404227 \n", "L 343.768125 84.924044 \n", "L 344.326125 60.76275 \n", "L 344.884125 40.039851 \n", "L 345.163125 38.4964 \n", "L 345.442125 43.488109 \n", "L 346.558125 88.742549 \n", "L 346.837125 90.262557 \n", "L 347.116125 86.117909 \n", "L 347.395125 75.491612 \n", "L 347.953125 45.337859 \n", "L 348.232125 35.688321 \n", "L 348.511125 33.777774 \n", "L 348.790125 39.451991 \n", "L 349.906125 89.27549 \n", "L 350.185125 90.889563 \n", "L 350.464125 86.59293 \n", "L 350.743125 75.967347 \n", "L 351.580125 36.163861 \n", "L 351.859125 34.021788 \n", "L 352.138125 39.463021 \n", "L 352.696125 67.754679 \n", "L 352.975125 81.77478 \n", "L 353.254125 89.750295 \n", "L 353.533125 91.227029 \n", "L 353.812125 86.576026 \n", "L 354.091125 74.995939 \n", "L 354.649125 42.529471 \n", "L 354.928125 32.904307 \n", "L 355.207125 31.259773 \n", "L 355.486125 37.724457 \n", "L 356.602125 90.974663 \n", "L 356.881125 91.340776 \n", "L 357.160125 85.189685 \n", "L 357.718125 54.463794 \n", "L 357.997125 39.457622 \n", "L 358.276125 31.602168 \n", "L 358.555125 31.836907 \n", "L 358.834125 40.711181 \n", "L 359.671125 86.524614 \n", "L 359.950125 91.887194 \n", "L 360.229125 90.781668 \n", "L 360.508125 83.117859 \n", "L 361.624125 29.840621 \n", "L 361.903125 31.831041 \n", "L 362.182125 42.744408 \n", "L 362.740125 77.3161 \n", "L 363.019125 88.306398 \n", "L 363.298125 92.088288 \n", "L 363.577125 89.359255 \n", "L 363.856125 79.856277 \n", "L 364.693125 34.204804 \n", "L 364.972125 30.27571 \n", "L 365.251125 34.929509 \n", "L 365.809125 65.083211 \n", "L 366.088125 80.60942 \n", "L 366.367125 89.7102 \n", "L 366.646125 91.788648 \n", "L 366.925125 87.552434 \n", "L 367.204125 76.430802 \n", "L 367.762125 43.288797 \n", "L 368.041125 33.262506 \n", "L 368.320125 31.226795 \n", "L 368.599125 37.351643 \n", "L 369.715125 90.421192 \n", "L 369.994125 91.695752 \n", "L 370.273125 86.690931 \n", "L 370.552125 74.535287 \n", "L 371.110125 40.863016 \n", "L 371.389125 31.244025 \n", "L 371.668125 30.260518 \n", "L 371.947125 37.12048 \n", "L 373.063125 91.252226 \n", "L 373.342125 91.629921 \n", "L 373.621125 85.532262 \n", "L 374.179125 54.048009 \n", "L 374.458125 38.685852 \n", "L 374.737125 30.747143 \n", "L 375.016125 30.941885 \n", "L 375.295125 39.112805 \n", "L 376.132125 86.024836 \n", "L 376.411125 91.819008 \n", "L 376.690125 90.738278 \n", "L 376.969125 83.181791 \n", "L 378.085125 29.366184 \n", "L 378.364125 31.553513 \n", "L 378.643125 42.018083 \n", "L 379.480125 88.116923 \n", "L 379.759125 92.096084 \n", "L 380.038125 89.587104 \n", "L 380.317125 80.161094 \n", "L 381.154125 33.177999 \n", "L 381.433125 28.598082 \n", "L 381.712125 32.423034 \n", "L 381.991125 44.695086 \n", "L 382.549125 78.835675 \n", "L 382.828125 89.308625 \n", "L 383.107125 92.340252 \n", "L 383.386125 88.888952 \n", "L 383.665125 78.374883 \n", "L 384.502125 32.210739 \n", "L 384.781125 29.097206 \n", "L 385.060125 34.86093 \n", "L 386.176125 90.83178 \n", "L 386.455125 91.888011 \n", "L 386.734125 86.557016 \n", "L 387.013125 73.793542 \n", "L 387.571125 39.694324 \n", "L 387.850125 30.881325 \n", "L 388.129125 30.75458 \n", "L 388.408125 38.784619 \n", "L 389.245125 84.599655 \n", "L 389.524125 91.082576 \n", "L 389.803125 90.844082 \n", "L 390.082125 84.315832 \n", "L 391.198125 30.504931 \n", "L 391.477125 31.563575 \n", "L 391.756125 41.07346 \n", "L 392.593125 86.465984 \n", "L 392.872125 91.759892 \n", "L 393.151125 90.57237 \n", "L 393.430125 82.716043 \n", "L 394.267125 36.738565 \n", "L 394.546125 31.07571 \n", "L 394.825125 33.67287 \n", "L 395.104125 44.015767 \n", "L 395.941125 87.600658 \n", "L 396.220125 91.692718 \n", "L 396.499125 89.551024 \n", "L 396.778125 81.257659 \n", "L 397.615125 36.88629 \n", "L 397.894125 31.573243 \n", "L 398.173125 34.254999 \n", "L 398.452125 44.833735 \n", "L 399.289125 87.995063 \n", "L 399.568125 91.802161 \n", "L 399.847125 89.391206 \n", "L 400.126125 80.31302 \n", "L 400.963125 34.494469 \n", "L 401.242125 30.120383 \n", "L 401.521125 34.095046 \n", "L 401.800125 46.174712 \n", "L 402.358125 79.509446 \n", "L 402.637125 89.190799 \n", "L 402.916125 91.884948 \n", "L 403.195125 88.407051 \n", "L 403.474125 77.90616 \n", "L 404.311125 32.026059 \n", "L 404.590125 29.05153 \n", "L 404.869125 34.567906 \n", "L 405.427125 66.374714 \n", "L 405.706125 81.886083 \n", "L 405.985125 90.370506 \n", "L 406.264125 91.880281 \n", "L 406.543125 87.039601 \n", "L 406.822125 74.839437 \n", "L 407.380125 40.553881 \n", "L 407.659125 30.972725 \n", "L 407.938125 30.063305 \n", "L 408.217125 37.76444 \n", "L 409.054125 85.115346 \n", "L 409.333125 91.651776 \n", "L 409.612125 91.32243 \n", "L 409.891125 84.467676 \n", "L 411.007125 29.306123 \n", "L 411.286125 30.764485 \n", "L 411.565125 40.429876 \n", "L 412.402125 86.783695 \n", "L 412.681125 91.747193 \n", "L 412.960125 90.2794 \n", "L 413.239125 82.082368 \n", "L 414.076125 36.063149 \n", "L 414.355125 30.365559 \n", "L 414.634125 32.914732 \n", "L 414.913125 43.723333 \n", "L 415.750125 88.033824 \n", "L 416.029125 91.937925 \n", "L 416.308125 89.551373 \n", "L 416.587125 80.756947 \n", "L 417.424125 34.040185 \n", "L 417.703125 29.216756 \n", "L 417.982125 32.94051 \n", "L 418.261125 45.124285 \n", "L 418.819125 78.924008 \n", "L 419.098125 88.994194 \n", "L 419.377125 91.914389 \n", "L 419.656125 88.704954 \n", "L 419.935125 78.57186 \n", "L 420.772125 33.118609 \n", "L 421.051125 29.841475 \n", "L 421.330125 34.562309 \n", "L 421.888125 65.761804 \n", "L 422.167125 81.597263 \n", "L 422.446125 90.357404 \n", "L 422.725125 91.81531 \n", "L 423.004125 86.885714 \n", "L 423.283125 74.957918 \n", "L 423.841125 40.680791 \n", "L 424.120125 30.74935 \n", "L 424.399125 29.46259 \n", "L 424.678125 36.48624 \n", "L 425.515125 84.264776 \n", "L 425.794125 91.272873 \n", "L 426.073125 91.499906 \n", "L 426.352125 85.178111 \n", "L 426.910125 53.275891 \n", "L 427.189125 37.752112 \n", "L 427.468125 29.928344 \n", "L 427.747125 30.792106 \n", "L 428.026125 40.410904 \n", "L 428.863125 86.782518 \n", "L 429.142125 91.753583 \n", "L 429.421125 90.214299 \n", "L 429.700125 82.244371 \n", "L 430.537125 36.263414 \n", "L 430.816125 30.589917 \n", "L 431.095125 33.042092 \n", "L 431.374125 43.944761 \n", "L 432.211125 88.327578 \n", "L 432.490125 92.006598 \n", "L 432.769125 89.353188 \n", "L 433.048125 79.770631 \n", "L 433.885125 33.607716 \n", "L 434.164125 29.884784 \n", "L 434.443125 34.71322 \n", "L 435.001125 64.786756 \n", "L 435.280125 80.437168 \n", "L 435.559125 89.276715 \n", "L 435.838125 90.194916 \n", "L 436.117125 84.063273 \n", "L 437.233125 32.911584 \n", "L 437.512125 37.907559 \n", "L 438.628125 91.223922 \n", "L 438.907125 89.90449 \n", "L 439.186125 80.813502 \n", "L 440.023125 33.046725 \n", "L 440.302125 31.631754 \n", "L 440.581125 40.062494 \n", "L 441.418125 88.029039 \n", "L 441.697125 91.669855 \n", "L 441.976125 87.623926 \n", "L 442.255125 75.362501 \n", "L 442.813125 39.791937 \n", "L 443.092125 31.478161 \n", "L 443.371125 33.134348 \n", "L 443.650125 44.61987 \n", "L 444.208125 80.815829 \n", "L 444.487125 90.123372 \n", "L 444.766125 91.25396 \n", "L 445.045125 84.814116 \n", "L 446.161125 30.906601 \n", "L 446.440125 35.731216 \n", "L 447.556125 91.647039 \n", "L 447.835125 90.108662 \n", "L 448.114125 80.381232 \n", "L 448.951125 31.449988 \n", "L 449.230125 30.394929 \n", "L 449.509125 39.615386 \n", "L 450.346125 89.172052 \n", "L 450.625125 92.033334 \n", "L 450.904125 87.06928 \n", "L 451.183125 73.674257 \n", "L 451.741125 37.32327 \n", "L 452.020125 30.297669 \n", "L 452.299125 33.602785 \n", "L 452.578125 47.172455 \n", "L 453.136125 83.467294 \n", "L 453.415125 91.273391 \n", "L 453.694125 90.894558 \n", "L 453.973125 82.303202 \n", "L 454.810125 32.929505 \n", "L 455.089125 30.376591 \n", "L 455.368125 37.856435 \n", "L 456.205125 87.657907 \n", "L 456.484125 92.02932 \n", "L 456.763125 88.491845 \n", "L 457.042125 76.457954 \n", "L 457.600125 39.607913 \n", "L 457.879125 30.534049 \n", "L 458.158125 31.955936 \n", "L 458.437125 43.500749 \n", "L 458.995125 80.830745 \n", "L 459.274125 90.460622 \n", "L 459.553125 91.434493 \n", "L 459.832125 84.677577 \n", "L 460.669125 35.299123 \n", "L 460.948125 30.439594 \n", "L 461.227125 35.641593 \n", "L 462.343125 91.601136 \n", "L 462.622125 89.872473 \n", "L 462.901125 80.066295 \n", "L 463.738125 31.663749 \n", "L 464.017125 30.778088 \n", "L 464.296125 40.350464 \n", "L 465.133125 88.893495 \n", "L 465.412125 91.816525 \n", "L 465.691125 87.093345 \n", "L 465.970125 73.92511 \n", "L 466.528125 37.107386 \n", "L 466.807125 29.894725 \n", "L 467.086125 33.224186 \n", "L 467.365125 46.794194 \n", "L 467.923125 83.144858 \n", "L 468.202125 91.22504 \n", "L 468.481125 90.864478 \n", "L 468.760125 82.555865 \n", "L 469.597125 33.318787 \n", "L 469.876125 30.017931 \n", "L 470.155125 36.970713 \n", "L 470.992125 87.300818 \n", "L 471.271125 92.002524 \n", "L 471.550125 88.684834 \n", "L 471.829125 76.88753 \n", "L 472.387125 40.150172 \n", "L 472.666125 31.014039 \n", "L 472.945125 32.108226 \n", "L 473.224125 43.490368 \n", "L 473.782125 80.289492 \n", "L 474.061125 90.294292 \n", "L 474.340125 91.582777 \n", "L 474.619125 85.258049 \n", "L 475.735125 31.360384 \n", "L 476.014125 36.531147 \n", "L 477.130125 91.611103 \n", "L 477.409125 89.809478 \n", "L 477.688125 79.900227 \n", "L 478.525125 32.556893 \n", "L 478.804125 31.792896 \n", "L 479.083125 41.08497 \n", "L 479.920125 88.464931 \n", "L 480.199125 91.711509 \n", "L 480.478125 87.253007 \n", "L 480.757125 74.675002 \n", "L 481.315125 39.515621 \n", "L 481.594125 31.887393 \n", "L 481.873125 33.933892 \n", "L 482.152125 46.026513 \n", "L 482.710125 81.624383 \n", "L 482.989125 90.297853 \n", "L 483.268125 90.868661 \n", "L 483.547125 83.948312 \n", "L 484.384125 36.121259 \n", "L 484.663125 32.16735 \n", "L 484.942125 38.039233 \n", "L 486.058125 91.671761 \n", "L 486.337125 89.527503 \n", "L 486.616125 79.233864 \n", "L 487.453125 31.843507 \n", "L 487.732125 31.717753 \n", "L 488.011125 41.626532 \n", "L 488.848125 89.539683 \n", "L 489.127125 91.853901 \n", "L 489.406125 86.084562 \n", "L 489.685125 71.596596 \n", "L 490.243125 34.95852 \n", "L 490.522125 29.18173 \n", "L 490.801125 33.636677 \n", "L 491.080125 48.104084 \n", "L 491.638125 84.846504 \n", "L 491.917125 91.957395 \n", "L 492.196125 90.624695 \n", "L 492.475125 80.559193 \n", "L 493.312125 30.794058 \n", "L 493.591125 29.697914 \n", "L 493.870125 39.096147 \n", "L 494.707125 89.25225 \n", "L 494.986125 92.080938 \n", "L 495.265125 87.247 \n", "L 495.544125 73.669352 \n", "L 496.102125 36.807211 \n", "L 496.381125 29.444446 \n", "L 496.660125 32.471819 \n", "L 496.939125 46.166515 \n", "L 497.497125 82.818719 \n", "L 497.776125 90.980937 \n", "L 498.055125 90.817822 \n", "L 498.334125 82.636297 \n", "L 499.171125 32.332232 \n", "L 499.450125 29.520044 \n", "L 499.729125 36.778065 \n", "L 500.566125 86.948605 \n", "L 500.845125 91.925192 \n", "L 501.124125 89.089535 \n", "L 501.403125 77.953712 \n", "L 501.961125 40.459863 \n", "L 502.240125 30.657902 \n", "L 502.519125 31.47488 \n", "L 502.798125 42.641569 \n", "L 503.356125 80.264321 \n", "L 503.635125 90.28697 \n", "L 503.914125 91.704248 \n", "L 504.193125 85.427602 \n", "L 505.309125 30.358958 \n", "L 505.588125 35.20763 \n", "L 506.704125 91.777304 \n", "L 506.983125 90.088879 \n", "L 507.262125 80.365092 \n", "L 508.099125 30.981645 \n", "L 508.378125 29.564308 \n", "L 508.657125 38.278646 \n", "L 509.494125 88.817119 \n", "L 509.773125 92.168263 \n", "L 510.052125 87.683804 \n", "L 510.331125 74.567622 \n", "L 510.889125 37.167801 \n", "L 511.168125 29.499232 \n", "L 511.447125 31.923349 \n", "L 511.726125 44.91772 \n", "L 512.284125 82.157296 \n", "L 512.563125 90.986299 \n", "L 512.842125 91.464205 \n", "L 513.121125 84.067127 \n", "L 513.958125 33.416296 \n", "L 514.237125 29.134975 \n", "L 514.516125 35.092852 \n", "L 515.353125 86.401555 \n", "L 515.632125 92.166839 \n", "L 515.911125 89.828671 \n", "L 516.190125 78.93652 \n", "L 516.748125 41.0828 \n", "L 517.027125 30.203456 \n", "L 517.306125 29.919598 \n", "L 517.585125 39.950605 \n", "L 518.422125 89.242616 \n", "L 518.701125 91.726692 \n", "L 518.980125 86.484403 \n", "L 519.259125 73.012952 \n", "L 519.817125 38.012548 \n", "L 520.096125 31.10763 \n", "L 520.375125 34.535229 \n", "L 520.654125 47.653925 \n", "L 521.212125 82.97691 \n", "L 521.491125 90.913684 \n", "L 521.770125 90.518493 \n", "L 522.049125 82.228432 \n", "L 522.886125 33.777404 \n", "L 523.165125 31.396582 \n", "L 523.444125 39.13335 \n", "L 524.281125 87.877726 \n", "L 524.560125 91.959037 \n", "L 524.839125 88.403303 \n", "L 525.118125 76.470085 \n", "L 525.676125 39.130175 \n", "L 525.955125 29.911858 \n", "L 526.234125 31.105668 \n", "L 526.513125 42.803463 \n", "L 527.071125 80.664396 \n", "L 527.350125 90.537681 \n", "L 527.629125 91.885283 \n", "L 527.908125 85.343279 \n", "L 529.024125 29.384497 \n", "L 529.303125 34.599334 \n", "L 530.419125 92.046246 \n", "L 530.698125 90.333269 \n", "L 530.977125 79.955384 \n", "L 531.814125 31.351948 \n", "L 532.093125 29.188716 \n", "L 532.372125 34.838922 \n", "L 532.930125 64.982892 \n", "L 533.209125 80.411655 \n", "L 533.488125 89.579246 \n", "L 533.767125 92.169945 \n", "L 534.046125 88.903473 \n", "L 534.325125 79.374933 \n", "L 535.162125 33.352982 \n", "L 535.441125 28.951859 \n", "L 535.720125 32.254359 \n", "L 535.999125 43.416526 \n", "L 536.836125 87.858908 \n", "L 537.115125 92.123607 \n", "L 537.394125 90.484954 \n", "L 537.673125 82.607155 \n", "L 538.789125 28.713171 \n", "L 539.068125 29.806071 \n", "L 539.347125 38.928978 \n", "L 540.184125 85.462965 \n", "L 540.463125 91.56625 \n", "L 540.742125 91.476871 \n", "L 541.021125 85.531215 \n", "L 541.579125 56.159166 \n", "L 541.858125 40.630604 \n", "L 542.137125 31.344683 \n", "L 542.416125 29.774707 \n", "L 542.695125 35.806025 \n", "L 543.811125 89.665924 \n", "L 544.090125 91.848492 \n", "L 544.369125 88.224263 \n", "L 544.648125 78.010019 \n", "L 545.485125 33.222978 \n", "L 545.764125 29.260243 \n", "L 546.043125 32.929395 \n", "L 546.322125 44.523566 \n", "L 546.880125 77.536035 \n", "L 547.159125 88.101859 \n", "L 547.438125 91.960521 \n", "L 547.717125 89.793023 \n", "L 547.996125 81.617689 \n", "L 548.833125 37.741495 \n", "L 549.112125 31.236071 \n", "L 549.391125 32.400122 \n", "L 549.670125 41.057066 \n", "L 550.507125 84.600595 \n", "L 550.786125 90.705019 \n", "L 551.065125 90.952853 \n", "L 551.344125 85.549176 \n", "L 551.902125 57.731488 \n", "L 552.460125 32.397412 \n", "L 552.739125 30.348064 \n", "L 553.018125 35.623648 \n", "L 553.576125 64.808803 \n", "L 554.134125 89.215631 \n", "L 554.413125 92.018947 \n", "L 554.692125 88.82537 \n", "L 554.971125 79.384977 \n", "L 555.808125 33.301471 \n", "L 556.087125 28.313865 \n", "L 556.366125 31.505 \n", "L 556.645125 42.660192 \n", "L 557.482125 87.851321 \n", "L 557.761125 92.304363 \n", "L 558.040125 90.773737 \n", "L 558.319125 82.988924 \n", "L 559.435125 29.122328 \n", "L 559.714125 29.973705 \n", "L 559.993125 38.602668 \n", "L 560.830125 84.653893 \n", "L 561.109125 91.285815 \n", "L 561.388125 91.567216 \n", "L 561.667125 86.103166 \n", "L 561.946125 74.099197 \n", "L 562.504125 41.025711 \n", "L 562.783125 31.33401 \n", "L 563.062125 29.955661 \n", "L 563.341125 36.227609 \n", "L 564.457125 89.760592 \n", "L 564.736125 91.823846 \n", "L 565.015125 88.179847 \n", "L 565.294125 78.163228 \n", "L 566.131125 34.196195 \n", "L 566.410125 29.9168 \n", "L 566.689125 33.462432 \n", "L 566.968125 44.401521 \n", "L 567.805125 87.669633 \n", "L 568.084125 91.984795 \n", "L 568.363125 90.330207 \n", "L 568.642125 82.489099 \n", "L 569.758125 29.885744 \n", "L 570.037125 31.04987 \n", "L 570.316125 40.173005 \n", "L 571.153125 84.663276 \n", "L 571.432125 90.994653 \n", "L 571.711125 91.356395 \n", "L 571.990125 85.798511 \n", "L 572.269125 73.966168 \n", "L 572.827125 41.826768 \n", "L 573.106125 32.005718 \n", "L 573.385125 30.02823 \n", "L 573.664125 35.651644 \n", "L 574.222125 66.272067 \n", "L 574.501125 81.211155 \n", "L 574.780125 90.097262 \n", "L 575.059125 92.12305 \n", "L 575.338125 88.278885 \n", "L 575.617125 78.040713 \n", "L 576.454125 33.47142 \n", "L 576.733125 29.369671 \n", "L 577.012125 33.203074 \n", "L 577.291125 44.853164 \n", "L 577.849125 77.221281 \n", "L 578.128125 87.777472 \n", "L 578.407125 91.941276 \n", "L 578.686125 90.257719 \n", "L 578.965125 82.604403 \n", "L 579.802125 37.054815 \n", "L 580.081125 30.962758 \n", "L 580.360125 32.508754 \n", "L 580.639125 42.267327 \n", "L 581.476125 86.416504 \n", "L 581.755125 91.680639 \n", "L 582.034125 91.082012 \n", "L 582.313125 84.243929 \n", "L 582.871125 52.52212 \n", "L 583.150125 37.437348 \n", "L 583.429125 29.544237 \n", "L 583.708125 29.544743 \n", "L 583.987125 37.702899 \n", "L 584.824125 84.332852 \n", "L 584.824125 84.332852 \n", "\" style=\"fill:none;stroke:#ff0000;stroke-linecap:square;\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 27.103125 21.318125 \n", "L 585.103125 21.318125 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 27.103125 105.018125 \n", "L 27.103125 21.318125 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 27.103125 105.018125 \n", "L 585.103125 105.018125 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 585.103125 105.018125 \n", "L 585.103125 21.318125 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_4\">\n", "      <defs>\n", "       <path d=\"M 0 0 \n", "L 0 -4 \n", "\" id=\"mc6b2d6bfc6\" style=\"stroke:#000000;stroke-width:0.5;\"/>\n", "      </defs>\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"27.103125\" xlink:href=\"#mc6b2d6bfc6\" y=\"105.018125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_5\">\n", "      <defs>\n", "       <path d=\"M 0 0 \n", "L 0 4 \n", "\" id=\"m8d1a18dd0a\" style=\"stroke:#000000;stroke-width:0.5;\"/>\n", "      </defs>\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"27.103125\" xlink:href=\"#m8d1a18dd0a\" y=\"21.318125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <defs>\n", "       <path d=\"M 31.78125 66.40625 \n", "Q 24.171875 66.40625 20.328125 58.90625 \n", "Q 16.5 51.421875 16.5 36.375 \n", "Q 16.5 21.390625 20.328125 13.890625 \n", "Q 24.171875 6.390625 31.78125 6.390625 \n", "Q 39.453125 6.390625 43.28125 13.890625 \n", "Q 47.125 21.390625 47.125 36.375 \n", "Q 47.125 51.421875 43.28125 58.90625 \n", "Q 39.453125 66.40625 31.78125 66.40625 \n", "M 31.78125 74.21875 \n", "Q 44.046875 74.21875 50.515625 64.515625 \n", "Q 56.984375 54.828125 56.984375 36.375 \n", "Q 56.984375 17.96875 50.515625 8.265625 \n", "Q 44.046875 -1.421875 31.78125 -1.421875 \n", "Q 19.53125 -1.421875 13.0625 8.265625 \n", "Q 6.59375 17.96875 6.59375 36.375 \n", "Q 6.59375 54.828125 13.0625 64.515625 \n", "Q 19.53125 74.21875 31.78125 74.21875 \n", "\" id=\"BitstreamVeraSans-Roman-30\"/>\n", "      </defs>\n", "      <g transform=\"translate(23.921875 116.6165625)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"166.603125\" xlink:href=\"#mc6b2d6bfc6\" y=\"105.018125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"166.603125\" xlink:href=\"#m8d1a18dd0a\" y=\"21.318125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 500 -->\n", "      <defs>\n", "       <path d=\"M 10.796875 72.90625 \n", "L 49.515625 72.90625 \n", "L 49.515625 64.59375 \n", "L 19.828125 64.59375 \n", "L 19.828125 46.734375 \n", "Q 21.96875 47.46875 24.109375 47.828125 \n", "Q 26.265625 48.1875 28.421875 48.1875 \n", "Q 40.625 48.1875 47.75 41.5 \n", "Q 54.890625 34.8125 54.890625 23.390625 \n", "Q 54.890625 11.625 47.5625 5.09375 \n", "Q 40.234375 -1.421875 26.90625 -1.421875 \n", "Q 22.3125 -1.421875 17.546875 -0.640625 \n", "Q 12.796875 0.140625 7.71875 1.703125 \n", "L 7.71875 11.625 \n", "Q 12.109375 9.234375 16.796875 8.0625 \n", "Q 21.484375 6.890625 26.703125 6.890625 \n", "Q 35.15625 6.890625 40.078125 11.328125 \n", "Q 45.015625 15.765625 45.015625 23.390625 \n", "Q 45.015625 31 40.078125 35.4375 \n", "Q 35.15625 39.890625 26.703125 39.890625 \n", "Q 22.75 39.890625 18.8125 39.015625 \n", "Q 14.890625 38.140625 10.796875 36.28125 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-35\"/>\n", "      </defs>\n", "      <g transform=\"translate(157.059375 116.6165625)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-35\"/>\n", "       <use x=\"63.623046875\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"127.24609375\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"306.103125\" xlink:href=\"#mc6b2d6bfc6\" y=\"105.018125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"306.103125\" xlink:href=\"#m8d1a18dd0a\" y=\"21.318125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 1000 -->\n", "      <defs>\n", "       <path d=\"M 12.40625 8.296875 \n", "L 28.515625 8.296875 \n", "L 28.515625 63.921875 \n", "L 10.984375 60.40625 \n", "L 10.984375 69.390625 \n", "L 28.421875 72.90625 \n", "L 38.28125 72.90625 \n", "L 38.28125 8.296875 \n", "L 54.390625 8.296875 \n", "L 54.390625 0 \n", "L 12.40625 0 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-31\"/>\n", "      </defs>\n", "      <g transform=\"translate(293.378125 116.6165625)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-31\"/>\n", "       <use x=\"63.623046875\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"127.24609375\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"190.869140625\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"445.603125\" xlink:href=\"#mc6b2d6bfc6\" y=\"105.018125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"445.603125\" xlink:href=\"#m8d1a18dd0a\" y=\"21.318125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 1500 -->\n", "      <g transform=\"translate(432.878125 116.6165625)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-31\"/>\n", "       <use x=\"63.623046875\" xlink:href=\"#BitstreamVeraSans-Roman-35\"/>\n", "       <use x=\"127.24609375\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"190.869140625\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"585.103125\" xlink:href=\"#mc6b2d6bfc6\" y=\"105.018125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"585.103125\" xlink:href=\"#m8d1a18dd0a\" y=\"21.318125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 2000 -->\n", "      <defs>\n", "       <path d=\"M 19.1875 8.296875 \n", "L 53.609375 8.296875 \n", "L 53.609375 0 \n", "L 7.328125 0 \n", "L 7.328125 8.296875 \n", "Q 12.9375 14.109375 22.625 23.890625 \n", "Q 32.328125 33.6875 34.8125 36.53125 \n", "Q 39.546875 41.84375 41.421875 45.53125 \n", "Q 43.3125 49.21875 43.3125 52.78125 \n", "Q 43.3125 58.59375 39.234375 62.25 \n", "Q 35.15625 65.921875 28.609375 65.921875 \n", "Q 23.96875 65.921875 18.8125 64.3125 \n", "Q 13.671875 62.703125 7.8125 59.421875 \n", "L 7.8125 69.390625 \n", "Q 13.765625 71.78125 18.9375 73 \n", "Q 24.125 74.21875 28.421875 74.21875 \n", "Q 39.75 74.21875 46.484375 68.546875 \n", "Q 53.21875 62.890625 53.21875 53.421875 \n", "Q 53.21875 48.921875 51.53125 44.890625 \n", "Q 49.859375 40.875 45.40625 35.40625 \n", "Q 44.1875 33.984375 37.640625 27.21875 \n", "Q 31.109375 20.453125 19.1875 8.296875 \n", "\" id=\"BitstreamVeraSans-Roman-32\"/>\n", "      </defs>\n", "      <g transform=\"translate(572.378125 116.6165625)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-32\"/>\n", "       <use x=\"63.623046875\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"127.24609375\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"190.869140625\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_14\">\n", "      <defs>\n", "       <path d=\"M 0 0 \n", "L 4 0 \n", "\" id=\"mceb57ea8e6\" style=\"stroke:#000000;stroke-width:0.5;\"/>\n", "      </defs>\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"27.103125\" xlink:href=\"#mceb57ea8e6\" y=\"98.043125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_15\">\n", "      <defs>\n", "       <path d=\"M 0 0 \n", "L -4 0 \n", "\" id=\"m15422dcb9f\" style=\"stroke:#000000;stroke-width:0.5;\"/>\n", "      </defs>\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"585.103125\" xlink:href=\"#m15422dcb9f\" y=\"98.043125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 0.0 -->\n", "      <defs>\n", "       <path d=\"M 10.6875 12.40625 \n", "L 21 12.40625 \n", "L 21 0 \n", "L 10.6875 0 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-2e\"/>\n", "      </defs>\n", "      <g transform=\"translate(7.2 100.8025)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"63.623046875\" xlink:href=\"#BitstreamVeraSans-Roman-2e\"/>\n", "       <use x=\"95.41015625\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"27.103125\" xlink:href=\"#mceb57ea8e6\" y=\"84.093125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"585.103125\" xlink:href=\"#m15422dcb9f\" y=\"84.093125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(7.2 86.8525)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"63.623046875\" xlink:href=\"#BitstreamVeraSans-Roman-2e\"/>\n", "       <use x=\"95.41015625\" xlink:href=\"#BitstreamVeraSans-Roman-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"27.103125\" xlink:href=\"#mceb57ea8e6\" y=\"70.143125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"585.103125\" xlink:href=\"#m15422dcb9f\" y=\"70.143125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.4 -->\n", "      <defs>\n", "       <path d=\"M 37.796875 64.3125 \n", "L 12.890625 25.390625 \n", "L 37.796875 25.390625 \n", "z\n", "M 35.203125 72.90625 \n", "L 47.609375 72.90625 \n", "L 47.609375 25.390625 \n", "L 58.015625 25.390625 \n", "L 58.015625 17.1875 \n", "L 47.609375 17.1875 \n", "L 47.609375 0 \n", "L 37.796875 0 \n", "L 37.796875 17.1875 \n", "L 4.890625 17.1875 \n", "L 4.890625 26.703125 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-34\"/>\n", "      </defs>\n", "      <g transform=\"translate(7.2 72.9025)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"63.623046875\" xlink:href=\"#BitstreamVeraSans-Roman-2e\"/>\n", "       <use x=\"95.41015625\" xlink:href=\"#BitstreamVeraSans-Roman-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"27.103125\" xlink:href=\"#mceb57ea8e6\" y=\"56.193125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"585.103125\" xlink:href=\"#m15422dcb9f\" y=\"56.193125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.6 -->\n", "      <defs>\n", "       <path d=\"M 33.015625 40.375 \n", "Q 26.375 40.375 22.484375 35.828125 \n", "Q 18.609375 31.296875 18.609375 23.390625 \n", "Q 18.609375 15.53125 22.484375 10.953125 \n", "Q 26.375 6.390625 33.015625 6.390625 \n", "Q 39.65625 6.390625 43.53125 10.953125 \n", "Q 47.40625 15.53125 47.40625 23.390625 \n", "Q 47.40625 31.296875 43.53125 35.828125 \n", "Q 39.65625 40.375 33.015625 40.375 \n", "M 52.59375 71.296875 \n", "L 52.59375 62.3125 \n", "Q 48.875 64.0625 45.09375 64.984375 \n", "Q 41.3125 65.921875 37.59375 65.921875 \n", "Q 27.828125 65.921875 22.671875 59.328125 \n", "Q 17.53125 52.734375 16.796875 39.40625 \n", "Q 19.671875 43.65625 24.015625 45.921875 \n", "Q 28.375 48.1875 33.59375 48.1875 \n", "Q 44.578125 48.1875 50.953125 41.515625 \n", "Q 57.328125 34.859375 57.328125 23.390625 \n", "Q 57.328125 12.15625 50.6875 5.359375 \n", "Q 44.046875 -1.421875 33.015625 -1.421875 \n", "Q 20.359375 -1.421875 13.671875 8.265625 \n", "Q 6.984375 17.96875 6.984375 36.375 \n", "Q 6.984375 53.65625 15.1875 63.9375 \n", "Q 23.390625 74.21875 37.203125 74.21875 \n", "Q 40.921875 74.21875 44.703125 73.484375 \n", "Q 48.484375 72.75 52.59375 71.296875 \n", "\" id=\"BitstreamVeraSans-Roman-36\"/>\n", "      </defs>\n", "      <g transform=\"translate(7.2 58.9525)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"63.623046875\" xlink:href=\"#BitstreamVeraSans-Roman-2e\"/>\n", "       <use x=\"95.41015625\" xlink:href=\"#BitstreamVeraSans-Roman-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"27.103125\" xlink:href=\"#mceb57ea8e6\" y=\"42.243125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"585.103125\" xlink:href=\"#m15422dcb9f\" y=\"42.243125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.8 -->\n", "      <defs>\n", "       <path d=\"M 31.78125 34.625 \n", "Q 24.75 34.625 20.71875 30.859375 \n", "Q 16.703125 27.09375 16.703125 20.515625 \n", "Q 16.703125 13.921875 20.71875 10.15625 \n", "Q 24.75 6.390625 31.78125 6.390625 \n", "Q 38.8125 6.390625 42.859375 10.171875 \n", "Q 46.921875 13.96875 46.921875 20.515625 \n", "Q 46.921875 27.09375 42.890625 30.859375 \n", "Q 38.875 34.625 31.78125 34.625 \n", "M 21.921875 38.8125 \n", "Q 15.578125 40.375 12.03125 44.71875 \n", "Q 8.5 49.078125 8.5 55.328125 \n", "Q 8.5 64.0625 14.71875 69.140625 \n", "Q 20.953125 74.21875 31.78125 74.21875 \n", "Q 42.671875 74.21875 48.875 69.140625 \n", "Q 55.078125 64.0625 55.078125 55.328125 \n", "Q 55.078125 49.078125 51.53125 44.71875 \n", "Q 48 40.375 41.703125 38.8125 \n", "Q 48.828125 37.15625 52.796875 32.3125 \n", "Q 56.78125 27.484375 56.78125 20.515625 \n", "Q 56.78125 9.90625 50.3125 4.234375 \n", "Q 43.84375 -1.421875 31.78125 -1.421875 \n", "Q 19.734375 -1.421875 13.25 4.234375 \n", "Q 6.78125 9.90625 6.78125 20.515625 \n", "Q 6.78125 27.484375 10.78125 32.3125 \n", "Q 14.796875 37.15625 21.921875 38.8125 \n", "M 18.3125 54.390625 \n", "Q 18.3125 48.734375 21.84375 45.5625 \n", "Q 25.390625 42.390625 31.78125 42.390625 \n", "Q 38.140625 42.390625 41.71875 45.5625 \n", "Q 45.3125 48.734375 45.3125 54.390625 \n", "Q 45.3125 60.0625 41.71875 63.234375 \n", "Q 38.140625 66.40625 31.78125 66.40625 \n", "Q 25.390625 66.40625 21.84375 63.234375 \n", "Q 18.3125 60.0625 18.3125 54.390625 \n", "\" id=\"BitstreamVeraSans-Roman-38\"/>\n", "      </defs>\n", "      <g transform=\"translate(7.2 45.0025)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "       <use x=\"63.623046875\" xlink:href=\"#BitstreamVeraSans-Roman-2e\"/>\n", "       <use x=\"95.41015625\" xlink:href=\"#BitstreamVeraSans-Roman-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"27.103125\" xlink:href=\"#mceb57ea8e6\" y=\"28.293125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"line2d_25\">\n", "      <g>\n", "       <use style=\"stroke:#000000;stroke-width:0.5;\" x=\"585.103125\" xlink:href=\"#m15422dcb9f\" y=\"28.293125\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(7.2 31.0525)scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#BitstreamVeraSans-Roman-31\"/>\n", "       <use x=\"63.623046875\" xlink:href=\"#BitstreamVeraSans-Roman-2e\"/>\n", "       <use x=\"95.41015625\" xlink:href=\"#BitstreamVeraSans-Roman-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"text_12\">\n", "    <!-- test (excerpt) -->\n", "    <defs>\n", "     <path d=\"M 18.109375 8.203125 \n", "L 18.109375 -20.796875 \n", "L 9.078125 -20.796875 \n", "L 9.078125 54.6875 \n", "L 18.109375 54.6875 \n", "L 18.109375 46.390625 \n", "Q 20.953125 51.265625 25.265625 53.625 \n", "Q 29.59375 56 35.59375 56 \n", "Q 45.5625 56 51.78125 48.09375 \n", "Q 58.015625 40.1875 58.015625 27.296875 \n", "Q 58.015625 14.40625 51.78125 6.484375 \n", "Q 45.5625 -1.421875 35.59375 -1.421875 \n", "Q 29.59375 -1.421875 25.265625 0.953125 \n", "Q 20.953125 3.328125 18.109375 8.203125 \n", "M 48.6875 27.296875 \n", "Q 48.6875 37.203125 44.609375 42.84375 \n", "Q 40.53125 48.484375 33.40625 48.484375 \n", "Q 26.265625 48.484375 22.1875 42.84375 \n", "Q 18.109375 37.203125 18.109375 27.296875 \n", "Q 18.109375 17.390625 22.1875 11.75 \n", "Q 26.265625 6.109375 33.40625 6.109375 \n", "Q 40.53125 6.109375 44.609375 11.75 \n", "Q 48.6875 17.390625 48.6875 27.296875 \n", "\" id=\"BitstreamVeraSans-Roman-70\"/>\n", "     <path d=\"M 31 75.875 \n", "Q 24.46875 64.65625 21.28125 53.65625 \n", "Q 18.109375 42.671875 18.109375 31.390625 \n", "Q 18.109375 20.125 21.3125 9.0625 \n", "Q 24.515625 -2 31 -13.1875 \n", "L 23.1875 -13.1875 \n", "Q 15.875 -1.703125 12.234375 9.375 \n", "Q 8.59375 20.453125 8.59375 31.390625 \n", "Q 8.59375 42.28125 12.203125 53.3125 \n", "Q 15.828125 64.359375 23.1875 75.875 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-28\"/>\n", "     <path id=\"BitstreamVeraSans-Roman-20\"/>\n", "     <path d=\"M 8.015625 75.875 \n", "L 15.828125 75.875 \n", "Q 23.140625 64.359375 26.78125 53.3125 \n", "Q 30.421875 42.28125 30.421875 31.390625 \n", "Q 30.421875 20.453125 26.78125 9.375 \n", "Q 23.140625 -1.703125 15.828125 -13.1875 \n", "L 8.015625 -13.1875 \n", "Q 14.5 -2 17.703125 9.0625 \n", "Q 20.90625 20.125 20.90625 31.390625 \n", "Q 20.90625 42.671875 17.703125 53.65625 \n", "Q 14.5 64.65625 8.015625 75.875 \n", "\" id=\"BitstreamVeraSans-Roman-29\"/>\n", "     <path d=\"M 54.890625 54.6875 \n", "L 35.109375 28.078125 \n", "L 55.90625 0 \n", "L 45.3125 0 \n", "L 29.390625 21.484375 \n", "L 13.484375 0 \n", "L 2.875 0 \n", "L 24.125 28.609375 \n", "L 4.6875 54.6875 \n", "L 15.28125 54.6875 \n", "L 29.78125 35.203125 \n", "L 44.28125 54.6875 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-78\"/>\n", "     <path d=\"M 56.203125 29.59375 \n", "L 56.203125 25.203125 \n", "L 14.890625 25.203125 \n", "Q 15.484375 15.921875 20.484375 11.0625 \n", "Q 25.484375 6.203125 34.421875 6.203125 \n", "Q 39.59375 6.203125 44.453125 7.46875 \n", "Q 49.3125 8.734375 54.109375 11.28125 \n", "L 54.109375 2.78125 \n", "Q 49.265625 0.734375 44.1875 -0.34375 \n", "Q 39.109375 -1.421875 33.890625 -1.421875 \n", "Q 20.796875 -1.421875 13.15625 6.1875 \n", "Q 5.515625 13.8125 5.515625 26.8125 \n", "Q 5.515625 40.234375 12.765625 48.109375 \n", "Q 20.015625 56 32.328125 56 \n", "Q 43.359375 56 49.78125 48.890625 \n", "Q 56.203125 41.796875 56.203125 29.59375 \n", "M 47.21875 32.234375 \n", "Q 47.125 39.59375 43.09375 43.984375 \n", "Q 39.0625 48.390625 32.421875 48.390625 \n", "Q 24.90625 48.390625 20.390625 44.140625 \n", "Q 15.875 39.890625 15.1875 32.171875 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-65\"/>\n", "     <path d=\"M 48.78125 52.59375 \n", "L 48.78125 44.1875 \n", "Q 44.96875 46.296875 41.140625 47.34375 \n", "Q 37.3125 48.390625 33.40625 48.390625 \n", "Q 24.65625 48.390625 19.8125 42.84375 \n", "Q 14.984375 37.3125 14.984375 27.296875 \n", "Q 14.984375 17.28125 19.8125 11.734375 \n", "Q 24.65625 6.203125 33.40625 6.203125 \n", "Q 37.3125 6.203125 41.140625 7.25 \n", "Q 44.96875 8.296875 48.78125 10.40625 \n", "L 48.78125 2.09375 \n", "Q 45.015625 0.34375 40.984375 -0.53125 \n", "Q 36.96875 -1.421875 32.421875 -1.421875 \n", "Q 20.0625 -1.421875 12.78125 6.34375 \n", "Q 5.515625 14.109375 5.515625 27.296875 \n", "Q 5.515625 40.671875 12.859375 48.328125 \n", "Q 20.21875 56 33.015625 56 \n", "Q 37.15625 56 41.109375 55.140625 \n", "Q 45.0625 54.296875 48.78125 52.59375 \n", "\" id=\"BitstreamVeraSans-Roman-63\"/>\n", "     <path d=\"M 41.109375 46.296875 \n", "Q 39.59375 47.171875 37.8125 47.578125 \n", "Q 36.03125 48 33.890625 48 \n", "Q 26.265625 48 22.1875 43.046875 \n", "Q 18.109375 38.09375 18.109375 28.8125 \n", "L 18.109375 0 \n", "L 9.078125 0 \n", "L 9.078125 54.6875 \n", "L 18.109375 54.6875 \n", "L 18.109375 46.1875 \n", "Q 20.953125 51.171875 25.484375 53.578125 \n", "Q 30.03125 56 36.53125 56 \n", "Q 37.453125 56 38.578125 55.875 \n", "Q 39.703125 55.765625 41.0625 55.515625 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-72\"/>\n", "     <path d=\"M 18.3125 70.21875 \n", "L 18.3125 54.6875 \n", "L 36.8125 54.6875 \n", "L 36.8125 47.703125 \n", "L 18.3125 47.703125 \n", "L 18.3125 18.015625 \n", "Q 18.3125 11.328125 20.140625 9.421875 \n", "Q 21.96875 7.515625 27.59375 7.515625 \n", "L 36.8125 7.515625 \n", "L 36.8125 0 \n", "L 27.59375 0 \n", "Q 17.1875 0 13.234375 3.875 \n", "Q 9.28125 7.765625 9.28125 18.015625 \n", "L 9.28125 47.703125 \n", "L 2.6875 47.703125 \n", "L 2.6875 54.6875 \n", "L 9.28125 54.6875 \n", "L 9.28125 70.21875 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-74\"/>\n", "     <path d=\"M 44.28125 53.078125 \n", "L 44.28125 44.578125 \n", "Q 40.484375 46.53125 36.375 47.5 \n", "Q 32.28125 48.484375 27.875 48.484375 \n", "Q 21.1875 48.484375 17.84375 46.4375 \n", "Q 14.5 44.390625 14.5 40.28125 \n", "Q 14.5 37.15625 16.890625 35.375 \n", "Q 19.28125 33.59375 26.515625 31.984375 \n", "L 29.59375 31.296875 \n", "Q 39.15625 29.25 43.1875 25.515625 \n", "Q 47.21875 21.78125 47.21875 15.09375 \n", "Q 47.21875 7.46875 41.1875 3.015625 \n", "Q 35.15625 -1.421875 24.609375 -1.421875 \n", "Q 20.21875 -1.421875 15.453125 -0.5625 \n", "Q 10.6875 0.296875 5.421875 2 \n", "L 5.421875 11.28125 \n", "Q 10.40625 8.6875 15.234375 7.390625 \n", "Q 20.0625 6.109375 24.8125 6.109375 \n", "Q 31.15625 6.109375 34.5625 8.28125 \n", "Q 37.984375 10.453125 37.984375 14.40625 \n", "Q 37.984375 18.0625 35.515625 20.015625 \n", "Q 33.0625 21.96875 24.703125 23.78125 \n", "L 21.578125 24.515625 \n", "Q 13.234375 26.265625 9.515625 29.90625 \n", "Q 5.8125 33.546875 5.8125 39.890625 \n", "Q 5.8125 47.609375 11.28125 51.796875 \n", "Q 16.75 56 26.8125 56 \n", "Q 31.78125 56 36.171875 55.265625 \n", "Q 40.578125 54.546875 44.28125 53.078125 \n", "\" id=\"BitstreamVeraSans-Roman-73\"/>\n", "    </defs>\n", "    <g transform=\"translate(265.1325 16.318125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#BitstreamVeraSans-Roman-74\"/>\n", "     <use x=\"39.208984375\" xlink:href=\"#BitstreamVeraSans-Roman-65\"/>\n", "     <use x=\"100.732421875\" xlink:href=\"#BitstreamVeraSans-Roman-73\"/>\n", "     <use x=\"152.83203125\" xlink:href=\"#BitstreamVeraSans-Roman-74\"/>\n", "     <use x=\"192.041015625\" xlink:href=\"#BitstreamVeraSans-Roman-20\"/>\n", "     <use x=\"223.828125\" xlink:href=\"#BitstreamVeraSans-Roman-28\"/>\n", "     <use x=\"262.841796875\" xlink:href=\"#BitstreamVeraSans-Roman-65\"/>\n", "     <use x=\"324.349609375\" xlink:href=\"#BitstreamVeraSans-Roman-78\"/>\n", "     <use x=\"383.513671875\" xlink:href=\"#BitstreamVeraSans-Roman-63\"/>\n", "     <use x=\"438.494140625\" xlink:href=\"#BitstreamVeraSans-Roman-65\"/>\n", "     <use x=\"500.017578125\" xlink:href=\"#BitstreamVeraSans-Roman-72\"/>\n", "     <use x=\"541.130859375\" xlink:href=\"#BitstreamVeraSans-Roman-70\"/>\n", "     <use x=\"604.607421875\" xlink:href=\"#BitstreamVeraSans-Roman-74\"/>\n", "     <use x=\"643.81640625\" xlink:href=\"#BitstreamVeraSans-Roman-29\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 532.442622 57.429981 \n", "L 581.633125 57.429981 \n", "L 581.633125 24.788125 \n", "L 532.442622 24.788125 \n", "z\n", "\" style=\"fill:#ffffff;stroke:#000000;stroke-linejoin:miter;\"/>\n", "    </g>\n", "    <g id=\"line2d_26\">\n", "     <path d=\"M 537.300622 30.408441 \n", "L 547.016622 30.408441 \n", "\" style=\"fill:none;stroke:#0000ff;stroke-linecap:square;\"/>\n", "    </g>\n", "    <g id=\"line2d_27\"/>\n", "    <g id=\"text_13\">\n", "     <!-- control -->\n", "     <defs>\n", "      <path d=\"M 30.609375 48.390625 \n", "Q 23.390625 48.390625 19.1875 42.75 \n", "Q 14.984375 37.109375 14.984375 27.296875 \n", "Q 14.984375 17.484375 19.15625 11.84375 \n", "Q 23.34375 6.203125 30.609375 6.203125 \n", "Q 37.796875 6.203125 41.984375 11.859375 \n", "Q 46.1875 17.53125 46.1875 27.296875 \n", "Q 46.1875 37.015625 41.984375 42.703125 \n", "Q 37.796875 48.390625 30.609375 48.390625 \n", "M 30.609375 56 \n", "Q 42.328125 56 49.015625 48.375 \n", "Q 55.71875 40.765625 55.71875 27.296875 \n", "Q 55.71875 13.875 49.015625 6.21875 \n", "Q 42.328125 -1.421875 30.609375 -1.421875 \n", "Q 18.84375 -1.421875 12.171875 6.21875 \n", "Q 5.515625 13.875 5.515625 27.296875 \n", "Q 5.515625 40.765625 12.171875 48.375 \n", "Q 18.84375 56 30.609375 56 \n", "\" id=\"BitstreamVeraSans-Roman-6f\"/>\n", "      <path d=\"M 9.421875 75.984375 \n", "L 18.40625 75.984375 \n", "L 18.40625 0 \n", "L 9.421875 0 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-6c\"/>\n", "      <path d=\"M 54.890625 33.015625 \n", "L 54.890625 0 \n", "L 45.90625 0 \n", "L 45.90625 32.71875 \n", "Q 45.90625 40.484375 42.875 44.328125 \n", "Q 39.84375 48.1875 33.796875 48.1875 \n", "Q 26.515625 48.1875 22.3125 43.546875 \n", "Q 18.109375 38.921875 18.109375 30.90625 \n", "L 18.109375 0 \n", "L 9.078125 0 \n", "L 9.078125 54.6875 \n", "L 18.109375 54.6875 \n", "L 18.109375 46.1875 \n", "Q 21.34375 51.125 25.703125 53.5625 \n", "Q 30.078125 56 35.796875 56 \n", "Q 45.21875 56 50.046875 50.171875 \n", "Q 54.890625 44.34375 54.890625 33.015625 \n", "\" id=\"BitstreamVeraSans-Roman-6e\"/>\n", "     </defs>\n", "     <g transform=\"translate(554.650621875 32.837440625)scale(0.06939999999999999 -0.06939999999999999)\">\n", "      <use xlink:href=\"#BitstreamVeraSans-Roman-63\"/>\n", "      <use x=\"54.98046875\" xlink:href=\"#BitstreamVeraSans-Roman-6f\"/>\n", "      <use x=\"116.162109375\" xlink:href=\"#BitstreamVeraSans-Roman-6e\"/>\n", "      <use x=\"179.541015625\" xlink:href=\"#BitstreamVeraSans-Roman-74\"/>\n", "      <use x=\"218.75\" xlink:href=\"#BitstreamVeraSans-Roman-72\"/>\n", "      <use x=\"259.83203125\" xlink:href=\"#BitstreamVeraSans-Roman-6f\"/>\n", "      <use x=\"321.013671875\" xlink:href=\"#BitstreamVeraSans-Roman-6c\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_28\">\n", "     <path d=\"M 537.300622 40.595059 \n", "L 547.016622 40.595059 \n", "\" style=\"fill:none;stroke:#008000;stroke-linecap:square;\"/>\n", "    </g>\n", "    <g id=\"line2d_29\"/>\n", "    <g id=\"text_14\">\n", "     <!-- target -->\n", "     <defs>\n", "      <path d=\"M 34.28125 27.484375 \n", "Q 23.390625 27.484375 19.1875 25 \n", "Q 14.984375 22.515625 14.984375 16.5 \n", "Q 14.984375 11.71875 18.140625 8.90625 \n", "Q 21.296875 6.109375 26.703125 6.109375 \n", "Q 34.1875 6.109375 38.703125 11.40625 \n", "Q 43.21875 16.703125 43.21875 25.484375 \n", "L 43.21875 27.484375 \n", "z\n", "M 52.203125 31.203125 \n", "L 52.203125 0 \n", "L 43.21875 0 \n", "L 43.21875 8.296875 \n", "Q 40.140625 3.328125 35.546875 0.953125 \n", "Q 30.953125 -1.421875 24.3125 -1.421875 \n", "Q 15.921875 -1.421875 10.953125 3.296875 \n", "Q 6 8.015625 6 15.921875 \n", "Q 6 25.140625 12.171875 29.828125 \n", "Q 18.359375 34.515625 30.609375 34.515625 \n", "L 43.21875 34.515625 \n", "L 43.21875 35.40625 \n", "Q 43.21875 41.609375 39.140625 45 \n", "Q 35.0625 48.390625 27.6875 48.390625 \n", "Q 23 48.390625 18.546875 47.265625 \n", "Q 14.109375 46.140625 10.015625 43.890625 \n", "L 10.015625 52.203125 \n", "Q 14.9375 54.109375 19.578125 55.046875 \n", "Q 24.21875 56 28.609375 56 \n", "Q 40.484375 56 46.34375 49.84375 \n", "Q 52.203125 43.703125 52.203125 31.203125 \n", "\" id=\"BitstreamVeraSans-Roman-61\"/>\n", "      <path d=\"M 45.40625 27.984375 \n", "Q 45.40625 37.75 41.375 43.109375 \n", "Q 37.359375 48.484375 30.078125 48.484375 \n", "Q 22.859375 48.484375 18.828125 43.109375 \n", "Q 14.796875 37.75 14.796875 27.984375 \n", "Q 14.796875 18.265625 18.828125 12.890625 \n", "Q 22.859375 7.515625 30.078125 7.515625 \n", "Q 37.359375 7.515625 41.375 12.890625 \n", "Q 45.40625 18.265625 45.40625 27.984375 \n", "M 54.390625 6.78125 \n", "Q 54.390625 -7.171875 48.1875 -13.984375 \n", "Q 42 -20.796875 29.203125 -20.796875 \n", "Q 24.46875 -20.796875 20.265625 -20.09375 \n", "Q 16.0625 -19.390625 12.109375 -17.921875 \n", "L 12.109375 -9.1875 \n", "Q 16.0625 -11.328125 19.921875 -12.34375 \n", "Q 23.78125 -13.375 27.78125 -13.375 \n", "Q 36.625 -13.375 41.015625 -8.765625 \n", "Q 45.40625 -4.15625 45.40625 5.171875 \n", "L 45.40625 9.625 \n", "Q 42.625 4.78125 38.28125 2.390625 \n", "Q 33.9375 0 27.875 0 \n", "Q 17.828125 0 11.671875 7.65625 \n", "Q 5.515625 15.328125 5.515625 27.984375 \n", "Q 5.515625 40.671875 11.671875 48.328125 \n", "Q 17.828125 56 27.875 56 \n", "Q 33.9375 56 38.28125 53.609375 \n", "Q 42.625 51.21875 45.40625 46.390625 \n", "L 45.40625 54.6875 \n", "L 54.390625 54.6875 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-67\"/>\n", "     </defs>\n", "     <g transform=\"translate(554.650621875 43.024059375)scale(0.06939999999999999 -0.06939999999999999)\">\n", "      <use xlink:href=\"#BitstreamVeraSans-Roman-74\"/>\n", "      <use x=\"39.208984375\" xlink:href=\"#BitstreamVeraSans-Roman-61\"/>\n", "      <use x=\"100.48828125\" xlink:href=\"#BitstreamVeraSans-Roman-72\"/>\n", "      <use x=\"141.5859375\" xlink:href=\"#BitstreamVeraSans-Roman-67\"/>\n", "      <use x=\"205.0625\" xlink:href=\"#BitstreamVeraSans-Roman-65\"/>\n", "      <use x=\"266.5859375\" xlink:href=\"#BitstreamVeraSans-Roman-74\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_30\">\n", "     <path d=\"M 537.300622 50.781678 \n", "L 547.016622 50.781678 \n", "\" style=\"fill:none;stroke:#ff0000;stroke-linecap:square;\"/>\n", "    </g>\n", "    <g id=\"line2d_31\"/>\n", "    <g id=\"text_15\">\n", "     <!-- model -->\n", "     <defs>\n", "      <path d=\"M 45.40625 46.390625 \n", "L 45.40625 75.984375 \n", "L 54.390625 75.984375 \n", "L 54.390625 0 \n", "L 45.40625 0 \n", "L 45.40625 8.203125 \n", "Q 42.578125 3.328125 38.25 0.953125 \n", "Q 33.9375 -1.421875 27.875 -1.421875 \n", "Q 17.96875 -1.421875 11.734375 6.484375 \n", "Q 5.515625 14.40625 5.515625 27.296875 \n", "Q 5.515625 40.1875 11.734375 48.09375 \n", "Q 17.96875 56 27.875 56 \n", "Q 33.9375 56 38.25 53.625 \n", "Q 42.578125 51.265625 45.40625 46.390625 \n", "M 14.796875 27.296875 \n", "Q 14.796875 17.390625 18.875 11.75 \n", "Q 22.953125 6.109375 30.078125 6.109375 \n", "Q 37.203125 6.109375 41.296875 11.75 \n", "Q 45.40625 17.390625 45.40625 27.296875 \n", "Q 45.40625 37.203125 41.296875 42.84375 \n", "Q 37.203125 48.484375 30.078125 48.484375 \n", "Q 22.953125 48.484375 18.875 42.84375 \n", "Q 14.796875 37.203125 14.796875 27.296875 \n", "\" id=\"BitstreamVeraSans-Roman-64\"/>\n", "      <path d=\"M 52 44.1875 \n", "Q 55.375 50.25 60.0625 53.125 \n", "Q 64.75 56 71.09375 56 \n", "Q 79.640625 56 84.28125 50.015625 \n", "Q 88.921875 44.046875 88.921875 33.015625 \n", "L 88.921875 0 \n", "L 79.890625 0 \n", "L 79.890625 32.71875 \n", "Q 79.890625 40.578125 77.09375 44.375 \n", "Q 74.3125 48.1875 68.609375 48.1875 \n", "Q 61.625 48.1875 57.5625 43.546875 \n", "Q 53.515625 38.921875 53.515625 30.90625 \n", "L 53.515625 0 \n", "L 44.484375 0 \n", "L 44.484375 32.71875 \n", "Q 44.484375 40.625 41.703125 44.40625 \n", "Q 38.921875 48.1875 33.109375 48.1875 \n", "Q 26.21875 48.1875 22.15625 43.53125 \n", "Q 18.109375 38.875 18.109375 30.90625 \n", "L 18.109375 0 \n", "L 9.078125 0 \n", "L 9.078125 54.6875 \n", "L 18.109375 54.6875 \n", "L 18.109375 46.1875 \n", "Q 21.1875 51.21875 25.484375 53.609375 \n", "Q 29.78125 56 35.6875 56 \n", "Q 41.65625 56 45.828125 52.96875 \n", "Q 50 49.953125 52 44.1875 \n", "\" id=\"BitstreamVeraSans-Roman-6d\"/>\n", "     </defs>\n", "     <g transform=\"translate(554.650621875 53.210678125)scale(0.06939999999999999 -0.06939999999999999)\">\n", "      <use xlink:href=\"#BitstreamVeraSans-Roman-6d\"/>\n", "      <use x=\"97.412109375\" xlink:href=\"#BitstreamVeraSans-Roman-6f\"/>\n", "      <use x=\"158.59375\" xlink:href=\"#BitstreamVeraSans-Roman-64\"/>\n", "      <use x=\"222.0703125\" xlink:href=\"#BitstreamVeraSans-Roman-65\"/>\n", "      <use x=\"283.59375\" xlink:href=\"#BitstreamVeraSans-Roman-6c\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p286ae4acb9\">\n", "   <rect height=\"83.7\" width=\"558.0\" x=\"27.103125\" y=\"21.318125\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<matplotlib.figure.Figure at 0x10eafd860>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["window_tr = range(int(len(train_output)/4),int(len(train_output)/4+2000))\n", "plt.figure(figsize=(10,1.5))\n", "plt.plot(train_ctrl[window_tr,1],label='control')\n", "plt.plot(train_output[window_tr],label='target')\n", "plt.plot(pred_train[window_tr],label='model')\n", "plt.legend(fontsize='x-small')\n", "plt.title('training (excerpt)')\n", "plt.ylim([-0.1,1.1])\n", "\n", "window_test = range(2000)\n", "plt.figure(figsize=(10,1.5))\n", "plt.plot(test_ctrl[window_test,1],label='control')\n", "plt.plot(test_output[window_test],label='target')\n", "plt.plot(pred_test[window_test],label='model')\n", "plt.legend(fontsize='x-small')\n", "plt.title('test (excerpt)')\n", "plt.ylim([-0.1,1.1]);"]}, {"cell_type": "markdown", "metadata": {}, "source": ["What we see is that we can't see much, except that the amplitude is systematically too small (any ideas why that is?).\n", "\n", "So let's look at a few spectrograms to see how the frequency spectrum of these signals changes over time."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.text.Text at 0x1119d10f0>"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Created with matplotlib (http://matplotlib.org/) -->\n", "<svg height=\"126pt\" version=\"1.1\" viewBox=\"0 0 419 126\" width=\"419pt\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", " <defs>\n", "  <style type=\"text/css\">\n", "*{stroke-linecap:butt;stroke-linejoin:round;stroke-miterlimit:100000;}\n", "  </style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 126.89625 \n", "L 419.678125 126.89625 \n", "L 419.678125 0 \n", "L 0 0 \n", "L 0 126.89625 \n", "z\n", "\" style=\"fill:none;\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 21.**********.018125 \n", "L 412.478125 105.018125 \n", "L 412.478125 21.318125 \n", "L 21.878125 21.318125 \n", "z\n", "\" style=\"fill:#ffffff;\"/>\n", "   </g>\n", "   <g clip-path=\"url(#pfe7c2c4a07)\">\n", "    <image height=\"85.0\" id=\"image46e0363d37\" width=\"391.0\" x=\"21.878125\" xlink:href=\"data:image/png;base64,\n", "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\" y=\"20.018125\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 21.878125 21.318125 \n", "L 412.478125 21.318125 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 21.**********.018125 \n", "L 21.878125 21.318125 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 21.**********.018125 \n", "L 412.478125 105.018125 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 412.478125 105.018125 \n", "L 412.478125 21.318125 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"text_1\">\n", "     <!-- time -->\n", "     <defs>\n", "      <path d=\"M 9.421875 54.6875 \n", "L 18.40625 54.6875 \n", "L 18.40625 0 \n", "L 9.421875 0 \n", "z\n", "M 9.421875 75.984375 \n", "L 18.40625 75.984375 \n", "L 18.40625 64.59375 \n", "L 9.421875 64.59375 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-69\"/>\n", "      <path d=\"M 56.203125 29.59375 \n", "L 56.203125 25.203125 \n", "L 14.890625 25.203125 \n", "Q 15.484375 15.921875 20.484375 11.0625 \n", "Q 25.484375 6.203125 34.421875 6.203125 \n", "Q 39.59375 6.203125 44.453125 7.46875 \n", "Q 49.3125 8.734375 54.109375 11.28125 \n", "L 54.109375 2.78125 \n", "Q 49.265625 0.734375 44.1875 -0.34375 \n", "Q 39.109375 -1.421875 33.890625 -1.421875 \n", "Q 20.796875 -1.421875 13.15625 6.1875 \n", "Q 5.515625 13.8125 5.515625 26.8125 \n", "Q 5.515625 40.234375 12.765625 48.109375 \n", "Q 20.015625 56 32.328125 56 \n", "Q 43.359375 56 49.78125 48.890625 \n", "Q 56.203125 41.796875 56.203125 29.59375 \n", "M 47.21875 32.234375 \n", "Q 47.125 39.59375 43.09375 43.984375 \n", "Q 39.0625 48.390625 32.421875 48.390625 \n", "Q 24.90625 48.390625 20.390625 44.140625 \n", "Q 15.875 39.890625 15.1875 32.171875 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-65\"/>\n", "      <path d=\"M 18.3125 70.21875 \n", "L 18.3125 54.6875 \n", "L 36.8125 54.6875 \n", "L 36.8125 47.703125 \n", "L 18.3125 47.703125 \n", "L 18.3125 18.015625 \n", "Q 18.3125 11.328125 20.140625 9.421875 \n", "Q 21.96875 7.515625 27.59375 7.515625 \n", "L 36.8125 7.515625 \n", "L 36.8125 0 \n", "L 27.59375 0 \n", "Q 17.1875 0 13.234375 3.875 \n", "Q 9.28125 7.765625 9.28125 18.015625 \n", "L 9.28125 47.703125 \n", "L 2.6875 47.703125 \n", "L 2.6875 54.6875 \n", "L 9.28125 54.6875 \n", "L 9.28125 70.21875 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-74\"/>\n", "      <path d=\"M 52 44.1875 \n", "Q 55.375 50.25 60.0625 53.125 \n", "Q 64.75 56 71.09375 56 \n", "Q 79.640625 56 84.28125 50.015625 \n", "Q 88.921875 44.046875 88.921875 33.015625 \n", "L 88.921875 0 \n", "L 79.890625 0 \n", "L 79.890625 32.71875 \n", "Q 79.890625 40.578125 77.09375 44.375 \n", "Q 74.3125 48.1875 68.609375 48.1875 \n", "Q 61.625 48.1875 57.5625 43.546875 \n", "Q 53.515625 38.921875 53.515625 30.90625 \n", "L 53.515625 0 \n", "L 44.484375 0 \n", "L 44.484375 32.71875 \n", "Q 44.484375 40.625 41.703125 44.40625 \n", "Q 38.921875 48.1875 33.109375 48.1875 \n", "Q 26.21875 48.1875 22.15625 43.53125 \n", "Q 18.109375 38.875 18.109375 30.90625 \n", "L 18.109375 0 \n", "L 9.078125 0 \n", "L 9.078125 54.6875 \n", "L 18.109375 54.6875 \n", "L 18.109375 46.1875 \n", "Q 21.1875 51.21875 25.484375 53.609375 \n", "Q 29.78125 56 35.6875 56 \n", "Q 41.65625 56 45.828125 52.96875 \n", "Q 50 49.953125 52 44.1875 \n", "\" id=\"BitstreamVeraSans-Roman-6d\"/>\n", "     </defs>\n", "     <g transform=\"translate(205.88203125 117.6165625)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#BitstreamVeraSans-Roman-74\"/>\n", "      <use x=\"39.208984375\" xlink:href=\"#BitstreamVeraSans-Roman-69\"/>\n", "      <use x=\"66.9921875\" xlink:href=\"#BitstreamVeraSans-Roman-6d\"/>\n", "      <use x=\"164.404296875\" xlink:href=\"#BitstreamVeraSans-Roman-65\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"text_2\">\n", "     <!-- freq -->\n", "     <defs>\n", "      <path d=\"M 37.109375 75.984375 \n", "L 37.109375 68.5 \n", "L 28.515625 68.5 \n", "Q 23.6875 68.5 21.796875 66.546875 \n", "Q 19.921875 64.59375 19.921875 59.515625 \n", "L 19.921875 54.6875 \n", "L 34.71875 54.6875 \n", "L 34.71875 47.703125 \n", "L 19.921875 47.703125 \n", "L 19.921875 0 \n", "L 10.890625 0 \n", "L 10.890625 47.703125 \n", "L 2.296875 47.703125 \n", "L 2.296875 54.6875 \n", "L 10.890625 54.6875 \n", "L 10.890625 58.5 \n", "Q 10.890625 67.625 15.140625 71.796875 \n", "Q 19.390625 75.984375 28.609375 75.984375 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-66\"/>\n", "      <path d=\"M 14.796875 27.296875 \n", "Q 14.796875 17.390625 18.875 11.75 \n", "Q 22.953125 6.109375 30.078125 6.109375 \n", "Q 37.203125 6.109375 41.296875 11.75 \n", "Q 45.40625 17.390625 45.40625 27.296875 \n", "Q 45.40625 37.203125 41.296875 42.84375 \n", "Q 37.203125 48.484375 30.078125 48.484375 \n", "Q 22.953125 48.484375 18.875 42.84375 \n", "Q 14.796875 37.203125 14.796875 27.296875 \n", "M 45.40625 8.203125 \n", "Q 42.578125 3.328125 38.25 0.953125 \n", "Q 33.9375 -1.421875 27.875 -1.421875 \n", "Q 17.96875 -1.421875 11.734375 6.484375 \n", "Q 5.515625 14.40625 5.515625 27.296875 \n", "Q 5.515625 40.1875 11.734375 48.09375 \n", "Q 17.96875 56 27.875 56 \n", "Q 33.9375 56 38.25 53.625 \n", "Q 42.578125 51.265625 45.40625 46.390625 \n", "L 45.40625 54.6875 \n", "L 54.390625 54.6875 \n", "L 54.390625 -20.796875 \n", "L 45.40625 -20.796875 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-71\"/>\n", "      <path d=\"M 41.109375 46.296875 \n", "Q 39.59375 47.171875 37.8125 47.578125 \n", "Q 36.03125 48 33.890625 48 \n", "Q 26.265625 48 22.1875 43.046875 \n", "Q 18.109375 38.09375 18.109375 28.8125 \n", "L 18.109375 0 \n", "L 9.078125 0 \n", "L 9.078125 54.6875 \n", "L 18.109375 54.6875 \n", "L 18.109375 46.1875 \n", "Q 20.953125 51.171875 25.484375 53.578125 \n", "Q 30.03125 56 36.53125 56 \n", "Q 37.453125 56 38.578125 55.875 \n", "Q 39.703125 55.765625 41.0625 55.515625 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-72\"/>\n", "     </defs>\n", "     <g transform=\"translate(14.7984375 73.23296875)rotate(-90.0)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#BitstreamVeraSans-Roman-66\"/>\n", "      <use x=\"35.205078125\" xlink:href=\"#BitstreamVeraSans-Roman-72\"/>\n", "      <use x=\"76.287109375\" xlink:href=\"#BitstreamVeraSans-Roman-65\"/>\n", "      <use x=\"137.810546875\" xlink:href=\"#BitstreamVeraSans-Roman-71\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"text_3\">\n", "    <!-- training: target -->\n", "    <defs>\n", "     <path d=\"M 11.71875 12.40625 \n", "L 22.015625 12.40625 \n", "L 22.015625 0 \n", "L 11.71875 0 \n", "z\n", "M 11.71875 51.703125 \n", "L 22.015625 51.703125 \n", "L 22.015625 39.3125 \n", "L 11.71875 39.3125 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-3a\"/>\n", "     <path d=\"M 34.28125 27.484375 \n", "Q 23.390625 27.484375 19.1875 25 \n", "Q 14.984375 22.515625 14.984375 16.5 \n", "Q 14.984375 11.71875 18.140625 8.90625 \n", "Q 21.296875 6.109375 26.703125 6.109375 \n", "Q 34.1875 6.109375 38.703125 11.40625 \n", "Q 43.21875 16.703125 43.21875 25.484375 \n", "L 43.21875 27.484375 \n", "z\n", "M 52.203125 31.203125 \n", "L 52.203125 0 \n", "L 43.21875 0 \n", "L 43.21875 8.296875 \n", "Q 40.140625 3.328125 35.546875 0.953125 \n", "Q 30.953125 -1.421875 24.3125 -1.421875 \n", "Q 15.921875 -1.421875 10.953125 3.296875 \n", "Q 6 8.015625 6 15.921875 \n", "Q 6 25.140625 12.171875 29.828125 \n", "Q 18.359375 34.515625 30.609375 34.515625 \n", "L 43.21875 34.515625 \n", "L 43.21875 35.40625 \n", "Q 43.21875 41.609375 39.140625 45 \n", "Q 35.0625 48.390625 27.6875 48.390625 \n", "Q 23 48.390625 18.546875 47.265625 \n", "Q 14.109375 46.140625 10.015625 43.890625 \n", "L 10.015625 52.203125 \n", "Q 14.9375 54.109375 19.578125 55.046875 \n", "Q 24.21875 56 28.609375 56 \n", "Q 40.484375 56 46.34375 49.84375 \n", "Q 52.203125 43.703125 52.203125 31.203125 \n", "\" id=\"BitstreamVeraSans-Roman-61\"/>\n", "     <path id=\"BitstreamVeraSans-Roman-20\"/>\n", "     <path d=\"M 45.40625 27.984375 \n", "Q 45.40625 37.75 41.375 43.109375 \n", "Q 37.359375 48.484375 30.078125 48.484375 \n", "Q 22.859375 48.484375 18.828125 43.109375 \n", "Q 14.796875 37.75 14.796875 27.984375 \n", "Q 14.796875 18.265625 18.828125 12.890625 \n", "Q 22.859375 7.515625 30.078125 7.515625 \n", "Q 37.359375 7.515625 41.375 12.890625 \n", "Q 45.40625 18.265625 45.40625 27.984375 \n", "M 54.390625 6.78125 \n", "Q 54.390625 -7.171875 48.1875 -13.984375 \n", "Q 42 -20.796875 29.203125 -20.796875 \n", "Q 24.46875 -20.796875 20.265625 -20.09375 \n", "Q 16.0625 -19.390625 12.109375 -17.921875 \n", "L 12.109375 -9.1875 \n", "Q 16.0625 -11.328125 19.921875 -12.34375 \n", "Q 23.78125 -13.375 27.78125 -13.375 \n", "Q 36.625 -13.375 41.015625 -8.765625 \n", "Q 45.40625 -4.15625 45.40625 5.171875 \n", "L 45.40625 9.625 \n", "Q 42.625 4.78125 38.28125 2.390625 \n", "Q 33.9375 0 27.875 0 \n", "Q 17.828125 0 11.671875 7.65625 \n", "Q 5.515625 15.328125 5.515625 27.984375 \n", "Q 5.515625 40.671875 11.671875 48.328125 \n", "Q 17.828125 56 27.875 56 \n", "Q 33.9375 56 38.28125 53.609375 \n", "Q 42.625 51.21875 45.40625 46.390625 \n", "L 45.40625 54.6875 \n", "L 54.390625 54.6875 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-67\"/>\n", "     <path d=\"M 54.890625 33.015625 \n", "L 54.890625 0 \n", "L 45.90625 0 \n", "L 45.90625 32.71875 \n", "Q 45.90625 40.484375 42.875 44.328125 \n", "Q 39.84375 48.1875 33.796875 48.1875 \n", "Q 26.515625 48.1875 22.3125 43.546875 \n", "Q 18.109375 38.921875 18.109375 30.90625 \n", "L 18.109375 0 \n", "L 9.078125 0 \n", "L 9.078125 54.6875 \n", "L 18.109375 54.6875 \n", "L 18.109375 46.1875 \n", "Q 21.34375 51.125 25.703125 53.5625 \n", "Q 30.078125 56 35.796875 56 \n", "Q 45.21875 56 50.046875 50.171875 \n", "Q 54.890625 44.34375 54.890625 33.015625 \n", "\" id=\"BitstreamVeraSans-Roman-6e\"/>\n", "    </defs>\n", "    <g transform=\"translate(171.65875 16.318125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#BitstreamVeraSans-Roman-74\"/>\n", "     <use x=\"39.208984375\" xlink:href=\"#BitstreamVeraSans-Roman-72\"/>\n", "     <use x=\"80.322265625\" xlink:href=\"#BitstreamVeraSans-Roman-61\"/>\n", "     <use x=\"141.6015625\" xlink:href=\"#BitstreamVeraSans-Roman-69\"/>\n", "     <use x=\"169.384765625\" xlink:href=\"#BitstreamVeraSans-Roman-6e\"/>\n", "     <use x=\"232.763671875\" xlink:href=\"#BitstreamVeraSans-Roman-69\"/>\n", "     <use x=\"260.546875\" xlink:href=\"#BitstreamVeraSans-Roman-6e\"/>\n", "     <use x=\"323.92578125\" xlink:href=\"#BitstreamVeraSans-Roman-67\"/>\n", "     <use x=\"387.40234375\" xlink:href=\"#BitstreamVeraSans-Roman-3a\"/>\n", "     <use x=\"421.09375\" xlink:href=\"#BitstreamVeraSans-Roman-20\"/>\n", "     <use x=\"452.880859375\" xlink:href=\"#BitstreamVeraSans-Roman-74\"/>\n", "     <use x=\"492.08984375\" xlink:href=\"#BitstreamVeraSans-Roman-61\"/>\n", "     <use x=\"553.369140625\" xlink:href=\"#BitstreamVeraSans-Roman-72\"/>\n", "     <use x=\"594.466796875\" xlink:href=\"#BitstreamVeraSans-Roman-67\"/>\n", "     <use x=\"657.943359375\" xlink:href=\"#BitstreamVeraSans-Roman-65\"/>\n", "     <use x=\"719.466796875\" xlink:href=\"#BitstreamVeraSans-Roman-74\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pfe7c2c4a07\">\n", "   <rect height=\"83.7\" width=\"390.6\" x=\"21.878125\" y=\"21.318125\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<matplotlib.figure.Figure at 0x10ea00e48>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Created with matplotlib (http://matplotlib.org/) -->\n", "<svg height=\"126pt\" version=\"1.1\" viewBox=\"0 0 419 126\" width=\"419pt\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", " <defs>\n", "  <style type=\"text/css\">\n", "*{stroke-linecap:butt;stroke-linejoin:round;stroke-miterlimit:100000;}\n", "  </style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 126.89625 \n", "L 419.678125 126.89625 \n", "L 419.678125 0 \n", "L 0 0 \n", "L 0 126.89625 \n", "z\n", "\" style=\"fill:none;\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 21.**********.018125 \n", "L 412.478125 105.018125 \n", "L 412.478125 21.318125 \n", "L 21.878125 21.318125 \n", "z\n", "\" style=\"fill:#ffffff;\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p1912000a7d)\">\n", "    <image height=\"85.0\" id=\"image90767f815d\" width=\"391.0\" x=\"21.878125\" xlink:href=\"data:image/png;base64,\n", "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\" y=\"20.018125\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 21.878125 21.318125 \n", "L 412.478125 21.318125 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 21.**********.018125 \n", "L 21.878125 21.318125 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 21.**********.018125 \n", "L 412.478125 105.018125 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 412.478125 105.018125 \n", "L 412.478125 21.318125 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"text_1\">\n", "     <!-- time -->\n", "     <defs>\n", "      <path d=\"M 9.421875 54.6875 \n", "L 18.40625 54.6875 \n", "L 18.40625 0 \n", "L 9.421875 0 \n", "z\n", "M 9.421875 75.984375 \n", "L 18.40625 75.984375 \n", "L 18.40625 64.59375 \n", "L 9.421875 64.59375 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-69\"/>\n", "      <path d=\"M 56.203125 29.59375 \n", "L 56.203125 25.203125 \n", "L 14.890625 25.203125 \n", "Q 15.484375 15.921875 20.484375 11.0625 \n", "Q 25.484375 6.203125 34.421875 6.203125 \n", "Q 39.59375 6.203125 44.453125 7.46875 \n", "Q 49.3125 8.734375 54.109375 11.28125 \n", "L 54.109375 2.78125 \n", "Q 49.265625 0.734375 44.1875 -0.34375 \n", "Q 39.109375 -1.421875 33.890625 -1.421875 \n", "Q 20.796875 -1.421875 13.15625 6.1875 \n", "Q 5.515625 13.8125 5.515625 26.8125 \n", "Q 5.515625 40.234375 12.765625 48.109375 \n", "Q 20.015625 56 32.328125 56 \n", "Q 43.359375 56 49.78125 48.890625 \n", "Q 56.203125 41.796875 56.203125 29.59375 \n", "M 47.21875 32.234375 \n", "Q 47.125 39.59375 43.09375 43.984375 \n", "Q 39.0625 48.390625 32.421875 48.390625 \n", "Q 24.90625 48.390625 20.390625 44.140625 \n", "Q 15.875 39.890625 15.1875 32.171875 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-65\"/>\n", "      <path d=\"M 18.3125 70.21875 \n", "L 18.3125 54.6875 \n", "L 36.8125 54.6875 \n", "L 36.8125 47.703125 \n", "L 18.3125 47.703125 \n", "L 18.3125 18.015625 \n", "Q 18.3125 11.328125 20.140625 9.421875 \n", "Q 21.96875 7.515625 27.59375 7.515625 \n", "L 36.8125 7.515625 \n", "L 36.8125 0 \n", "L 27.59375 0 \n", "Q 17.1875 0 13.234375 3.875 \n", "Q 9.28125 7.765625 9.28125 18.015625 \n", "L 9.28125 47.703125 \n", "L 2.6875 47.703125 \n", "L 2.6875 54.6875 \n", "L 9.28125 54.6875 \n", "L 9.28125 70.21875 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-74\"/>\n", "      <path d=\"M 52 44.1875 \n", "Q 55.375 50.25 60.0625 53.125 \n", "Q 64.75 56 71.09375 56 \n", "Q 79.640625 56 84.28125 50.015625 \n", "Q 88.921875 44.046875 88.921875 33.015625 \n", "L 88.921875 0 \n", "L 79.890625 0 \n", "L 79.890625 32.71875 \n", "Q 79.890625 40.578125 77.09375 44.375 \n", "Q 74.3125 48.1875 68.609375 48.1875 \n", "Q 61.625 48.1875 57.5625 43.546875 \n", "Q 53.515625 38.921875 53.515625 30.90625 \n", "L 53.515625 0 \n", "L 44.484375 0 \n", "L 44.484375 32.71875 \n", "Q 44.484375 40.625 41.703125 44.40625 \n", "Q 38.921875 48.1875 33.109375 48.1875 \n", "Q 26.21875 48.1875 22.15625 43.53125 \n", "Q 18.109375 38.875 18.109375 30.90625 \n", "L 18.109375 0 \n", "L 9.078125 0 \n", "L 9.078125 54.6875 \n", "L 18.109375 54.6875 \n", "L 18.109375 46.1875 \n", "Q 21.1875 51.21875 25.484375 53.609375 \n", "Q 29.78125 56 35.6875 56 \n", "Q 41.65625 56 45.828125 52.96875 \n", "Q 50 49.953125 52 44.1875 \n", "\" id=\"BitstreamVeraSans-Roman-6d\"/>\n", "     </defs>\n", "     <g transform=\"translate(205.88203125 117.6165625)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#BitstreamVeraSans-Roman-74\"/>\n", "      <use x=\"39.208984375\" xlink:href=\"#BitstreamVeraSans-Roman-69\"/>\n", "      <use x=\"66.9921875\" xlink:href=\"#BitstreamVeraSans-Roman-6d\"/>\n", "      <use x=\"164.404296875\" xlink:href=\"#BitstreamVeraSans-Roman-65\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"text_2\">\n", "     <!-- freq -->\n", "     <defs>\n", "      <path d=\"M 37.109375 75.984375 \n", "L 37.109375 68.5 \n", "L 28.515625 68.5 \n", "Q 23.6875 68.5 21.796875 66.546875 \n", "Q 19.921875 64.59375 19.921875 59.515625 \n", "L 19.921875 54.6875 \n", "L 34.71875 54.6875 \n", "L 34.71875 47.703125 \n", "L 19.921875 47.703125 \n", "L 19.921875 0 \n", "L 10.890625 0 \n", "L 10.890625 47.703125 \n", "L 2.296875 47.703125 \n", "L 2.296875 54.6875 \n", "L 10.890625 54.6875 \n", "L 10.890625 58.5 \n", "Q 10.890625 67.625 15.140625 71.796875 \n", "Q 19.390625 75.984375 28.609375 75.984375 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-66\"/>\n", "      <path d=\"M 14.796875 27.296875 \n", "Q 14.796875 17.390625 18.875 11.75 \n", "Q 22.953125 6.109375 30.078125 6.109375 \n", "Q 37.203125 6.109375 41.296875 11.75 \n", "Q 45.40625 17.390625 45.40625 27.296875 \n", "Q 45.40625 37.203125 41.296875 42.84375 \n", "Q 37.203125 48.484375 30.078125 48.484375 \n", "Q 22.953125 48.484375 18.875 42.84375 \n", "Q 14.796875 37.203125 14.796875 27.296875 \n", "M 45.40625 8.203125 \n", "Q 42.578125 3.328125 38.25 0.953125 \n", "Q 33.9375 -1.421875 27.875 -1.421875 \n", "Q 17.96875 -1.421875 11.734375 6.484375 \n", "Q 5.515625 14.40625 5.515625 27.296875 \n", "Q 5.515625 40.1875 11.734375 48.09375 \n", "Q 17.96875 56 27.875 56 \n", "Q 33.9375 56 38.25 53.625 \n", "Q 42.578125 51.265625 45.40625 46.390625 \n", "L 45.40625 54.6875 \n", "L 54.390625 54.6875 \n", "L 54.390625 -20.796875 \n", "L 45.40625 -20.796875 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-71\"/>\n", "      <path d=\"M 41.109375 46.296875 \n", "Q 39.59375 47.171875 37.8125 47.578125 \n", "Q 36.03125 48 33.890625 48 \n", "Q 26.265625 48 22.1875 43.046875 \n", "Q 18.109375 38.09375 18.109375 28.8125 \n", "L 18.109375 0 \n", "L 9.078125 0 \n", "L 9.078125 54.6875 \n", "L 18.109375 54.6875 \n", "L 18.109375 46.1875 \n", "Q 20.953125 51.171875 25.484375 53.578125 \n", "Q 30.03125 56 36.53125 56 \n", "Q 37.453125 56 38.578125 55.875 \n", "Q 39.703125 55.765625 41.0625 55.515625 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-72\"/>\n", "     </defs>\n", "     <g transform=\"translate(14.7984375 73.23296875)rotate(-90.0)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#BitstreamVeraSans-Roman-66\"/>\n", "      <use x=\"35.205078125\" xlink:href=\"#BitstreamVeraSans-Roman-72\"/>\n", "      <use x=\"76.287109375\" xlink:href=\"#BitstreamVeraSans-Roman-65\"/>\n", "      <use x=\"137.810546875\" xlink:href=\"#BitstreamVeraSans-Roman-71\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"text_3\">\n", "    <!-- training: model -->\n", "    <defs>\n", "     <path d=\"M 30.609375 48.390625 \n", "Q 23.390625 48.390625 19.1875 42.75 \n", "Q 14.984375 37.109375 14.984375 27.296875 \n", "Q 14.984375 17.484375 19.15625 11.84375 \n", "Q 23.34375 6.203125 30.609375 6.203125 \n", "Q 37.796875 6.203125 41.984375 11.859375 \n", "Q 46.1875 17.53125 46.1875 27.296875 \n", "Q 46.1875 37.015625 41.984375 42.703125 \n", "Q 37.796875 48.390625 30.609375 48.390625 \n", "M 30.609375 56 \n", "Q 42.328125 56 49.015625 48.375 \n", "Q 55.71875 40.765625 55.71875 27.296875 \n", "Q 55.71875 13.875 49.015625 6.21875 \n", "Q 42.328125 -1.421875 30.609375 -1.421875 \n", "Q 18.84375 -1.421875 12.171875 6.21875 \n", "Q 5.515625 13.875 5.515625 27.296875 \n", "Q 5.515625 40.765625 12.171875 48.375 \n", "Q 18.84375 56 30.609375 56 \n", "\" id=\"BitstreamVeraSans-Roman-6f\"/>\n", "     <path d=\"M 9.421875 75.984375 \n", "L 18.40625 75.984375 \n", "L 18.40625 0 \n", "L 9.421875 0 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-6c\"/>\n", "     <path id=\"BitstreamVeraSans-Roman-20\"/>\n", "     <path d=\"M 11.71875 12.40625 \n", "L 22.015625 12.40625 \n", "L 22.015625 0 \n", "L 11.71875 0 \n", "z\n", "M 11.71875 51.703125 \n", "L 22.015625 51.703125 \n", "L 22.015625 39.3125 \n", "L 11.71875 39.3125 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-3a\"/>\n", "     <path d=\"M 34.28125 27.484375 \n", "Q 23.390625 27.484375 19.1875 25 \n", "Q 14.984375 22.515625 14.984375 16.5 \n", "Q 14.984375 11.71875 18.140625 8.90625 \n", "Q 21.296875 6.109375 26.703125 6.109375 \n", "Q 34.1875 6.109375 38.703125 11.40625 \n", "Q 43.21875 16.703125 43.21875 25.484375 \n", "L 43.21875 27.484375 \n", "z\n", "M 52.203125 31.203125 \n", "L 52.203125 0 \n", "L 43.21875 0 \n", "L 43.21875 8.296875 \n", "Q 40.140625 3.328125 35.546875 0.953125 \n", "Q 30.953125 -1.421875 24.3125 -1.421875 \n", "Q 15.921875 -1.421875 10.953125 3.296875 \n", "Q 6 8.015625 6 15.921875 \n", "Q 6 25.140625 12.171875 29.828125 \n", "Q 18.359375 34.515625 30.609375 34.515625 \n", "L 43.21875 34.515625 \n", "L 43.21875 35.40625 \n", "Q 43.21875 41.609375 39.140625 45 \n", "Q 35.0625 48.390625 27.6875 48.390625 \n", "Q 23 48.390625 18.546875 47.265625 \n", "Q 14.109375 46.140625 10.015625 43.890625 \n", "L 10.015625 52.203125 \n", "Q 14.9375 54.109375 19.578125 55.046875 \n", "Q 24.21875 56 28.609375 56 \n", "Q 40.484375 56 46.34375 49.84375 \n", "Q 52.203125 43.703125 52.203125 31.203125 \n", "\" id=\"BitstreamVeraSans-Roman-61\"/>\n", "     <path d=\"M 45.40625 46.390625 \n", "L 45.40625 75.984375 \n", "L 54.390625 75.984375 \n", "L 54.390625 0 \n", "L 45.40625 0 \n", "L 45.40625 8.203125 \n", "Q 42.578125 3.328125 38.25 0.953125 \n", "Q 33.9375 -1.421875 27.875 -1.421875 \n", "Q 17.96875 -1.421875 11.734375 6.484375 \n", "Q 5.515625 14.40625 5.515625 27.296875 \n", "Q 5.515625 40.1875 11.734375 48.09375 \n", "Q 17.96875 56 27.875 56 \n", "Q 33.9375 56 38.25 53.625 \n", "Q 42.578125 51.265625 45.40625 46.390625 \n", "M 14.796875 27.296875 \n", "Q 14.796875 17.390625 18.875 11.75 \n", "Q 22.953125 6.109375 30.078125 6.109375 \n", "Q 37.203125 6.109375 41.296875 11.75 \n", "Q 45.40625 17.390625 45.40625 27.296875 \n", "Q 45.40625 37.203125 41.296875 42.84375 \n", "Q 37.203125 48.484375 30.078125 48.484375 \n", "Q 22.953125 48.484375 18.875 42.84375 \n", "Q 14.796875 37.203125 14.796875 27.296875 \n", "\" id=\"BitstreamVeraSans-Roman-64\"/>\n", "     <path d=\"M 45.40625 27.984375 \n", "Q 45.40625 37.75 41.375 43.109375 \n", "Q 37.359375 48.484375 30.078125 48.484375 \n", "Q 22.859375 48.484375 18.828125 43.109375 \n", "Q 14.796875 37.75 14.796875 27.984375 \n", "Q 14.796875 18.265625 18.828125 12.890625 \n", "Q 22.859375 7.515625 30.078125 7.515625 \n", "Q 37.359375 7.515625 41.375 12.890625 \n", "Q 45.40625 18.265625 45.40625 27.984375 \n", "M 54.390625 6.78125 \n", "Q 54.390625 -7.171875 48.1875 -13.984375 \n", "Q 42 -20.796875 29.203125 -20.796875 \n", "Q 24.46875 -20.796875 20.265625 -20.09375 \n", "Q 16.0625 -19.390625 12.109375 -17.921875 \n", "L 12.109375 -9.1875 \n", "Q 16.0625 -11.328125 19.921875 -12.34375 \n", "Q 23.78125 -13.375 27.78125 -13.375 \n", "Q 36.625 -13.375 41.015625 -8.765625 \n", "Q 45.40625 -4.15625 45.40625 5.171875 \n", "L 45.40625 9.625 \n", "Q 42.625 4.78125 38.28125 2.390625 \n", "Q 33.9375 0 27.875 0 \n", "Q 17.828125 0 11.671875 7.65625 \n", "Q 5.515625 15.328125 5.515625 27.984375 \n", "Q 5.515625 40.671875 11.671875 48.328125 \n", "Q 17.828125 56 27.875 56 \n", "Q 33.9375 56 38.28125 53.609375 \n", "Q 42.625 51.21875 45.40625 46.390625 \n", "L 45.40625 54.6875 \n", "L 54.390625 54.6875 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-67\"/>\n", "     <path d=\"M 54.890625 33.015625 \n", "L 54.890625 0 \n", "L 45.90625 0 \n", "L 45.90625 32.71875 \n", "Q 45.90625 40.484375 42.875 44.328125 \n", "Q 39.84375 48.1875 33.796875 48.1875 \n", "Q 26.515625 48.1875 22.3125 43.546875 \n", "Q 18.109375 38.921875 18.109375 30.90625 \n", "L 18.109375 0 \n", "L 9.078125 0 \n", "L 9.078125 54.6875 \n", "L 18.109375 54.6875 \n", "L 18.109375 46.1875 \n", "Q 21.34375 51.125 25.703125 53.5625 \n", "Q 30.078125 56 35.796875 56 \n", "Q 45.21875 56 50.046875 50.171875 \n", "Q 54.890625 44.34375 54.890625 33.015625 \n", "\" id=\"BitstreamVeraSans-Roman-6e\"/>\n", "    </defs>\n", "    <g transform=\"translate(171.323125 16.318125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#BitstreamVeraSans-Roman-74\"/>\n", "     <use x=\"39.208984375\" xlink:href=\"#BitstreamVeraSans-Roman-72\"/>\n", "     <use x=\"80.322265625\" xlink:href=\"#BitstreamVeraSans-Roman-61\"/>\n", "     <use x=\"141.6015625\" xlink:href=\"#BitstreamVeraSans-Roman-69\"/>\n", "     <use x=\"169.384765625\" xlink:href=\"#BitstreamVeraSans-Roman-6e\"/>\n", "     <use x=\"232.763671875\" xlink:href=\"#BitstreamVeraSans-Roman-69\"/>\n", "     <use x=\"260.546875\" xlink:href=\"#BitstreamVeraSans-Roman-6e\"/>\n", "     <use x=\"323.92578125\" xlink:href=\"#BitstreamVeraSans-Roman-67\"/>\n", "     <use x=\"387.40234375\" xlink:href=\"#BitstreamVeraSans-Roman-3a\"/>\n", "     <use x=\"421.09375\" xlink:href=\"#BitstreamVeraSans-Roman-20\"/>\n", "     <use x=\"452.880859375\" xlink:href=\"#BitstreamVeraSans-Roman-6d\"/>\n", "     <use x=\"550.29296875\" xlink:href=\"#BitstreamVeraSans-Roman-6f\"/>\n", "     <use x=\"611.474609375\" xlink:href=\"#BitstreamVeraSans-Roman-64\"/>\n", "     <use x=\"674.951171875\" xlink:href=\"#BitstreamVeraSans-Roman-65\"/>\n", "     <use x=\"736.474609375\" xlink:href=\"#BitstreamVeraSans-Roman-6c\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p1912000a7d\">\n", "   <rect height=\"83.7\" width=\"390.6\" x=\"21.878125\" y=\"21.318125\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<matplotlib.figure.Figure at 0x1119894e0>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Created with matplotlib (http://matplotlib.org/) -->\n", "<svg height=\"126pt\" version=\"1.1\" viewBox=\"0 0 196 126\" width=\"196pt\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", " <defs>\n", "  <style type=\"text/css\">\n", "*{stroke-linecap:butt;stroke-linejoin:round;stroke-miterlimit:100000;}\n", "  </style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 126.89625 \n", "L 196.478125 126.89625 \n", "L 196.478125 0 \n", "L 0 0 \n", "L 0 126.89625 \n", "z\n", "\" style=\"fill:none;\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 21.**********.018125 \n", "L 189.**********.018125 \n", "L 189.278125 21.318125 \n", "L 21.878125 21.318125 \n", "z\n", "\" style=\"fill:#ffffff;\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p8ef1f5dfd3)\">\n", "    <image height=\"85.0\" id=\"image98dfd16e12\" width=\"168.0\" x=\"21.878125\" xlink:href=\"data:image/png;base64,\n", "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\" y=\"20.018125\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 21.878125 21.318125 \n", "L 189.278125 21.318125 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 21.**********.018125 \n", "L 21.878125 21.318125 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 21.**********.018125 \n", "L 189.**********.018125 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 189.**********.018125 \n", "L 189.278125 21.318125 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"text_1\">\n", "     <!-- time -->\n", "     <defs>\n", "      <path d=\"M 9.421875 54.6875 \n", "L 18.40625 54.6875 \n", "L 18.40625 0 \n", "L 9.421875 0 \n", "z\n", "M 9.421875 75.984375 \n", "L 18.40625 75.984375 \n", "L 18.40625 64.59375 \n", "L 9.421875 64.59375 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-69\"/>\n", "      <path d=\"M 56.203125 29.59375 \n", "L 56.203125 25.203125 \n", "L 14.890625 25.203125 \n", "Q 15.484375 15.921875 20.484375 11.0625 \n", "Q 25.484375 6.203125 34.421875 6.203125 \n", "Q 39.59375 6.203125 44.453125 7.46875 \n", "Q 49.3125 8.734375 54.109375 11.28125 \n", "L 54.109375 2.78125 \n", "Q 49.265625 0.734375 44.1875 -0.34375 \n", "Q 39.109375 -1.421875 33.890625 -1.421875 \n", "Q 20.796875 -1.421875 13.15625 6.1875 \n", "Q 5.515625 13.8125 5.515625 26.8125 \n", "Q 5.515625 40.234375 12.765625 48.109375 \n", "Q 20.015625 56 32.328125 56 \n", "Q 43.359375 56 49.78125 48.890625 \n", "Q 56.203125 41.796875 56.203125 29.59375 \n", "M 47.21875 32.234375 \n", "Q 47.125 39.59375 43.09375 43.984375 \n", "Q 39.0625 48.390625 32.421875 48.390625 \n", "Q 24.90625 48.390625 20.390625 44.140625 \n", "Q 15.875 39.890625 15.1875 32.171875 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-65\"/>\n", "      <path d=\"M 18.3125 70.21875 \n", "L 18.3125 54.6875 \n", "L 36.8125 54.6875 \n", "L 36.8125 47.703125 \n", "L 18.3125 47.703125 \n", "L 18.3125 18.015625 \n", "Q 18.3125 11.328125 20.140625 9.421875 \n", "Q 21.96875 7.515625 27.59375 7.515625 \n", "L 36.8125 7.515625 \n", "L 36.8125 0 \n", "L 27.59375 0 \n", "Q 17.1875 0 13.234375 3.875 \n", "Q 9.28125 7.765625 9.28125 18.015625 \n", "L 9.28125 47.703125 \n", "L 2.6875 47.703125 \n", "L 2.6875 54.6875 \n", "L 9.28125 54.6875 \n", "L 9.28125 70.21875 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-74\"/>\n", "      <path d=\"M 52 44.1875 \n", "Q 55.375 50.25 60.0625 53.125 \n", "Q 64.75 56 71.09375 56 \n", "Q 79.640625 56 84.28125 50.015625 \n", "Q 88.921875 44.046875 88.921875 33.015625 \n", "L 88.921875 0 \n", "L 79.890625 0 \n", "L 79.890625 32.71875 \n", "Q 79.890625 40.578125 77.09375 44.375 \n", "Q 74.3125 48.1875 68.609375 48.1875 \n", "Q 61.625 48.1875 57.5625 43.546875 \n", "Q 53.515625 38.921875 53.515625 30.90625 \n", "L 53.515625 0 \n", "L 44.484375 0 \n", "L 44.484375 32.71875 \n", "Q 44.484375 40.625 41.703125 44.40625 \n", "Q 38.921875 48.1875 33.109375 48.1875 \n", "Q 26.21875 48.1875 22.15625 43.53125 \n", "Q 18.109375 38.875 18.109375 30.90625 \n", "L 18.109375 0 \n", "L 9.078125 0 \n", "L 9.078125 54.6875 \n", "L 18.109375 54.6875 \n", "L 18.109375 46.1875 \n", "Q 21.1875 51.21875 25.484375 53.609375 \n", "Q 29.78125 56 35.6875 56 \n", "Q 41.65625 56 45.828125 52.96875 \n", "Q 50 49.953125 52 44.1875 \n", "\" id=\"BitstreamVeraSans-Roman-6d\"/>\n", "     </defs>\n", "     <g transform=\"translate(94.28203125 117.6165625)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#BitstreamVeraSans-Roman-74\"/>\n", "      <use x=\"39.208984375\" xlink:href=\"#BitstreamVeraSans-Roman-69\"/>\n", "      <use x=\"66.9921875\" xlink:href=\"#BitstreamVeraSans-Roman-6d\"/>\n", "      <use x=\"164.404296875\" xlink:href=\"#BitstreamVeraSans-Roman-65\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"text_2\">\n", "     <!-- freq -->\n", "     <defs>\n", "      <path d=\"M 37.109375 75.984375 \n", "L 37.109375 68.5 \n", "L 28.515625 68.5 \n", "Q 23.6875 68.5 21.796875 66.546875 \n", "Q 19.921875 64.59375 19.921875 59.515625 \n", "L 19.921875 54.6875 \n", "L 34.71875 54.6875 \n", "L 34.71875 47.703125 \n", "L 19.921875 47.703125 \n", "L 19.921875 0 \n", "L 10.890625 0 \n", "L 10.890625 47.703125 \n", "L 2.296875 47.703125 \n", "L 2.296875 54.6875 \n", "L 10.890625 54.6875 \n", "L 10.890625 58.5 \n", "Q 10.890625 67.625 15.140625 71.796875 \n", "Q 19.390625 75.984375 28.609375 75.984375 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-66\"/>\n", "      <path d=\"M 14.796875 27.296875 \n", "Q 14.796875 17.390625 18.875 11.75 \n", "Q 22.953125 6.109375 30.078125 6.109375 \n", "Q 37.203125 6.109375 41.296875 11.75 \n", "Q 45.40625 17.390625 45.40625 27.296875 \n", "Q 45.40625 37.203125 41.296875 42.84375 \n", "Q 37.203125 48.484375 30.078125 48.484375 \n", "Q 22.953125 48.484375 18.875 42.84375 \n", "Q 14.796875 37.203125 14.796875 27.296875 \n", "M 45.40625 8.203125 \n", "Q 42.578125 3.328125 38.25 0.953125 \n", "Q 33.9375 -1.421875 27.875 -1.421875 \n", "Q 17.96875 -1.421875 11.734375 6.484375 \n", "Q 5.515625 14.40625 5.515625 27.296875 \n", "Q 5.515625 40.1875 11.734375 48.09375 \n", "Q 17.96875 56 27.875 56 \n", "Q 33.9375 56 38.25 53.625 \n", "Q 42.578125 51.265625 45.40625 46.390625 \n", "L 45.40625 54.6875 \n", "L 54.390625 54.6875 \n", "L 54.390625 -20.796875 \n", "L 45.40625 -20.796875 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-71\"/>\n", "      <path d=\"M 41.109375 46.296875 \n", "Q 39.59375 47.171875 37.8125 47.578125 \n", "Q 36.03125 48 33.890625 48 \n", "Q 26.265625 48 22.1875 43.046875 \n", "Q 18.109375 38.09375 18.109375 28.8125 \n", "L 18.109375 0 \n", "L 9.078125 0 \n", "L 9.078125 54.6875 \n", "L 18.109375 54.6875 \n", "L 18.109375 46.1875 \n", "Q 20.953125 51.171875 25.484375 53.578125 \n", "Q 30.03125 56 36.53125 56 \n", "Q 37.453125 56 38.578125 55.875 \n", "Q 39.703125 55.765625 41.0625 55.515625 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-72\"/>\n", "     </defs>\n", "     <g transform=\"translate(14.7984375 73.23296875)rotate(-90.0)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#BitstreamVeraSans-Roman-66\"/>\n", "      <use x=\"35.205078125\" xlink:href=\"#BitstreamVeraSans-Roman-72\"/>\n", "      <use x=\"76.287109375\" xlink:href=\"#BitstreamVeraSans-Roman-65\"/>\n", "      <use x=\"137.810546875\" xlink:href=\"#BitstreamVeraSans-Roman-71\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"text_3\">\n", "    <!-- test: target -->\n", "    <defs>\n", "     <path d=\"M 11.71875 12.40625 \n", "L 22.015625 12.40625 \n", "L 22.015625 0 \n", "L 11.71875 0 \n", "z\n", "M 11.71875 51.703125 \n", "L 22.015625 51.703125 \n", "L 22.015625 39.3125 \n", "L 11.71875 39.3125 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-3a\"/>\n", "     <path d=\"M 34.28125 27.484375 \n", "Q 23.390625 27.484375 19.1875 25 \n", "Q 14.984375 22.515625 14.984375 16.5 \n", "Q 14.984375 11.71875 18.140625 8.90625 \n", "Q 21.296875 6.109375 26.703125 6.109375 \n", "Q 34.1875 6.109375 38.703125 11.40625 \n", "Q 43.21875 16.703125 43.21875 25.484375 \n", "L 43.21875 27.484375 \n", "z\n", "M 52.203125 31.203125 \n", "L 52.203125 0 \n", "L 43.21875 0 \n", "L 43.21875 8.296875 \n", "Q 40.140625 3.328125 35.546875 0.953125 \n", "Q 30.953125 -1.421875 24.3125 -1.421875 \n", "Q 15.921875 -1.421875 10.953125 3.296875 \n", "Q 6 8.015625 6 15.921875 \n", "Q 6 25.140625 12.171875 29.828125 \n", "Q 18.359375 34.515625 30.609375 34.515625 \n", "L 43.21875 34.515625 \n", "L 43.21875 35.40625 \n", "Q 43.21875 41.609375 39.140625 45 \n", "Q 35.0625 48.390625 27.6875 48.390625 \n", "Q 23 48.390625 18.546875 47.265625 \n", "Q 14.109375 46.140625 10.015625 43.890625 \n", "L 10.015625 52.203125 \n", "Q 14.9375 54.109375 19.578125 55.046875 \n", "Q 24.21875 56 28.609375 56 \n", "Q 40.484375 56 46.34375 49.84375 \n", "Q 52.203125 43.703125 52.203125 31.203125 \n", "\" id=\"BitstreamVeraSans-Roman-61\"/>\n", "     <path d=\"M 45.40625 27.984375 \n", "Q 45.40625 37.75 41.375 43.109375 \n", "Q 37.359375 48.484375 30.078125 48.484375 \n", "Q 22.859375 48.484375 18.828125 43.109375 \n", "Q 14.796875 37.75 14.796875 27.984375 \n", "Q 14.796875 18.265625 18.828125 12.890625 \n", "Q 22.859375 7.515625 30.078125 7.515625 \n", "Q 37.359375 7.515625 41.375 12.890625 \n", "Q 45.40625 18.265625 45.40625 27.984375 \n", "M 54.390625 6.78125 \n", "Q 54.390625 -7.171875 48.1875 -13.984375 \n", "Q 42 -20.796875 29.203125 -20.796875 \n", "Q 24.46875 -20.796875 20.265625 -20.09375 \n", "Q 16.0625 -19.390625 12.109375 -17.921875 \n", "L 12.109375 -9.1875 \n", "Q 16.0625 -11.328125 19.921875 -12.34375 \n", "Q 23.78125 -13.375 27.78125 -13.375 \n", "Q 36.625 -13.375 41.015625 -8.765625 \n", "Q 45.40625 -4.15625 45.40625 5.171875 \n", "L 45.40625 9.625 \n", "Q 42.625 4.78125 38.28125 2.390625 \n", "Q 33.9375 0 27.875 0 \n", "Q 17.828125 0 11.671875 7.65625 \n", "Q 5.515625 15.328125 5.515625 27.984375 \n", "Q 5.515625 40.671875 11.671875 48.328125 \n", "Q 17.828125 56 27.875 56 \n", "Q 33.9375 56 38.28125 53.609375 \n", "Q 42.625 51.21875 45.40625 46.390625 \n", "L 45.40625 54.6875 \n", "L 54.390625 54.6875 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-67\"/>\n", "     <path id=\"BitstreamVeraSans-Roman-20\"/>\n", "     <path d=\"M 44.28125 53.078125 \n", "L 44.28125 44.578125 \n", "Q 40.484375 46.53125 36.375 47.5 \n", "Q 32.28125 48.484375 27.875 48.484375 \n", "Q 21.1875 48.484375 17.84375 46.4375 \n", "Q 14.5 44.390625 14.5 40.28125 \n", "Q 14.5 37.15625 16.890625 35.375 \n", "Q 19.28125 33.59375 26.515625 31.984375 \n", "L 29.59375 31.296875 \n", "Q 39.15625 29.25 43.1875 25.515625 \n", "Q 47.21875 21.78125 47.21875 15.09375 \n", "Q 47.21875 7.46875 41.1875 3.015625 \n", "Q 35.15625 -1.421875 24.609375 -1.421875 \n", "Q 20.21875 -1.421875 15.453125 -0.5625 \n", "Q 10.6875 0.296875 5.421875 2 \n", "L 5.421875 11.28125 \n", "Q 10.40625 8.6875 15.234375 7.390625 \n", "Q 20.0625 6.109375 24.8125 6.109375 \n", "Q 31.15625 6.109375 34.5625 8.28125 \n", "Q 37.984375 10.453125 37.984375 14.40625 \n", "Q 37.984375 18.0625 35.515625 20.015625 \n", "Q 33.0625 21.96875 24.703125 23.78125 \n", "L 21.578125 24.515625 \n", "Q 13.234375 26.265625 9.515625 29.90625 \n", "Q 5.8125 33.546875 5.8125 39.890625 \n", "Q 5.8125 47.609375 11.28125 51.796875 \n", "Q 16.75 56 26.8125 56 \n", "Q 31.78125 56 36.171875 55.265625 \n", "Q 40.578125 54.546875 44.28125 53.078125 \n", "\" id=\"BitstreamVeraSans-Roman-73\"/>\n", "    </defs>\n", "    <g transform=\"translate(71.7803125 16.318125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#BitstreamVeraSans-Roman-74\"/>\n", "     <use x=\"39.208984375\" xlink:href=\"#BitstreamVeraSans-Roman-65\"/>\n", "     <use x=\"100.732421875\" xlink:href=\"#BitstreamVeraSans-Roman-73\"/>\n", "     <use x=\"152.83203125\" xlink:href=\"#BitstreamVeraSans-Roman-74\"/>\n", "     <use x=\"192.041015625\" xlink:href=\"#BitstreamVeraSans-Roman-3a\"/>\n", "     <use x=\"225.732421875\" xlink:href=\"#BitstreamVeraSans-Roman-20\"/>\n", "     <use x=\"257.51953125\" xlink:href=\"#BitstreamVeraSans-Roman-74\"/>\n", "     <use x=\"296.728515625\" xlink:href=\"#BitstreamVeraSans-Roman-61\"/>\n", "     <use x=\"358.0078125\" xlink:href=\"#BitstreamVeraSans-Roman-72\"/>\n", "     <use x=\"399.10546875\" xlink:href=\"#BitstreamVeraSans-Roman-67\"/>\n", "     <use x=\"462.58203125\" xlink:href=\"#BitstreamVeraSans-Roman-65\"/>\n", "     <use x=\"524.10546875\" xlink:href=\"#BitstreamVeraSans-Roman-74\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p8ef1f5dfd3\">\n", "   <rect height=\"83.7\" width=\"167.4\" x=\"21.878125\" y=\"21.318125\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<matplotlib.figure.Figure at 0x111a42470>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Created with matplotlib (http://matplotlib.org/) -->\n", "<svg height=\"126pt\" version=\"1.1\" viewBox=\"0 0 196 126\" width=\"196pt\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", " <defs>\n", "  <style type=\"text/css\">\n", "*{stroke-linecap:butt;stroke-linejoin:round;stroke-miterlimit:100000;}\n", "  </style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 126.89625 \n", "L 196.478125 126.89625 \n", "L 196.478125 0 \n", "L 0 0 \n", "L 0 126.89625 \n", "z\n", "\" style=\"fill:none;\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 21.**********.018125 \n", "L 189.**********.018125 \n", "L 189.278125 21.318125 \n", "L 21.878125 21.318125 \n", "z\n", "\" style=\"fill:#ffffff;\"/>\n", "   </g>\n", "   <g clip-path=\"url(#p52cfe5a760)\">\n", "    <image height=\"85.0\" id=\"imaged9dbc88baf\" width=\"168.0\" x=\"21.878125\" xlink:href=\"data:image/png;base64,\n", "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\" y=\"20.018125\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 21.878125 21.318125 \n", "L 189.278125 21.318125 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 21.**********.018125 \n", "L 21.878125 21.318125 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 21.**********.018125 \n", "L 189.**********.018125 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 189.**********.018125 \n", "L 189.278125 21.318125 \n", "\" style=\"fill:none;stroke:#000000;stroke-linecap:square;stroke-linejoin:miter;\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"text_1\">\n", "     <!-- time -->\n", "     <defs>\n", "      <path d=\"M 9.421875 54.6875 \n", "L 18.40625 54.6875 \n", "L 18.40625 0 \n", "L 9.421875 0 \n", "z\n", "M 9.421875 75.984375 \n", "L 18.40625 75.984375 \n", "L 18.40625 64.59375 \n", "L 9.421875 64.59375 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-69\"/>\n", "      <path d=\"M 56.203125 29.59375 \n", "L 56.203125 25.203125 \n", "L 14.890625 25.203125 \n", "Q 15.484375 15.921875 20.484375 11.0625 \n", "Q 25.484375 6.203125 34.421875 6.203125 \n", "Q 39.59375 6.203125 44.453125 7.46875 \n", "Q 49.3125 8.734375 54.109375 11.28125 \n", "L 54.109375 2.78125 \n", "Q 49.265625 0.734375 44.1875 -0.34375 \n", "Q 39.109375 -1.421875 33.890625 -1.421875 \n", "Q 20.796875 -1.421875 13.15625 6.1875 \n", "Q 5.515625 13.8125 5.515625 26.8125 \n", "Q 5.515625 40.234375 12.765625 48.109375 \n", "Q 20.015625 56 32.328125 56 \n", "Q 43.359375 56 49.78125 48.890625 \n", "Q 56.203125 41.796875 56.203125 29.59375 \n", "M 47.21875 32.234375 \n", "Q 47.125 39.59375 43.09375 43.984375 \n", "Q 39.0625 48.390625 32.421875 48.390625 \n", "Q 24.90625 48.390625 20.390625 44.140625 \n", "Q 15.875 39.890625 15.1875 32.171875 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-65\"/>\n", "      <path d=\"M 18.3125 70.21875 \n", "L 18.3125 54.6875 \n", "L 36.8125 54.6875 \n", "L 36.8125 47.703125 \n", "L 18.3125 47.703125 \n", "L 18.3125 18.015625 \n", "Q 18.3125 11.328125 20.140625 9.421875 \n", "Q 21.96875 7.515625 27.59375 7.515625 \n", "L 36.8125 7.515625 \n", "L 36.8125 0 \n", "L 27.59375 0 \n", "Q 17.1875 0 13.234375 3.875 \n", "Q 9.28125 7.765625 9.28125 18.015625 \n", "L 9.28125 47.703125 \n", "L 2.6875 47.703125 \n", "L 2.6875 54.6875 \n", "L 9.28125 54.6875 \n", "L 9.28125 70.21875 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-74\"/>\n", "      <path d=\"M 52 44.1875 \n", "Q 55.375 50.25 60.0625 53.125 \n", "Q 64.75 56 71.09375 56 \n", "Q 79.640625 56 84.28125 50.015625 \n", "Q 88.921875 44.046875 88.921875 33.015625 \n", "L 88.921875 0 \n", "L 79.890625 0 \n", "L 79.890625 32.71875 \n", "Q 79.890625 40.578125 77.09375 44.375 \n", "Q 74.3125 48.1875 68.609375 48.1875 \n", "Q 61.625 48.1875 57.5625 43.546875 \n", "Q 53.515625 38.921875 53.515625 30.90625 \n", "L 53.515625 0 \n", "L 44.484375 0 \n", "L 44.484375 32.71875 \n", "Q 44.484375 40.625 41.703125 44.40625 \n", "Q 38.921875 48.1875 33.109375 48.1875 \n", "Q 26.21875 48.1875 22.15625 43.53125 \n", "Q 18.109375 38.875 18.109375 30.90625 \n", "L 18.109375 0 \n", "L 9.078125 0 \n", "L 9.078125 54.6875 \n", "L 18.109375 54.6875 \n", "L 18.109375 46.1875 \n", "Q 21.1875 51.21875 25.484375 53.609375 \n", "Q 29.78125 56 35.6875 56 \n", "Q 41.65625 56 45.828125 52.96875 \n", "Q 50 49.953125 52 44.1875 \n", "\" id=\"BitstreamVeraSans-Roman-6d\"/>\n", "     </defs>\n", "     <g transform=\"translate(94.28203125 117.6165625)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#BitstreamVeraSans-Roman-74\"/>\n", "      <use x=\"39.208984375\" xlink:href=\"#BitstreamVeraSans-Roman-69\"/>\n", "      <use x=\"66.9921875\" xlink:href=\"#BitstreamVeraSans-Roman-6d\"/>\n", "      <use x=\"164.404296875\" xlink:href=\"#BitstreamVeraSans-Roman-65\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"text_2\">\n", "     <!-- freq -->\n", "     <defs>\n", "      <path d=\"M 37.109375 75.984375 \n", "L 37.109375 68.5 \n", "L 28.515625 68.5 \n", "Q 23.6875 68.5 21.796875 66.546875 \n", "Q 19.921875 64.59375 19.921875 59.515625 \n", "L 19.921875 54.6875 \n", "L 34.71875 54.6875 \n", "L 34.71875 47.703125 \n", "L 19.921875 47.703125 \n", "L 19.921875 0 \n", "L 10.890625 0 \n", "L 10.890625 47.703125 \n", "L 2.296875 47.703125 \n", "L 2.296875 54.6875 \n", "L 10.890625 54.6875 \n", "L 10.890625 58.5 \n", "Q 10.890625 67.625 15.140625 71.796875 \n", "Q 19.390625 75.984375 28.609375 75.984375 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-66\"/>\n", "      <path d=\"M 14.796875 27.296875 \n", "Q 14.796875 17.390625 18.875 11.75 \n", "Q 22.953125 6.109375 30.078125 6.109375 \n", "Q 37.203125 6.109375 41.296875 11.75 \n", "Q 45.40625 17.390625 45.40625 27.296875 \n", "Q 45.40625 37.203125 41.296875 42.84375 \n", "Q 37.203125 48.484375 30.078125 48.484375 \n", "Q 22.953125 48.484375 18.875 42.84375 \n", "Q 14.796875 37.203125 14.796875 27.296875 \n", "M 45.40625 8.203125 \n", "Q 42.578125 3.328125 38.25 0.953125 \n", "Q 33.9375 -1.421875 27.875 -1.421875 \n", "Q 17.96875 -1.421875 11.734375 6.484375 \n", "Q 5.515625 14.40625 5.515625 27.296875 \n", "Q 5.515625 40.1875 11.734375 48.09375 \n", "Q 17.96875 56 27.875 56 \n", "Q 33.9375 56 38.25 53.625 \n", "Q 42.578125 51.265625 45.40625 46.390625 \n", "L 45.40625 54.6875 \n", "L 54.390625 54.6875 \n", "L 54.390625 -20.796875 \n", "L 45.40625 -20.796875 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-71\"/>\n", "      <path d=\"M 41.109375 46.296875 \n", "Q 39.59375 47.171875 37.8125 47.578125 \n", "Q 36.03125 48 33.890625 48 \n", "Q 26.265625 48 22.1875 43.046875 \n", "Q 18.109375 38.09375 18.109375 28.8125 \n", "L 18.109375 0 \n", "L 9.078125 0 \n", "L 9.078125 54.6875 \n", "L 18.109375 54.6875 \n", "L 18.109375 46.1875 \n", "Q 20.953125 51.171875 25.484375 53.578125 \n", "Q 30.03125 56 36.53125 56 \n", "Q 37.453125 56 38.578125 55.875 \n", "Q 39.703125 55.765625 41.0625 55.515625 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-72\"/>\n", "     </defs>\n", "     <g transform=\"translate(14.7984375 73.23296875)rotate(-90.0)scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#BitstreamVeraSans-Roman-66\"/>\n", "      <use x=\"35.205078125\" xlink:href=\"#BitstreamVeraSans-Roman-72\"/>\n", "      <use x=\"76.287109375\" xlink:href=\"#BitstreamVeraSans-Roman-65\"/>\n", "      <use x=\"137.810546875\" xlink:href=\"#BitstreamVeraSans-Roman-71\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"text_3\">\n", "    <!-- test: model -->\n", "    <defs>\n", "     <path d=\"M 30.609375 48.390625 \n", "Q 23.390625 48.390625 19.1875 42.75 \n", "Q 14.984375 37.109375 14.984375 27.296875 \n", "Q 14.984375 17.484375 19.15625 11.84375 \n", "Q 23.34375 6.203125 30.609375 6.203125 \n", "Q 37.796875 6.203125 41.984375 11.859375 \n", "Q 46.1875 17.53125 46.1875 27.296875 \n", "Q 46.1875 37.015625 41.984375 42.703125 \n", "Q 37.796875 48.390625 30.609375 48.390625 \n", "M 30.609375 56 \n", "Q 42.328125 56 49.015625 48.375 \n", "Q 55.71875 40.765625 55.71875 27.296875 \n", "Q 55.71875 13.875 49.015625 6.21875 \n", "Q 42.328125 -1.421875 30.609375 -1.421875 \n", "Q 18.84375 -1.421875 12.171875 6.21875 \n", "Q 5.515625 13.875 5.515625 27.296875 \n", "Q 5.515625 40.765625 12.171875 48.375 \n", "Q 18.84375 56 30.609375 56 \n", "\" id=\"BitstreamVeraSans-Roman-6f\"/>\n", "     <path d=\"M 9.421875 75.984375 \n", "L 18.40625 75.984375 \n", "L 18.40625 0 \n", "L 9.421875 0 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-6c\"/>\n", "     <path id=\"BitstreamVeraSans-Roman-20\"/>\n", "     <path d=\"M 11.71875 12.40625 \n", "L 22.015625 12.40625 \n", "L 22.015625 0 \n", "L 11.71875 0 \n", "z\n", "M 11.71875 51.703125 \n", "L 22.015625 51.703125 \n", "L 22.015625 39.3125 \n", "L 11.71875 39.3125 \n", "z\n", "\" id=\"BitstreamVeraSans-Roman-3a\"/>\n", "     <path d=\"M 45.40625 46.390625 \n", "L 45.40625 75.984375 \n", "L 54.390625 75.984375 \n", "L 54.390625 0 \n", "L 45.40625 0 \n", "L 45.40625 8.203125 \n", "Q 42.578125 3.328125 38.25 0.953125 \n", "Q 33.9375 -1.421875 27.875 -1.421875 \n", "Q 17.96875 -1.421875 11.734375 6.484375 \n", "Q 5.515625 14.40625 5.515625 27.296875 \n", "Q 5.515625 40.1875 11.734375 48.09375 \n", "Q 17.96875 56 27.875 56 \n", "Q 33.9375 56 38.25 53.625 \n", "Q 42.578125 51.265625 45.40625 46.390625 \n", "M 14.796875 27.296875 \n", "Q 14.796875 17.390625 18.875 11.75 \n", "Q 22.953125 6.109375 30.078125 6.109375 \n", "Q 37.203125 6.109375 41.296875 11.75 \n", "Q 45.40625 17.390625 45.40625 27.296875 \n", "Q 45.40625 37.203125 41.296875 42.84375 \n", "Q 37.203125 48.484375 30.078125 48.484375 \n", "Q 22.953125 48.484375 18.875 42.84375 \n", "Q 14.796875 37.203125 14.796875 27.296875 \n", "\" id=\"BitstreamVeraSans-Roman-64\"/>\n", "     <path d=\"M 44.28125 53.078125 \n", "L 44.28125 44.578125 \n", "Q 40.484375 46.53125 36.375 47.5 \n", "Q 32.28125 48.484375 27.875 48.484375 \n", "Q 21.1875 48.484375 17.84375 46.4375 \n", "Q 14.5 44.390625 14.5 40.28125 \n", "Q 14.5 37.15625 16.890625 35.375 \n", "Q 19.28125 33.59375 26.515625 31.984375 \n", "L 29.59375 31.296875 \n", "Q 39.15625 29.25 43.1875 25.515625 \n", "Q 47.21875 21.78125 47.21875 15.09375 \n", "Q 47.21875 7.46875 41.1875 3.015625 \n", "Q 35.15625 -1.421875 24.609375 -1.421875 \n", "Q 20.21875 -1.421875 15.453125 -0.5625 \n", "Q 10.6875 0.296875 5.421875 2 \n", "L 5.421875 11.28125 \n", "Q 10.40625 8.6875 15.234375 7.390625 \n", "Q 20.0625 6.109375 24.8125 6.109375 \n", "Q 31.15625 6.109375 34.5625 8.28125 \n", "Q 37.984375 10.453125 37.984375 14.40625 \n", "Q 37.984375 18.0625 35.515625 20.015625 \n", "Q 33.0625 21.96875 24.703125 23.78125 \n", "L 21.578125 24.515625 \n", "Q 13.234375 26.265625 9.515625 29.90625 \n", "Q 5.8125 33.546875 5.8125 39.890625 \n", "Q 5.8125 47.609375 11.28125 51.796875 \n", "Q 16.75 56 26.8125 56 \n", "Q 31.78125 56 36.171875 55.265625 \n", "Q 40.578125 54.546875 44.28125 53.078125 \n", "\" id=\"BitstreamVeraSans-Roman-73\"/>\n", "    </defs>\n", "    <g transform=\"translate(71.4446875 16.318125)scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#BitstreamVeraSans-Roman-74\"/>\n", "     <use x=\"39.208984375\" xlink:href=\"#BitstreamVeraSans-Roman-65\"/>\n", "     <use x=\"100.732421875\" xlink:href=\"#BitstreamVeraSans-Roman-73\"/>\n", "     <use x=\"152.83203125\" xlink:href=\"#BitstreamVeraSans-Roman-74\"/>\n", "     <use x=\"192.041015625\" xlink:href=\"#BitstreamVeraSans-Roman-3a\"/>\n", "     <use x=\"225.732421875\" xlink:href=\"#BitstreamVeraSans-Roman-20\"/>\n", "     <use x=\"257.51953125\" xlink:href=\"#BitstreamVeraSans-Roman-6d\"/>\n", "     <use x=\"354.931640625\" xlink:href=\"#BitstreamVeraSans-Roman-6f\"/>\n", "     <use x=\"416.11328125\" xlink:href=\"#BitstreamVeraSans-Roman-64\"/>\n", "     <use x=\"479.58984375\" xlink:href=\"#BitstreamVeraSans-Roman-65\"/>\n", "     <use x=\"541.11328125\" xlink:href=\"#BitstreamVeraSans-Roman-6c\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p52cfe5a760\">\n", "   <rect height=\"83.7\" width=\"167.4\" x=\"21.878125\" y=\"21.318125\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<matplotlib.figure.Figure at 0x111a9db00>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def draw_spectogram(data):\n", "    plt.specgram(data,Fs=4,NFFT=256,noverlap=150,cmap=plt.cm.bone,detrend=lambda x:(x-0.5))\n", "    plt.gca().autoscale('x')\n", "    plt.ylim([0,0.5])\n", "    plt.ylabel(\"freq\")\n", "    plt.yticks([])\n", "    plt.xlabel(\"time\")\n", "    plt.xticks([])\n", "\n", "plt.figure(figsize=(7,1.5))\n", "draw_spectogram(train_output.flatten())\n", "plt.title(\"training: target\")\n", "plt.figure(figsize=(7,1.5))\n", "draw_spectogram(pred_train.flatten())\n", "plt.title(\"training: model\")\n", "\n", "plt.figure(figsize=(3,1.5))\n", "draw_spectogram(test_output.flatten())\n", "plt.title(\"test: target\")\n", "plt.figure(figsize=(3,1.5))\n", "draw_spectogram(pred_test.flatten())\n", "plt.title(\"test: model\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["It's a frequency generator!"]}], "metadata": {"kernelspec": {"display_name": "Python [default]", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.0"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 1}