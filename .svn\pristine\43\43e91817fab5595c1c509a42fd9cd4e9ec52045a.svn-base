# Copyright (c) Microsoft Corporation.
# Licensed under the MIT License.
# coding=utf-8
"""
Loader里，主要功能是加载原始数据到内存，不做太多的处理
"""
import abc
import warnings
import pandas as pd
import numpy as np
import datetime
import json
import os

from sklearn.model_selection import StratifiedKFold # 将全部训练集S分成k个不相交的子集
from sklearn.preprocessing import LabelEncoder # 标签编码LabelEncoder 作用： 利用LabelEncoder() 将转换成连续的数值型变量。即是对不连续的数字或者文本进行编号

from typing import Tuple, Union, List

from qlib.data import D
from qlib.utils import load_dataset, init_instance_by_config, time_to_slc_point
from qlib.log import get_module_logger
from qlib.data.dataset.loader import DataLoader
from pyqlab.const import MAIN_SEL_FUT_CODES


class AHFDataLoader(DataLoader):
    '''
    (A)icm (H)istory (F)actor Data Loader

    '''
    def __init__(
           self,
           data_path = '',
           train_codes=[],
       ) -> None:
            self.data_path = data_path
            self.train_codes = train_codes
            # self.lb_df = pd.DataFrame()
            self.lf_df = pd.DataFrame()
            self.sf_df = pd.DataFrame()
            self.mf_df = pd.DataFrame()
            self.ct_df = pd.DataFrame()
            self.interface_params = {
                'input_dim': 2, # 1: expression call 2: API call
                'code_encoding': 0, # 0:unsing, 1:onehot, 2:embedding
            }
            super().__init__()

    def set_data(self, lf_df, sf_df, mf_df, ct_df):
        self.lf_df = lf_df
        self.sf_df = sf_df
        self.mf_df = mf_df
        self.ct_df = ct_df

    def clear_data(self):
        self.lf_df = pd.DataFrame()
        self.sf_df = pd.DataFrame()
        self.mf_df = pd.DataFrame()
        self.ct_df = pd.DataFrame()

    def _load_data_from_file(self):
        for year in self.years:
            print(f"Loading {year} data...")
            if os.path.isfile('%s/ffs_lf.%s.parquet'%(self.data_path, year)):
                self.lf_df = pd.concat([self.lf_df, pd.read_parquet('%s/ffs_lf.%s.parquet'%(self.data_path, year), engine='fastparquet')])
            if os.path.isfile('%s/ffs_sf.%s.parquet'%(self.data_path, year)):
                self.sf_df = pd.concat([self.sf_df, pd.read_parquet('%s/ffs_sf.%s.parquet'%(self.data_path, year), engine='fastparquet')])
            if os.path.isfile('%s/ffs_mf.%s.parquet'%(self.data_path, year)):
                self.mf_df = pd.concat([self.mf_df, pd.read_parquet('%s/ffs_mf.%s.parquet'%(self.data_path, year), engine='fastparquet')])
            if os.path.isfile('%s/ffs_ct.%s.parquet'%(self.data_path, year)):
                self.ct_df = pd.concat([self.ct_df, pd.read_parquet('%s/ffs_ct.%s.parquet'%(self.data_path, year), engine='fastparquet')])
        
    def _load(self):
        # self.lf_df, self.sf_df, self.ct_df = self.fd.get_pf_data(self.years)
        pass

    def _prepare_data(self):
        self._load_data_from_file()
        assert not self.sf_df.empty, "load raw data is empty."

        # 清除不需要的数据
        if len(self.train_codes) > 0:
            self.sf_df = self.sf_df[self.sf_df['code'].isin(self.train_codes)]
            self.mf_df = self.mf_df[self.mf_df['code'].isin(self.train_codes)]
            self.lf_df = self.lf_df[self.lf_df['code'].isin(self.train_codes)]
            self.ct_df = self.ct_df[self.ct_df['code'].isin(self.train_codes)]

        if not self.sf_df.empty:
            # todo: 最好是分code处理，在两个code衔接处分组时会发生跨代码的问题
            self.sf_df.sort_values(by=['code', 'date'], ascending=True, inplace=True)
            self.sf_df['change'] = self.sf_df['change'].astype(np.float32)
            # 将当前的涨跌幅移动后5行，作为当前的label
            self.sf_df['change'] = self.sf_df['change'].shift(-5)
            self.sf_df = self.sf_df[:-5]

        if not self.lf_df.empty:
            self.lf_df.sort_values(by=['code', 'date'], ascending=True, inplace=True)
            self.lf_df = self.lf_df[:-5]
            # self.lf_df['change'] = self.lf_df['change'].astype(np.float32)
            # self.lf_df['change'] = self.lf_df['change'].shift(-5)
            # self.lf_df = self.lf_df[:-1]
            # self.lf_df['RSI_2'] = self.lf_df['RSI_2'].astype(np.float32)
            # self.lf_df = self.lf_df[self.lf_df['RSI_2'] != 0.0]

        if not self.mf_df.empty:
            self.mf_df.sort_values(by=['code', 'date'], ascending=True, inplace=True)
            self.mf_df = self.mf_df[:-5]

        if not self.ct_df.empty:
            self.ct_df.sort_values(by=['code', 'date'], ascending=True, inplace=True)
            self.ct_df = self.ct_df[:-5]


    def _get_folds(self):
        # skf = StratifiedKFold(n_splits=self.n_splits, shuffle=self.shuffle, random_state=self.random_state)
        # for fold, (_, val_idx) in enumerate(skf.split(X=self.lb_df, y=self.lb_df['label']), 1):
        #     self.lb_df.loc[val_idx, 'fold'] = fold
        # self.lb_df['fold'] = self.lb_df['fold'].astype(np.uint8)
        pass

                        
    def transform(self):
        self._prepare_data()
        # self._label_encode()
        # self._get_folds()
        # self._dump_input_param_json()
        return self.lf_df, self.sf_df, self.mf_df, self.ct_df

    def get_num_embeddings(self):
        return len(self.sf_df.code.unique())    

    def load(self, instruments, start_time=None, end_time=None) -> pd.DataFrame:
        # TODO: ...
        self.years=instruments
        return self.transform()   
        