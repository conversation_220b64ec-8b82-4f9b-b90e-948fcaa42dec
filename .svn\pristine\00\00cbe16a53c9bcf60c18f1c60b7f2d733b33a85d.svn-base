{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# 示例 logits\n", "logits = np.array([2.0, 1.0, 0.1])\n", "\n", "# 温度参数\n", "temperatures = [0.01, .5, 1.0, 2.0]\n", "\n", "# 计算 softmax 分布\n", "def softmax(x):\n", "    return np.exp(x) / np.sum(np.exp(x))\n", "\n", "# 绘制结果\n", "plt.figure(figsize=(10, 6))\n", "for t in temperatures:\n", "    scaled_logits = logits / t\n", "    probs = softmax(scaled_logits)\n", "    plt.plot(probs, label=f'Temperature = {t}')\n", "\n", "plt.xlabel('Logit Index')\n", "plt.ylabel('Probability')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["# from pyecharts import online\n", "import datetime\n", "import struct\n", "import pandas as pd\n", "import sys\n", "import matplotlib as mpl\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "sys.path.append(\"d:/QuantLab\")\n", "from qtunnel import DB,Mode,Cursor,Transaction,create_db,registered_dbs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["from datetime import datetime\n", "\n", "timestamp = datetime.fromtimestamp(5474764*300 - 8*3600)\n", "print(\"日期 =\", timestamp)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"tags": ["outputPrepend"]}, "outputs": [], "source": ["def open_db(file_name, mode):\n", "    db=create_db(\"leveldb\", file_name, mode)\n", "    return db\n", "\n", "def write_db(db, key, value):\n", "    transaction = db.new_transaction()\n", "    transaction.put(key, value)\n", "    del transaction\n", "    \n", "def delete_db(db, key):\n", "    transaction = db.new_transaction()\n", "    transaction.delete(key)\n", "    del transaction\n", "    \n", "def query_db(db):\n", "    cursor = db.new_cursor()\n", "    while cursor.valid():\n", "        print(cursor.key())\n", "        # print(cursor.key(), cursor.value())\n", "        cursor.next()\n", "    del cursor\n", "    \n", "def read_db(db):\n", "    cursor = db.new_cursor()\n", "    cnt = 0\n", "    while cursor.valid():\n", "        # print(cursor.key(), cursor.value())\n", "        print(str(cursor.key(), \"utf-8\"), len(cursor.value())//48)\n", "        for i in range(len(cursor.value())//48):\n", "            tt, p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2 = struct.unpack(\"qfl2f2l2f2l\", cursor.value()[i*48:(i+1)*48])\n", "            print(datetime.datetime.fromtimestamp(tt-8*3600), p, vol, bp1,bp2, bv1, bv2, ap1, ap2, av1,av2)\n", "\n", "        cursor.next()\n", "        cnt += 1\n", "        if cnt > 2:\n", "            break\n", "    del cursor\n", "\n", "def test_1():\n", "    db = open_db(\"d:/QuantLab/store/historydata.db\", Mode.read)\n", "    query_db(db)\n", "    db.close()\n", "    del db\n", "\n", "def test_2():\n", "    db = open_db(\"d:/QuantLab/store/kv.db\", Mode.write)\n", "    write_db(db, \"welcome\", \"very good! very good!\")\n", "    #delete_db(db, \"welcome\")\n", "    query_db(db)\n", "    db.close()\n", "    del db\n", "\n", "def test_3():\n", "    db = open_db(\"d:/QuantLab/store/kv.db\", Mode.read)\n", "    query_db(db)\n", "    db.close()\n", "    del db\n", "\n", "def test_4():\n", "    db = open_db(\"d:/RoboQuant/store/tickdata.db\", Mode.read)\n", "    query_db(db)\n", "    db.close()\n", "    del db\n", "\n", "def test_5():\n", "    db = open_db(\"d:/RoboQuant/store/tickdata.db\", Mode.read)\n", "    read_db(db)\n", "    db.close()\n", "    del db    "]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tick_I8888.DC_5475131 1\n", "2022-01-18 20:59:00 705.0 3513 704.0 0.0 24 0 705.0 0.0 416 0\n", "tick_I8888.DC_5475132 601\n", "2022-01-18 20:59:00 705.0 3513 704.0 0.0 24 0 705.0 0.0 416 0\n", "2022-01-18 21:00:00 704.0 642 703.0 0.0 323 0 704.0 0.0 363 0\n", "2022-01-18 21:00:00 704.5 725 704.0 0.0 168 0 704.5 0.0 243 0\n", "2022-01-18 21:00:01 704.5 519 704.0 0.0 343 0 704.5 0.0 6 0\n", "2022-01-18 21:00:01 704.5 249 704.5 0.0 11 0 705.0 0.0 256 0\n", "2022-01-18 21:00:02 705.0 309 705.0 0.0 73 0 705.5 0.0 617 0\n", "2022-01-18 21:00:02 706.0 688 705.5 0.0 164 0 706.0 0.0 177 0\n", "2022-01-18 21:00:03 707.0 491 706.5 0.0 22 0 707.0 0.0 263 0\n", "2022-01-18 21:00:03 706.5 273 706.5 0.0 306 0 707.0 0.0 42 0\n", "2022-01-18 21:00:04 707.0 453 707.0 0.0 109 0 707.5 0.0 83 0\n", "2022-01-18 21:00:04 707.5 349 707.0 0.0 44 0 707.5 0.0 73 0\n", "2022-01-18 21:00:05 707.5 188 707.0 0.0 104 0 707.5 0.0 26 0\n", "2022-01-18 21:00:05 707.5 23 707.0 0.0 130 0 707.5 0.0 43 0\n", "2022-01-18 21:00:06 707.0 190 707.0 0.0 92 0 707.5 0.0 62 0\n", "2022-01-18 21:00:06 707.5 224 707.0 0.0 64 0 707.5 0.0 498 0\n", "2022-01-18 21:00:07 707.0 165 706.5 0.0 27 0 707.5 0.0 170 0\n", "2022-01-18 21:00:07 707.0 150 707.0 0.0 1 0 707.5 0.0 142 0\n", "2022-01-18 21:00:08 707.5 105 707.0 0.0 28 0 707.5 0.0 181 0\n", "2022-01-18 21:00:08 707.0 64 707.0 0.0 140 0 707.5 0.0 174 0\n", "2022-01-18 21:00:09 707.5 75 707.0 0.0 178 0 707.5 0.0 144 0\n", "2022-01-18 21:00:09 707.0 132 707.0 0.0 181 0 707.5 0.0 104 0\n", "2022-01-18 21:00:10 707.0 171 707.0 0.0 50 0 707.5 0.0 203 0\n", "2022-01-18 21:00:10 707.0 140 706.5 0.0 57 0 707.0 0.0 166 0\n", "2022-01-18 21:00:11 706.5 163 706.0 0.0 70 0 706.5 0.0 1 0\n", "2022-01-18 21:00:11 706.5 117 706.5 0.0 88 0 707.0 0.0 150 0\n", "2022-01-18 21:00:12 707.0 193 707.0 0.0 17 0 707.5 0.0 216 0\n", "2022-01-18 21:00:12 707.5 55 707.0 0.0 28 0 707.5 0.0 261 0\n", "2022-01-18 21:00:13 707.0 140 707.0 0.0 91 0 707.5 0.0 143 0\n", "2022-01-18 21:00:13 707.5 71 707.0 0.0 349 0 707.5 0.0 116 0\n", "2022-01-18 21:00:14 707.5 169 707.5 0.0 430 0 708.0 0.0 649 0\n", "2022-01-18 21:00:14 708.0 154 707.5 0.0 395 0 708.0 0.0 665 0\n", "2022-01-18 21:00:15 708.0 621 707.5 0.0 318 0 708.0 0.0 198 0\n", "2022-01-18 21:00:15 708.0 250 708.0 0.0 392 0 708.5 0.0 156 0\n", "2022-01-18 21:00:16 708.0 54 708.0 0.0 389 0 708.5 0.0 95 0\n", "2022-01-18 21:00:16 708.0 113 708.0 0.0 478 0 708.5 0.0 29 0\n", "2022-01-18 21:00:17 708.5 62 708.5 0.0 37 0 709.0 0.0 374 0\n", "2022-01-18 21:00:17 709.0 144 708.5 0.0 142 0 709.0 0.0 350 0\n", "2022-01-18 21:00:18 708.5 266 708.5 0.0 163 0 709.0 0.0 138 0\n", "2022-01-18 21:00:18 709.0 117 708.5 0.0 182 0 709.0 0.0 56 0\n", "2022-01-18 21:00:19 709.0 172 709.0 0.0 9 0 709.5 0.0 262 0\n", "2022-01-18 21:00:19 709.0 120 709.0 0.0 50 0 709.5 0.0 274 0\n", "2022-01-18 21:00:20 708.5 111 708.5 0.0 179 0 709.0 0.0 34 0\n", "2022-01-18 21:00:20 708.5 128 708.5 0.0 178 0 709.0 0.0 24 0\n", "2022-01-18 21:00:21 709.5 405 709.5 0.0 62 0 710.0 0.0 283 0\n", "2022-01-18 21:00:21 708.5 336 708.5 0.0 232 0 709.0 0.0 6 0\n", "2022-01-18 21:00:22 709.0 59 709.0 0.0 39 0 709.5 0.0 223 0\n", "2022-01-18 21:00:22 709.0 54 709.0 0.0 126 0 709.5 0.0 203 0\n", "2022-01-18 21:00:23 709.0 86 709.0 0.0 151 0 709.5 0.0 173 0\n", "2022-01-18 21:00:23 709.0 81 709.0 0.0 242 0 709.5 0.0 149 0\n", "2022-01-18 21:00:24 709.5 90 709.0 0.0 259 0 709.5 0.0 124 0\n", "2022-01-18 21:00:24 709.0 170 709.0 0.0 105 0 709.5 0.0 142 0\n", "2022-01-18 21:00:25 709.0 298 708.5 0.0 408 0 709.0 0.0 92 0\n", "2022-01-18 21:00:25 708.5 74 708.5 0.0 315 0 709.0 0.0 183 0\n", "2022-01-18 21:00:26 708.5 84 708.5 0.0 257 0 709.0 0.0 249 0\n", "2022-01-18 21:00:26 708.5 312 708.0 0.0 413 0 708.5 0.0 322 0\n", "2022-01-18 21:00:27 708.5 90 708.0 0.0 301 0 708.5 0.0 329 0\n", "2022-01-18 21:00:27 708.0 427 707.5 0.0 134 0 708.0 0.0 125 0\n", "2022-01-18 21:00:28 708.0 143 707.5 0.0 142 0 708.0 0.0 22 0\n", "2022-01-18 21:00:28 708.0 94 708.0 0.0 13 0 708.5 0.0 355 0\n", "2022-01-18 21:00:29 708.5 258 708.0 0.0 10 0 708.5 0.0 398 0\n", "2022-01-18 21:00:29 708.0 72 707.5 0.0 349 0 708.0 0.0 24 0\n", "2022-01-18 21:00:30 707.5 72 707.5 0.0 314 0 708.0 0.0 182 0\n", "2022-01-18 21:00:30 708.0 101 707.5 0.0 307 0 708.0 0.0 148 0\n", "2022-01-18 21:00:31 707.5 103 707.5 0.0 268 0 708.0 0.0 178 0\n", "2022-01-18 21:00:31 707.5 59 707.5 0.0 240 0 708.0 0.0 197 0\n", "2022-01-18 21:00:32 707.5 107 707.5 0.0 332 0 708.0 0.0 141 0\n", "2022-01-18 21:00:32 707.5 36 707.5 0.0 319 0 708.0 0.0 136 0\n", "2022-01-18 21:00:33 707.5 93 707.5 0.0 275 0 708.0 0.0 120 0\n", "2022-01-18 21:00:33 708.0 170 708.0 0.0 178 0 708.5 0.0 404 0\n", "2022-01-18 21:00:34 708.0 225 707.5 0.0 249 0 708.0 0.0 6 0\n", "2022-01-18 21:00:34 707.5 74 707.5 0.0 199 0 708.0 0.0 110 0\n", "2022-01-18 21:00:35 707.5 321 707.5 0.0 206 0 708.5 0.0 285 0\n", "2022-01-18 21:00:35 708.0 28 708.0 0.0 152 0 708.5 0.0 286 0\n", "2022-01-18 21:00:36 708.0 44 708.0 0.0 110 0 708.5 0.0 284 0\n", "2022-01-18 21:00:36 708.0 131 707.5 0.0 208 0 708.0 0.0 49 0\n", "2022-01-18 21:00:37 707.5 59 707.5 0.0 236 0 708.0 0.0 36 0\n", "2022-01-18 21:00:37 708.0 153 708.0 0.0 178 0 708.5 0.0 208 0\n", "2022-01-18 21:00:38 708.5 20 708.0 0.0 178 0 708.5 0.0 186 0\n", "2022-01-18 21:00:38 707.5 290 707.5 0.0 217 0 708.0 0.0 392 0\n", "2022-01-18 21:00:39 707.5 26 707.5 0.0 199 0 708.0 0.0 392 0\n", "2022-01-18 21:00:39 707.5 67 707.5 0.0 253 0 708.0 0.0 372 0\n", "2022-01-18 21:00:40 708.0 12 707.5 0.0 251 0 708.0 0.0 374 0\n", "2022-01-18 21:00:40 708.0 54 707.5 0.0 204 0 708.0 0.0 408 0\n", "2022-01-18 21:00:41 707.5 58 707.5 0.0 174 0 708.0 0.0 395 0\n", "2022-01-18 21:00:41 707.5 20 707.5 0.0 198 0 708.0 0.0 384 0\n", "2022-01-18 21:00:42 707.5 77 707.5 0.0 156 0 708.0 0.0 351 0\n", "2022-01-18 21:00:42 707.5 9 707.5 0.0 157 0 708.0 0.0 362 0\n", "2022-01-18 21:00:43 708.0 70 707.5 0.0 119 0 708.0 0.0 344 0\n", "2022-01-18 21:00:43 707.5 20 707.5 0.0 129 0 708.0 0.0 349 0\n", "2022-01-18 21:00:44 707.5 31 707.5 0.0 153 0 708.0 0.0 362 0\n", "2022-01-18 21:00:44 707.5 62 707.5 0.0 103 0 708.0 0.0 377 0\n", "2022-01-18 21:00:45 707.5 83 707.5 0.0 66 0 708.0 0.0 381 0\n", "2022-01-18 21:00:45 707.5 11 707.5 0.0 61 0 708.0 0.0 384 0\n", "2022-01-18 21:00:46 707.5 20 707.5 0.0 58 0 708.0 0.0 386 0\n", "2022-01-18 21:00:46 707.5 20 707.5 0.0 52 0 708.0 0.0 399 0\n", "2022-01-18 21:00:47 707.5 63 707.0 0.0 316 0 707.5 0.0 147 0\n", "2022-01-18 21:00:47 707.0 28 707.0 0.0 322 0 707.5 0.0 76 0\n", "2022-01-18 21:00:48 707.5 46 707.0 0.0 306 0 707.5 0.0 70 0\n", "2022-01-18 21:00:48 707.0 13 707.0 0.0 288 0 707.5 0.0 105 0\n", "2022-01-18 21:00:49 707.5 121 707.5 0.0 91 0 708.0 0.0 453 0\n", "2022-01-18 21:00:49 707.5 34 707.5 0.0 83 0 708.0 0.0 452 0\n", "2022-01-18 21:00:50 707.5 5 707.5 0.0 208 0 708.0 0.0 504 0\n", "2022-01-18 21:00:50 707.5 31 707.5 0.0 215 0 708.0 0.0 512 0\n", "2022-01-18 21:00:51 707.5 9 707.5 0.0 238 0 708.0 0.0 510 0\n", "2022-01-18 21:00:51 707.5 99 707.5 0.0 167 0 708.0 0.0 509 0\n", "2022-01-18 21:00:52 707.5 6 707.5 0.0 203 0 708.0 0.0 510 0\n", "2022-01-18 21:00:52 707.5 27 707.5 0.0 212 0 708.0 0.0 506 0\n", "2022-01-18 21:00:53 707.5 30 707.5 0.0 224 0 708.0 0.0 502 0\n", "2022-01-18 21:00:53 708.0 7 707.5 0.0 238 0 708.0 0.0 505 0\n", "2022-01-18 21:00:54 707.5 106 707.5 0.0 238 0 708.0 0.0 412 0\n", "2022-01-18 21:00:54 707.5 33 707.5 0.0 229 0 708.0 0.0 393 0\n", "2022-01-18 21:00:55 708.0 61 707.5 0.0 214 0 708.0 0.0 352 0\n", "2022-01-18 21:00:55 707.5 37 707.5 0.0 216 0 708.0 0.0 326 0\n", "2022-01-18 21:00:56 707.5 22 707.5 0.0 215 0 708.0 0.0 317 0\n", "2022-01-18 21:00:56 707.5 29 707.5 0.0 251 0 708.0 0.0 310 0\n", "2022-01-18 21:00:57 708.0 16 707.5 0.0 250 0 708.0 0.0 294 0\n", "2022-01-18 21:00:57 708.5 343 708.0 0.0 31 0 708.5 0.0 281 0\n", "2022-01-18 21:00:58 708.0 54 708.0 0.0 112 0 708.5 0.0 256 0\n", "2022-01-18 21:00:58 708.0 9 708.0 0.0 125 0 708.5 0.0 259 0\n", "2022-01-18 21:00:59 708.0 8 708.0 0.0 125 0 708.5 0.0 260 0\n", "2022-01-18 21:00:59 708.5 17 708.0 0.0 138 0 708.5 0.0 250 0\n", "2022-01-18 21:01:00 708.5 54 708.0 0.0 97 0 708.5 0.0 274 0\n", "2022-01-18 21:01:00 708.5 28 708.0 0.0 106 0 708.5 0.0 278 0\n", "2022-01-18 21:01:01 708.0 18 708.0 0.0 131 0 708.5 0.0 286 0\n", "2022-01-18 21:01:01 708.5 20 708.0 0.0 127 0 708.5 0.0 305 0\n", "2022-01-18 21:01:02 708.0 51 708.0 0.0 137 0 708.5 0.0 258 0\n", "2022-01-18 21:01:02 708.0 12 708.0 0.0 132 0 708.5 0.0 280 0\n", "2022-01-18 21:01:03 708.0 55 708.0 0.0 113 0 708.5 0.0 377 0\n", "2022-01-18 21:01:03 708.0 139 708.0 0.0 149 0 708.5 0.0 305 0\n", "2022-01-18 21:01:04 708.5 25 708.0 0.0 139 0 708.5 0.0 283 0\n", "2022-01-18 21:01:04 708.0 41 708.0 0.0 108 0 708.5 0.0 310 0\n", "2022-01-18 21:01:05 708.0 12 708.0 0.0 130 0 708.5 0.0 323 0\n", "2022-01-18 21:01:05 708.5 5 708.0 0.0 135 0 708.5 0.0 320 0\n", "2022-01-18 21:01:06 708.0 9 708.0 0.0 158 0 708.5 0.0 320 0\n", "2022-01-18 21:01:06 708.5 37 708.0 0.0 161 0 708.5 0.0 290 0\n", "2022-01-18 21:01:07 708.0 24 708.0 0.0 149 0 708.5 0.0 277 0\n", "2022-01-18 21:01:07 708.5 49 708.0 0.0 155 0 708.5 0.0 250 0\n", "2022-01-18 21:01:08 708.0 30 708.0 0.0 156 0 708.5 0.0 164 0\n", "2022-01-18 21:01:08 708.0 31 708.0 0.0 164 0 708.5 0.0 162 0\n", "2022-01-18 21:01:09 708.0 16 708.0 0.0 163 0 708.5 0.0 167 0\n", "2022-01-18 21:01:09 708.5 10 708.0 0.0 173 0 708.5 0.0 167 0\n", "2022-01-18 21:01:10 708.5 28 708.0 0.0 159 0 708.5 0.0 168 0\n", "2022-01-18 21:01:10 708.5 16 708.0 0.0 157 0 708.5 0.0 169 0\n", "2022-01-18 21:01:11 708.0 21 708.0 0.0 164 0 708.5 0.0 163 0\n", "2022-01-18 21:01:11 708.5 10 708.0 0.0 181 0 708.5 0.0 157 0\n", "2022-01-18 21:01:12 708.0 17 708.0 0.0 175 0 708.5 0.0 159 0\n", "2022-01-18 21:01:12 708.5 23 708.0 0.0 158 0 708.5 0.0 159 0\n", "2022-01-18 21:01:13 708.5 18 708.0 0.0 152 0 708.5 0.0 162 0\n", "2022-01-18 21:01:13 708.5 445 707.5 0.0 260 0 708.5 0.0 24 0\n", "2022-01-18 21:01:14 708.0 60 708.0 0.0 75 0 708.5 0.0 202 0\n", "2022-01-18 21:01:14 708.5 78 708.0 0.0 74 0 708.5 0.0 187 0\n", "2022-01-18 21:01:15 708.0 41 708.0 0.0 77 0 708.5 0.0 181 0\n", "2022-01-18 21:01:15 708.5 4 708.0 0.0 84 0 708.5 0.0 203 0\n", "2022-01-18 21:01:16 708.0 20 708.0 0.0 119 0 708.5 0.0 207 0\n", "2022-01-18 21:01:16 708.5 14 708.0 0.0 132 0 708.5 0.0 203 0\n", "2022-01-18 21:01:17 708.0 42 708.0 0.0 108 0 708.5 0.0 167 0\n", "2022-01-18 21:01:17 708.0 33 708.0 0.0 120 0 708.5 0.0 177 0\n", "2022-01-18 21:01:18 708.0 146 708.0 0.0 68 0 708.5 0.0 202 0\n", "2022-01-18 21:01:18 708.5 18 707.5 0.0 285 0 708.5 0.0 205 0\n", "2022-01-18 21:01:19 708.5 16 708.0 0.0 113 0 708.5 0.0 199 0\n", "2022-01-18 21:01:19 708.0 14 708.0 0.0 176 0 708.5 0.0 190 0\n", "2022-01-18 21:01:20 708.5 41 708.0 0.0 173 0 708.5 0.0 143 0\n", "2022-01-18 21:01:20 708.0 21 708.0 0.0 173 0 708.5 0.0 127 0\n", "2022-01-18 21:01:21 708.0 29 708.0 0.0 223 0 708.5 0.0 172 0\n", "2022-01-18 21:01:21 708.0 61 708.0 0.0 239 0 708.5 0.0 112 0\n", "2022-01-18 21:01:22 708.0 11 708.0 0.0 236 0 708.5 0.0 133 0\n", "2022-01-18 21:01:22 708.5 31 708.0 0.0 250 0 708.5 0.0 128 0\n", "2022-01-18 21:01:23 708.5 35 708.0 0.0 222 0 708.5 0.0 145 0\n", "2022-01-18 21:01:23 708.5 7 708.0 0.0 216 0 708.5 0.0 148 0\n", "2022-01-18 21:01:24 708.0 5 708.0 0.0 214 0 708.5 0.0 152 0\n", "2022-01-18 21:01:24 708.5 11 708.0 0.0 227 0 708.5 0.0 147 0\n", "2022-01-18 21:01:25 708.0 12 708.0 0.0 248 0 708.5 0.0 180 0\n", "2022-01-18 21:01:25 708.5 45 708.0 0.0 244 0 708.5 0.0 146 0\n", "2022-01-18 21:01:26 708.0 4 708.0 0.0 255 0 708.5 0.0 193 0\n", "2022-01-18 21:01:26 708.5 32 708.0 0.0 237 0 708.5 0.0 190 0\n", "2022-01-18 21:01:27 708.5 5 708.0 0.0 253 0 708.5 0.0 190 0\n", "2022-01-18 21:01:27 708.0 44 708.0 0.0 258 0 708.5 0.0 164 0\n", "2022-01-18 21:01:28 708.5 177 708.5 0.0 167 0 709.0 0.0 651 0\n", "2022-01-18 21:01:28 708.5 88 708.5 0.0 38 0 709.0 0.0 653 0\n", "2022-01-18 21:01:29 708.0 71 708.0 0.0 221 0 708.5 0.0 473 0\n", "2022-01-18 21:01:29 708.5 40 708.0 0.0 193 0 708.5 0.0 471 0\n", "2022-01-18 21:01:30 708.5 4 708.0 0.0 201 0 708.5 0.0 468 0\n", "2022-01-18 21:01:30 708.0 24 708.0 0.0 192 0 708.5 0.0 448 0\n", "2022-01-18 21:01:31 708.0 12 708.0 0.0 234 0 708.5 0.0 462 0\n", "2022-01-18 21:01:31 708.0 23 708.0 0.0 263 0 708.5 0.0 452 0\n", "2022-01-18 21:01:32 708.5 18 708.0 0.0 293 0 708.5 0.0 450 0\n", "2022-01-18 21:01:32 708.5 9 708.0 0.0 306 0 708.5 0.0 459 0\n", "2022-01-18 21:01:33 708.5 50 708.0 0.0 307 0 708.5 0.0 416 0\n", "2022-01-18 21:01:33 708.0 104 708.0 0.0 305 0 708.5 0.0 316 0\n", "2022-01-18 21:01:34 708.0 45 708.0 0.0 269 0 708.5 0.0 317 0\n", "2022-01-18 21:01:34 708.0 128 708.0 0.0 222 0 708.5 0.0 210 0\n", "2022-01-18 21:01:35 708.0 29 708.0 0.0 220 0 708.5 0.0 201 0\n", "2022-01-18 21:01:35 708.5 21 708.0 0.0 211 0 708.5 0.0 207 0\n", "2022-01-18 21:01:36 708.5 5 708.0 0.0 211 0 708.5 0.0 210 0\n", "2022-01-18 21:01:36 708.0 7 708.0 0.0 228 0 708.5 0.0 225 0\n", "2022-01-18 21:01:37 708.5 11 708.0 0.0 234 0 708.5 0.0 216 0\n", "2022-01-18 21:01:37 708.0 12 708.0 0.0 227 0 708.5 0.0 241 0\n", "2022-01-18 21:01:38 708.0 4 708.0 0.0 217 0 708.5 0.0 250 0\n", "2022-01-18 21:01:38 708.5 29 708.0 0.0 206 0 708.5 0.0 230 0\n", "2022-01-18 21:01:39 708.5 6 708.0 0.0 207 0 708.5 0.0 259 0\n", "2022-01-18 21:01:39 708.5 11 708.0 0.0 225 0 708.5 0.0 303 0\n", "2022-01-18 21:01:40 708.5 4 708.0 0.0 226 0 708.5 0.0 304 0\n", "2022-01-18 21:01:40 708.0 20 708.0 0.0 228 0 708.5 0.0 307 0\n", "2022-01-18 21:01:41 708.0 22 708.0 0.0 236 0 708.5 0.0 306 0\n", "2022-01-18 21:01:41 708.0 44 708.0 0.0 261 0 708.5 0.0 301 0\n", "2022-01-18 21:01:42 708.0 9 708.0 0.0 256 0 708.5 0.0 299 0\n", "2022-01-18 21:01:42 708.0 13 708.0 0.0 261 0 708.5 0.0 307 0\n", "2022-01-18 21:01:43 708.0 13 708.0 0.0 261 0 708.5 0.0 304 0\n", "2022-01-18 21:01:43 708.0 2 708.0 0.0 279 0 708.5 0.0 308 0\n", "2022-01-18 21:01:44 708.0 29 708.0 0.0 270 0 708.5 0.0 294 0\n", "2022-01-18 21:01:44 708.5 18 708.0 0.0 280 0 708.5 0.0 286 0\n", "2022-01-18 21:01:45 708.0 2 708.0 0.0 293 0 708.5 0.0 301 0\n", "2022-01-18 21:01:45 708.0 3 708.0 0.0 306 0 708.5 0.0 307 0\n", "2022-01-18 21:01:46 708.0 28 708.0 0.0 307 0 708.5 0.0 286 0\n", "2022-01-18 21:01:46 708.5 5 708.0 0.0 307 0 708.5 0.0 285 0\n", "2022-01-18 21:01:47 708.5 15 708.0 0.0 311 0 708.5 0.0 278 0\n", "2022-01-18 21:01:47 708.5 24 708.0 0.0 302 0 708.5 0.0 265 0\n", "2022-01-18 21:01:48 708.5 32 708.0 0.0 292 0 708.5 0.0 249 0\n", "2022-01-18 21:01:48 708.5 26 708.0 0.0 294 0 708.5 0.0 230 0\n", "2022-01-18 21:01:49 708.5 33 708.0 0.0 308 0 708.5 0.0 209 0\n", "2022-01-18 21:01:49 708.5 2 708.0 0.0 313 0 708.5 0.0 218 0\n", "2022-01-18 21:01:50 708.5 16 708.0 0.0 299 0 708.5 0.0 225 0\n", "2022-01-18 21:01:50 708.5 4 708.0 0.0 310 0 708.5 0.0 228 0\n", "2022-01-18 21:01:51 708.0 16 708.0 0.0 316 0 708.5 0.0 229 0\n", "2022-01-18 21:01:51 708.5 2 708.0 0.0 312 0 708.5 0.0 331 0\n", "2022-01-18 21:01:52 708.5 5 708.0 0.0 323 0 708.5 0.0 332 0\n", "2022-01-18 21:01:52 708.5 24 708.0 0.0 317 0 708.5 0.0 308 0\n", "2022-01-18 21:01:53 708.5 14 708.0 0.0 311 0 708.5 0.0 303 0\n", "2022-01-18 21:01:53 708.0 17 708.0 0.0 316 0 708.5 0.0 297 0\n", "2022-01-18 21:01:54 708.0 55 708.0 0.0 311 0 708.5 0.0 255 0\n", "2022-01-18 21:01:54 708.5 32 708.0 0.0 333 0 708.5 0.0 277 0\n", "2022-01-18 21:01:55 708.0 6 708.0 0.0 329 0 708.5 0.0 279 0\n", "2022-01-18 21:01:55 708.0 4 708.0 0.0 331 0 708.5 0.0 284 0\n", "2022-01-18 21:01:56 708.5 62 708.0 0.0 324 0 708.5 0.0 233 0\n", "2022-01-18 21:01:56 708.5 14 708.0 0.0 325 0 708.5 0.0 221 0\n", "2022-01-18 21:01:57 708.5 18 708.0 0.0 309 0 708.5 0.0 221 0\n", "2022-01-18 21:01:57 708.0 17 708.0 0.0 291 0 708.5 0.0 224 0\n", "2022-01-18 21:01:58 708.5 95 708.0 0.0 228 0 708.5 0.0 222 0\n", "2022-01-18 21:01:58 708.0 71 708.0 0.0 178 0 708.5 0.0 229 0\n", "2022-01-18 21:01:59 708.0 19 708.0 0.0 172 0 708.5 0.0 238 0\n", "2022-01-18 21:01:59 708.0 13 708.0 0.0 174 0 708.5 0.0 241 0\n", "2022-01-18 21:02:00 708.5 169 708.0 0.0 49 0 708.5 0.0 237 0\n", "2022-01-18 21:02:00 708.0 86 707.5 0.0 326 0 708.0 0.0 1 0\n", "2022-01-18 21:02:01 708.0 33 707.5 0.0 333 0 708.5 0.0 315 0\n", "2022-01-18 21:02:01 708.5 30 708.0 0.0 9 0 708.5 0.0 306 0\n", "2022-01-18 21:02:02 708.0 37 708.0 0.0 37 0 708.5 0.0 318 0\n", "2022-01-18 21:02:02 708.0 4 708.0 0.0 165 0 708.5 0.0 324 0\n", "2022-01-18 21:02:03 708.0 62 708.0 0.0 129 0 708.5 0.0 375 0\n", "2022-01-18 21:02:03 708.5 49 708.0 0.0 164 0 708.5 0.0 312 0\n", "2022-01-18 21:02:04 708.0 73 708.0 0.0 107 0 708.5 0.0 315 0\n", "2022-01-18 21:02:04 708.0 34 708.0 0.0 76 0 708.5 0.0 324 0\n", "2022-01-18 21:02:05 708.0 4 708.0 0.0 207 0 708.5 0.0 326 0\n", "2022-01-18 21:02:05 708.0 5 708.0 0.0 206 0 708.5 0.0 329 0\n", "2022-01-18 21:02:06 708.0 27 708.0 0.0 182 0 708.5 0.0 329 0\n", "2022-01-18 21:02:06 708.0 0 708.0 0.0 188 0 708.5 0.0 327 0\n", "2022-01-18 21:02:07 708.5 83 708.0 0.0 144 0 708.5 0.0 304 0\n", "2022-01-18 21:02:07 708.0 6 708.0 0.0 141 0 708.5 0.0 311 0\n", "2022-01-18 21:02:08 708.0 13 708.0 0.0 147 0 708.5 0.0 303 0\n", "2022-01-18 21:02:08 708.0 10 708.0 0.0 147 0 708.5 0.0 303 0\n", "2022-01-18 21:02:09 708.0 9 708.0 0.0 146 0 708.5 0.0 299 0\n", "2022-01-18 21:02:09 708.0 24 708.0 0.0 146 0 708.5 0.0 299 0\n", "2022-01-18 21:02:10 708.5 130 708.0 0.0 166 0 708.5 0.0 146 0\n", "2022-01-18 21:02:10 708.5 164 708.5 0.0 78 0 709.0 0.0 827 0\n", "2022-01-18 21:02:11 708.5 8 708.5 0.0 90 0 709.0 0.0 832 0\n", "2022-01-18 21:02:11 708.5 24 708.5 0.0 83 0 709.0 0.0 838 0\n", "2022-01-18 21:02:12 709.0 43 708.5 0.0 128 0 709.0 0.0 795 0\n", "2022-01-18 21:02:12 709.0 41 708.5 0.0 120 0 709.0 0.0 1085 0\n", "2022-01-18 21:02:13 708.5 18 708.5 0.0 184 0 709.0 0.0 1078 0\n", "2022-01-18 21:02:13 708.5 43 708.5 0.0 94 0 709.0 0.0 1059 0\n", "2022-01-18 21:02:14 709.0 39 708.5 0.0 52 0 709.0 0.0 1054 0\n", "2022-01-18 21:02:14 709.0 79 708.0 0.0 289 0 708.5 0.0 26 0\n", "2022-01-18 21:02:15 708.5 59 708.5 0.0 19 0 709.0 0.0 1057 0\n", "2022-01-18 21:02:15 709.0 7 708.5 0.0 81 0 709.0 0.0 1060 0\n", "2022-01-18 21:02:16 709.0 32 708.5 0.0 84 0 709.0 0.0 1045 0\n", "2022-01-18 21:02:16 708.5 11 708.5 0.0 59 0 709.0 0.0 1040 0\n", "2022-01-18 21:02:17 708.5 9 708.5 0.0 96 0 709.0 0.0 1038 0\n", "2022-01-18 21:02:17 709.0 6 708.5 0.0 75 0 709.0 0.0 1047 0\n", "2022-01-18 21:02:18 708.5 93 708.5 0.0 37 0 709.0 0.0 1431 0\n", "2022-01-18 21:02:18 708.5 48 708.0 0.0 307 0 708.5 0.0 47 0\n", "2022-01-18 21:02:19 709.0 63 708.5 0.0 4 0 709.0 0.0 1430 0\n", "2022-01-18 21:02:19 709.0 26 708.5 0.0 49 0 709.0 0.0 1438 0\n", "2022-01-18 21:02:20 709.0 8 708.5 0.0 127 0 709.0 0.0 1431 0\n", "2022-01-18 21:02:20 708.5 19 708.5 0.0 113 0 709.0 0.0 1438 0\n", "2022-01-18 21:02:21 708.5 10 708.5 0.0 119 0 709.0 0.0 1437 0\n", "2022-01-18 21:02:21 709.0 5 708.5 0.0 137 0 709.0 0.0 1432 0\n", "2022-01-18 21:02:22 708.5 157 708.0 0.0 345 0 708.5 0.0 300 0\n", "2022-01-18 21:02:22 708.5 14 708.0 0.0 331 0 708.5 0.0 314 0\n", "2022-01-18 21:02:23 708.5 29 708.0 0.0 314 0 708.5 0.0 323 0\n", "2022-01-18 21:02:23 708.5 55 708.0 0.0 314 0 708.5 0.0 281 0\n", "2022-01-18 21:02:24 708.5 6 708.0 0.0 315 0 708.5 0.0 252 0\n", "2022-01-18 21:02:24 708.5 20 708.0 0.0 321 0 708.5 0.0 264 0\n", "2022-01-18 21:02:25 708.0 8 708.0 0.0 348 0 708.5 0.0 260 0\n", "2022-01-18 21:02:25 708.5 2 708.0 0.0 366 0 708.5 0.0 256 0\n", "2022-01-18 21:02:26 708.5 2 708.0 0.0 366 0 708.5 0.0 292 0\n", "2022-01-18 21:02:26 708.5 3 708.0 0.0 401 0 708.5 0.0 304 0\n", "2022-01-18 21:02:27 708.5 14 708.0 0.0 418 0 708.5 0.0 311 0\n", "2022-01-18 21:02:27 708.5 4 708.0 0.0 425 0 708.5 0.0 313 0\n", "2022-01-18 21:02:28 708.0 66 708.0 0.0 430 0 708.5 0.0 254 0\n", "2022-01-18 21:02:28 708.0 31 708.0 0.0 441 0 708.5 0.0 96 0\n", "2022-01-18 21:02:29 708.5 112 708.5 0.0 42 0 709.0 0.0 1456 0\n", "2022-01-18 21:02:29 708.5 0 708.5 0.0 83 0 709.0 0.0 1453 0\n", "2022-01-18 21:02:30 708.5 73 708.5 0.0 73 0 709.0 0.0 1413 0\n", "2022-01-18 21:02:30 708.5 25 708.5 0.0 39 0 709.0 0.0 1413 0\n", "2022-01-18 21:02:31 708.5 44 708.0 0.0 489 0 708.5 0.0 8 0\n", "2022-01-18 21:02:31 708.5 6 708.0 0.0 475 0 708.5 0.0 191 0\n", "2022-01-18 21:02:32 708.5 15 708.0 0.0 476 0 708.5 0.0 199 0\n", "2022-01-18 21:02:32 708.5 4 708.0 0.0 467 0 708.5 0.0 550 0\n", "2022-01-18 21:02:33 708.5 11 708.0 0.0 442 0 708.5 0.0 561 0\n", "2022-01-18 21:02:33 708.5 6 708.0 0.0 444 0 708.5 0.0 586 0\n", "2022-01-18 21:02:34 708.5 4 708.0 0.0 451 0 708.5 0.0 592 0\n", "2022-01-18 21:02:34 708.5 4 708.0 0.0 451 0 708.5 0.0 589 0\n", "2022-01-18 21:02:35 708.0 7 708.0 0.0 447 0 708.5 0.0 587 0\n", "2022-01-18 21:02:35 708.0 0 708.0 0.0 449 0 708.5 0.0 595 0\n", "2022-01-18 21:02:36 708.5 4 708.0 0.0 449 0 708.5 0.0 594 0\n", "2022-01-18 21:02:36 708.5 6 708.0 0.0 510 0 708.5 0.0 596 0\n", "2022-01-18 21:02:37 708.0 1 708.0 0.0 517 0 708.5 0.0 598 0\n", "2022-01-18 21:02:37 708.5 20 708.0 0.0 525 0 708.5 0.0 592 0\n", "2022-01-18 21:02:38 708.5 1 708.0 0.0 526 0 708.5 0.0 246 0\n", "2022-01-18 21:02:38 708.5 154 708.5 0.0 4 0 709.0 0.0 1458 0\n", "2022-01-18 21:02:39 708.5 33 708.5 0.0 109 0 709.0 0.0 1432 0\n", "2022-01-18 21:02:39 709.0 32 708.5 0.0 70 0 709.0 0.0 1429 0\n", "2022-01-18 21:02:40 708.5 147 708.5 0.0 85 0 709.0 0.0 1329 0\n", "2022-01-18 21:02:40 708.5 91 708.0 0.0 641 0 708.5 0.0 266 0\n", "2022-01-18 21:02:41 708.0 12 708.0 0.0 640 0 708.5 0.0 362 0\n", "2022-01-18 21:02:41 708.5 41 708.0 0.0 601 0 708.5 0.0 328 0\n", "2022-01-18 21:02:42 708.0 1 708.0 0.0 600 0 708.5 0.0 343 0\n", "2022-01-18 21:02:42 708.5 19 708.0 0.0 594 0 708.5 0.0 325 0\n", "2022-01-18 21:02:43 708.5 2 708.0 0.0 596 0 708.5 0.0 337 0\n", "2022-01-18 21:02:43 708.5 41 708.0 0.0 606 0 708.5 0.0 316 0\n", "2022-01-18 21:02:44 708.5 6 708.0 0.0 605 0 708.5 0.0 302 0\n", "2022-01-18 21:02:44 708.5 23 708.0 0.0 632 0 708.5 0.0 384 0\n", "2022-01-18 21:02:45 708.5 6 708.0 0.0 619 0 708.5 0.0 379 0\n", "2022-01-18 21:02:45 708.5 18 708.0 0.0 622 0 708.5 0.0 384 0\n", "2022-01-18 21:02:46 708.5 1 708.0 0.0 631 0 708.5 0.0 387 0\n", "2022-01-18 21:02:46 708.5 18 708.0 0.0 632 0 708.5 0.0 371 0\n", "2022-01-18 21:02:47 708.5 8 708.0 0.0 634 0 708.5 0.0 375 0\n", "2022-01-18 21:02:47 708.0 5 708.0 0.0 639 0 708.5 0.0 374 0\n", "2022-01-18 21:02:48 708.5 1 708.0 0.0 645 0 708.5 0.0 376 0\n", "2022-01-18 21:02:48 708.5 6 708.0 0.0 634 0 708.5 0.0 382 0\n", "2022-01-18 21:02:49 708.0 133 708.0 0.0 530 0 708.5 0.0 354 0\n", "2022-01-18 21:02:49 708.5 5 708.0 0.0 539 0 708.5 0.0 385 0\n", "2022-01-18 21:02:50 708.5 9 708.0 0.0 538 0 708.5 0.0 388 0\n", "2022-01-18 21:02:50 708.5 46 708.0 0.0 528 0 708.5 0.0 390 0\n", "2022-01-18 21:02:51 708.0 3 708.0 0.0 526 0 708.5 0.0 390 0\n", "2022-01-18 21:02:51 708.5 4 708.0 0.0 527 0 708.5 0.0 397 0\n", "2022-01-18 21:02:52 708.5 0 708.0 0.0 527 0 708.5 0.0 406 0\n", "2022-01-18 21:02:52 708.0 21 708.0 0.0 555 0 708.5 0.0 400 0\n", "2022-01-18 21:02:53 708.5 18 708.0 0.0 554 0 708.5 0.0 387 0\n", "2022-01-18 21:02:53 708.5 7 708.0 0.0 542 0 708.5 0.0 483 0\n", "2022-01-18 21:02:54 708.5 14 708.0 0.0 533 0 708.5 0.0 480 0\n", "2022-01-18 21:02:54 708.5 88 708.0 0.0 539 0 708.5 0.0 396 0\n", "2022-01-18 21:02:55 708.5 13 708.0 0.0 539 0 708.5 0.0 364 0\n", "2022-01-18 21:02:55 708.5 4 708.0 0.0 552 0 708.5 0.0 353 0\n", "2022-01-18 21:02:56 708.5 16 708.0 0.0 617 0 708.5 0.0 342 0\n", "2022-01-18 21:02:56 708.5 31 708.0 0.0 634 0 708.5 0.0 301 0\n", "2022-01-18 21:02:57 708.5 324 708.5 0.0 150 0 709.0 0.0 1426 0\n", "2022-01-18 21:02:57 708.5 142 708.0 0.0 657 0 708.5 0.0 9 0\n", "2022-01-18 21:02:58 708.5 12 708.0 0.0 651 0 708.5 0.0 56 0\n", "2022-01-18 21:02:58 708.5 9 708.0 0.0 642 0 708.5 0.0 235 0\n", "2022-01-18 21:02:59 708.0 5 708.0 0.0 638 0 708.5 0.0 251 0\n", "2022-01-18 21:02:59 708.5 7 708.0 0.0 607 0 708.5 0.0 380 0\n", "2022-01-18 21:03:00 708.5 16 708.0 0.0 517 0 708.5 0.0 380 0\n", "2022-01-18 21:03:00 708.5 19 708.0 0.0 496 0 708.5 0.0 370 0\n", "2022-01-18 21:03:01 708.5 37 708.0 0.0 510 0 708.5 0.0 350 0\n", "2022-01-18 21:03:01 708.5 4 708.0 0.0 528 0 708.5 0.0 399 0\n", "2022-01-18 21:03:02 708.5 5 708.0 0.0 611 0 708.5 0.0 394 0\n", "2022-01-18 21:03:02 708.5 14 708.0 0.0 624 0 708.5 0.0 397 0\n", "2022-01-18 21:03:03 708.0 53 708.0 0.0 572 0 708.5 0.0 497 0\n", "2022-01-18 21:03:03 708.0 68 708.0 0.0 508 0 708.5 0.0 505 0\n", "2022-01-18 21:03:04 708.0 0 708.0 0.0 537 0 708.5 0.0 540 0\n", "2022-01-18 21:03:04 708.0 35 708.0 0.0 519 0 708.5 0.0 534 0\n", "2022-01-18 21:03:05 708.0 56 708.0 0.0 511 0 708.5 0.0 552 0\n", "2022-01-18 21:03:05 708.0 6 708.0 0.0 501 0 708.5 0.0 553 0\n", "2022-01-18 21:03:06 708.0 123 708.0 0.0 372 0 708.5 0.0 559 0\n", "2022-01-18 21:03:06 708.0 446 707.5 0.0 333 0 708.0 0.0 202 0\n", "2022-01-18 21:03:07 707.5 69 707.5 0.0 242 0 708.0 0.0 227 0\n", "2022-01-18 21:03:07 708.0 39 707.5 0.0 237 0 708.0 0.0 235 0\n", "2022-01-18 21:03:08 707.5 14 707.5 0.0 218 0 708.0 0.0 236 0\n", "2022-01-18 21:03:08 707.5 111 707.5 0.0 125 0 708.0 0.0 234 0\n", "2022-01-18 21:03:09 707.5 39 707.5 0.0 117 0 708.0 0.0 248 0\n", "2022-01-18 21:03:09 707.5 0 707.5 0.0 188 0 708.0 0.0 250 0\n", "2022-01-18 21:03:10 707.5 64 707.5 0.0 148 0 708.0 0.0 233 0\n", "2022-01-18 21:03:10 708.0 24 707.5 0.0 159 0 708.0 0.0 223 0\n", "2022-01-18 21:03:11 707.5 50 707.5 0.0 157 0 708.0 0.0 196 0\n", "2022-01-18 21:03:11 708.0 5 707.5 0.0 206 0 708.0 0.0 193 0\n", "2022-01-18 21:03:12 708.0 22 707.5 0.0 251 0 708.0 0.0 152 0\n", "2022-01-18 21:03:12 707.5 18 707.5 0.0 282 0 708.0 0.0 128 0\n", "2022-01-18 21:03:13 707.5 2 707.5 0.0 285 0 708.0 0.0 138 0\n", "2022-01-18 21:03:13 707.5 11 707.5 0.0 331 0 708.0 0.0 144 0\n", "2022-01-18 21:03:14 707.5 27 707.5 0.0 304 0 708.0 0.0 177 0\n", "2022-01-18 21:03:14 707.5 3 707.5 0.0 273 0 708.0 0.0 181 0\n", "2022-01-18 21:03:15 707.5 284 707.0 0.0 708 0 708.0 0.0 208 0\n", "2022-01-18 21:03:15 707.5 58 707.0 0.0 800 0 707.5 0.0 50 0\n", "2022-01-18 21:03:16 707.5 8 707.0 0.0 801 0 707.5 0.0 85 0\n", "2022-01-18 21:03:16 707.5 6 707.0 0.0 802 0 707.5 0.0 115 0\n", "2022-01-18 21:03:17 707.5 5 707.0 0.0 815 0 707.5 0.0 114 0\n", "2022-01-18 21:03:17 707.0 7 707.0 0.0 811 0 707.5 0.0 130 0\n", "2022-01-18 21:03:18 707.0 67 707.0 0.0 743 0 707.5 0.0 135 0\n", "2022-01-18 21:03:18 707.0 3 707.0 0.0 743 0 707.5 0.0 138 0\n", "2022-01-18 21:03:19 707.0 17 707.0 0.0 729 0 707.5 0.0 132 0\n", "2022-01-18 21:03:19 707.5 42 707.0 0.0 674 0 707.5 0.0 149 0\n", "2022-01-18 21:03:20 707.0 8 707.0 0.0 659 0 707.5 0.0 148 0\n", "2022-01-18 21:03:20 707.0 44 707.0 0.0 625 0 707.5 0.0 154 0\n", "2022-01-18 21:03:21 707.0 3 707.0 0.0 625 0 707.5 0.0 173 0\n", "2022-01-18 21:03:21 707.0 3 707.0 0.0 630 0 707.5 0.0 173 0\n", "2022-01-18 21:03:22 707.0 16 707.0 0.0 612 0 707.5 0.0 193 0\n", "2022-01-18 21:03:22 707.5 102 707.0 0.0 250 0 707.5 0.0 191 0\n", "2022-01-18 21:03:23 707.0 256 706.5 0.0 377 0 707.0 0.0 186 0\n", "2022-01-18 21:03:23 706.5 41 706.5 0.0 354 0 707.0 0.0 263 0\n", "2022-01-18 21:03:24 707.0 65 706.5 0.0 374 0 707.0 0.0 262 0\n", "2022-01-18 21:03:24 706.5 21 706.5 0.0 429 0 707.0 0.0 242 0\n", "2022-01-18 21:03:25 707.0 5 706.5 0.0 428 0 707.0 0.0 236 0\n", "2022-01-18 21:03:25 706.5 26 706.5 0.0 409 0 707.0 0.0 239 0\n", "2022-01-18 21:03:26 706.5 15 706.5 0.0 399 0 707.0 0.0 227 0\n", "2022-01-18 21:03:26 707.0 141 706.5 0.0 416 0 707.0 0.0 99 0\n", "2022-01-18 21:03:27 707.0 132 707.0 0.0 276 0 707.5 0.0 187 0\n", "2022-01-18 21:03:27 707.0 18 707.0 0.0 299 0 707.5 0.0 147 0\n", "2022-01-18 21:03:28 707.0 19 707.0 0.0 332 0 707.5 0.0 132 0\n", "2022-01-18 21:03:28 707.0 47 707.0 0.0 291 0 707.5 0.0 145 0\n", "2022-01-18 21:03:29 707.5 255 707.0 0.0 63 0 707.5 0.0 169 0\n", "2022-01-18 21:03:29 707.0 20 707.0 0.0 125 0 707.5 0.0 183 0\n", "2022-01-18 21:03:30 707.0 8 707.0 0.0 162 0 707.5 0.0 181 0\n", "2022-01-18 21:03:30 707.0 33 707.0 0.0 129 0 707.5 0.0 194 0\n", "2022-01-18 21:03:31 707.0 14 707.0 0.0 121 0 707.5 0.0 196 0\n", "2022-01-18 21:03:31 707.0 5 707.0 0.0 126 0 707.5 0.0 201 0\n", "2022-01-18 21:03:32 707.0 15 707.0 0.0 191 0 707.5 0.0 214 0\n", "2022-01-18 21:03:32 707.0 9 707.0 0.0 195 0 707.5 0.0 213 0\n", "2022-01-18 21:03:33 707.0 7 707.0 0.0 669 0 707.5 0.0 212 0\n", "2022-01-18 21:03:33 707.5 219 707.5 0.0 107 0 708.0 0.0 304 0\n", "2022-01-18 21:03:34 707.5 26 707.5 0.0 126 0 708.0 0.0 286 0\n", "2022-01-18 21:03:34 707.5 9 707.5 0.0 169 0 708.0 0.0 286 0\n", "2022-01-18 21:03:35 707.5 2 707.5 0.0 174 0 708.0 0.0 292 0\n", "2022-01-18 21:03:35 707.5 52 707.5 0.0 125 0 708.0 0.0 296 0\n", "2022-01-18 21:03:36 707.5 9 707.5 0.0 120 0 708.0 0.0 299 0\n", "2022-01-18 21:03:36 707.5 7 707.5 0.0 516 0 708.0 0.0 265 0\n", "2022-01-18 21:03:37 707.5 9 707.5 0.0 515 0 708.0 0.0 269 0\n", "2022-01-18 21:03:37 707.5 20 707.5 0.0 496 0 708.0 0.0 277 0\n", "2022-01-18 21:03:38 707.5 2 707.5 0.0 496 0 708.0 0.0 280 0\n", "2022-01-18 21:03:38 708.0 8 707.5 0.0 498 0 708.0 0.0 279 0\n", "2022-01-18 21:03:39 708.0 8 707.5 0.0 491 0 708.0 0.0 289 0\n", "2022-01-18 21:03:39 707.5 3 707.5 0.0 487 0 708.0 0.0 315 0\n", "2022-01-18 21:03:40 707.5 3 707.5 0.0 485 0 708.0 0.0 317 0\n", "2022-01-18 21:03:40 708.0 3 707.5 0.0 508 0 708.0 0.0 345 0\n", "2022-01-18 21:03:41 707.5 12 707.5 0.0 514 0 708.0 0.0 339 0\n", "2022-01-18 21:03:41 707.5 2 707.5 0.0 517 0 708.0 0.0 390 0\n", "2022-01-18 21:03:42 707.5 101 707.5 0.0 409 0 708.0 0.0 309 0\n", "2022-01-18 21:03:42 707.5 92 707.0 0.0 370 0 707.5 0.0 111 0\n", "2022-01-18 21:03:43 707.5 5 707.0 0.0 381 0 707.5 0.0 139 0\n", "2022-01-18 21:03:43 707.5 6 707.0 0.0 383 0 707.5 0.0 187 0\n", "2022-01-18 21:03:44 707.5 51 707.0 0.0 384 0 707.5 0.0 148 0\n", "2022-01-18 21:03:44 707.5 4 707.0 0.0 386 0 707.5 0.0 148 0\n", "2022-01-18 21:03:45 707.0 15 707.0 0.0 374 0 707.5 0.0 149 0\n", "2022-01-18 21:03:45 707.0 3 707.0 0.0 384 0 707.5 0.0 155 0\n", "2022-01-18 21:03:46 707.0 1 707.0 0.0 379 0 707.5 0.0 170 0\n", "2022-01-18 21:03:46 707.5 15 707.0 0.0 379 0 707.5 0.0 164 0\n", "2022-01-18 21:03:47 707.5 5 707.0 0.0 402 0 707.5 0.0 171 0\n", "2022-01-18 21:03:47 707.0 40 707.0 0.0 368 0 707.5 0.0 156 0\n", "2022-01-18 21:03:48 707.0 5 707.0 0.0 368 0 707.5 0.0 164 0\n", "2022-01-18 21:03:48 707.0 1 707.0 0.0 374 0 707.5 0.0 167 0\n", "2022-01-18 21:03:49 707.0 2 707.0 0.0 382 0 707.5 0.0 189 0\n", "2022-01-18 21:03:49 707.0 4 707.0 0.0 393 0 707.5 0.0 190 0\n", "2022-01-18 21:03:50 707.0 0 707.0 0.0 403 0 707.5 0.0 176 0\n", "2022-01-18 21:03:50 707.5 10 707.0 0.0 403 0 707.5 0.0 168 0\n", "2022-01-18 21:03:51 707.5 171 707.5 0.0 211 0 708.0 0.0 359 0\n", "2022-01-18 21:03:51 707.5 36 707.5 0.0 206 0 708.0 0.0 357 0\n", "2022-01-18 21:03:52 707.5 3 707.5 0.0 204 0 708.0 0.0 358 0\n", "2022-01-18 21:03:52 707.5 9 707.5 0.0 300 0 708.0 0.0 358 0\n", "2022-01-18 21:03:53 708.0 3 707.5 0.0 302 0 708.0 0.0 355 0\n", "2022-01-18 21:03:53 707.5 11 707.5 0.0 296 0 708.0 0.0 355 0\n", "2022-01-18 21:03:54 707.5 3 707.5 0.0 296 0 708.0 0.0 355 0\n", "2022-01-18 21:03:54 707.5 2 707.5 0.0 294 0 708.0 0.0 360 0\n", "2022-01-18 21:03:55 707.5 1 707.5 0.0 293 0 708.0 0.0 371 0\n", "2022-01-18 21:03:55 708.0 6 707.5 0.0 290 0 708.0 0.0 378 0\n", "2022-01-18 21:03:56 707.5 3 707.5 0.0 241 0 708.0 0.0 386 0\n", "2022-01-18 21:03:56 707.5 53 707.5 0.0 278 0 708.0 0.0 335 0\n", "2022-01-18 21:03:57 707.5 0 707.5 0.0 281 0 708.0 0.0 323 0\n", "2022-01-18 21:03:57 707.5 20 707.5 0.0 259 0 708.0 0.0 328 0\n", "2022-01-18 21:03:58 707.5 12 707.5 0.0 249 0 708.0 0.0 326 0\n", "2022-01-18 21:03:58 708.0 41 707.5 0.0 267 0 708.0 0.0 293 0\n", "2022-01-18 21:03:59 707.5 103 707.5 0.0 168 0 708.0 0.0 301 0\n", "2022-01-18 21:03:59 708.0 9 707.5 0.0 171 0 708.0 0.0 298 0\n", "2022-01-18 21:04:00 707.5 135 707.5 0.0 38 0 708.0 0.0 316 0\n", "2022-01-18 21:04:00 708.0 42 707.5 0.0 86 0 708.0 0.0 297 0\n", "2022-01-18 21:04:01 707.5 13 707.5 0.0 243 0 708.0 0.0 319 0\n", "2022-01-18 21:04:01 708.0 4 707.5 0.0 324 0 708.0 0.0 327 0\n", "2022-01-18 21:04:02 707.5 4 707.5 0.0 389 0 708.0 0.0 311 0\n", "2022-01-18 21:04:02 707.5 1 707.5 0.0 409 0 708.0 0.0 320 0\n", "2022-01-18 21:04:03 707.5 16 707.5 0.0 396 0 708.0 0.0 316 0\n", "2022-01-18 21:04:03 707.5 34 707.5 0.0 760 0 708.0 0.0 304 0\n", "2022-01-18 21:04:04 707.5 55 707.5 0.0 747 0 708.0 0.0 257 0\n", "2022-01-18 21:04:04 707.5 12 707.5 0.0 740 0 708.0 0.0 287 0\n", "2022-01-18 21:04:05 708.0 5 707.5 0.0 741 0 708.0 0.0 284 0\n", "2022-01-18 21:04:05 708.0 13 707.5 0.0 742 0 708.0 0.0 277 0\n", "2022-01-18 21:04:06 707.5 4 707.5 0.0 741 0 708.0 0.0 278 0\n", "2022-01-18 21:04:06 708.0 10 707.5 0.0 740 0 708.0 0.0 270 0\n", "2022-01-18 21:04:07 707.5 10 707.5 0.0 736 0 708.0 0.0 276 0\n", "2022-01-18 21:04:07 708.0 11 707.5 0.0 742 0 708.0 0.0 269 0\n", "2022-01-18 21:04:08 708.0 17 707.5 0.0 753 0 708.0 0.0 248 0\n", "2022-01-18 21:04:08 707.5 7 707.5 0.0 747 0 708.0 0.0 253 0\n", "2022-01-18 21:04:09 707.5 11 707.5 0.0 739 0 708.0 0.0 252 0\n", "2022-01-18 21:04:09 708.0 10 707.5 0.0 735 0 708.0 0.0 245 0\n", "2022-01-18 21:04:10 708.0 4 707.5 0.0 736 0 708.0 0.0 239 0\n", "2022-01-18 21:04:10 708.0 9 707.5 0.0 735 0 708.0 0.0 257 0\n", "2022-01-18 21:04:11 707.5 3 707.5 0.0 738 0 708.0 0.0 259 0\n", "2022-01-18 21:04:11 708.0 259 708.0 0.0 65 0 708.5 0.0 769 0\n", "2022-01-18 21:04:12 708.0 13 708.0 0.0 553 0 708.5 0.0 734 0\n", "2022-01-18 21:04:12 708.0 6 708.0 0.0 567 0 708.5 0.0 733 0\n", "2022-01-18 21:04:13 708.5 732 708.5 0.0 525 0 709.0 0.0 829 0\n", "2022-01-18 21:04:13 709.0 34 708.5 0.0 551 0 709.0 0.0 779 0\n", "2022-01-18 21:04:14 709.0 302 708.5 0.0 683 0 709.0 0.0 479 0\n", "2022-01-18 21:04:14 709.0 501 709.0 0.0 294 0 709.5 0.0 382 0\n", "2022-01-18 21:04:15 709.5 37 709.0 0.0 408 0 709.5 0.0 348 0\n", "2022-01-18 21:04:15 709.5 19 709.0 0.0 481 0 709.5 0.0 372 0\n", "2022-01-18 21:04:16 709.5 9 709.0 0.0 489 0 709.5 0.0 404 0\n", "2022-01-18 21:04:16 709.5 14 709.0 0.0 494 0 709.5 0.0 394 0\n", "2022-01-18 21:04:17 709.0 23 709.0 0.0 473 0 709.5 0.0 394 0\n", "2022-01-18 21:04:17 709.5 21 709.0 0.0 478 0 709.5 0.0 391 0\n", "2022-01-18 21:04:18 709.0 29 709.0 0.0 458 0 709.5 0.0 406 0\n", "2022-01-18 21:04:18 709.0 20 709.0 0.0 481 0 709.5 0.0 416 0\n", "2022-01-18 21:04:19 709.5 32 709.0 0.0 519 0 709.5 0.0 390 0\n", "2022-01-18 21:04:19 709.5 13 709.0 0.0 535 0 709.5 0.0 388 0\n", "2022-01-18 21:04:20 709.5 14 709.0 0.0 533 0 709.5 0.0 381 0\n", "2022-01-18 21:04:20 710.0 420 709.5 0.0 490 0 710.0 0.0 899 0\n", "2022-01-18 21:04:21 710.0 401 709.5 0.0 603 0 710.0 0.0 560 0\n", "2022-01-18 21:04:21 710.0 27 709.5 0.0 635 0 710.0 0.0 535 0\n", "2022-01-18 21:04:22 710.5 582 710.0 0.0 653 0 710.5 0.0 406 0\n", "2022-01-18 21:04:22 710.5 364 710.0 0.0 802 0 710.5 0.0 57 0\n", "2022-01-18 21:04:23 711.0 301 710.5 0.0 57 0 711.0 0.0 738 0\n", "2022-01-18 21:04:23 711.0 447 710.5 0.0 167 0 711.0 0.0 381 0\n", "2022-01-18 21:04:24 710.5 23 710.5 0.0 220 0 711.0 0.0 367 0\n", "2022-01-18 21:04:24 711.0 54 710.5 0.0 248 0 711.0 0.0 364 0\n", "2022-01-18 21:04:25 711.0 19 710.5 0.0 249 0 711.0 0.0 438 0\n", "2022-01-18 21:04:25 710.5 117 710.5 0.0 228 0 711.0 0.0 374 0\n", "2022-01-18 21:04:26 711.0 88 710.5 0.0 315 0 711.0 0.0 316 0\n", "2022-01-18 21:04:26 710.5 20 710.5 0.0 321 0 711.0 0.0 375 0\n", "2022-01-18 21:04:27 711.0 99 710.5 0.0 707 0 711.0 0.0 284 0\n", "2022-01-18 21:04:27 711.0 371 710.5 0.0 706 0 711.0 0.0 243 0\n", "2022-01-18 21:04:28 711.0 51 710.5 0.0 717 0 711.0 0.0 224 0\n", "2022-01-18 21:04:28 711.0 69 710.5 0.0 701 0 711.0 0.0 199 0\n", "2022-01-18 21:04:29 711.0 42 710.5 0.0 700 0 711.0 0.0 184 0\n", "2022-01-18 21:04:29 711.0 89 710.5 0.0 653 0 711.0 0.0 227 0\n", "2022-01-18 21:04:30 711.0 27 710.5 0.0 646 0 711.0 0.0 225 0\n", "2022-01-18 21:04:30 711.0 18 710.5 0.0 651 0 711.0 0.0 246 0\n", "2022-01-18 21:04:31 710.5 20 710.5 0.0 641 0 711.0 0.0 338 0\n", "2022-01-18 21:04:31 710.5 228 710.5 0.0 548 0 711.0 0.0 441 0\n", "2022-01-18 21:04:32 711.0 99 710.5 0.0 510 0 711.0 0.0 428 0\n", "2022-01-18 21:04:32 711.0 32 710.5 0.0 529 0 711.0 0.0 451 0\n", "2022-01-18 21:04:33 711.0 31 710.5 0.0 519 0 711.0 0.0 448 0\n", "2022-01-18 21:04:33 711.0 13 710.5 0.0 581 0 711.0 0.0 441 0\n", "2022-01-18 21:04:34 711.0 62 710.5 0.0 592 0 711.0 0.0 392 0\n", "2022-01-18 21:04:34 710.5 15 710.5 0.0 585 0 711.0 0.0 389 0\n", "2022-01-18 21:04:35 710.5 30 710.5 0.0 578 0 711.0 0.0 376 0\n", "2022-01-18 21:04:35 711.0 17 710.5 0.0 578 0 711.0 0.0 375 0\n", "2022-01-18 21:04:36 711.0 69 710.5 0.0 574 0 711.0 0.0 338 0\n", "2022-01-18 21:04:36 711.0 21 710.5 0.0 570 0 711.0 0.0 345 0\n", "2022-01-18 21:04:37 711.0 42 710.5 0.0 565 0 711.0 0.0 319 0\n", "2022-01-18 21:04:37 711.0 239 710.5 0.0 573 0 711.0 0.0 76 0\n", "2022-01-18 21:04:38 711.0 14 710.5 0.0 601 0 711.0 0.0 94 0\n", "2022-01-18 21:04:38 711.0 21 710.5 0.0 607 0 711.0 0.0 102 0\n", "2022-01-18 21:04:39 710.5 48 710.5 0.0 602 0 711.0 0.0 128 0\n", "2022-01-18 21:04:39 711.0 9 710.5 0.0 640 0 711.0 0.0 245 0\n", "2022-01-18 21:04:40 711.0 88 710.5 0.0 629 0 711.0 0.0 225 0\n", "2022-01-18 21:04:40 711.0 29 710.5 0.0 627 0 711.0 0.0 208 0\n", "2022-01-18 21:04:41 711.0 135 710.5 0.0 624 0 711.0 0.0 182 0\n", "2022-01-18 21:04:41 710.5 132 710.5 0.0 641 0 711.0 0.0 74 0\n", "2022-01-18 21:04:42 711.0 56 710.5 0.0 667 0 711.0 0.0 48 0\n", "2022-01-18 21:04:42 711.0 65 711.0 0.0 34 0 711.5 0.0 494 0\n", "2022-01-18 21:04:43 711.0 22 711.0 0.0 89 0 711.5 0.0 494 0\n", "2022-01-18 21:04:43 711.0 138 711.0 0.0 215 0 711.5 0.0 360 0\n", "2022-01-18 21:04:44 711.0 30 711.0 0.0 237 0 711.5 0.0 339 0\n", "2022-01-18 21:04:44 711.5 29 711.0 0.0 251 0 711.5 0.0 320 0\n", "2022-01-18 21:04:45 711.5 651 711.0 0.0 291 0 711.5 0.0 130 0\n", "2022-01-18 21:04:45 711.5 61 711.0 0.0 269 0 711.5 0.0 283 0\n", "2022-01-18 21:04:46 711.5 13 711.0 0.0 246 0 711.5 0.0 325 0\n", "2022-01-18 21:04:46 711.5 123 711.0 0.0 196 0 711.5 0.0 361 0\n", "2022-01-18 21:04:47 711.0 26 711.0 0.0 198 0 711.5 0.0 366 0\n", "2022-01-18 21:04:47 711.5 19 711.0 0.0 242 0 711.5 0.0 386 0\n", "2022-01-18 21:04:48 711.5 19 711.0 0.0 300 0 711.5 0.0 388 0\n", "2022-01-18 21:04:48 711.0 33 711.0 0.0 360 0 711.5 0.0 370 0\n", "2022-01-18 21:04:49 711.5 10 711.0 0.0 352 0 711.5 0.0 378 0\n", "2022-01-18 21:04:49 711.0 19 711.0 0.0 345 0 711.5 0.0 370 0\n", "2022-01-18 21:04:50 711.5 26 711.0 0.0 349 0 711.5 0.0 374 0\n", "2022-01-18 21:04:50 711.5 23 711.0 0.0 319 0 711.5 0.0 409 0\n", "2022-01-18 21:04:51 711.0 9 711.0 0.0 330 0 711.5 0.0 415 0\n", "2022-01-18 21:04:51 711.0 30 711.0 0.0 312 0 711.5 0.0 391 0\n", "2022-01-18 21:04:52 711.0 13 711.0 0.0 309 0 711.5 0.0 400 0\n", "2022-01-18 21:04:52 711.0 4 711.0 0.0 322 0 711.5 0.0 404 0\n", "2022-01-18 21:04:53 711.5 89 711.0 0.0 323 0 711.5 0.0 334 0\n", "2022-01-18 21:04:53 711.5 13 711.0 0.0 323 0 711.5 0.0 330 0\n", "2022-01-18 21:04:54 711.0 25 711.0 0.0 332 0 711.5 0.0 343 0\n", "2022-01-18 21:04:54 711.5 13 711.0 0.0 379 0 711.5 0.0 344 0\n", "2022-01-18 21:04:55 711.5 38 711.0 0.0 371 0 711.5 0.0 329 0\n", "2022-01-18 21:04:55 711.0 62 711.0 0.0 392 0 711.5 0.0 301 0\n", "2022-01-18 21:04:56 711.0 54 711.0 0.0 363 0 711.5 0.0 313 0\n", "2022-01-18 21:04:56 711.0 93 711.0 0.0 308 0 711.5 0.0 327 0\n", "2022-01-18 21:04:57 711.0 18 711.0 0.0 307 0 711.5 0.0 337 0\n", "2022-01-18 21:04:57 711.0 12 711.0 0.0 389 0 711.5 0.0 352 0\n", "2022-01-18 21:04:58 712.0 427 711.5 0.0 47 0 712.0 0.0 626 0\n", "2022-01-18 21:04:58 712.0 1046 712.0 0.0 248 0 712.5 0.0 141 0\n", "2022-01-18 21:04:59 712.0 208 712.0 0.0 218 0 712.5 0.0 284 0\n", "2022-01-18 21:04:59 712.5 8 712.0 0.0 295 0 712.5 0.0 386 0\n", "tick_I8888.DC_5475133 600\n", "2022-01-18 21:05:00 712.5 247 712.0 0.0 219 0 712.5 0.0 453 0\n", "2022-01-18 21:05:00 712.0 139 712.0 0.0 317 0 712.5 0.0 415 0\n", "2022-01-18 21:05:01 712.0 129 712.0 0.0 287 0 712.5 0.0 444 0\n", "2022-01-18 21:05:01 712.5 246 712.0 0.0 220 0 712.5 0.0 409 0\n", "2022-01-18 21:05:02 712.5 390 712.0 0.0 210 0 712.5 0.0 206 0\n", "2022-01-18 21:05:02 712.5 42 712.0 0.0 260 0 712.5 0.0 384 0\n", "2022-01-18 21:05:03 712.0 29 712.0 0.0 305 0 712.5 0.0 483 0\n", "2022-01-18 21:05:03 712.5 27 712.0 0.0 333 0 712.5 0.0 534 0\n", "2022-01-18 21:05:04 712.0 380 711.5 0.0 358 0 712.0 0.0 190 0\n", "2022-01-18 21:05:04 712.0 70 711.5 0.0 282 0 712.0 0.0 306 0\n", "2022-01-18 21:05:05 711.5 43 711.5 0.0 246 0 712.0 0.0 532 0\n", "2022-01-18 21:05:05 711.5 330 711.0 0.0 461 0 711.5 0.0 701 0\n", "2022-01-18 21:05:06 711.5 92 711.0 0.0 436 0 711.5 0.0 655 0\n", "2022-01-18 21:05:06 711.5 23 711.0 0.0 475 0 711.5 0.0 601 0\n", "2022-01-18 21:05:07 711.0 12 711.0 0.0 507 0 711.5 0.0 556 0\n", "2022-01-18 21:05:07 711.0 14 711.0 0.0 545 0 711.5 0.0 517 0\n", "2022-01-18 21:05:08 711.0 55 711.0 0.0 521 0 711.5 0.0 485 0\n", "2022-01-18 21:05:08 711.5 144 711.0 0.0 495 0 711.5 0.0 358 0\n", "2022-01-18 21:05:09 711.5 15 711.0 0.0 501 0 711.5 0.0 347 0\n", "2022-01-18 21:05:09 711.5 25 711.0 0.0 479 0 711.5 0.0 357 0\n", "2022-01-18 21:05:10 711.0 18 711.0 0.0 488 0 711.5 0.0 365 0\n", "2022-01-18 21:05:10 711.0 49 711.0 0.0 483 0 711.5 0.0 348 0\n", "2022-01-18 21:05:11 711.0 20 711.0 0.0 495 0 711.5 0.0 345 0\n", "2022-01-18 21:05:11 711.0 24 711.0 0.0 541 0 711.5 0.0 352 0\n", "2022-01-18 21:05:12 711.0 21 711.0 0.0 556 0 711.5 0.0 345 0\n", "2022-01-18 21:05:12 711.5 24 711.0 0.0 537 0 711.5 0.0 379 0\n", "2022-01-18 21:05:13 711.0 272 711.0 0.0 531 0 711.5 0.0 183 0\n", "2022-01-18 21:05:13 711.5 104 711.0 0.0 482 0 711.5 0.0 210 0\n", "2022-01-18 21:05:14 711.5 10 711.0 0.0 514 0 711.5 0.0 223 0\n", "2022-01-18 21:05:14 711.0 56 711.0 0.0 477 0 711.5 0.0 215 0\n", "2022-01-18 21:05:15 711.5 21 711.0 0.0 486 0 711.5 0.0 233 0\n", "2022-01-18 21:05:15 711.0 68 711.0 0.0 544 0 711.5 0.0 176 0\n", "2022-01-18 21:05:16 711.5 64 711.0 0.0 539 0 711.5 0.0 124 0\n", "2022-01-18 21:05:16 711.0 16 711.0 0.0 608 0 711.5 0.0 136 0\n", "2022-01-18 21:05:17 711.5 152 711.5 0.0 67 0 712.0 0.0 797 0\n", "2022-01-18 21:05:17 711.5 12 711.5 0.0 339 0 712.0 0.0 763 0\n", "2022-01-18 21:05:18 711.5 22 711.5 0.0 381 0 712.0 0.0 649 0\n", "2022-01-18 21:05:18 711.5 66 711.5 0.0 366 0 712.0 0.0 629 0\n", "2022-01-18 21:05:19 712.0 11 711.5 0.0 368 0 712.0 0.0 602 0\n", "2022-01-18 21:05:19 712.0 18 711.5 0.0 395 0 712.0 0.0 600 0\n", "2022-01-18 21:05:20 711.5 17 711.5 0.0 415 0 712.0 0.0 630 0\n", "2022-01-18 21:05:20 712.0 50 711.5 0.0 418 0 712.0 0.0 612 0\n", "2022-01-18 21:05:21 711.5 28 711.5 0.0 421 0 712.0 0.0 589 0\n", "2022-01-18 21:05:21 712.0 7 711.5 0.0 420 0 712.0 0.0 748 0\n", "2022-01-18 21:05:22 712.0 10 711.5 0.0 415 0 712.0 0.0 742 0\n", "2022-01-18 21:05:22 711.5 73 711.5 0.0 458 0 712.0 0.0 735 0\n", "2022-01-18 21:05:23 711.5 20 711.5 0.0 448 0 712.0 0.0 734 0\n", "2022-01-18 21:05:23 711.5 37 711.5 0.0 413 0 712.0 0.0 736 0\n", "2022-01-18 21:05:24 711.5 133 711.5 0.0 297 0 712.0 0.0 756 0\n", "2022-01-18 21:05:24 711.5 75 711.5 0.0 237 0 712.0 0.0 793 0\n", "2022-01-18 21:05:25 712.0 16 711.5 0.0 235 0 712.0 0.0 789 0\n", "2022-01-18 21:05:25 711.5 19 711.5 0.0 264 0 712.0 0.0 783 0\n", "2022-01-18 21:05:26 711.5 12 711.5 0.0 259 0 712.0 0.0 784 0\n", "2022-01-18 21:05:26 711.5 11 711.5 0.0 283 0 712.0 0.0 812 0\n", "2022-01-18 21:05:27 712.0 13 711.5 0.0 284 0 712.0 0.0 834 0\n", "2022-01-18 21:05:27 711.5 45 711.5 0.0 264 0 712.0 0.0 831 0\n", "2022-01-18 21:05:28 711.5 39 711.5 0.0 306 0 712.0 0.0 835 0\n", "2022-01-18 21:05:28 711.5 17 711.5 0.0 290 0 712.0 0.0 837 0\n", "2022-01-18 21:05:29 711.5 1 711.5 0.0 290 0 712.0 0.0 847 0\n", "2022-01-18 21:05:29 712.0 18 711.5 0.0 303 0 712.0 0.0 836 0\n", "2022-01-18 21:05:30 711.5 20 711.5 0.0 301 0 712.0 0.0 836 0\n", "2022-01-18 21:05:30 712.0 65 711.5 0.0 391 0 712.0 0.0 802 0\n", "2022-01-18 21:05:31 711.5 61 711.5 0.0 423 0 712.0 0.0 744 0\n", "2022-01-18 21:05:31 712.0 122 711.5 0.0 416 0 712.0 0.0 637 0\n", "2022-01-18 21:05:32 712.0 93 711.5 0.0 415 0 712.0 0.0 547 0\n", "2022-01-18 21:05:32 712.0 101 711.5 0.0 417 0 712.0 0.0 455 0\n", "2022-01-18 21:05:33 712.0 74 711.5 0.0 410 0 712.0 0.0 377 0\n", "2022-01-18 21:05:33 712.0 145 711.5 0.0 412 0 712.0 0.0 293 0\n", "2022-01-18 21:05:34 712.0 99 711.5 0.0 412 0 712.0 0.0 202 0\n", "2022-01-18 21:05:34 712.0 104 711.5 0.0 418 0 712.0 0.0 115 0\n", "2022-01-18 21:05:35 712.0 114 712.0 0.0 297 0 712.5 0.0 644 0\n", "2022-01-18 21:05:35 712.0 224 712.0 0.0 171 0 712.5 0.0 543 0\n", "2022-01-18 21:05:36 712.0 202 711.5 0.0 483 0 712.0 0.0 15 0\n", "2022-01-18 21:05:36 712.0 98 711.5 0.0 530 0 712.0 0.0 132 0\n", "2022-01-18 21:05:37 712.0 24 711.5 0.0 500 0 712.0 0.0 137 0\n", "2022-01-18 21:05:37 712.0 40 711.5 0.0 485 0 712.0 0.0 219 0\n", "2022-01-18 21:05:38 712.0 179 711.5 0.0 394 0 712.0 0.0 176 0\n", "2022-01-18 21:05:38 712.0 44 711.5 0.0 400 0 712.0 0.0 159 0\n", "2022-01-18 21:05:39 712.0 33 711.5 0.0 403 0 712.0 0.0 187 0\n", "2022-01-18 21:05:39 712.0 10 711.5 0.0 553 0 712.0 0.0 212 0\n", "2022-01-18 21:05:40 711.5 4 711.5 0.0 651 0 712.0 0.0 218 0\n", "2022-01-18 21:05:40 711.5 74 711.5 0.0 588 0 712.0 0.0 226 0\n", "2022-01-18 21:05:41 712.0 14 711.5 0.0 596 0 712.0 0.0 180 0\n", "2022-01-18 21:05:41 711.5 37 711.5 0.0 566 0 712.0 0.0 203 0\n", "2022-01-18 21:05:42 712.0 9 711.5 0.0 564 0 712.0 0.0 202 0\n", "2022-01-18 21:05:42 712.0 0 711.5 0.0 579 0 712.0 0.0 218 0\n", "2022-01-18 21:05:43 711.5 11 711.5 0.0 569 0 712.0 0.0 217 0\n", "2022-01-18 21:05:43 711.5 26 711.5 0.0 616 0 712.0 0.0 212 0\n", "2022-01-18 21:05:44 712.0 3 711.5 0.0 620 0 712.0 0.0 198 0\n", "2022-01-18 21:05:44 712.0 6 711.5 0.0 618 0 712.0 0.0 200 0\n", "2022-01-18 21:05:45 712.0 20 711.5 0.0 618 0 712.0 0.0 186 0\n", "2022-01-18 21:05:45 711.5 30 711.5 0.0 598 0 712.0 0.0 186 0\n", "2022-01-18 21:05:46 712.0 32 711.5 0.0 619 0 712.0 0.0 174 0\n", "2022-01-18 21:05:46 712.0 29 711.5 0.0 615 0 712.0 0.0 167 0\n", "2022-01-18 21:05:47 711.5 23 711.5 0.0 598 0 712.0 0.0 170 0\n", "2022-01-18 21:05:47 711.5 10 711.5 0.0 594 0 712.0 0.0 285 0\n", "2022-01-18 21:05:48 711.5 141 711.5 0.0 581 0 712.0 0.0 193 0\n", "2022-01-18 21:05:48 711.5 3 711.5 0.0 565 0 712.0 0.0 467 0\n", "2022-01-18 21:05:49 712.0 19 711.5 0.0 553 0 712.0 0.0 474 0\n", "2022-01-18 21:05:49 711.5 10 711.5 0.0 570 0 712.0 0.0 473 0\n", "2022-01-18 21:05:50 711.5 15 711.5 0.0 556 0 712.0 0.0 475 0\n", "2022-01-18 21:05:50 711.5 8 711.5 0.0 556 0 712.0 0.0 483 0\n", "2022-01-18 21:05:51 711.5 5 711.5 0.0 536 0 712.0 0.0 484 0\n", "2022-01-18 21:05:51 712.0 12 711.5 0.0 413 0 712.0 0.0 487 0\n", "2022-01-18 21:05:52 711.5 34 711.5 0.0 382 0 712.0 0.0 607 0\n", "2022-01-18 21:05:52 711.5 11 711.5 0.0 371 0 712.0 0.0 623 0\n", "2022-01-18 21:05:53 712.0 24 711.5 0.0 431 0 712.0 0.0 613 0\n", "2022-01-18 21:05:53 712.0 159 711.5 0.0 379 0 712.0 0.0 516 0\n", "2022-01-18 21:05:54 711.5 15 711.5 0.0 366 0 712.0 0.0 516 0\n", "2022-01-18 21:05:54 711.5 60 711.5 0.0 322 0 712.0 0.0 528 0\n", "2022-01-18 21:05:55 711.5 22 711.5 0.0 326 0 712.0 0.0 520 0\n", "2022-01-18 21:05:55 711.5 19 711.5 0.0 310 0 712.0 0.0 517 0\n", "2022-01-18 21:05:56 712.0 54 711.5 0.0 386 0 712.0 0.0 476 0\n", "2022-01-18 21:05:56 711.5 25 711.5 0.0 384 0 712.0 0.0 492 0\n", "2022-01-18 21:05:57 712.0 3 711.5 0.0 382 0 712.0 0.0 505 0\n", "2022-01-18 21:05:57 712.0 2 711.5 0.0 382 0 712.0 0.0 508 0\n", "2022-01-18 21:05:58 711.5 9 711.5 0.0 387 0 712.0 0.0 504 0\n", "2022-01-18 21:05:58 711.5 23 711.5 0.0 398 0 712.0 0.0 490 0\n", "2022-01-18 21:05:59 711.5 4 711.5 0.0 395 0 712.0 0.0 493 0\n", "2022-01-18 21:05:59 711.5 31 711.5 0.0 391 0 712.0 0.0 483 0\n", "2022-01-18 21:06:00 712.0 47 711.5 0.0 387 0 712.0 0.0 448 0\n", "2022-01-18 21:06:00 712.0 48 711.5 0.0 365 0 712.0 0.0 434 0\n", "2022-01-18 21:06:01 712.0 4 711.5 0.0 374 0 712.0 0.0 478 0\n", "2022-01-18 21:06:01 712.0 27 711.5 0.0 390 0 712.0 0.0 515 0\n", "2022-01-18 21:06:02 711.5 20 711.5 0.0 386 0 712.0 0.0 519 0\n", "2022-01-18 21:06:02 711.5 16 711.5 0.0 374 0 712.0 0.0 537 0\n", "2022-01-18 21:06:03 712.0 7 711.5 0.0 375 0 712.0 0.0 533 0\n", "2022-01-18 21:06:03 712.0 6 711.5 0.0 374 0 712.0 0.0 534 0\n", "2022-01-18 21:06:04 711.5 22 711.5 0.0 353 0 712.0 0.0 537 0\n", "2022-01-18 21:06:04 712.0 20 711.5 0.0 377 0 712.0 0.0 552 0\n", "2022-01-18 21:06:05 712.0 24 711.5 0.0 386 0 712.0 0.0 539 0\n", "2022-01-18 21:06:05 711.5 4 711.5 0.0 385 0 712.0 0.0 547 0\n", "2022-01-18 21:06:06 711.5 3 711.5 0.0 392 0 712.0 0.0 548 0\n", "2022-01-18 21:06:06 711.5 6 711.5 0.0 388 0 712.0 0.0 550 0\n", "2022-01-18 21:06:07 712.0 30 711.5 0.0 393 0 712.0 0.0 723 0\n", "2022-01-18 21:06:07 711.5 5 711.5 0.0 396 0 712.0 0.0 725 0\n", "2022-01-18 21:06:08 712.0 4 711.5 0.0 433 0 712.0 0.0 737 0\n", "2022-01-18 21:06:08 711.5 10 711.5 0.0 430 0 712.0 0.0 747 0\n", "2022-01-18 21:06:09 711.5 447 711.0 0.0 821 0 711.5 0.0 96 0\n", "2022-01-18 21:06:09 711.0 15 711.0 0.0 833 0 711.5 0.0 375 0\n", "2022-01-18 21:06:10 711.5 26 711.0 0.0 845 0 711.5 0.0 385 0\n", "2022-01-18 21:06:10 711.5 20 711.0 0.0 850 0 711.5 0.0 474 0\n", "2022-01-18 21:06:11 711.5 1 711.0 0.0 858 0 711.5 0.0 541 0\n", "2022-01-18 21:06:11 711.5 4 711.0 0.0 857 0 711.5 0.0 544 0\n", "2022-01-18 21:06:12 711.0 9 711.0 0.0 851 0 711.5 0.0 555 0\n", "2022-01-18 21:06:12 711.5 157 711.0 0.0 727 0 711.5 0.0 324 0\n", "2022-01-18 21:06:13 711.0 4 711.0 0.0 728 0 711.5 0.0 357 0\n", "2022-01-18 21:06:13 711.0 15 711.0 0.0 721 0 711.5 0.0 360 0\n", "2022-01-18 21:06:14 711.5 11 711.0 0.0 711 0 711.5 0.0 367 0\n", "2022-01-18 21:06:14 711.0 6 711.0 0.0 714 0 711.5 0.0 368 0\n", "2022-01-18 21:06:15 711.0 17 711.0 0.0 699 0 711.5 0.0 382 0\n", "2022-01-18 21:06:15 711.5 56 711.0 0.0 698 0 711.5 0.0 363 0\n", "2022-01-18 21:06:16 711.0 13 711.0 0.0 688 0 711.5 0.0 365 0\n", "2022-01-18 21:06:16 711.5 8 711.0 0.0 684 0 711.5 0.0 384 0\n", "2022-01-18 21:06:17 711.0 12 711.0 0.0 679 0 711.5 0.0 407 0\n", "2022-01-18 21:06:17 711.5 32 711.0 0.0 674 0 711.5 0.0 383 0\n", "2022-01-18 21:06:18 711.0 54 711.0 0.0 671 0 711.5 0.0 337 0\n", "2022-01-18 21:06:18 711.0 27 711.0 0.0 652 0 711.5 0.0 339 0\n", "2022-01-18 21:06:19 711.0 2 711.0 0.0 650 0 711.5 0.0 342 0\n", "2022-01-18 21:06:19 711.5 72 711.0 0.0 699 0 711.5 0.0 326 0\n", "2022-01-18 21:06:20 711.0 5 711.0 0.0 697 0 711.5 0.0 338 0\n", "2022-01-18 21:06:20 711.0 106 711.0 0.0 638 0 711.5 0.0 290 0\n", "2022-01-18 21:06:21 711.5 10 711.0 0.0 697 0 711.5 0.0 311 0\n", "2022-01-18 21:06:21 711.0 86 711.0 0.0 682 0 711.5 0.0 250 0\n", "2022-01-18 21:06:22 711.5 19 711.0 0.0 692 0 711.5 0.0 234 0\n", "2022-01-18 21:06:22 711.5 247 711.5 0.0 764 0 712.0 0.0 817 0\n", "2022-01-18 21:06:23 711.5 13 711.5 0.0 782 0 712.0 0.0 799 0\n", "2022-01-18 21:06:23 711.5 7 711.5 0.0 844 0 712.0 0.0 781 0\n", "2022-01-18 21:06:24 711.5 11 711.5 0.0 842 0 712.0 0.0 782 0\n", "2022-01-18 21:06:24 711.5 35 711.5 0.0 831 0 712.0 0.0 785 0\n", "2022-01-18 21:06:25 711.5 9 711.5 0.0 590 0 712.0 0.0 786 0\n", "2022-01-18 21:06:25 711.5 33 711.5 0.0 557 0 712.0 0.0 787 0\n", "2022-01-18 21:06:26 711.5 59 711.5 0.0 499 0 712.0 0.0 792 0\n", "2022-01-18 21:06:26 712.0 7 711.5 0.0 507 0 712.0 0.0 811 0\n", "2022-01-18 21:06:27 712.0 2 711.5 0.0 507 0 712.0 0.0 815 0\n", "2022-01-18 21:06:27 712.0 18 711.5 0.0 495 0 712.0 0.0 803 0\n", "2022-01-18 21:06:28 712.0 96 711.5 0.0 403 0 712.0 0.0 803 0\n", "2022-01-18 21:06:28 712.0 13 711.5 0.0 444 0 712.0 0.0 840 0\n", "2022-01-18 21:06:29 711.5 2 711.5 0.0 436 0 712.0 0.0 843 0\n", "2022-01-18 21:06:29 711.5 127 711.5 0.0 226 0 712.0 0.0 841 0\n", "2022-01-18 21:06:30 711.5 239 711.0 0.0 708 0 711.5 0.0 144 0\n", "2022-01-18 21:06:30 711.0 17 711.0 0.0 695 0 711.5 0.0 255 0\n", "2022-01-18 21:06:31 711.0 12 711.0 0.0 691 0 711.5 0.0 282 0\n", "2022-01-18 21:06:31 711.5 3 711.0 0.0 691 0 711.5 0.0 294 0\n", "2022-01-18 21:06:32 711.0 121 711.0 0.0 667 0 711.5 0.0 197 0\n", "2022-01-18 21:06:32 711.5 12 711.0 0.0 683 0 711.5 0.0 188 0\n", "2022-01-18 21:06:33 711.5 198 711.5 0.0 39 0 712.0 0.0 817 0\n", "2022-01-18 21:06:33 711.5 43 711.5 0.0 198 0 712.0 0.0 798 0\n", "2022-01-18 21:06:34 711.5 131 711.5 0.0 104 0 712.0 0.0 790 0\n", "2022-01-18 21:06:34 711.5 42 711.5 0.0 40 0 712.0 0.0 804 0\n", "2022-01-18 21:06:35 711.0 43 711.0 0.0 667 0 711.5 0.0 200 0\n", "2022-01-18 21:06:35 711.0 8 711.0 0.0 658 0 711.5 0.0 236 0\n", "2022-01-18 21:06:36 711.5 2 711.0 0.0 660 0 711.5 0.0 245 0\n", "2022-01-18 21:06:36 711.0 10 711.0 0.0 665 0 711.5 0.0 243 0\n", "2022-01-18 21:06:37 711.0 4 711.0 0.0 663 0 711.5 0.0 249 0\n", "2022-01-18 21:06:37 711.0 27 711.0 0.0 673 0 711.5 0.0 228 0\n", "2022-01-18 21:06:38 711.0 13 711.0 0.0 665 0 711.5 0.0 210 0\n", "2022-01-18 21:06:38 711.0 31 711.0 0.0 650 0 711.5 0.0 211 0\n", "2022-01-18 21:06:39 711.5 15 711.0 0.0 644 0 711.5 0.0 235 0\n", "2022-01-18 21:06:39 711.5 1 711.0 0.0 646 0 711.5 0.0 236 0\n", "2022-01-18 21:06:40 711.0 214 711.0 0.0 435 0 711.5 0.0 242 0\n", "2022-01-18 21:06:40 711.0 304 711.0 0.0 144 0 711.5 0.0 279 0\n", "2022-01-18 21:06:41 711.0 190 710.5 0.0 733 0 711.0 0.0 181 0\n", "2022-01-18 21:06:41 710.5 30 710.5 0.0 724 0 711.0 0.0 204 0\n", "2022-01-18 21:06:42 710.5 30 710.5 0.0 696 0 711.0 0.0 212 0\n", "2022-01-18 21:06:42 711.0 9 710.5 0.0 690 0 711.0 0.0 230 0\n", "2022-01-18 21:06:43 711.0 14 710.5 0.0 659 0 711.0 0.0 235 0\n", "2022-01-18 21:06:43 711.0 35 710.5 0.0 663 0 711.0 0.0 210 0\n", "2022-01-18 21:06:44 711.0 71 710.5 0.0 661 0 711.0 0.0 146 0\n", "2022-01-18 21:06:44 710.5 8 710.5 0.0 663 0 711.0 0.0 162 0\n", "2022-01-18 21:06:45 710.5 25 710.5 0.0 666 0 711.0 0.0 205 0\n", "2022-01-18 21:06:45 710.5 8 710.5 0.0 684 0 711.0 0.0 207 0\n", "2022-01-18 21:06:46 711.0 21 710.5 0.0 675 0 711.0 0.0 194 0\n", "2022-01-18 21:06:46 710.5 7 710.5 0.0 671 0 711.0 0.0 202 0\n", "2022-01-18 21:06:47 710.5 30 710.5 0.0 647 0 711.0 0.0 323 0\n", "2022-01-18 21:06:47 710.5 3 710.5 0.0 647 0 711.0 0.0 329 0\n", "2022-01-18 21:06:48 711.0 100 710.5 0.0 653 0 711.0 0.0 226 0\n", "2022-01-18 21:06:48 711.0 12 710.5 0.0 647 0 711.0 0.0 240 0\n", "2022-01-18 21:06:49 710.5 7 710.5 0.0 645 0 711.0 0.0 245 0\n", "2022-01-18 21:06:49 710.5 3 710.5 0.0 670 0 711.0 0.0 253 0\n", "2022-01-18 21:06:50 711.0 10 710.5 0.0 671 0 711.0 0.0 256 0\n", "2022-01-18 21:06:50 710.5 4 710.5 0.0 671 0 711.0 0.0 263 0\n", "2022-01-18 21:06:51 710.5 1 710.5 0.0 675 0 711.0 0.0 269 0\n", "2022-01-18 21:06:51 710.5 124 710.5 0.0 559 0 711.0 0.0 279 0\n", "2022-01-18 21:06:52 711.0 20 710.5 0.0 563 0 711.0 0.0 265 0\n", "2022-01-18 21:06:52 710.5 1 710.5 0.0 574 0 711.0 0.0 265 0\n", "2022-01-18 21:06:53 711.0 10 710.5 0.0 567 0 711.0 0.0 260 0\n", "2022-01-18 21:06:53 711.0 6 710.5 0.0 563 0 711.0 0.0 334 0\n", "2022-01-18 21:06:54 711.0 4 710.5 0.0 570 0 711.0 0.0 334 0\n", "2022-01-18 21:06:54 710.5 4 710.5 0.0 568 0 711.0 0.0 335 0\n", "2022-01-18 21:06:55 710.5 24 710.5 0.0 545 0 711.0 0.0 345 0\n", "2022-01-18 21:06:55 710.5 7 710.5 0.0 551 0 711.0 0.0 364 0\n", "2022-01-18 21:06:56 710.5 3 710.5 0.0 555 0 711.0 0.0 366 0\n", "2022-01-18 21:06:56 710.5 3 710.5 0.0 552 0 711.0 0.0 363 0\n", "2022-01-18 21:06:57 710.5 5 710.5 0.0 571 0 711.0 0.0 376 0\n", "2022-01-18 21:06:57 710.5 1 710.5 0.0 576 0 711.0 0.0 384 0\n", "2022-01-18 21:06:58 710.5 6 710.5 0.0 574 0 711.0 0.0 485 0\n", "2022-01-18 21:06:58 711.0 4 710.5 0.0 573 0 711.0 0.0 490 0\n", "2022-01-18 21:06:59 710.5 12 710.5 0.0 561 0 711.0 0.0 479 0\n", "2022-01-18 21:06:59 710.5 66 710.5 0.0 499 0 711.0 0.0 480 0\n", "2022-01-18 21:07:00 711.0 113 710.5 0.0 548 0 711.0 0.0 389 0\n", "2022-01-18 21:07:00 710.5 135 710.5 0.0 539 0 711.0 0.0 276 0\n", "2022-01-18 21:07:01 710.5 10 710.5 0.0 550 0 711.0 0.0 281 0\n", "2022-01-18 21:07:01 710.5 8 710.5 0.0 543 0 711.0 0.0 404 0\n", "2022-01-18 21:07:02 710.5 4 710.5 0.0 539 0 711.0 0.0 420 0\n", "2022-01-18 21:07:02 711.0 24 710.5 0.0 538 0 711.0 0.0 414 0\n", "2022-01-18 21:07:03 710.5 2 710.5 0.0 539 0 711.0 0.0 421 0\n", "2022-01-18 21:07:03 710.5 6 710.5 0.0 535 0 711.0 0.0 428 0\n", "2022-01-18 21:07:04 711.0 5 710.5 0.0 513 0 711.0 0.0 430 0\n", "2022-01-18 21:07:04 710.5 3 710.5 0.0 511 0 711.0 0.0 430 0\n", "2022-01-18 21:07:05 710.5 137 710.5 0.0 384 0 711.0 0.0 442 0\n", "2022-01-18 21:07:05 710.5 11 710.5 0.0 373 0 711.0 0.0 443 0\n", "2022-01-18 21:07:06 710.5 243 710.5 0.0 122 0 711.0 0.0 519 0\n", "2022-01-18 21:07:06 710.5 116 710.5 0.0 16 0 711.0 0.0 529 0\n", "2022-01-18 21:07:07 710.5 139 710.5 0.0 5 0 711.0 0.0 444 0\n", "2022-01-18 21:07:07 710.5 5 710.5 0.0 302 0 711.0 0.0 426 0\n", "2022-01-18 21:07:08 710.5 101 710.5 0.0 265 0 711.0 0.0 437 0\n", "2022-01-18 21:07:08 711.0 7 710.5 0.0 407 0 711.0 0.0 440 0\n", "2022-01-18 21:07:09 711.0 7 710.5 0.0 445 0 711.0 0.0 494 0\n", "2022-01-18 21:07:09 711.0 4 710.5 0.0 449 0 711.0 0.0 498 0\n", "2022-01-18 21:07:10 710.5 10 710.5 0.0 447 0 711.0 0.0 495 0\n", "2022-01-18 21:07:10 710.5 22 710.5 0.0 427 0 711.0 0.0 497 0\n", "2022-01-18 21:07:11 710.5 3 710.5 0.0 427 0 711.0 0.0 512 0\n", "2022-01-18 21:07:11 711.0 9 710.5 0.0 427 0 711.0 0.0 520 0\n", "2022-01-18 21:07:12 711.0 9 710.5 0.0 423 0 711.0 0.0 515 0\n", "2022-01-18 21:07:12 711.0 16 710.5 0.0 431 0 711.0 0.0 500 0\n", "2022-01-18 21:07:13 710.5 5 710.5 0.0 430 0 711.0 0.0 497 0\n", "2022-01-18 21:07:13 710.5 32 710.5 0.0 405 0 711.0 0.0 488 0\n", "2022-01-18 21:07:14 710.5 2 710.5 0.0 410 0 711.0 0.0 495 0\n", "2022-01-18 21:07:14 710.5 7 710.5 0.0 407 0 711.0 0.0 495 0\n", "2022-01-18 21:07:15 711.0 6 710.5 0.0 404 0 711.0 0.0 494 0\n", "2022-01-18 21:07:15 710.5 2 710.5 0.0 409 0 711.0 0.0 496 0\n", "2022-01-18 21:07:16 710.5 17 710.5 0.0 443 0 711.0 0.0 500 0\n", "2022-01-18 21:07:16 710.5 48 710.5 0.0 448 0 711.0 0.0 454 0\n", "2022-01-18 21:07:17 710.5 9 710.5 0.0 452 0 711.0 0.0 455 0\n", "2022-01-18 21:07:17 710.5 21 710.5 0.0 453 0 711.0 0.0 439 0\n", "2022-01-18 21:07:18 711.0 3 710.5 0.0 453 0 711.0 0.0 440 0\n", "2022-01-18 21:07:18 711.0 46 710.5 0.0 459 0 711.0 0.0 401 0\n", "2022-01-18 21:07:19 711.0 7 710.5 0.0 465 0 711.0 0.0 400 0\n", "2022-01-18 21:07:19 711.0 107 710.5 0.0 462 0 711.0 0.0 443 0\n", "2022-01-18 21:07:20 711.0 4 710.5 0.0 463 0 711.0 0.0 438 0\n", "2022-01-18 21:07:20 711.0 2 710.5 0.0 467 0 711.0 0.0 440 0\n", "2022-01-18 21:07:21 711.0 4 710.5 0.0 466 0 711.0 0.0 445 0\n", "2022-01-18 21:07:21 710.5 57 710.5 0.0 473 0 711.0 0.0 398 0\n", "2022-01-18 21:07:22 711.0 56 710.5 0.0 476 0 711.0 0.0 377 0\n", "2022-01-18 21:07:22 711.0 26 710.5 0.0 464 0 711.0 0.0 378 0\n", "2022-01-18 21:07:23 711.0 11 710.5 0.0 460 0 711.0 0.0 379 0\n", "2022-01-18 21:07:23 710.5 1 710.5 0.0 464 0 711.0 0.0 384 0\n", "2022-01-18 21:07:24 711.0 24 710.5 0.0 448 0 711.0 0.0 480 0\n", "2022-01-18 21:07:24 710.5 8 710.5 0.0 450 0 711.0 0.0 488 0\n", "2022-01-18 21:07:25 711.0 32 710.5 0.0 434 0 711.0 0.0 486 0\n", "2022-01-18 21:07:25 710.5 2 710.5 0.0 436 0 711.0 0.0 486 0\n", "2022-01-18 21:07:26 710.5 5 710.5 0.0 432 0 711.0 0.0 487 0\n", "2022-01-18 21:07:26 710.5 9 710.5 0.0 453 0 711.0 0.0 484 0\n", "2022-01-18 21:07:27 710.5 53 710.5 0.0 460 0 711.0 0.0 466 0\n", "2022-01-18 21:07:27 710.5 7 710.5 0.0 459 0 711.0 0.0 467 0\n", "2022-01-18 21:07:28 711.0 55 710.5 0.0 458 0 711.0 0.0 414 0\n", "2022-01-18 21:07:28 710.5 8 710.5 0.0 451 0 711.0 0.0 415 0\n", "2022-01-18 21:07:29 710.5 5 710.5 0.0 448 0 711.0 0.0 416 0\n", "2022-01-18 21:07:29 711.0 3 710.5 0.0 453 0 711.0 0.0 414 0\n", "2022-01-18 21:07:30 710.5 4 710.5 0.0 451 0 711.0 0.0 416 0\n", "2022-01-18 21:07:30 710.5 4 710.5 0.0 465 0 711.0 0.0 416 0\n", "2022-01-18 21:07:31 710.5 15 710.5 0.0 544 0 711.0 0.0 418 0\n", "2022-01-18 21:07:31 710.5 7 710.5 0.0 539 0 711.0 0.0 420 0\n", "2022-01-18 21:07:32 710.5 16 710.5 0.0 528 0 711.0 0.0 418 0\n", "2022-01-18 21:07:32 711.0 65 710.5 0.0 518 0 711.0 0.0 376 0\n", "2022-01-18 21:07:33 710.5 10 710.5 0.0 515 0 711.0 0.0 374 0\n", "2022-01-18 21:07:33 711.0 2 710.5 0.0 525 0 711.0 0.0 383 0\n", "2022-01-18 21:07:34 711.0 3 710.5 0.0 538 0 711.0 0.0 381 0\n", "2022-01-18 21:07:34 710.5 7 710.5 0.0 537 0 711.0 0.0 377 0\n", "2022-01-18 21:07:35 711.0 3 710.5 0.0 490 0 711.0 0.0 380 0\n", "2022-01-18 21:07:35 711.0 63 710.5 0.0 484 0 711.0 0.0 328 0\n", "2022-01-18 21:07:36 710.5 6 710.5 0.0 483 0 711.0 0.0 329 0\n", "2022-01-18 21:07:36 711.0 53 710.5 0.0 485 0 711.0 0.0 278 0\n", "2022-01-18 21:07:37 711.0 12 710.5 0.0 482 0 711.0 0.0 274 0\n", "2022-01-18 21:07:37 710.5 76 710.5 0.0 482 0 711.0 0.0 202 0\n", "2022-01-18 21:07:38 711.0 7 710.5 0.0 488 0 711.0 0.0 195 0\n", "2022-01-18 21:07:38 710.5 3 710.5 0.0 485 0 711.0 0.0 197 0\n", "2022-01-18 21:07:39 711.0 17 710.5 0.0 475 0 711.0 0.0 186 0\n", "2022-01-18 21:07:39 711.0 3 710.5 0.0 474 0 711.0 0.0 235 0\n", "2022-01-18 21:07:40 711.0 5 710.5 0.0 478 0 711.0 0.0 259 0\n", "2022-01-18 21:07:40 711.0 8 710.5 0.0 481 0 711.0 0.0 314 0\n", "2022-01-18 21:07:41 711.0 3 710.5 0.0 489 0 711.0 0.0 343 0\n", "2022-01-18 21:07:41 711.0 8 710.5 0.0 487 0 711.0 0.0 343 0\n", "2022-01-18 21:07:42 711.0 12 710.5 0.0 483 0 711.0 0.0 340 0\n", "2022-01-18 21:07:42 711.0 7 710.5 0.0 483 0 711.0 0.0 349 0\n", "2022-01-18 21:07:43 711.0 9 710.5 0.0 488 0 711.0 0.0 354 0\n", "2022-01-18 21:07:43 711.0 4 710.5 0.0 493 0 711.0 0.0 355 0\n", "2022-01-18 21:07:44 710.5 19 710.5 0.0 480 0 711.0 0.0 333 0\n", "2022-01-18 21:07:44 710.5 24 710.5 0.0 459 0 711.0 0.0 336 0\n", "2022-01-18 21:07:45 711.0 22 710.5 0.0 453 0 711.0 0.0 327 0\n", "2022-01-18 21:07:45 710.5 8 710.5 0.0 458 0 711.0 0.0 326 0\n", "2022-01-18 21:07:46 711.0 8 710.5 0.0 459 0 711.0 0.0 327 0\n", "2022-01-18 21:07:46 710.5 12 710.5 0.0 469 0 711.0 0.0 323 0\n", "2022-01-18 21:07:47 710.5 69 710.5 0.0 462 0 711.0 0.0 261 0\n", "2022-01-18 21:07:47 710.5 6 710.5 0.0 467 0 711.0 0.0 266 0\n", "2022-01-18 21:07:48 710.5 6 710.5 0.0 462 0 711.0 0.0 272 0\n", "2022-01-18 21:07:48 710.5 10 710.5 0.0 461 0 711.0 0.0 266 0\n", "2022-01-18 21:07:49 711.0 7 710.5 0.0 463 0 711.0 0.0 259 0\n", "2022-01-18 21:07:49 711.0 14 710.5 0.0 466 0 711.0 0.0 273 0\n", "2022-01-18 21:07:50 710.5 5 710.5 0.0 463 0 711.0 0.0 272 0\n", "2022-01-18 21:07:50 711.0 2 710.5 0.0 467 0 711.0 0.0 266 0\n", "2022-01-18 21:07:51 711.0 12 710.5 0.0 483 0 711.0 0.0 266 0\n", "2022-01-18 21:07:51 710.5 34 710.5 0.0 451 0 711.0 0.0 264 0\n", "2022-01-18 21:07:52 711.0 7 710.5 0.0 453 0 711.0 0.0 260 0\n", "2022-01-18 21:07:52 711.0 14 710.5 0.0 445 0 711.0 0.0 253 0\n", "2022-01-18 21:07:53 710.5 4 710.5 0.0 442 0 711.0 0.0 259 0\n", "2022-01-18 21:07:53 711.0 3 710.5 0.0 439 0 711.0 0.0 255 0\n", "2022-01-18 21:07:54 710.5 12 710.5 0.0 428 0 711.0 0.0 257 0\n", "2022-01-18 21:07:54 710.5 19 710.5 0.0 424 0 711.0 0.0 242 0\n", "2022-01-18 21:07:55 711.0 10 710.5 0.0 426 0 711.0 0.0 239 0\n", "2022-01-18 21:07:55 711.0 9 710.5 0.0 432 0 711.0 0.0 234 0\n", "2022-01-18 21:07:56 711.0 9 710.5 0.0 430 0 711.0 0.0 228 0\n", "2022-01-18 21:07:56 711.0 7 710.5 0.0 430 0 711.0 0.0 224 0\n", "2022-01-18 21:07:57 711.0 11 710.5 0.0 431 0 711.0 0.0 211 0\n", "2022-01-18 21:07:57 711.0 7 710.5 0.0 434 0 711.0 0.0 207 0\n", "2022-01-18 21:07:58 710.5 15 710.5 0.0 431 0 711.0 0.0 203 0\n", "2022-01-18 21:07:58 711.0 6 710.5 0.0 434 0 711.0 0.0 200 0\n", "2022-01-18 21:07:59 711.0 16 710.5 0.0 429 0 711.0 0.0 197 0\n", "2022-01-18 21:07:59 711.0 10 710.5 0.0 426 0 711.0 0.0 192 0\n", "2022-01-18 21:08:00 711.0 0 710.5 0.0 431 0 711.0 0.0 438 0\n", "2022-01-18 21:08:00 711.0 101 710.5 0.0 432 0 711.0 0.0 356 0\n", "2022-01-18 21:08:01 711.0 24 710.5 0.0 425 0 711.0 0.0 358 0\n", "2022-01-18 21:08:01 710.5 13 710.5 0.0 444 0 711.0 0.0 353 0\n", "2022-01-18 21:08:02 710.5 5 710.5 0.0 450 0 711.0 0.0 354 0\n", "2022-01-18 21:08:02 711.0 14 710.5 0.0 438 0 711.0 0.0 347 0\n", "2022-01-18 21:08:03 711.0 18 710.5 0.0 435 0 711.0 0.0 345 0\n", "2022-01-18 21:08:03 710.5 7 710.5 0.0 428 0 711.0 0.0 349 0\n", "2022-01-18 21:08:04 710.5 37 710.5 0.0 429 0 711.0 0.0 315 0\n", "2022-01-18 21:08:04 711.0 24 710.5 0.0 427 0 711.0 0.0 300 0\n", "2022-01-18 21:08:05 710.5 34 710.5 0.0 391 0 711.0 0.0 302 0\n", "2022-01-18 21:08:05 711.0 31 710.5 0.0 355 0 711.0 0.0 308 0\n", "2022-01-18 21:08:06 711.0 12 710.5 0.0 355 0 711.0 0.0 283 0\n", "2022-01-18 21:08:06 711.0 10 710.5 0.0 374 0 711.0 0.0 288 0\n", "2022-01-18 21:08:07 710.5 11 710.5 0.0 372 0 711.0 0.0 281 0\n", "2022-01-18 21:08:07 711.0 6 710.5 0.0 363 0 711.0 0.0 275 0\n", "2022-01-18 21:08:08 710.5 15 710.5 0.0 359 0 711.0 0.0 278 0\n", "2022-01-18 21:08:08 710.5 4 710.5 0.0 387 0 711.0 0.0 277 0\n", "2022-01-18 21:08:09 710.5 40 710.5 0.0 347 0 711.0 0.0 278 0\n", "2022-01-18 21:08:09 711.0 15 710.5 0.0 374 0 711.0 0.0 266 0\n", "2022-01-18 21:08:10 711.0 43 710.5 0.0 369 0 711.0 0.0 244 0\n", "2022-01-18 21:08:10 711.0 251 711.0 0.0 142 0 711.5 0.0 661 0\n", "2022-01-18 21:08:11 711.0 23 711.0 0.0 190 0 711.5 0.0 642 0\n", "2022-01-18 21:08:11 711.5 13 711.0 0.0 212 0 711.5 0.0 629 0\n", "2022-01-18 21:08:12 711.5 13 711.0 0.0 281 0 711.5 0.0 603 0\n", "2022-01-18 21:08:12 711.0 13 711.0 0.0 281 0 711.5 0.0 638 0\n", "2022-01-18 21:08:13 711.5 1 711.0 0.0 282 0 711.5 0.0 640 0\n", "2022-01-18 21:08:13 711.5 18 711.0 0.0 277 0 711.5 0.0 629 0\n", "2022-01-18 21:08:14 711.5 3 711.0 0.0 279 0 711.5 0.0 627 0\n", "2022-01-18 21:08:14 711.5 16 711.0 0.0 276 0 711.5 0.0 469 0\n", "2022-01-18 21:08:15 711.0 7 711.0 0.0 271 0 711.5 0.0 449 0\n", "2022-01-18 21:08:15 711.0 76 711.0 0.0 203 0 711.5 0.0 415 0\n", "2022-01-18 21:08:16 711.0 3 711.0 0.0 208 0 711.5 0.0 429 0\n", "2022-01-18 21:08:16 711.0 4 711.0 0.0 209 0 711.5 0.0 429 0\n", "2022-01-18 21:08:17 711.5 5 711.0 0.0 207 0 711.5 0.0 428 0\n", "2022-01-18 21:08:17 711.5 5 711.0 0.0 231 0 711.5 0.0 426 0\n", "2022-01-18 21:08:18 711.5 84 711.0 0.0 272 0 711.5 0.0 379 0\n", "2022-01-18 21:08:18 711.5 23 711.0 0.0 297 0 711.5 0.0 357 0\n", "2022-01-18 21:08:19 711.0 14 711.0 0.0 300 0 711.5 0.0 345 0\n", "2022-01-18 21:08:19 711.5 4 711.0 0.0 313 0 711.5 0.0 344 0\n", "2022-01-18 21:08:20 711.0 31 711.0 0.0 299 0 711.5 0.0 329 0\n", "2022-01-18 21:08:20 711.5 90 711.0 0.0 301 0 711.5 0.0 211 0\n", "2022-01-18 21:08:21 711.5 18 711.0 0.0 301 0 711.5 0.0 215 0\n", "2022-01-18 21:08:21 711.5 9 711.0 0.0 310 0 711.5 0.0 217 0\n", "2022-01-18 21:08:22 711.5 36 711.0 0.0 312 0 711.5 0.0 184 0\n", "2022-01-18 21:08:22 711.5 13 711.0 0.0 315 0 711.5 0.0 174 0\n", "2022-01-18 21:08:23 711.5 5 711.0 0.0 315 0 711.5 0.0 214 0\n", "2022-01-18 21:08:23 711.5 2 711.0 0.0 319 0 711.5 0.0 215 0\n", "2022-01-18 21:08:24 711.0 20 711.0 0.0 324 0 711.5 0.0 205 0\n", "2022-01-18 21:08:24 711.0 35 711.0 0.0 318 0 711.5 0.0 191 0\n", "2022-01-18 21:08:25 711.5 29 711.0 0.0 318 0 711.5 0.0 170 0\n", "2022-01-18 21:08:25 711.5 32 711.0 0.0 328 0 711.5 0.0 138 0\n", "2022-01-18 21:08:26 711.0 4 711.0 0.0 320 0 711.5 0.0 96 0\n", "2022-01-18 21:08:26 711.5 10 711.0 0.0 330 0 711.5 0.0 133 0\n", "2022-01-18 21:08:27 711.0 18 711.0 0.0 332 0 711.5 0.0 149 0\n", "2022-01-18 21:08:27 711.0 19 711.0 0.0 351 0 711.5 0.0 198 0\n", "2022-01-18 21:08:28 711.5 3 711.0 0.0 352 0 711.5 0.0 239 0\n", "2022-01-18 21:08:28 711.0 11 711.0 0.0 359 0 711.5 0.0 244 0\n", "2022-01-18 21:08:29 711.5 33 711.0 0.0 357 0 711.5 0.0 283 0\n", "2022-01-18 21:08:29 711.5 0 711.0 0.0 355 0 711.5 0.0 283 0\n", "2022-01-18 21:08:30 711.5 17 711.0 0.0 344 0 711.5 0.0 286 0\n", "2022-01-18 21:08:30 711.5 12 711.0 0.0 377 0 711.5 0.0 281 0\n", "2022-01-18 21:08:31 711.5 12 711.0 0.0 375 0 711.5 0.0 272 0\n", "2022-01-18 21:08:31 711.5 17 711.0 0.0 376 0 711.5 0.0 267 0\n", "2022-01-18 21:08:32 711.0 1 711.0 0.0 385 0 711.5 0.0 271 0\n", "2022-01-18 21:08:32 711.0 8 711.0 0.0 384 0 711.5 0.0 242 0\n", "2022-01-18 21:08:33 711.0 4 711.0 0.0 380 0 711.5 0.0 229 0\n", "2022-01-18 21:08:33 711.5 5 711.0 0.0 377 0 711.5 0.0 236 0\n", "2022-01-18 21:08:34 711.0 4 711.0 0.0 373 0 711.5 0.0 260 0\n", "2022-01-18 21:08:34 711.5 40 711.0 0.0 376 0 711.5 0.0 230 0\n", "2022-01-18 21:08:35 711.5 54 711.0 0.0 375 0 711.5 0.0 162 0\n", "2022-01-18 21:08:35 711.5 31 711.0 0.0 375 0 711.5 0.0 115 0\n", "2022-01-18 21:08:36 711.5 26 711.0 0.0 414 0 711.5 0.0 123 0\n", "2022-01-18 21:08:36 711.5 19 711.0 0.0 418 0 711.5 0.0 104 0\n", "2022-01-18 21:08:37 711.5 5 711.0 0.0 423 0 711.5 0.0 98 0\n", "2022-01-18 21:08:37 711.5 9 711.0 0.0 428 0 711.5 0.0 112 0\n", "2022-01-18 21:08:38 711.5 107 711.0 0.0 455 0 711.5 0.0 9 0\n", "2022-01-18 21:08:38 711.5 14 711.5 0.0 331 0 712.0 0.0 871 0\n", "2022-01-18 21:08:39 711.5 4 711.5 0.0 381 0 712.0 0.0 868 0\n", "2022-01-18 21:08:39 711.5 4 711.5 0.0 392 0 712.0 0.0 864 0\n", "2022-01-18 21:08:40 712.0 8 711.5 0.0 426 0 712.0 0.0 862 0\n", "2022-01-18 21:08:40 712.0 52 711.5 0.0 404 0 712.0 0.0 838 0\n", "2022-01-18 21:08:41 712.0 9 711.5 0.0 405 0 712.0 0.0 836 0\n", "2022-01-18 21:08:41 712.0 12 711.5 0.0 409 0 712.0 0.0 839 0\n", "2022-01-18 21:08:42 712.0 12 711.5 0.0 408 0 712.0 0.0 846 0\n", "2022-01-18 21:08:42 712.0 12 711.5 0.0 405 0 712.0 0.0 841 0\n", "2022-01-18 21:08:43 712.0 2 711.5 0.0 400 0 712.0 0.0 842 0\n", "2022-01-18 21:08:43 711.5 27 711.5 0.0 401 0 712.0 0.0 818 0\n", "2022-01-18 21:08:44 711.5 3 711.5 0.0 389 0 712.0 0.0 821 0\n", "2022-01-18 21:08:44 712.0 3 711.5 0.0 380 0 712.0 0.0 840 0\n", "2022-01-18 21:08:45 712.0 1 711.5 0.0 382 0 712.0 0.0 841 0\n", "2022-01-18 21:08:45 712.0 5 711.5 0.0 383 0 712.0 0.0 836 0\n", "2022-01-18 21:08:46 711.5 39 711.5 0.0 352 0 712.0 0.0 833 0\n", "2022-01-18 21:08:46 712.0 31 711.5 0.0 347 0 712.0 0.0 810 0\n", "2022-01-18 21:08:47 711.5 4 711.5 0.0 347 0 712.0 0.0 813 0\n", "2022-01-18 21:08:47 712.0 24 711.5 0.0 336 0 712.0 0.0 900 0\n", "2022-01-18 21:08:48 711.5 4 711.5 0.0 362 0 712.0 0.0 978 0\n", "2022-01-18 21:08:48 712.0 4 711.5 0.0 361 0 712.0 0.0 974 0\n", "2022-01-18 21:08:49 712.0 3 711.5 0.0 363 0 712.0 0.0 972 0\n", "2022-01-18 21:08:49 712.0 4 711.5 0.0 382 0 712.0 0.0 971 0\n", "2022-01-18 21:08:50 712.0 11 711.5 0.0 395 0 712.0 0.0 965 0\n", "2022-01-18 21:08:50 712.0 112 711.5 0.0 407 0 712.0 0.0 865 0\n", "2022-01-18 21:08:51 712.0 8 711.5 0.0 402 0 712.0 0.0 864 0\n", "2022-01-18 21:08:51 712.0 31 711.5 0.0 404 0 712.0 0.0 846 0\n", "2022-01-18 21:08:52 712.0 11 711.5 0.0 398 0 712.0 0.0 848 0\n", "2022-01-18 21:08:52 712.0 6 711.5 0.0 398 0 712.0 0.0 847 0\n", "2022-01-18 21:08:53 712.0 6 711.5 0.0 396 0 712.0 0.0 851 0\n", "2022-01-18 21:08:53 712.0 4 711.5 0.0 395 0 712.0 0.0 902 0\n", "2022-01-18 21:08:54 712.0 35 711.5 0.0 368 0 712.0 0.0 885 0\n", "2022-01-18 21:08:54 711.5 39 711.5 0.0 364 0 712.0 0.0 853 0\n", "2022-01-18 21:08:55 711.5 3 711.5 0.0 366 0 712.0 0.0 849 0\n", "2022-01-18 21:08:55 711.5 3 711.5 0.0 365 0 712.0 0.0 855 0\n", "2022-01-18 21:08:56 712.0 43 711.5 0.0 382 0 712.0 0.0 820 0\n", "2022-01-18 21:08:56 712.0 6 711.5 0.0 383 0 712.0 0.0 818 0\n", "2022-01-18 21:08:57 712.0 15 711.5 0.0 383 0 712.0 0.0 827 0\n", "2022-01-18 21:08:57 711.5 404 711.0 0.0 369 0 711.5 0.0 276 0\n", "2022-01-18 21:08:58 711.5 17 711.0 0.0 274 0 711.5 0.0 294 0\n", "2022-01-18 21:08:58 711.5 37 711.0 0.0 290 0 711.5 0.0 273 0\n", "2022-01-18 21:08:59 711.0 8 711.0 0.0 330 0 711.5 0.0 284 0\n", "2022-01-18 21:08:59 711.5 15 711.0 0.0 340 0 711.5 0.0 270 0\n", "2022-01-18 21:09:00 711.5 116 711.0 0.0 340 0 711.5 0.0 152 0\n", "2022-01-18 21:09:00 711.5 164 711.5 0.0 131 0 712.0 0.0 876 0\n", "2022-01-18 21:09:01 711.5 74 711.5 0.0 42 0 712.0 0.0 873 0\n", "2022-01-18 21:09:01 711.0 53 711.0 0.0 351 0 711.5 0.0 166 0\n", "2022-01-18 21:09:02 711.5 45 711.0 0.0 296 0 711.5 0.0 191 0\n", "2022-01-18 21:09:02 711.5 5 711.0 0.0 299 0 711.5 0.0 261 0\n", "2022-01-18 21:09:03 711.5 29 711.0 0.0 255 0 711.5 0.0 281 0\n", "2022-01-18 21:09:03 711.5 6 711.0 0.0 261 0 711.5 0.0 299 0\n", "2022-01-18 21:09:04 711.0 6 711.0 0.0 256 0 711.5 0.0 309 0\n", "2022-01-18 21:09:04 711.0 31 711.0 0.0 255 0 711.5 0.0 284 0\n", "2022-01-18 21:09:05 711.0 12 711.0 0.0 254 0 711.5 0.0 275 0\n", "2022-01-18 21:09:05 711.0 3 711.0 0.0 252 0 711.5 0.0 274 0\n", "2022-01-18 21:09:06 711.5 5 711.0 0.0 260 0 711.5 0.0 269 0\n", "2022-01-18 21:09:06 711.5 3 711.0 0.0 258 0 711.5 0.0 269 0\n", "2022-01-18 21:09:07 711.5 2 711.0 0.0 277 0 711.5 0.0 278 0\n", "2022-01-18 21:09:07 711.0 10 711.0 0.0 269 0 711.5 0.0 293 0\n", "2022-01-18 21:09:08 711.0 51 711.0 0.0 226 0 711.5 0.0 297 0\n", "2022-01-18 21:09:08 711.5 8 711.0 0.0 222 0 711.5 0.0 295 0\n", "2022-01-18 21:09:09 711.5 3 711.0 0.0 229 0 711.5 0.0 298 0\n", "2022-01-18 21:09:09 711.0 2 711.0 0.0 231 0 711.5 0.0 299 0\n", "2022-01-18 21:09:10 711.5 22 711.0 0.0 229 0 711.5 0.0 298 0\n", "2022-01-18 21:09:10 711.0 21 711.0 0.0 234 0 711.5 0.0 286 0\n", "2022-01-18 21:09:11 711.0 38 711.0 0.0 271 0 711.5 0.0 250 0\n", "2022-01-18 21:09:11 711.5 51 711.0 0.0 243 0 711.5 0.0 268 0\n", "2022-01-18 21:09:12 711.0 5 711.0 0.0 241 0 711.5 0.0 268 0\n", "2022-01-18 21:09:12 711.5 19 711.0 0.0 212 0 711.5 0.0 253 0\n", "2022-01-18 21:09:13 711.5 1 711.0 0.0 219 0 711.5 0.0 275 0\n", "2022-01-18 21:09:13 711.5 2 711.0 0.0 218 0 711.5 0.0 271 0\n", "2022-01-18 21:09:14 711.0 75 711.0 0.0 156 0 711.5 0.0 274 0\n", "2022-01-18 21:09:14 711.5 69 711.0 0.0 79 0 711.5 0.0 285 0\n", "2022-01-18 21:09:15 711.5 115 710.5 0.0 331 0 711.5 0.0 279 0\n", "2022-01-18 21:09:15 711.0 24 711.0 0.0 83 0 711.5 0.0 282 0\n", "2022-01-18 21:09:16 711.0 100 710.5 0.0 337 0 711.0 0.0 93 0\n", "2022-01-18 21:09:16 711.0 28 711.0 0.0 56 0 711.5 0.0 313 0\n", "2022-01-18 21:09:17 711.0 81 710.5 0.0 348 0 711.5 0.0 335 0\n", "2022-01-18 21:09:17 711.0 26 710.5 0.0 334 0 711.0 0.0 43 0\n", "2022-01-18 21:09:18 711.0 25 710.5 0.0 307 0 711.0 0.0 116 0\n", "2022-01-18 21:09:18 711.0 11 710.5 0.0 309 0 711.0 0.0 150 0\n", "2022-01-18 21:09:19 711.0 4 710.5 0.0 300 0 711.0 0.0 150 0\n", "2022-01-18 21:09:19 710.5 1 710.5 0.0 302 0 711.0 0.0 150 0\n", "2022-01-18 21:09:20 711.0 16 710.5 0.0 303 0 711.0 0.0 141 0\n", "2022-01-18 21:09:20 711.0 30 710.5 0.0 339 0 711.0 0.0 111 0\n", "2022-01-18 21:09:21 711.0 3 710.5 0.0 339 0 711.0 0.0 67 0\n", "2022-01-18 21:09:21 710.5 31 710.5 0.0 309 0 711.0 0.0 58 0\n", "2022-01-18 21:09:22 711.0 12 710.5 0.0 305 0 711.0 0.0 99 0\n", "2022-01-18 21:09:22 711.0 27 710.5 0.0 335 0 711.0 0.0 67 0\n", "2022-01-18 21:09:23 711.0 11 710.5 0.0 328 0 711.0 0.0 69 0\n", "2022-01-18 21:09:23 711.0 40 710.5 0.0 319 0 711.0 0.0 67 0\n", "2022-01-18 21:09:24 710.5 35 710.5 0.0 315 0 711.0 0.0 1 0\n", "2022-01-18 21:09:24 710.5 62 710.5 0.0 290 0 711.0 0.0 36 0\n", "2022-01-18 21:09:25 711.0 11 710.5 0.0 323 0 711.0 0.0 117 0\n", "2022-01-18 21:09:25 711.0 23 710.5 0.0 325 0 711.0 0.0 95 0\n", "2022-01-18 21:09:26 711.0 40 710.5 0.0 330 0 711.0 0.0 12 0\n", "2022-01-18 21:09:26 711.0 44 711.0 0.0 26 0 711.5 0.0 356 0\n", "2022-01-18 21:09:27 711.0 30 711.0 0.0 23 0 711.5 0.0 357 0\n", "2022-01-18 21:09:27 711.5 22 711.0 0.0 47 0 711.5 0.0 337 0\n", "2022-01-18 21:09:28 711.0 29 711.0 0.0 75 0 711.5 0.0 316 0\n", "2022-01-18 21:09:28 711.0 8 711.0 0.0 138 0 711.5 0.0 318 0\n", "2022-01-18 21:09:29 711.0 7 711.0 0.0 213 0 711.5 0.0 318 0\n", "2022-01-18 21:09:29 711.5 21 711.0 0.0 250 0 711.5 0.0 274 0\n", "2022-01-18 21:09:30 711.0 19 711.0 0.0 263 0 711.5 0.0 242 0\n", "2022-01-18 21:09:30 711.5 82 711.0 0.0 293 0 711.5 0.0 159 0\n", "2022-01-18 21:09:31 711.5 174 711.5 0.0 429 0 712.0 0.0 1350 0\n", "2022-01-18 21:09:31 712.0 7 711.5 0.0 435 0 712.0 0.0 1358 0\n", "2022-01-18 21:09:32 711.5 60 711.5 0.0 423 0 712.0 0.0 1287 0\n", "2022-01-18 21:09:32 711.5 2 711.5 0.0 434 0 712.0 0.0 1305 0\n", "2022-01-18 21:09:33 711.5 3 711.5 0.0 437 0 712.0 0.0 1305 0\n", "2022-01-18 21:09:33 711.5 10 711.5 0.0 428 0 712.0 0.0 1309 0\n", "2022-01-18 21:09:34 712.0 9 711.5 0.0 407 0 712.0 0.0 1306 0\n", "2022-01-18 21:09:34 711.5 4 711.5 0.0 406 0 712.0 0.0 1307 0\n", "2022-01-18 21:09:35 711.5 14 711.5 0.0 399 0 712.0 0.0 1304 0\n", "2022-01-18 21:09:35 712.0 4 711.5 0.0 399 0 712.0 0.0 1308 0\n", "2022-01-18 21:09:36 711.5 15 711.5 0.0 398 0 712.0 0.0 1302 0\n", "2022-01-18 21:09:36 712.0 57 711.5 0.0 372 0 712.0 0.0 1274 0\n", "2022-01-18 21:09:37 712.0 2 711.5 0.0 373 0 712.0 0.0 1274 0\n", "2022-01-18 21:09:37 711.5 2 711.5 0.0 389 0 712.0 0.0 1270 0\n", "2022-01-18 21:09:38 711.5 3 711.5 0.0 393 0 712.0 0.0 1274 0\n", "2022-01-18 21:09:38 712.0 6 711.5 0.0 391 0 712.0 0.0 920 0\n", "2022-01-18 21:09:39 712.0 4 711.5 0.0 354 0 712.0 0.0 917 0\n", "2022-01-18 21:09:39 712.0 14 711.5 0.0 347 0 712.0 0.0 918 0\n", "2022-01-18 21:09:40 712.0 18 711.5 0.0 347 0 712.0 0.0 903 0\n", "2022-01-18 21:09:40 712.0 56 711.5 0.0 326 0 712.0 0.0 869 0\n", "2022-01-18 21:09:41 711.5 343 711.0 0.0 319 0 711.5 0.0 119 0\n", "2022-01-18 21:09:41 712.0 167 711.5 0.0 118 0 712.0 0.0 884 0\n", "2022-01-18 21:09:42 711.5 32 711.5 0.0 130 0 712.0 0.0 890 0\n", "2022-01-18 21:09:42 711.5 13 711.5 0.0 154 0 712.0 0.0 887 0\n", "2022-01-18 21:09:43 711.5 35 711.5 0.0 149 0 712.0 0.0 856 0\n", "2022-01-18 21:09:43 711.5 10 711.5 0.0 161 0 712.0 0.0 856 0\n", "2022-01-18 21:09:44 711.5 2 711.5 0.0 194 0 712.0 0.0 870 0\n", "2022-01-18 21:09:44 711.5 2 711.5 0.0 195 0 712.0 0.0 871 0\n", "2022-01-18 21:09:45 711.5 2 711.5 0.0 200 0 712.0 0.0 876 0\n", "2022-01-18 21:09:45 712.0 2 711.5 0.0 175 0 712.0 0.0 888 0\n", "2022-01-18 21:09:46 712.0 7 711.5 0.0 169 0 712.0 0.0 891 0\n", "2022-01-18 21:09:46 711.5 92 711.5 0.0 81 0 712.0 0.0 890 0\n", "2022-01-18 21:09:47 712.0 53 711.5 0.0 74 0 712.0 0.0 858 0\n", "2022-01-18 21:09:47 712.0 87 711.5 0.0 136 0 712.0 0.0 1129 0\n", "2022-01-18 21:09:48 712.0 30 711.5 0.0 170 0 712.0 0.0 1119 0\n", "2022-01-18 21:09:48 712.0 3 711.5 0.0 186 0 712.0 0.0 1119 0\n", "2022-01-18 21:09:49 711.5 5 711.5 0.0 182 0 712.0 0.0 1121 0\n", "2022-01-18 21:09:49 712.0 34 711.5 0.0 177 0 712.0 0.0 1091 0\n", "2022-01-18 21:09:50 712.0 9 711.5 0.0 179 0 712.0 0.0 1090 0\n", "2022-01-18 21:09:50 711.5 2 711.5 0.0 179 0 712.0 0.0 1112 0\n", "2022-01-18 21:09:51 711.5 5 711.5 0.0 127 0 712.0 0.0 1143 0\n", "2022-01-18 21:09:51 711.5 11 711.5 0.0 124 0 712.0 0.0 1144 0\n", "2022-01-18 21:09:52 711.5 0 711.5 0.0 124 0 712.0 0.0 1143 0\n", "2022-01-18 21:09:52 712.0 11 711.5 0.0 122 0 712.0 0.0 1140 0\n", "2022-01-18 21:09:53 712.0 5 711.5 0.0 119 0 712.0 0.0 1164 0\n", "2022-01-18 21:09:53 711.5 154 711.0 0.0 397 0 711.5 0.0 147 0\n", "2022-01-18 21:09:54 711.5 5 711.0 0.0 379 0 711.5 0.0 367 0\n", "2022-01-18 21:09:54 711.5 46 711.0 0.0 376 0 711.5 0.0 335 0\n", "2022-01-18 21:09:55 711.5 26 711.0 0.0 376 0 711.5 0.0 314 0\n", "2022-01-18 21:09:55 711.5 5 711.0 0.0 381 0 711.5 0.0 317 0\n", "2022-01-18 21:09:56 711.0 11 711.0 0.0 370 0 711.5 0.0 318 0\n", "2022-01-18 21:09:56 711.5 18 711.0 0.0 376 0 711.5 0.0 309 0\n", "2022-01-18 21:09:57 711.5 8 711.0 0.0 373 0 711.5 0.0 306 0\n", "2022-01-18 21:09:57 711.5 3 711.0 0.0 370 0 711.5 0.0 307 0\n", "2022-01-18 21:09:58 711.5 2 711.0 0.0 361 0 711.5 0.0 311 0\n", "2022-01-18 21:09:58 711.5 26 711.0 0.0 355 0 711.5 0.0 305 0\n", "2022-01-18 21:09:59 711.5 35 711.0 0.0 376 0 711.5 0.0 277 0\n", "2022-01-18 21:09:59 711.0 17 711.0 0.0 380 0 711.5 0.0 266 0\n"]}], "source": ["test_5()"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["# db=create_db(\"leveldb\", \"d:/QuantLab/store/kv.db\", Mode.read)\n", "# cursor = db.new_cursor()\n", "# cursor.seek_to_first()\n", "# while cursor.valid():\n", "#     print(cursor.key())\n", "#     # print(cursor.key(), cursor.value())\n", "#     cursor.next()\n", "# del cursor\n", "# db.close()\n", "# del db"]}], "metadata": {"interpreter": {"hash": "af09bc94d41e018aa4bb791c06386d7d2a0d085b02fa573368449120a8cb3c6e"}, "kernelspec": {"display_name": "Python 3.7.6 64-bit ('base': conda)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}