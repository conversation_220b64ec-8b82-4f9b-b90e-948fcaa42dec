"""
## GPT-2 to GPT-4的主要优化和变更包括：
- 使用RMSNorm替代LayerNorm，这是GPT-4中的一个改进。
- 实现了更高效的RotaryEmbedding，并在Attention模块中应用。
- 简化了Attention模块，使用PyTorch的scaled_dot_product_attention函数，
  这在支持的硬件上可以提供更好的性能。
- 调整了MLP结构，使用GELU激活函数。
- 在Block中使用了更现代的残差连接方式。
- 优化了权重初始化方法。
- 移除了MoE（Mixture of Experts）相关的代码，因为标准的GPT-4模型不使用MoE。
  如果你特别需要MoE，可以考虑将其作为一个可选的特性。
- 简化了forward方法，使其更加清晰和高效。
  这些变更应该能够提高模型的性能和训练稳定性。
  请注意，这些修改可能需要相应地调整训练脚本和其他相关代码。
  此外，你可能需要根据具体的任务和数据集来微调超参数。
"""

import math
import torch
import torch.nn as nn
from torch.nn import functional as F
from pyqlab.models.layers.Embed import (
    TimeFeatureEmbedding, TemporalEmbedding,
    PeriodicTimeEncoding, RelativeTimeEncoding,
    ContinuousTimeEmbedding, MultiScaleTimeEncoding,
    AdaptiveTimeEncoding
)

class RMSNorm(nn.Module):
    """
    RMSNorm层
    使用均方根归一化进行层归一化。
    """
    def __init__(self, dim, eps=1e-6):
        super().__init__()
        self.eps = eps
        self.weight = nn.Parameter(torch.ones(dim))

    def _norm(self, x):
        return x * torch.rsqrt(x.pow(2).mean(-1, keepdim=True) + self.eps)

    def forward(self, x):
        return self._norm(x) * self.weight

class RotaryEmbedding(nn.Module):
    """
    RoPE（Rotary Position Embedding）
    使用旋转位置嵌入进行位置编码。
    """
    def __init__(self, dim, max_position_embeddings=2048, base=10000):
        super().__init__()
        inv_freq = 1.0 / (base ** (torch.arange(0, dim, 2).float() / dim))
        self.register_buffer("inv_freq", inv_freq)
        self.max_seq_len_cached = max_position_embeddings
        t = torch.arange(self.max_seq_len_cached, device=self.inv_freq.device).type_as(self.inv_freq)
        freqs = torch.einsum("i,j->ij", t, self.inv_freq)
        emb = torch.cat((freqs, freqs), dim=-1)
        self.register_buffer("cos_cached", emb.cos()[None, None, :, :], persistent=False)
        self.register_buffer("sin_cached", emb.sin()[None, None, :, :], persistent=False)

    def forward(self, x, seq_len=None):
        if seq_len > self.max_seq_len_cached:
            self.max_seq_len_cached = seq_len
            t = torch.arange(self.max_seq_len_cached, device=x.device).type_as(self.inv_freq)
            freqs = torch.einsum("i,j->ij", t, self.inv_freq)
            emb = torch.cat((freqs, freqs), dim=-1).to(x.device)
            self.register_buffer("cos_cached", emb.cos()[None, None, :, :], persistent=False)
            self.register_buffer("sin_cached", emb.sin()[None, None, :, :], persistent=False)
        return (
            self.cos_cached[:, :, :seq_len, ...].to(dtype=x.dtype),
            self.sin_cached[:, :, :seq_len, ...].to(dtype=x.dtype),
        )

def rotate_half(x):
    """
    将输入向量的后半部分旋转到前半部分。
    """
    x1, x2 = x[..., : x.shape[-1] // 2], x[..., x.shape[-1] // 2 :]
    return torch.cat((-x2, x1), dim=-1)

def apply_rotary_pos_emb(q, k, cos, sin, offset=0):
    """
    应用旋转位置嵌入到查询和键向量。
    """
    q_embed = (q * cos) + (rotate_half(q) * sin)
    k_embed = (k * cos) + (rotate_half(k) * sin)
    return q_embed, k_embed

class MLP(nn.Module):
    """
    MLP（Multi-Layer Perceptron）
    包含两个全连接层和GELU激活函数。
    """
    def __init__(self, dim, hidden_dim):
        super().__init__()
        self.c_fc = nn.Linear(dim, hidden_dim, bias=False)
        self.c_proj = nn.Linear(hidden_dim, dim, bias=False)
        self.act = nn.GELU()

    def forward(self, x):
        return self.c_proj(self.act(self.c_fc(x)))

class AlibiPositionalEmbedding(nn.Module):
    """
    ALiBi（Attention with Linear Biases）位置嵌入
    使用线性偏置进行位置嵌入，适用于处理变长序列和外推。
    """
    def __init__(self, num_heads, max_seq_len=2048):
        super().__init__()
        self.num_heads = num_heads
        self.max_seq_len = max_seq_len
        slopes = torch.Tensor(self._get_slopes(num_heads))
        self.register_buffer("slopes", slopes)
        self.register_buffer("bias", self._get_bias(max_seq_len))
        
    def _get_slopes(self, num_heads):
        def get_slopes_power_of_2(n):
            start = (2**(-2**-(math.log2(n)-3)))
            ratio = start
            return [start*ratio**i for i in range(n)]

        if math.log2(num_heads).is_integer():
            return get_slopes_power_of_2(num_heads)
        else:
            closest_power_of_2 = 2**math.floor(math.log2(num_heads))
            return get_slopes_power_of_2(closest_power_of_2) + self._get_slopes(2*closest_power_of_2)[0::2][:num_heads-closest_power_of_2]

    def _get_bias(self, max_seq_len):
        bias = torch.arange(max_seq_len).unsqueeze(0).unsqueeze(0)
        return bias * self.slopes.unsqueeze(1).unsqueeze(1)

    def forward(self, x, seq_len):
        return self.bias[:, :, :seq_len, :seq_len]

class Attention(nn.Module):
    """
    注意力机制
    支持RoPE和ALiBi位置嵌入。
    """
    def __init__(self, dim, n_heads, dropout, pos_embed_type='rope'):
        super().__init__()
        self.n_heads = n_heads
        self.dim = dim
        self.head_dim = dim // n_heads
        self.wq = nn.Linear(dim, dim, bias=False)
        self.wk = nn.Linear(dim, dim, bias=False)
        self.wv = nn.Linear(dim, dim, bias=False)
        self.attn_dropout = nn.Dropout(dropout)
        self.resid_dropout = nn.Dropout(dropout)
        self.proj = nn.Linear(dim, dim, bias=False)
        self.pos_embed_type = pos_embed_type
        
        if pos_embed_type == 'alibi':
            self.alibi = AlibiPositionalEmbedding(n_heads)

    def forward(self, x, rotary_emb=None):
        B, T, C = x.size()
        q, k, v = self.wq(x), self.wk(x), self.wv(x)
        q = q.view(B, T, self.n_heads, C // self.n_heads).transpose(1, 2)
        k = k.view(B, T, self.n_heads, C // self.n_heads).transpose(1, 2)
        v = v.view(B, T, self.n_heads, C // self.n_heads).transpose(1, 2)

        if self.pos_embed_type == 'rope':
            cos, sin = rotary_emb
            q, k = apply_rotary_pos_emb(q, k, cos, sin)
            attn_bias = None
        elif self.pos_embed_type == 'alibi':
            attn_bias = self.alibi(x, T)
        else:
            attn_bias = None

        y = F.scaled_dot_product_attention(q, k, v, attn_mask=attn_bias, dropout_p=self.attn_dropout.p if self.training else 0, is_causal=True)
        y = y.transpose(1, 2).contiguous().view(B, T, C)
        y = self.resid_dropout(self.proj(y))
        return y

class Block(nn.Module):
    """
    Transformer Block
    包含RMSNorm、注意力机制和MLP。
    """
    def __init__(self, dim, n_heads, dropout, pos_embed_type='rope'):
        super().__init__()
        self.ln_1 = RMSNorm(dim)
        self.attn = Attention(dim, n_heads, dropout, pos_embed_type)
        self.ln_2 = RMSNorm(dim)
        self.mlp = MLP(dim, 4 * dim)
        self.pos_embed_type = pos_embed_type

    def forward(self, x, rotary_emb=None):
        x = x + self.attn(self.ln_1(x), rotary_emb)
        x = x + self.mlp(self.ln_2(x))
        return x

class BarGpt4(nn.Module):
    """
    BarGpt4模型
    支持多种时间编码嵌入方法和特征向量合并方法。
    """
    def __init__(self, block_size, code_size, vocab_size, n_layer, n_head, d_model, time_encoding,
                 time_embed_type='periodic', freq='t', pos_embed_type='rope', dropout=0.1):
        """
        初始化BarGpt4模型。

        参数:
        - block_size: 序列块大小
        - code_size: 代码嵌入大小
        - vocab_size: 词汇表大小
        - n_layer: Transformer层数
        - n_head: 注意力头数
        - d_model: 嵌入维度
        - time_embed_type: 时间编码嵌入方法
        - dropout: Dropout概率
        - pos_embed_type: 位置嵌入方法
        """
        super().__init__()
        self.block_size = block_size
        self.vocab_size = vocab_size
        self.n_layer = n_layer
        self.n_head = n_head
        self.d_model = d_model
        self.time_encoding = time_encoding
        self.pos_embed_type = pos_embed_type
        self.time_embed_type = time_embed_type
        self.freq = freq

        self.transformer = nn.ModuleDict(dict(
            bar_eb = nn.Embedding(vocab_size, d_model),
            code_eb = nn.Embedding(code_size, d_model),
            tf_eb = TimeFeatureEmbedding(d_model=d_model, freq=freq) if time_encoding =='timeF' else TemporalEmbedding(d_model=d_model, embed_type=time_encoding, freq=freq),
            time_eb = self._get_time_embedding(time_embed_type, d_model),
            drop = nn.Dropout(dropout),
            h = nn.ModuleList([Block(d_model, n_head, dropout, pos_embed_type) for _ in range(n_layer)]),
            ln_f = RMSNorm(d_model),
        ))
        self.lm_head = nn.Linear(d_model, vocab_size, bias=False)
        
        if pos_embed_type == 'rope':
            self.rotary_emb = RotaryEmbedding(d_model // n_head)
        elif pos_embed_type == 'alibi':
            self.alibi = AlibiPositionalEmbedding(n_head, block_size)

        # Initialize weights
        self.apply(self._init_weights)
        for pn, p in self.named_parameters():
            if pn.endswith('proj.weight'):
                torch.nn.init.normal_(p, mean=0.0, std=0.02/math.sqrt(2 * n_layer))

        # Weight tying
        self.transformer.bar_eb.weight = self.lm_head.weight

        print("="*30)
        print(f"Number of parameters: {sum(p.numel() for p in self.parameters())/1e6:.2f}M")
        print("="*30)

    def _init_weights(self, module):
        """
        初始化权重
        """
        if isinstance(module, (nn.Linear, nn.Embedding)):
            module.weight.data.normal_(mean=0.0, std=0.02)
        elif isinstance(module, nn.LayerNorm):
            module.bias.data.zero_()
            module.weight.data.fill_(1.0)
        if isinstance(module, nn.Linear) and module.bias is not None:
            module.bias.data.zero_()

    def _get_time_embedding(self, time_embed_type, d_model):
        """
        获取时间编码嵌入方法。

        参数:
        - time_embed_type: 时间编码嵌入方法
        - d_model: 嵌入维度

        返回:
        - 时间编码嵌入层
        """
        if time_embed_type == 'periodic':
            return PeriodicTimeEncoding(d_model=d_model, max_len=self.block_size)
        elif time_embed_type == 'relative':
            return RelativeTimeEncoding(d_model=d_model, max_len=self.block_size)
        elif time_embed_type == 'time_feature':
            return TimeFeatureEmbedding(d_model=d_model, freq=self.freq)
        elif time_embed_type == 'continuous':
            return ContinuousTimeEmbedding(d_model=d_model)
        elif time_embed_type == 'multiscale':
            return MultiScaleTimeEncoding(d_model=d_model)
        elif time_embed_type == 'adaptive':
            return AdaptiveTimeEncoding(d_model=d_model, max_len=self.block_size)
        else:
            raise ValueError(f"Unknown time embedding type: {time_embed_type}")

    def forward(self, code, x, x_mark, targets=None):
        """
        前向传播。

        参数:
        - code: 代码输入
        - x: 序列输入
        - x_mark: 时间标记输入
        - targets: 目标输出（可选）

        返回:
        - logits: 模型输出
        - loss: 损失（如果提供了目标输出）
        """
        b, t = x.size()
        assert t <= self.block_size, f"Cannot forward sequence of length {t}, block size is only {self.block_size}"
        
        bar_emb = self.transformer.bar_eb(x)
        code_emb = self.transformer.code_eb(code)
        if self.time_embed_type == 'time_feature':
            time_emb = self.transformer.tf_eb(x_mark if self.freq == 't' else x_mark[:, :, -3:])
        else:
            time_emb = self.transformer.tf_eb(x_mark if self.freq == 't' else x_mark[:, :, -3:])
            time_emb = self.transformer.time_eb(time_emb)

        x = self.transformer.drop(bar_emb + code_emb + time_emb)
        
        if self.pos_embed_type == 'rope':
            rotary_emb = self.rotary_emb(x, seq_len=t)
        else:
            rotary_emb = None
        
        for block in self.transformer.h:
            x = block(x, rotary_emb)
        x = self.transformer.ln_f(x)

        if targets is not None:
            logits = self.lm_head(x)
            loss = F.cross_entropy(logits.view(-1, logits.size(-1)), targets.view(-1), ignore_index=-1)
        else:
            logits = self.lm_head(x[:, [-1], :])
            loss = None

        return logits, loss

    def crop_block_size(self, block_size):
        """
        裁剪序列块大小。

        参数:
        - block_size: 新的序列块大小
        """
        # model surgery to decrease the block size if necessary
        # e.g. we may load the GPT2 pretrained model checkpoint (block size 1024)
        # but want to use a smaller block size for some smaller, simpler model
        assert block_size <= self.block_size
        self.block_size = block_size
        self.transformer.wpe.weight = nn.Parameter(self.transformer.wpe.weight[:block_size])
        for block in self.transformer.h:
            if hasattr(block.attn, 'bias'):
                block.attn.bias = block.attn.bias[:,:,:block_size,:block_size]

    def configure_optimizers(self, weight_decay, learning_rate, betas, device_type):
        """
        配置优化器。

        参数:
        - weight_decay: 权重衰减系数
        - learning_rate: 学习率
        - betas: Adam优化器的beta参数
        - device_type: 设备类型

        返回:
        - 优化器
        """
        import inspect
        # start with all of the candidate parameters
        param_dict = {pn: p for pn, p in self.named_parameters()}
        # filter out those that do not require grad
        param_dict = {pn: p for pn, p in param_dict.items() if p.requires_grad}
        # create optim groups. Any parameters that is 2D will be weight decayed, otherwise no.
        # i.e. all weight tensors in matmuls + embeddings decay, all biases and layernorms don't.
        decay_params = [p for n, p in param_dict.items() if p.dim() >= 2]
        nodecay_params = [p for n, p in param_dict.items() if p.dim() < 2]
        optim_groups = [
            {'params': decay_params, 'weight_decay': weight_decay},
            {'params': nodecay_params, 'weight_decay': 0.0}
        ]
        num_decay_params = sum(p.numel() for p in decay_params)
        num_nodecay_params = sum(p.numel() for p in nodecay_params)
        print(f"num decayed parameter tensors: {len(decay_params)}, with {num_decay_params:,} parameters")
        print(f"num non-decayed parameter tensors: {len(nodecay_params)}, with {num_nodecay_params:,} parameters")
        # Create AdamW optimizer and use the fused version if it is available
        fused_available = 'fused' in inspect.signature(torch.optim.AdamW).parameters
        use_fused = fused_available and device_type == 'cuda'
        extra_args = dict(fused=True) if use_fused else dict()
        optimizer = torch.optim.AdamW(optim_groups, lr=learning_rate, betas=betas, **extra_args)
        print(f"using fused AdamW: {use_fused}")

        return optimizer

    def estimate_mfu(self, fwdbwd_per_iter, dt):
        """
        估计模型FLOPs利用率（MFU），单位为A100 bfloat16峰值FLOPS。

        参数:
        - fwdbwd_per_iter: 每次迭代的前向传播和反向传播次数
        - dt: 迭代时间

        返回:
        - MFU
        """
        # first estimate the number of flops we do per iteration.
        # see PaLM paper Appendix B as ref: https://arxiv.org/abs/2204.02311
        N = self.get_num_params()
        cfg = self.config
        L, H, Q, T = cfg.n_layer, cfg.n_head, cfg.d_model//cfg.n_head, cfg.block_size
        flops_per_token = 6*N + 12*L*H*Q*T
        flops_per_fwdbwd = flops_per_token * T
        flops_per_iter = flops_per_fwdbwd * fwdbwd_per_iter
        # express our flops throughput as ratio of A100 bfloat16 peak flops
        flops_achieved = flops_per_iter * (1.0/dt) # per second
        flops_promised = 312e12 # A100 GPU bfloat16 peak flops is 312 TFLOPS
        mfu = flops_achieved / flops_promised
        return mfu

    @torch.no_grad()
    def generate(self, idx, max_new_tokens, temperature=1.0, top_k=None):
        """
        取一个条件序列的索引idx（LongTensor，形状为(b,t)）并完成序列max_new_tokens次，
        每次将预测结果馈回模型。
        最好确保模型处于model.eval()模式下进行操作。

        参数:
        - idx: 条件序列的索引
        - max_new_tokens: 最大生成的新标记数
        - temperature: 生成时的温度参数
        - top_k: 从top k个标记中进行采样

        返回:
        - 生成的序列的索引
        """
        for _ in range(max_new_tokens):
            # if the sequence context is growing too long we must crop it at block_size
            idx_cond = idx if idx.size(1) <= self.block_size else idx[:, -self.block_size:]
            # forward the model to get the logits for the index in the sequence
            logits, _ = self(idx_cond)
            # pluck the logits at the final step and scale by desired temperature
            logits = logits[:, -1, :] / temperature
            # optionally crop the logits to only the top k options
            if top_k is not None:
                v, _ = torch.topk(logits, min(top_k, logits.size(-1)))
                logits[logits < v[:, [-1]]] = -float('Inf')
            # apply softmax to convert logits to (normalized) probabilities
            probs = F.softmax(logits, dim=-1)
            # sample from the distribution
            idx_next = torch.multinomial(probs, num_samples=1)
            # append sampled index to the running sequence and continue
            idx = torch.cat((idx, idx_next), dim=1)

        return idx

