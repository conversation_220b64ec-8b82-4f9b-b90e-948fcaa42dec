{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### 因子质量提升"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- #TODO:剔除因子中的极端数据\n", "- #TODO:寻找更多有效因子"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Dataset"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import qlib\n", "from qlib.config import REG_CN\n", "from qlib.utils import init_instance_by_config, flatten_dict\n", "from copy import deepcopy\n", "\n", "from pyqlab.data.dataset.handler import DataHandlerAHF\n", "from pyqlab.const import MAIN_FUT_CODES, MAIN_SEL_FUT_CODES, SF_FUT_CODES"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Config"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[20580:MainThread](2023-12-11 21:25:50,450) INFO - qlib.Initialization - [config.py:416] - default_conf: client.\n", "[20580:MainThread](2023-12-11 21:25:50,926) INFO - qlib.Initialization - [__init__.py:74] - qlib successfully initialized based on client settings.\n", "[20580:MainThread](2023-12-11 21:25:50,926) INFO - qlib.Initialization - [__init__.py:76] - data_path={'__DEFAULT_FREQ': WindowsPath('C:/Users/<USER>/.qlib/qlib_data/cn_data')}\n"]}], "source": ["\n", "provider_uri = \"~/.qlib/qlib_data/cn_data\"  # target_dir\n", "qlib.init(provider_uri=provider_uri, region=REG_CN)\n", "\n", "###################################\n", "# train model\n", "###################################\n", "SEL_LONG_FACTOR_NAMES = [ # Slow period factor\n", "    # \"MACD\", \"MACD_DIFF\", \"MACD_DEA\", \"MOM\", \"RSI\",\n", "\n", "    \"LR_SLOPE_FAST\",\n", "    \"LR_SLOPE_MIDD\", \"LR_SLOPE_SLOW\",\n", "    # \"LR_SLOPE_FAST_THRESHOLD\", \"LR_SLOPE_SLOW_THRESHOLD\",\n", "\n", "    # \"SQUEEZE_ZERO_BARS\", \n", "    # \"SQUEEZE_GAP\", \"SQUEEZE_GAP_FAST\", \"SQUEEZE_GAP_SLOW\",\n", "    # \"SQUEEZE_GAP_THRESHOLD\", \"SQUEEZE_NARROW_BARS\",\n", "\n", "    # \"BAND_POSITION\", \"BAND_WIDTH\",\n", "    # \"BAND_EXPAND\", \"<PERSON>ND_GRADIENT\", \"BAND_GRADIENT_THRESHOLD\", \"BAND_GAP\",\n", "\n", "    # \"TL_FAST\", \"TL_SLOW\", \"TL_THRESHOLD\",\n", "\n", "    # \"TREND_VALUE\", \"TREND_BARS\", \"TREND_INBARS\", \"TREND_INPOSR\", \"TREND_HLR\",\n", "    # \"TREND_LEVEL\"\n", "]\n", "\n", "SEL_SHORT_FACTOR_NAMES = [ # Fast period factor\n", "    # \"VOLUME\", # 在RangeBar下，Volume是Bar的时长seconds\n", "    # \"AROON_UP\", \"AROON_DOWN\",\n", "    \"MACD\", \"MACD_DIFF\", \"MACD_DEA\", \"RSI\",\n", "\n", "    # \"LONG_TERM_HIGH\", \"LONG_TERM_LOW\", \"SHORT_TERM_HIGH\", \"SHORT_TERM_LOW\", \n", "    \"NEW_CHANGE_PERCENT\", \"SQUEEZE_NARROW_BARS\", \"SQUEEZE_ZERO_BARS\",\n", "\n", "\n", "    \"LR_SLOPE_FAST\", \"LR_SLOPE_MIDD\", \"LR_SLOPE_SLOW\",\n", "    \"LR_SLOPE_FAST_THRESHOLD\", \"LR_SLOPE_SLOW_THRESHOLD\",\n", "\n", "    \"STDDEV_FAST\", \"STDDEV_SLOW\", \"STDDEV_THRESHOLD\",\n", "\n", "    \"MOMENTUM_FAST\", \"MOMENTUM_MIDD\", \"MOMENTUM_SLOW\", \"MOMENTUM\",\n", "    \"MOMENTUM_THRESHOLD\",\n", "\n", "    \"SQUEEZE_ZERO_BARS\", \n", "    \"SQUEEZE_GAP\", \"SQUEEZE_GAP_FAST\", \"SQUEEZE_GAP_SLOW\",\n", "    \"SQUEEZE_GAP_THRESHOLD\", \"SQUEEZE_NARROW_BARS\",\n", "\n", "    \"BAND_POSITION\", \"BAND_WIDTH\",\n", "    \"BAND_EXPAND\", \"<PERSON>ND_GRADIENT\", \"BAND_GRADIENT_THRESHOLD\", \"BAND_GAP\",\n", "\n", "    \"TL_FAST\", \"TL_SLOW\", \"TL_THRESHOLD\",\n", "\n", "    # \"TREND_VALUE\",\n", "    \"TREND_BARS\",\n", "    \"TREND_INBARS\",\n", "    \"TREND_INPOSR\", \"TREND_HLR\",\n", "    \"TREND_LEVEL\"\n", "]\n", "\n", "SEL_CONTEXT_FACTOR_NAMES = [\n", "  \"STDDEV_RNG\", \"SHORT_RANGE\",\n", "  \"FAST_QH_RSI\", \"FAST_QH_ZSCORE\", #\"FAST_QH_DIRECT\",\n", "  \"FAST_QH_NATR\", \"FAST_QH_NATR_ZSCORE\", #\"FAST_QH_NATR_DIRECT\",\n", "  \"FAST_QH_MOM\", \"FAST_QH_MOM_ZSCORE\", #\"FAST_QH_MOM_DIRECT\",\n", "]\n", "\n", "\"\"\"\n", "根据当前测试的结果来看，主要有以下结论：\n", "1.训练的数据越多越好，如何仅用2023年的数据来训练，那么测试的结果就不好\n", "2.CONTEXT_FACTOR的因子对结果会造成噪点，最后不用\n", "3.WIN_SIZE的大小对结果有影响，WIN_SIZE越大，结果越好 10 > 5\n", "\"\"\"\n", "IS_CLASS=True # 是否是分类问题\n", "FACTOR_NUM=69\n", "WIN_SIZE=5\n", "FUT_CODES=MAIN_SEL_FUT_CODES\n", "# FUT_CODES=SF_FUT_CODES\n", "# FUT_CODES=MAIN_FUT_CODES\n", "VERSION=\"V3\"\n", "\n", "pfs = {\n", "    # ------------ main -----------\n", "    # only_trading_code\": False\n", "    # only_trading_code\": True\n", "    # ============================\n", "    # FUT_CODES=MAIN_SEL_FUT_CODES\n", "    # ============================\n", "    # =====================================================================\n", "    # '05HF_3Y_SEL': ['main.2021', 'main.2022', 'main.2023',],\n", "    # '10HF_3Y_SEL': ['main.2021', 'main.2022', 'main.2023',],\n", "    '15HF_3Y_SEL': ['main.2023',],\n", "    # '05HF_4Y_SEL': ['main.2020', 'main.2021', 'main.2022', 'main.2023',],\n", "    # '10HF_4Y_SEL': ['main.2020', 'main.2021', 'main.2022', 'main.2023',],\n", "    # '15HF_4Y_SEL': ['main.2020', 'main.2021', 'main.2022', 'main.2023',],\n", "    # -------------sf--------------\n", "    # =============================\n", "    # FUT_CODES=SF_FUT_CODES\n", "    # =============================\n", "    # '05HF_5Y_SF': ['sf.2019', 'sf.2020', 'sf.2021', 'sf.2022', 'sf.2023',],\n", "    # '10HF_5Y_SF': ['sf.2019', 'sf.2020', 'sf.2021', 'sf.2022', 'sf.2023',],\n", "    # '15HF_5Y_SF': ['sf.2019', 'sf.2020', 'sf.2021', 'sf.2022', 'sf.2023',],\n", "    # '05HF_7Y_SF': ['sf.2017', 'sf.2018', 'sf.2019', 'sf.2020', 'sf.2021', 'sf.2022', 'sf.2023',],\n", "    # '10HF_7Y_SF': ['sf.2017', 'sf.2018', 'sf.2019', 'sf.2020', 'sf.2021', 'sf.2022', 'sf.2023',],\n", "    # '15HF_7Y_SF': ['sf.2017', 'sf.2018', 'sf.2019', 'sf.2020', 'sf.2021', 'sf.2022', 'sf.2023',],\n", "}\n", "\n", "filter_win = { # default=1\n", "    '10HF_3Y_SEL': 1,\n", "}\n", "\n", "data_handler_config = {\n", "    \"start_time\": \"\",\n", "    \"end_time\": \"\",\n", "    \"instruments\": ['2020',],\n", "    \"kwargs\": {\n", "        \"win\": WIN_SIZE,                # 采样窗口,与下面的num_channel通道数保持一致\n", "        \"step\": 1,                      # 采样步长，通常为1\n", "        \"filter_win\": 1,                # 是否过滤掉特征数据\n", "        \"is_class\": IS_CLASS,           # 是否是分类问题\n", "        \"is_filter_extreme\": True,      # 是否过滤极端值\n", "        \"direct\": \"long\",\n", "        \"model_name\": \"CONV1D\",\n", "        \"model_name_suff\": \"\",          # 模型名称后缀，通常与上面的win保持一致\n", "        \"model_path\": \"./model\",\n", "        \"sel_lf_names\": SEL_LONG_FACTOR_NAMES,\n", "        \"sel_sf_names\": SEL_SHORT_FACTOR_NAMES,\n", "        \"sel_ct_names\": SEL_CONTEXT_FACTOR_NAMES,\n", "    },\n", "    \"data_loader\": {\n", "        \"class\": \"AHFDataLoader\",\n", "        \"module_path\": \"pyqlab.data.dataset.loader\",\n", "        \"kwargs\": {\n", "            \"data_path\": \"e:/featdata\",\n", "            \"train_codes\": FUT_CODES,   # 选期货交易标的\n", "        },\n", "    },\n", "}\n", "\n", "dataset_config = {\n", "    \"class\": \"AHFDatasetH\",\n", "    \"module_path\": \"pyqlab.data.dataset\",\n", "    \"kwargs\": {\n", "        \"handler\": {\n", "            \"class\": \"DataHandlerAHF\",\n", "            \"module_path\": \"pyqlab.data.dataset.handler2\",\n", "            \"kwargs\": data_handler_config,\n", "        },\n", "        \"segments\": [\"train\", \"valid\"],\n", "        \"col_set\": [\"feature\", \"label\", \"encoded\"],\n", "    },\n", "}\n", "    \n", "\n", "def get_win_size(pfs_name: str):\n", "    if pfs_name[:2] == \"05\":\n", "        return 5\n", "    elif pfs_name[:2] == \"10\":\n", "        return 10\n", "    elif pfs_name[:2] == \"15\":\n", "        return 15\n", "    else:\n", "        return 0\n", "    \n", "def get_filter_win(pfs_name: str):\n", "    if pfs_name in filter_win.keys():\n", "        return filter_win[pfs_name]\n", "    else:\n", "        return 1\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pfs_name = '15HF_3Y_SEL'\n", "direct = \"long\"\n", "handler_class_config = deepcopy(dataset_config[\"kwargs\"][\"handler\"])\n", "data_handler_config[\"instruments\"] = pfs[pfs_name]\n", "data_handler_config[\"kwargs\"][\"model_name_suff\"] = pfs_name\n", "\n", "data_handler_config[\"kwargs\"][\"win\"] = get_win_size(pfs_name)\n", "data_handler_config[\"kwargs\"][\"filter_win\"] = get_filter_win(pfs_name)\n", "\n", "data_handler_config[\"kwargs\"][\"direct\"] = direct\n", "handler_class_config[\"kwargs\"] = data_handler_config\n", "hd: DataHandlerAHF = init_instance_by_config(handler_class_config)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dataset_config[\"kwargs\"][\"handler\"] = hd\n", "dataset = init_instance_by_config(dataset_config)\n", "dataset.setup_data(handler_kwargs=data_handler_config)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# EDA"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### AICM EDA"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "from functools import partial\n", "import pandas as pd\n", "import numpy as np\n", "from sklearn.preprocessing import LabelEncoder\n", "from pyqlab.const import ALL_FACTOR_NAMES, TWO_VAL_FACTOR_NAMES, SNAPSHOT_CONTEXT, MAIN_FUT_CODES"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["#### 1.Factors\n", "- lf: long period factors\n", "- sf: short period factors\n", "- ct: market and portfolio context factors"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["feat_path = 'e:/featdata'\n", "\n", "year = 2023\n", "# lf_df = pd.read_parquet(f'{feat_path}/ffs_lf.main.{year}.parquet')\n", "sf_df = pd.read_parquet(f'{feat_path}/ffs_sf.main.{year}.parquet')\n", "# mf_df = pd.read_parquet(f'{feat_path}/ffs_mf.main.{year}.parquet')\n", "# ct_df = pd.read_parquet(f'{feat_path}/ffs_ct.main.{year}.parquet')"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["fut_codes_dict = {code: i for i, code in enumerate(MAIN_FUT_CODES)}\n", "sf_df['code_encoded'] = sf_df['code'].apply(lambda x: fut_codes_dict[x])\n", "sf_df['change'] = sf_df['change'].astype(np.float32)\n", "sf_df['change'].fillna(0.0, inplace=True)\n", "# sf_df['long_label'] = sf_df.loc[:, 'change'].apply(lambda x: 1 if x > 0.002 else 0)\n", "# sf_df['short_label'] = sf_df.loc[:, 'change'].apply(lambda x: 1 if x < -0.002 else 0)\n", "sf_df['change'] = sf_df['change'] * 100.0\n", "sf_df['change'] = sf_df.loc[(sf_df['change'] > -1) & (sf_df['change'] < 1), 'change']"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# change列标签化，用于分类问题<-0.25, -0.25~0.25, >0.25\n", "sf_df['label'] = sf_df['change'].apply(lambda x: 0 if x < -0.25 else 2 if x > 0.25 else 1)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["label\n", "1    435112\n", "0    214636\n", "2    211684\n", "Name: count, dtype: int64"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["sf_df['label'].value_counts()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["(array([  288.,   343.,   165.,   590.,   203.,   498.,   841.,   324.,\n", "          702.,  1301.,   459.,  1077.,  1824.,   681.,  2392.,  1825.,\n", "          998.,  2195.,  4079.,  3237.,  1712.,  5782.,  4715.,  2593.,\n", "         5548.,  9830.,  7575.,  4030.,  9324., 16034.,  6027., 13182.,\n", "        22946.,  8375., 28304., 21415., 11662., 37333., 12963., 41586.,\n", "        14024., 27680., 39259., 21165., 17003., 16114., 16156., 14447.,\n", "        14771., 18416., 35260., 25612., 13372., 39774., 12351., 36032.,\n", "        11413., 20820., 27842.,  8031., 22419., 12906.,  5919., 15908.,\n", "         9249.,  4065.,  7563.,  6694.,  8516.,  2528.,  4637.,  6049.,\n", "         1723.,  3234.,  4145.,  2216.,  1018.,  1807.,  2326.,   663.,\n", "         1855.,  1061.,   513.,  1181.,   686.,   298.,   860.,   520.,\n", "          248.,   594.,   176.,   343.,   287.]),\n", " array([-1.  , -0.98, -0.96, -0.94, -0.92, -0.9 , -0.88, -0.86, -0.84,\n", "        -0.82, -0.8 , -0.78, -0.76, -0.74, -0.72, -0.7 , -0.68, -0.66,\n", "        -0.64, -0.62, -0.6 , -0.58, -0.56, -0.54, -0.52, -0.5 , -0.48,\n", "        -0.46, -0.44, -0.42, -0.4 , -0.38, -0.36, -0.34, -0.32, -0.3 ,\n", "        -0.28, -0.26, -0.24, -0.22, -0.2 , -0.18, -0.16, -0.14, -0.12,\n", "        -0.1 , -0.07,  0.07,  0.1 ,  0.12,  0.14,  0.16,  0.18,  0.2 ,\n", "         0.22,  0.24,  0.26,  0.28,  0.3 ,  0.32,  0.34,  0.36,  0.38,\n", "         0.4 ,  0.42,  0.44,  0.46,  0.48,  0.5 ,  0.52,  0.54,  0.56,\n", "         0.58,  0.6 ,  0.62,  0.64,  0.66,  0.68,  0.7 ,  0.72,  0.74,\n", "         0.76,  0.78,  0.8 ,  0.82,  0.84,  0.86,  0.88,  0.9 ,  0.92,\n", "         0.94,  0.96,  0.98,  1.  ]),\n", " <BarContainer object of 93 artists>)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 画出change的分布图\n", "import matplotlib.pyplot as plt\n", "step=2\n", "midd=4\n", "bins = [x/100.0 for x in range(-100, 100+step, step)]\n", "bins = bins[:100//step-midd] + [-0.07, 0.07] + bins[-100//step+midd:]\n", "# bins = [-np.inf] + bins + [np.inf]\n", "plt.hist(sf_df['change'], bins=bins, label='change')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(bins)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 使用自定义分箱\n", "sf_df['change_bins'] = pd.cut(sf_df['change'], bins=bins, labels=False)\n", "sf_df['change_bins'].fillna(0, inplace=True)\n", "sf_df['change_bins'] = sf_df['change_bins'].astype(np.int32)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# change2列change按分箱区间映射取值\n", "sf_df['change2'] = sf_df['change_bins'].apply(lambda x: bins[x])\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### All Factors Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lf_df['code'].unique().shape, sf_df['code'].unique().shape, ct_df['code'].unique().shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lf_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_df"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# lf_df.head(100).to_csv(f'{feat_path}/lf_df.csv')\n", "sf_df.head(300).to_csv(f'{feat_path}/sf_df.csv')\n", "# ct_df.head(100).to_csv(f'{feat_path}/ct_df.csv')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ct_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ct_df[\"DAYOFWEEK\"].value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ct_df[\"HOUR\"].value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 通过date列timestamp，并将时区转换为北京时间\n", "ct_df['datetime'] = pd.to_datetime(ct_df['date'] + 28800, unit='s')\n", "ct_df['DAYOFWEEK'] = pd.to_datetime(ct_df['date'] + 28800, unit='s').dt.dayofweek\n", "ct_df['HOUR'] = pd.to_datetime(ct_df['date'] + 28800, unit='s').dt.hour\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ct_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_df.describe(percentiles=[0.01, 0.05, 0.95, 0.99])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 统计BAND_EXPAND的分布\n", "sf_df['BAND_EXPAND_2'].describe(percentiles=[0.01, 0.05, 0.95, 0.99])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_df['BAND_EXPAND_2'].hist(bins=100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_df.loc[sf_df['BAND_EXPAND_2']>15]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ct_df"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Select Factors\n", "- 有很多列都为空值，根据需要将其剔除"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["SEL_LONG_FACTOR_NAMES = [ # Slow period factor\n", "    # \"MACD\", \"MACD_DIFF\", \"MACD_DEA\", \"MOM\", \"RSI\",\n", "\n", "    \"LR_SLOPE_FAST\",\n", "    \"LR_SLOPE_MIDD\", \"LR_SLOPE_SLOW\",\n", "    # \"LR_SLOPE_FAST_THRESHOLD\", \"LR_SLOPE_SLOW_THRESHOLD\",\n", "\n", "    # \"SQUEEZE_ZERO_BARS\", \n", "    # \"SQUEEZE_GAP\", \"SQUEEZE_GAP_FAST\", \"SQUEEZE_GAP_SLOW\",\n", "    # \"SQUEEZE_GAP_THRESHOLD\", \"SQUEEZE_NARROW_BARS\",\n", "\n", "    # \"BAND_POSITION\", \"BAND_WIDTH\",\n", "    # \"BAND_EXPAND\", \"<PERSON>ND_GRADIENT\", \"BAND_GRADIENT_THRESHOLD\", \"BAND_GAP\",\n", "\n", "    # \"TL_FAST\", \"TL_SLOW\", \"TL_THRESHOLD\",\n", "\n", "    # \"TREND_VALUE\", \"TREND_BARS\", \"TREND_INBARS\", \"TREND_INPOSR\", \"TREND_HLR\",\n", "    # \"TREND_LEVEL\"\n", "]\n", "\n", "SEL_SHORT_FACTOR_NAMES = [ # Fast period factor\n", "    # \"VOLUME\", # 在RangeBar下，Volume是Bar的时长seconds\n", "    \"MACD\", \"MACD_DIFF\", \"MACD_DEA\", \"RSI\",\n", "\n", "    \"LR_SLOPE_FAST\", \"LR_SLOPE_MIDD\", \"LR_SLOPE_SLOW\",\n", "    \"LR_SLOPE_FAST_THRESHOLD\", \"LR_SLOPE_SLOW_THRESHOLD\",\n", "\n", "    \"STDDEV_FAST\", \"STDDEV_SLOW\", \"STDDEV_THRESHOLD\",\n", "\n", "    \"MOMENTUM_FAST\", \"MOMENTUM_MIDD\", \"MOMENTUM_SLOW\", \"MOMENTUM\",\n", "    \"MOMENTUM_THRESHOLD\",\n", "\n", "    \"SQUEEZE_ZERO_BARS\", \n", "    \"SQUEEZE_GAP\", \"SQUEEZE_GAP_FAST\", \"SQUEEZE_GAP_SLOW\",\n", "    \"SQUEEZE_GAP_THRESHOLD\", \"SQUEEZE_NARROW_BARS\",\n", "\n", "    \"BAND_POSITION\", \"BAND_WIDTH\",\n", "    \"BAND_EXPAND\", \"<PERSON>ND_GRADIENT\", \"BAND_GRADIENT_THRESHOLD\", \"BAND_GAP\",\n", "\n", "    \"TL_FAST\", \"TL_SLOW\", \"TL_THRESHOLD\",\n", "\n", "    \"TREND_VALUE\",\n", "    \"TREND_BARS\",\n", "    \"TREND_INBARS\",\n", "    \"TREND_INPOSR\", \"TREND_HLR\",\n", "    \"TREND_LEVEL\"\n", "]\n", "\n", "SEL_CONTEXT_FACTOR_NAMES = [\n", "  \"STDDEV_RNG\", \"SHORT_RANGE\",\n", "  \"FAST_QH_RSI\", \"FAST_QH_ZSCORE\", \"FAST_QH_DIRECT\",\n", "  \"FAST_QH_NATR\", \"FAST_QH_NATR_ZSCORE\", \"FAST_QH_NATR_DIRECT\",\n", "  \"FAST_QH_MOM\", \"FAST_QH_MOM_ZSCORE\", \"FAST_QH_MOM_DIRECT\",\n", "]\n", "\n", "def _long_factor_select(n):\n", "    if len(SEL_LONG_FACTOR_NAMES) == 0:\n", "        return 0\n", "    if ALL_FACTOR_NAMES[n] in SEL_LONG_FACTOR_NAMES:\n", "        if ALL_FACTOR_NAMES[n] in TWO_VAL_FACTOR_NAMES:\n", "            return 2\n", "        else:\n", "            return 1\n", "    return 0\n", "\n", "def _short_factor_select(n):\n", "    if len(SEL_SHORT_FACTOR_NAMES) == 0:\n", "        return 0\n", "    if ALL_FACTOR_NAMES[n] in SEL_SHORT_FACTOR_NAMES:\n", "        if ALL_FACTOR_NAMES[n] in TWO_VAL_FACTOR_NAMES:\n", "            return 2\n", "        else:\n", "            return 1\n", "    return 0\n", "\n", "def _factor_select_name(sel_list):\n", "    sel_name=[]\n", "    if len(sel_list) == 0:\n", "        return sel_name\n", "    for n in range(len(ALL_FACTOR_NAMES)):\n", "        if sel_list[n] > 0:\n", "            sel_name.append(ALL_FACTOR_NAMES[n])\n", "    return sel_name\n", "\n", "def _context_select_name(sel_list):\n", "    sel_name=[]\n", "    if len(sel_list) == 0:\n", "        return sel_name\n", "    for n in range(len(SNAPSHOT_CONTEXT)):\n", "        if sel_list[n] > 0:\n", "            sel_name.append(SNAPSHOT_CONTEXT[n])\n", "    return sel_name\n", "\n", "def _get_factor_cols(factor_type=\"lf\"):\n", "    \"\"\"\n", "    因子列名称\n", "    \"\"\"\n", "    col_names = []\n", "    if factor_type == \"lf\":\n", "        for name in SEL_LONG_FACTOR_NAMES:\n", "            if name in TWO_VAL_FACTOR_NAMES:\n", "                col_names.append(f\"{name}_1\")\n", "                col_names.append(f\"{name}_2\")\n", "            else:\n", "                col_names.append(f\"{name}_2\")\n", "\n", "    if factor_type == \"sf\":\n", "        for name in SEL_SHORT_FACTOR_NAMES: # SEL_SHORT_FACTOR_NAMES:\n", "            if name in TWO_VAL_FACTOR_NAMES:\n", "                col_names.append(f\"{name}_1\")\n", "                col_names.append(f\"{name}_2\")\n", "            else:\n", "                col_names.append(f\"{name}_2\")\n", "\n", "    if factor_type == \"ct\":\n", "        col_names.extend(SEL_CONTEXT_FACTOR_NAMES)\n", "\n", "    return col_names"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["col_names = _get_factor_cols(\"lf\")+_get_factor_cols(\"sf\")+_get_factor_cols(\"ct\")\n", "print(len(col_names))\n", "col_names"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sel_lf = [_long_factor_select(n) for n in range(len(ALL_FACTOR_NAMES))]\n", "sel_name = _factor_select_name(sel_lf)\n", "print(sel_name)\n", "print(len(sel_name), len(SEL_LONG_FACTOR_NAMES))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sel_sf = [_short_factor_select(n) for n in range(len(ALL_FACTOR_NAMES))]\n", "sel_name = _factor_select_name(sel_sf)\n", "print(sel_name)\n", "print(len(sel_name), len(SEL_SHORT_FACTOR_NAMES))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_df['change'] = sf_df['change'].astype('float')\n", "sf_df['label_long'] = sf_df.loc[:, 'change'].apply(lambda x: 1 if x > 0.002 else 0)\n", "sf_df['label_short'] = sf_df.loc[:, 'change'].apply(lambda x: 1 if x < -0.002 else 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["col_names = _get_factor_cols(factor_type=\"sf\")\n", "col_names += ['code', 'date', 'change', 'label_long', 'label_short']\n", "sel_sf_df = sf_df[col_names]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sel_sf_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sel_sf_df.reset_index(drop=True, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sel_sf_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Filter win"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 计算绝对值最大的行序号\n", "rolling_max_index = sel_sf_df['change'].rolling(window=5).apply(lambda x: abs(x).idxmax())\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rolling_max_index"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# 移动窗口的第一个值会返回 NaN，所以可以使用 fillna 方法填充为 0 或其他合适的值\n", "rolling_max_index = rolling_max_index.fillna(0)\n", "\n", "# 将结果转换为整数\n", "rolling_max_index = rolling_max_index.astype(int)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i in range(len(rolling_max_index)):\n", "    if rolling_max_index[i] == sel_sf_df.index[i]:\n", "        print(rolling_max_index[i])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "print(rolling_max_index.head(50))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Standard"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from functools import partial"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def standardize(group, means, stds):\n", "    code = group.name\n", "    mean = means.loc[code]\n", "    std = stds.loc[code]\n", "    group = (group - mean) / std\n", "    # print(code, mean, std)\n", "    return group\n", "\n", "lf_mean = pd.read_csv(f'{feat_path}/lf_mean.csv', index_col=0)\n", "lf_std = pd.read_csv(f'{feat_path}/lf_std.csv', index_col=0)\n", "sf_mean = pd.read_csv(f'{feat_path}/sf_mean.csv', index_col=0)\n", "sf_std = pd.read_csv(f'{feat_path}/sf_std.csv', index_col=0)\n", "ct_mean = pd.read_csv(f'{feat_path}/ct_mean.csv', index_col=0)\n", "ct_std = pd.read_csv(f'{feat_path}/ct_std.csv', index_col=0)\n", "print(sf_mean.shape, sf_std.shape)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Preparing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["le = LabelEncoder()\n", "sf_df['code_encoded'] = le.fit_transform(sf_df['code'].values)\n", "\n", "# 生成模型输入数据配置文件\n", "# 放在数据处理的前面，以保证因子顺序与系统一致\n", "# self._dump_input_param_json()\n", "is_class = False\n", "direct = 'long'\n", "if is_class: # 分类问题，生成标签\n", "    print(\"-----分类问题-----\")\n", "    if direct == 'long':\n", "        sf_df['label'] = sf_df.loc[:, 'change'].apply(lambda x: 1 if x > 0.002 else 0)\n", "    elif direct == 'short':\n", "        sf_df['label'] = sf_df.loc[:, 'change'].apply(lambda x: 1 if x < -0.002 else 0)\n", "    else:\n", "        raise ValueError(f\"direct {direct} is not supported\")\n", "else:\n", "    print(\"-----非分类问题-----\")\n", "    sf_df['label'] = sf_df.loc[:, 'change']\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "col_names = _get_factor_cols(factor_type=\"lf\")\n", "if len(col_names) > 0:\n", "    lf_df = lf_df[col_names + ['code']]\n", "    df_mean = lf_mean[col_names]\n", "    df_std = lf_std[col_names]\n", "    partial_func = partial(standardize, means=df_mean, stds=df_std)\n", "    df_standardized = lf_df[col_names + ['code']].groupby('code')[col_names].apply(partial_func)\n", "    df_standardized.fillna(0.0, inplace=True)\n", "    df_standardized.reset_index(drop=False, inplace=True)\n", "    # df_standardized.sort_values(by=['code'], inplace=True)\n", "    lf_df = df_standardized[col_names]\n", "else:\n", "    lf_df = pd.DataFrame()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lf_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "col_names = _get_factor_cols(factor_type=\"sf\")\n", "if len(col_names) > 0:\n", "    sf_df = sf_df[col_names + ['code', 'date', 'change', 'code_encoded', 'label']]\n", "    df_mean = sf_mean[col_names]\n", "    df_std = sf_std[col_names]\n", "    partial_func = partial(standardize, means=df_mean, stds=df_std)\n", "    df_standardized = sf_df[col_names + ['code']].groupby('code')[col_names].apply(partial_func)\n", "    df_standardized.fillna(0.0, inplace=True)\n", "    df_standardized.reset_index(drop=False, inplace=True)\n", "    # df_standardized.sort_values(by=['code'], inplace=True)\n", "    sf_df[col_names] = df_standardized[col_names]\n", "else:\n", "    sf_df = pd.DataFrame()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sf_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "col_names = _get_factor_cols(factor_type=\"ct\")\n", "if len(col_names) > 0:\n", "    ct_df = ct_df[col_names + ['code']]\n", "    df_mean = ct_mean[col_names]\n", "    df_std = ct_std[col_names]\n", "    partial_func = partial(standardize, means=df_mean, stds=df_std)\n", "    df_standardized = ct_df[col_names + ['code']].groupby('code')[col_names].apply(partial_func)\n", "    df_standardized.fillna(0.0, inplace=True)\n", "    df_standardized.reset_index(drop=False, inplace=True)\n", "    # df_standardized.sort_values(by=['code'], inplace=True)\n", "    ct_df = df_standardized[col_names]\n", "else:\n", "    ct_df = pd.DataFrame()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(lf_df)\n", "print(sf_df)\n", "print(ct_df)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ft_df = pd.concat([lf_df, sf_df, ct_df], axis=1)\n", "print(ft_df.columns.tolist())\n", "print(f\"\\n===============\\n\\nft{ft_df.shape} lf{lf_df.shape} sf{sf_df.shape} ct{ct_df.shape}\\n\\n================\\n\")\n", "print(ft_df)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 合并后清除数据\n", "# ft_df.dropna(axis=0, how='any', inplace=True)\n", "ft_df.fillna(0.0, inplace=True)\n", "if 'RSI_2' in ft_df.columns:\n", "    ft_df = ft_df[ft_df['RSI_2'] != 0.0]\n", "if 'FAST_QH_NATR_ZSCORE' in ft_df.columns:\n", "    ft_df = ft_df[ft_df['FAST_QH_NATR_ZSCORE'] != 0.0]\n", "\n", "lb_df = ft_df[['code', 'date', 'change', 'code_encoded', 'label']]\n", "lb_df.reset_index(drop=True, inplace=True)\n", "\n", "ft_df.drop(['code', 'date', 'change', 'code_encoded', 'label'], axis=1, inplace=True)\n", "ft_df = ft_df.astype(np.float32)\n", "\n", "print(f\"\\n===============\\n\\nlb{lb_df.shape} ft{ft_df.shape}\\n\\n================\\n\")\n", "print(ft_df)\n", "print(lb_df)\n", "print(direct)\n", "print(lb_df['label'].value_counts())\n", "\n", "data1 = ft_df.values\n", "data2 = lb_df.values[:, -1]\n", "data3 = lb_df.values[:, -2]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_col_names = df_mean.columns.to_list()\n", "col_names = _get_factor_cols(factor_type=\"sf\")\n", "# all_col_names在col_names中的索引\n", "sel_index = [all_col_names.index(name) for name in col_names]\n", "print(len(sel_index), sel_index)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_mean = df_mean[col_names]\n", "df_std = df_std[col_names]\n", "print(df_mean.shape, df_std.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sel_sf_df2 = sel_sf_df[['code', 'date'] + col_names]\n", "sel_sf_df2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建一个偏函数\n", "my_partial_func = partial(standardize, means=df_mean, stds=df_std)\n", "df_standardized = sel_sf_df2.groupby('code')[col_names].apply(my_partial_func)\n", "# df_standardized.reset_index(drop=False, inplace=True)\n", "# df_standardized.fillna(0.0, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_standardized"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_stand = df_standardized.loc[(df_standardized['RSI_2'] < 3) & (df_standardized['RSI_2'] > -3)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_stand"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_stand.describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_standardized.describe().to_csv(f'{feat_path}/sf_standardized_describe.csv')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sel_sf_df[col_names] = df_standardized[col_names]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sel_sf_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 将因子数据按CODE分组求均值和标准差\n", "sf_df_mean = sel_sf_df.groupby('code').mean()\n", "sf_df_std = sel_sf_df.groupby('code').std()\n", "print(sf_df_mean.shape, sf_df_std.shape)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Filter Factors"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TODO: 由于长假或主力期货合约变更，导致部分合约数据有很大跳空，行情失真，因此需要去除这些数据\n", "# 1. 选择特定Factor（不受合约不太影响，如RSI等），计算统计值\n", "# 2. 选择统计值在一定范围内的数据"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Check Model Report"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rpt_path = 'd:/RoboQuant2/rpt'\n", "json_file = 'model_test_202306172311.json'\n", "with open(f'{rpt_path}/{json_file}') as file:\n", "    # Load the JSON data\n", "    data = json.load(file)\n", "models = data['models']\n", "data.pop('models')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dfs = pd.DataFrame()\n", "for code in data.keys():\n", "    df = pd.DataFrame(data[code], columns=['change'] + models)\n", "    df [\"change\"] = df[\"change\"].shift(-1)\n", "    df.dropna(inplace=True)\n", "    df = df.loc[df['change'] != 0.0, :]\n", "    df.insert(0, 'code', code)\n", "    dfs = pd.concat([dfs, df], axis=0)\n", "# 删除loc.columns[2:]列所有列元素都为0的列\n", "# dfs = dfs.loc[:, dfs[dfs.columns[2:]].sum(axis=0) > 0.0]\n", "# # 删除所有行元素都为0的行\n", "dfs = dfs.loc[dfs[dfs.columns[2:]].sum(axis=1) > 0.0, :]\n", "dfs.reset_index(drop=True, inplace=True)\n", "print(dfs.shape)\n", "dfs.to_csv(f'{rpt_path}/{json_file}.csv', index=False)\n", "dfs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dfs.describe(percentiles=[0.75, 0.95, 0.98, 0.99])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Code test"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "data = {\n", "    'Column1': [1, 2, 3, 4, 5],\n", "    'Column2': [3, 4, 5, 25, 30],\n", "    'Column3': [0.5, 1.5, 2.5, 3.5, 4.5]\n", "}\n", "\n", "df = pd.DataFrame(data)\n", "print(df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 设置阈值\n", "threshold = 3\n", "\n", "# 创建逻辑掩码，筛选值大于阈值的行\n", "mask = df > threshold\n", "\n", "# 使用掩码剔除不满足条件的行\n", "filtered_df = df[~mask.any(axis=1)]\n", "\n", "print(filtered_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "# 创建一个示例矩阵\n", "matrix = np.array([[1, 2, 0.5],\n", "                   [2, 3, 1.5],\n", "                   [3, 4, 2.5],\n", "                   [4, 25, 3.5],\n", "                   [5, 30, 4.5]])\n", "\n", "print(\"Original matrix:\")\n", "print(matrix)\n", "\n", "# 设置阈值\n", "threshold_lower = 2\n", "threshold_upper = 4\n", "\n", "# 创建逻辑掩码，同时满足两个条件\n", "mask_lower = matrix[:, 1] > threshold_lower\n", "mask_upper = matrix[:, 1] < threshold_upper\n", "combined_mask = mask_lower & mask_upper\n", "\n", "# 使用掩码剔除不满足条件的行\n", "filtered_matrix = matrix[combined_mask]\n", "\n", "print(\"Filtered matrix:\")\n", "print(filtered_matrix)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(filtered_matrix)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "# 创建一个示例矩阵\n", "matrix = np.array([[1, 2, 0.5],\n", "                   [2, 3, 1.5],\n", "                   [3, 4, 2.5],\n", "                   [4, 25, 3.5],\n", "                   [5, 30, 4.5]])\n", "\n", "print(\"Original matrix:\")\n", "print(matrix)\n", "(matrix > 3).any() or (matrix < -3).any()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### TICK DATA"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["data_path = 'e:/hqdata/tick/2022'\n", "df = pd.read_parquet(f'{data_path}/SF202201.parquet', engine='fastparquet')"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["df.sort_values(by=['datetime'], inplace=True)\n", "df.reset_index(drop=True, inplace=True)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>datetime</th>\n", "      <th>code</th>\n", "      <th>price</th>\n", "      <th>volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>index</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2022-01-04 09:29:00</td>\n", "      <td>IF9999.SF</td>\n", "      <td>4960.0</td>\n", "      <td>144</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2022-01-04 09:30:00</td>\n", "      <td>IF9999.SF</td>\n", "      <td>4961.0</td>\n", "      <td>76</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2022-01-04 09:30:01</td>\n", "      <td>IF9999.SF</td>\n", "      <td>4964.0</td>\n", "      <td>58</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2022-01-04 09:30:01</td>\n", "      <td>IF9999.SF</td>\n", "      <td>4962.6</td>\n", "      <td>55</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2022-01-04 09:30:01</td>\n", "      <td>IF9999.SF</td>\n", "      <td>4962.0</td>\n", "      <td>67</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1795</th>\n", "      <td>2022-01-28 14:59:58</td>\n", "      <td>IF9999.SF</td>\n", "      <td>4541.0</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1796</th>\n", "      <td>2022-01-28 14:59:58</td>\n", "      <td>IF9999.SF</td>\n", "      <td>4542.0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1797</th>\n", "      <td>2022-01-28 14:59:59</td>\n", "      <td>IF9999.SF</td>\n", "      <td>4540.2</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1798</th>\n", "      <td>2022-01-28 14:59:59</td>\n", "      <td>IF9999.SF</td>\n", "      <td>4540.6</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1799</th>\n", "      <td>2022-01-28 15:00:00</td>\n", "      <td>IF9999.SF</td>\n", "      <td>4540.6</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>337269 rows × 4 columns</p>\n", "</div>"], "text/plain": ["                 datetime       code   price  volume\n", "index                                               \n", "0     2022-01-04 09:29:00  IF9999.SF  4960.0     144\n", "1     2022-01-04 09:30:00  IF9999.SF  4961.0      76\n", "4     2022-01-04 09:30:01  IF9999.SF  4964.0      58\n", "3     2022-01-04 09:30:01  IF9999.SF  4962.6      55\n", "2     2022-01-04 09:30:01  IF9999.SF  4962.0      67\n", "...                   ...        ...     ...     ...\n", "1795  2022-01-28 14:59:58  IF9999.SF  4541.0       3\n", "1796  2022-01-28 14:59:58  IF9999.SF  4542.0       2\n", "1797  2022-01-28 14:59:59  IF9999.SF  4540.2       2\n", "1798  2022-01-28 14:59:59  IF9999.SF  4540.6       2\n", "1799  2022-01-28 15:00:00  IF9999.SF  4540.6       2\n", "\n", "[337269 rows x 4 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df.loc[df['code'] == 'IF9999.SF', :]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}