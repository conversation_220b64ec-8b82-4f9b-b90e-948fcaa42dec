
from config import DQN_PARAMS, RLlib_PARAMS

# from rllab.env.env_stocktrading import StockTradingEnv
from rllab.env.env_futtrading import FutureTradingEnv


def test(
        drl_lib,
        env,
        model_name,
        **kwargs
):
    # import DRL agents
    from rllab.agents.stablebaselines3.models import DRLAgent as DRLAgent_sb3
    from rllab.agents.rllib.models import DRLAgent as DRLAgent_rllib

    env_config = {
        "if_train": False,
    }
    env_instance = env(if_train=False)

    # load elegantrl needs state dim, action dim and net dim
    net_dimension = kwargs.get("net_dimension", 2 ** 7)
    cwd = kwargs.get("cwd", "./" + str(model_name))
    # print("price_array: ", len(price_array))

    if drl_lib == "rllib":
        # load agent
        episode_total_assets = DRLAgent_rllib.DRL_prediction(
            model_name=model_name,
            env=env,
            agent_path=cwd,
        )

        return episode_total_assets

    elif drl_lib == "stable_baselines3":
        episode_total_assets = DRLAgent_sb3.DRL_prediction_load_from_file(
            model_name=model_name, environment=env_instance, cwd=cwd
        )

        return episode_total_assets
    else:
        raise ValueError("DRL library input is NOT supported. Please check.")


if __name__ == "__main__":
    env = FutureTradingEnv

    # demo for elegantrl
    kwargs = {}  # in current finrl_meta, with respect yahoofinance, kwargs is {}. For other data sources, such as joinquant, kwargs is not empty

    # demo for rllib
    import ray
    ray.shutdown()  # always shutdown previous session if any
    account_value_rllib = test(
        drl_lib="rllib",
        env=env,
        model_name="ppo",
        cwd="./test_ppo/checkpoint_000030/checkpoint-30",
        rllib_params=RLlib_PARAMS,
    )
    
    # # demo for stable baselines3
    # account_value_sb3 = test(
    #     start_date=TEST_START_DATE,
    #     end_date=TEST_END_DATE,
    #     ticker_list=DOW_30_TICKER,
    #     data_source="yahoofinance",
    #     time_interval="1D",
    #     technical_indicator_list=TECHNICAL_INDICATORS_LIST,
    #     drl_lib="stable_baselines3",
    #     env=env,
    #     model_name="sac",
    #     cwd="./test_sac.zip",
    # )
