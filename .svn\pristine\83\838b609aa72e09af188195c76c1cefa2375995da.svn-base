{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import datetime\n", "import talib as ta\n", "import pandas as pd\n", "import numpy as np\n", "import sys\n", "import matplotlib as mpl\n", "import matplotlib.pyplot as plt\n", "from pyecharts import Kline,Line,Overlap,Grid,online\n", "online()\n", "sys.path.append(\"d:/QuantLab\")\n", "from qtunnel import DataSource,Backtest,BarData,BarSize,DoRight,RunMode\n", "from qtunnel import DB,Mode,Cursor,Transaction,create_db,registered_dbs"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["ds=DataSource(RunMode.passive)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<script>\n", "    require.config({\n", "        paths: {\n", "            'echarts': 'https://pyecharts.github.io/jupyter-echarts/echarts/echarts.min'\n", "        }\n", "    });\n", "</script>\n", "    <div id=\"8140804aa45047b3a52dbfd1853e66c8\" style=\"width:980px;height:700px;\"></div>\n", "\n", "\n", "<script>\n", "    require(['echarts'], function(echarts) {\n", "        \n", "var myChart_8140804aa45047b3a52dbfd1853e66c8 = echarts.init(document.getElementById('8140804aa45047b3a52dbfd1853e66c8'), 'light', {renderer: 'canvas'});\n", "\n", "var option_8140804aa45047b3a52dbfd1853e66c8 = {\n", "    \"title\": [\n", "        {\n", "            \"text\": \"Index ATR/STDDEV\",\n", "            \"left\": \"auto\",\n", "            \"top\": \"50%\",\n", "            \"textStyle\": {\n", "                \"fontSize\": 18\n", "            },\n", "            \"subtextStyle\": {\n", "                \"fontSize\": 12\n", "            }\n", "        },\n", "        {\n", "            \"text\": \"K \\u7ebf\\u56fe MA8888.ZC BarSize.min5\",\n", "            \"left\": \"auto\",\n", "            \"top\": \"auto\",\n", "            \"textStyle\": {\n", "                \"fontSize\": 18\n", "            },\n", "            \"subtextStyle\": {\n", "                \"fontSize\": 12\n", "            }\n", "        }\n", "    ],\n", "    \"toolbox\": {\n", "        \"show\": true,\n", "        \"orient\": \"vertical\",\n", "        \"left\": \"95%\",\n", "        \"top\": \"center\",\n", "        \"feature\": {\n", "            \"saveAsImage\": {\n", "                \"show\": true,\n", "                \"title\": \"save as image\"\n", "            },\n", "            \"restore\": {\n", "                \"show\": true,\n", "                \"title\": \"restore\"\n", "            },\n", "            \"dataView\": {\n", "                \"show\": true,\n", "                \"title\": \"data view\"\n", "            }\n", "        }\n", "    },\n", "    \"series_id\": 4472606,\n", "    \"tooltip\": {\n", "        \"trigger\": \"item\",\n", "        \"triggerOn\": \"mousemove|click\",\n", "        \"axisPointer\": {\n", "            \"type\": \"line\"\n", "        },\n", "        \"textStyle\": {\n", "            \"fontSize\": 14\n", "        },\n", "        \"backgroundColor\": \"rgba(50,50,50,0.7)\",\n", "        \"borderColor\": \"#333\",\n", "        \"borderWidth\": 0\n", "    },\n", "    \"series\": [\n", "        {\n", "            \"type\": \"line\",\n", "            \"name\": \"ATR\",\n", "            \"symbol\": \"emptyCircle\",\n", "            \"symbolSize\": 4,\n", "            \"smooth\": false,\n", "            \"step\": false,\n", "            \"showSymbol\": true,\n", "            \"data\": [\n", "                [\n", "                    \"2018-12-20 21:55\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 22:00\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 22:05\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 22:10\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 22:15\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 22:20\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 22:25\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 22:30\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 22:35\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 22:40\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 22:45\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 22:50\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 22:55\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 23:00\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 23:05\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 23:10\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 23:15\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 23:20\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 23:25\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-21 09:00\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-21 09:05\",\n", "                    6.35\n", "                ],\n", "                [\n", "                    \"2018-12-21 09:10\",\n", "                    6.382499999999999\n", "                ],\n", "                [\n", "                    \"2018-12-21 09:15\",\n", "                    6.963374999999999\n", "                ],\n", "                [\n", "                    \"2018-12-21 09:20\",\n", "                    6.86520625\n", "                ],\n", "                [\n", "                    \"2018-12-21 09:25\",\n", "                    6.7219459375\n", "                ],\n", "                [\n", "                    \"2018-12-21 09:30\",\n", "                    6.535848640625\n", "                ],\n", "                [\n", "                    \"2018-12-21 09:35\",\n", "                    6.5090562085937504\n", "                ],\n", "                [\n", "                    \"2018-12-21 09:40\",\n", "                    6.433603398164064\n", "                ],\n", "                [\n", "                    \"2018-12-21 09:45\",\n", "                    6.411923228255861\n", "                ],\n", "                [\n", "                    \"2018-12-21 09:50\",\n", "                    6.291327066843069\n", "                ],\n", "                [\n", "                    \"2018-12-21 09:55\",\n", "                    6.176760713500915\n", "                ],\n", "                [\n", "                    \"2018-12-21 10:00\",\n", "                    6.21792267782587\n", "                ],\n", "                [\n", "                    \"2018-12-21 10:05\",\n", "                    6.207026543934576\n", "                ],\n", "                [\n", "                    \"2018-12-21 10:10\",\n", "                    6.196675216737847\n", "                ],\n", "                [\n", "                    \"2018-12-21 10:30\",\n", "                    6.236841455900955\n", "                ],\n", "                [\n", "                    \"2018-12-21 10:35\",\n", "                    6.124999383105907\n", "                ],\n", "                [\n", "                    \"2018-12-21 10:40\",\n", "                    6.018749413950611\n", "                ],\n", "                [\n", "                    \"2018-12-21 10:45\",\n", "                    5.96781194325308\n", "                ],\n", "                [\n", "                    \"2018-12-21 10:50\",\n", "                    6.1194213460904265\n", "                ],\n", "                [\n", "                    \"2018-12-21 10:55\",\n", "                    6.063450278785905\n", "                ],\n", "                [\n", "                    \"2018-12-21 11:00\",\n", "                    6.0102777648466095\n", "                ],\n", "                [\n", "                    \"2018-12-21 11:05\",\n", "                    5.909763876604279\n", "                ],\n", "                [\n", "                    \"2018-12-21 11:10\",\n", "                    5.864275682774065\n", "                ],\n", "                [\n", "                    \"2018-12-21 11:15\",\n", "                    5.971061898635361\n", "                ],\n", "                [\n", "                    \"2018-12-21 11:20\",\n", "                    5.872508803703593\n", "                ],\n", "                [\n", "                    \"2018-12-21 11:25\",\n", "                    5.828883363518413\n", "                ],\n", "                [\n", "                    \"2018-12-21 13:30\",\n", "                    6.5374391953424915\n", "                ],\n", "                [\n", "                    \"2018-12-21 13:35\",\n", "                    6.660567235575367\n", "                ],\n", "                [\n", "                    \"2018-12-21 13:40\",\n", "                    6.677538873796598\n", "                ],\n", "                [\n", "                    \"2018-12-21 13:45\",\n", "                    6.693661930106768\n", "                ],\n", "                [\n", "                    \"2018-12-21 13:50\",\n", "                    6.558978833601429\n", "                ],\n", "                [\n", "                    \"2018-12-21 13:55\",\n", "                    7.131029891921358\n", "                ],\n", "                [\n", "                    \"2018-12-21 14:00\",\n", "                    7.524478397325291\n", "                ],\n", "                [\n", "                    \"2018-12-21 14:05\",\n", "                    7.498254477459026\n", "                ],\n", "                [\n", "                    \"2018-12-21 14:10\",\n", "                    7.673341753586075\n", "                ],\n", "                [\n", "                    \"2018-12-21 14:15\",\n", "                    7.7396746659067714\n", "                ],\n", "                [\n", "                    \"2018-12-21 14:20\",\n", "                    7.602690932611433\n", "                ],\n", "                [\n", "                    \"2018-12-21 14:25\",\n", "                    7.7225563859808615\n", "                ],\n", "                [\n", "                    \"2018-12-21 14:30\",\n", "                    7.786428566681819\n", "                ],\n", "                [\n", "                    \"2018-12-21 14:35\",\n", "                    7.697107138347728\n", "                ],\n", "                [\n", "                    \"2018-12-21 14:40\",\n", "                    7.762251781430342\n", "                ],\n", "                [\n", "                    \"2018-12-21 14:45\",\n", "                    8.574139192358825\n", "                ],\n", "                [\n", "                    \"2018-12-21 14:50\",\n", "                    8.445432232740883\n", "                ],\n", "                [\n", "                    \"2018-12-21 14:55\",\n", "                    8.173160621103838\n", "                ],\n", "                [\n", "                    \"2018-12-21 21:00\",\n", "                    8.664502590048645\n", "                ],\n", "                [\n", "                    \"2018-12-21 21:05\",\n", "                    8.631277460546212\n", "                ],\n", "                [\n", "                    \"2018-12-21 21:10\",\n", "                    8.499713587518901\n", "                ],\n", "                [\n", "                    \"2018-12-21 21:15\",\n", "                    8.524727908142955\n", "                ],\n", "                [\n", "                    \"2018-12-21 21:20\",\n", "                    8.648491512735808\n", "                ],\n", "                [\n", "                    \"2018-12-21 21:25\",\n", "                    8.416066937099018\n", "                ],\n", "                [\n", "                    \"2018-12-21 21:30\",\n", "                    8.245263590244068\n", "                ],\n", "                [\n", "                    \"2018-12-21 21:35\",\n", "                    8.133000410731864\n", "                ],\n", "                [\n", "                    \"2018-12-21 21:40\",\n", "                    7.97635039019527\n", "                ],\n", "                [\n", "                    \"2018-12-21 21:45\",\n", "                    7.8275328706855065\n", "                ],\n", "                [\n", "                    \"2018-12-21 21:50\",\n", "                    7.636156227151231\n", "                ],\n", "                [\n", "                    \"2018-12-21 21:55\",\n", "                    7.504348415793669\n", "                ],\n", "                [\n", "                    \"2018-12-21 22:00\",\n", "                    7.479130995003986\n", "                ],\n", "                [\n", "                    \"2018-12-21 22:05\",\n", "                    7.305174445253788\n", "                ],\n", "                [\n", "                    \"2018-12-21 22:10\",\n", "                    7.189915722991098\n", "                ],\n", "                [\n", "                    \"2018-12-21 22:15\",\n", "                    7.030419936841544\n", "                ],\n", "                [\n", "                    \"2018-12-21 22:20\",\n", "                    7.028898939999467\n", "                ],\n", "                [\n", "                    \"2018-12-21 22:25\",\n", "                    6.927453992999493\n", "                ],\n", "                [\n", "                    \"2018-12-21 22:30\",\n", "                    6.881081293349519\n", "                ],\n", "                [\n", "                    \"2018-12-21 22:35\",\n", "                    6.787027228682042\n", "                ],\n", "                [\n", "                    \"2018-12-21 22:40\",\n", "                    6.647675867247941\n", "                ],\n", "                [\n", "                    \"2018-12-21 22:45\",\n", "                    6.515292073885544\n", "                ],\n", "                [\n", "                    \"2018-12-21 22:50\",\n", "                    6.489527470191267\n", "                ],\n", "                [\n", "                    \"2018-12-21 22:55\",\n", "                    6.415051096681704\n", "                ],\n", "                [\n", "                    \"2018-12-21 23:00\",\n", "                    6.394298541847618\n", "                ],\n", "                [\n", "                    \"2018-12-21 23:05\",\n", "                    6.274583614755238\n", "                ],\n", "                [\n", "                    \"2018-12-21 23:10\",\n", "                    6.160854434017476\n", "                ],\n", "                [\n", "                    \"2018-12-21 23:15\",\n", "                    6.202811712316602\n", "                ],\n", "                [\n", "                    \"2018-12-21 23:20\",\n", "                    6.142671126700772\n", "                ],\n", "                [\n", "                    \"2018-12-21 23:25\",\n", "                    6.035537570365733\n", "                ],\n", "                [\n", "                    \"2018-12-24 09:00\",\n", "                    6.833760691847447\n", "                ],\n", "                [\n", "                    \"2018-12-24 09:05\",\n", "                    6.992072657255075\n", "                ],\n", "                [\n", "                    \"2018-12-24 09:10\",\n", "                    7.0924690243923205\n", "                ],\n", "                [\n", "                    \"2018-12-24 09:15\",\n", "                    7.687845573172704\n", "                ],\n", "                [\n", "                    \"2018-12-24 09:20\",\n", "                    7.803453294514069\n", "                ],\n", "                [\n", "                    \"2018-12-24 09:25\",\n", "                    8.063280629788366\n", "                ],\n", "                [\n", "                    \"2018-12-24 09:30\",\n", "                    8.510116598298946\n", "                ],\n", "                [\n", "                    \"2018-12-24 09:35\",\n", "                    8.434610768384\n", "                ],\n", "                [\n", "                    \"2018-12-24 09:40\",\n", "                    8.262880229964798\n", "                ],\n", "                [\n", "                    \"2018-12-24 09:45\",\n", "                    8.249736218466559\n", "                ],\n", "                [\n", "                    \"2018-12-24 09:50\",\n", "                    8.237249407543231\n", "                ],\n", "                [\n", "                    \"2018-12-24 09:55\",\n", "                    8.17538693716607\n", "                ],\n", "                [\n", "                    \"2018-12-24 10:00\",\n", "                    8.116617590307767\n", "                ],\n", "                [\n", "                    \"2018-12-24 10:05\",\n", "                    8.210786710792378\n", "                ],\n", "                [\n", "                    \"2018-12-24 10:10\",\n", "                    8.45024737525276\n", "                ],\n", "                [\n", "                    \"2018-12-24 10:30\",\n", "                    8.327735006490121\n", "                ],\n", "                [\n", "                    \"2018-12-24 10:35\",\n", "                    8.361348256165616\n", "                ],\n", "                [\n", "                    \"2018-12-24 10:40\",\n", "                    8.343280843357336\n", "                ],\n", "                [\n", "                    \"2018-12-24 10:45\",\n", "                    8.176116801189469\n", "                ],\n", "                [\n", "                    \"2018-12-24 10:50\",\n", "                    8.017310961129995\n", "                ],\n", "                [\n", "                    \"2018-12-24 10:55\",\n", "                    7.866445413073495\n", "                ],\n", "                [\n", "                    \"2018-12-24 11:00\",\n", "                    7.77312314241982\n", "                ],\n", "                [\n", "                    \"2018-12-24 11:05\",\n", "                    7.584466985298829\n", "                ],\n", "                [\n", "                    \"2018-12-24 11:10\",\n", "                    7.655243636033887\n", "                ],\n", "                [\n", "                    \"2018-12-24 11:15\",\n", "                    7.672481454232193\n", "                ],\n", "                [\n", "                    \"2018-12-24 11:20\",\n", "                    7.4888573815205834\n", "                ],\n", "                [\n", "                    \"2018-12-24 11:25\",\n", "                    7.264414512444555\n", "                ],\n", "                [\n", "                    \"2018-12-24 13:30\",\n", "                    7.301193786822327\n", "                ],\n", "                [\n", "                    \"2018-12-24 13:35\",\n", "                    7.436134097481211\n", "                ],\n", "                [\n", "                    \"2018-12-24 13:40\",\n", "                    7.36432739260715\n", "                ],\n", "                [\n", "                    \"2018-12-24 13:45\",\n", "                    7.3461110229767925\n", "                ],\n", "                [\n", "                    \"2018-12-24 13:50\",\n", "                    7.178805471827952\n", "                ],\n", "                [\n", "                    \"2018-12-24 13:55\",\n", "                    7.069865198236554\n", "                ],\n", "                [\n", "                    \"2018-12-24 14:00\",\n", "                    6.916371938324727\n", "                ],\n", "                [\n", "                    \"2018-12-24 14:05\",\n", "                    6.770553341408491\n", "                ],\n", "                [\n", "                    \"2018-12-24 14:10\",\n", "                    6.632025674338067\n", "                ],\n", "                [\n", "                    \"2018-12-24 14:15\",\n", "                    6.600424390621162\n", "                ],\n", "                [\n", "                    \"2018-12-24 14:20\",\n", "                    6.6204031710901035\n", "                ],\n", "                [\n", "                    \"2018-12-24 14:25\",\n", "                    6.839383012535597\n", "                ],\n", "                [\n", "                    \"2018-12-24 14:30\",\n", "                    6.747413861908818\n", "                ],\n", "                [\n", "                    \"2018-12-24 14:35\",\n", "                    6.6600431688133765\n", "                ],\n", "                [\n", "                    \"2018-12-24 14:40\",\n", "                    6.477041010372707\n", "                ],\n", "                [\n", "                    \"2018-12-24 14:45\",\n", "                    6.353188959854071\n", "                ],\n", "                [\n", "                    \"2018-12-24 14:50\",\n", "                    6.285529511861368\n", "                ],\n", "                [\n", "                    \"2018-12-24 14:55\",\n", "                    6.3712530362683\n", "                ],\n", "                [\n", "                    \"2018-12-24 21:00\",\n", "                    7.352690384454886\n", "                ],\n", "                [\n", "                    \"2018-12-24 21:05\",\n", "                    7.385055865232141\n", "                ],\n", "                [\n", "                    \"2018-12-24 21:10\",\n", "                    7.2658030719705335\n", "                ],\n", "                [\n", "                    \"2018-12-24 21:15\",\n", "                    7.302512918372007\n", "                ],\n", "                [\n", "                    \"2018-12-24 21:20\",\n", "                    7.137387272453407\n", "                ],\n", "                [\n", "                    \"2018-12-24 21:25\",\n", "                    6.980517908830737\n", "                ],\n", "                [\n", "                    \"2018-12-24 21:30\",\n", "                    6.7314920133892\n", "                ],\n", "                [\n", "                    \"2018-12-24 21:35\",\n", "                    6.594917412719741\n", "                ],\n", "                [\n", "                    \"2018-12-24 21:40\",\n", "                    6.615171542083755\n", "                ],\n", "                [\n", "                    \"2018-12-24 21:45\",\n", "                    6.534412964979566\n", "                ],\n", "                [\n", "                    \"2018-12-24 21:50\",\n", "                    6.357692316730588\n", "                ],\n", "                [\n", "                    \"2018-12-24 21:55\",\n", "                    6.239807700894058\n", "                ],\n", "                [\n", "                    \"2018-12-24 22:00\",\n", "                    6.377817315849355\n", "                ],\n", "                [\n", "                    \"2018-12-24 22:05\",\n", "                    6.408926450056887\n", "                ],\n", "                [\n", "                    \"2018-12-24 22:10\",\n", "                    6.238480127554043\n", "                ],\n", "                [\n", "                    \"2018-12-24 22:15\",\n", "                    6.2265561211763405\n", "                ],\n", "                [\n", "                    \"2018-12-24 22:20\",\n", "                    6.115228315117523\n", "                ],\n", "                [\n", "                    \"2018-12-24 22:25\",\n", "                    5.959466899361647\n", "                ],\n", "                [\n", "                    \"2018-12-24 22:30\",\n", "                    5.961493554393565\n", "                ],\n", "                [\n", "                    \"2018-12-24 22:35\",\n", "                    5.863418876673887\n", "                ],\n", "                [\n", "                    \"2018-12-24 22:40\",\n", "                    5.9202479328401925\n", "                ],\n", "                [\n", "                    \"2018-12-24 22:45\",\n", "                    5.924235536198183\n", "                ],\n", "                [\n", "                    \"2018-12-24 22:50\",\n", "                    5.828023759388274\n", "                ],\n", "                [\n", "                    \"2018-12-24 22:55\",\n", "                    5.73662257141886\n", "                ],\n", "                [\n", "                    \"2018-12-24 23:00\",\n", "                    5.949791442847917\n", "                ],\n", "                [\n", "                    \"2018-12-24 23:05\",\n", "                    6.102301870705521\n", "                ],\n", "                [\n", "                    \"2018-12-24 23:10\",\n", "                    6.147186777170245\n", "                ],\n", "                [\n", "                    \"2018-12-24 23:15\",\n", "                    6.089827438311732\n", "                ],\n", "                [\n", "                    \"2018-12-24 23:20\",\n", "                    5.985336066396146\n", "                ],\n", "                [\n", "                    \"2018-12-24 23:25\",\n", "                    5.986069263076338\n", "                ],\n", "                [\n", "                    \"2018-12-25 09:00\",\n", "                    8.736765799922521\n", "                ],\n", "                [\n", "                    \"2018-12-25 09:05\",\n", "                    9.149927509926396\n", "                ],\n", "                [\n", "                    \"2018-12-25 09:10\",\n", "                    9.192431134430077\n", "                ],\n", "                [\n", "                    \"2018-12-25 09:15\",\n", "                    9.232809577708574\n", "                ],\n", "                [\n", "                    \"2018-12-25 09:20\",\n", "                    9.021169098823146\n", "                ],\n", "                [\n", "                    \"2018-12-25 09:25\",\n", "                    8.920110643881987\n", "                ],\n", "                [\n", "                    \"2018-12-25 09:30\",\n", "                    9.524105111687888\n", "                ],\n", "                [\n", "                    \"2018-12-25 09:35\",\n", "                    9.397899856103493\n", "                ],\n", "                [\n", "                    \"2018-12-25 09:40\",\n", "                    9.478004863298318\n", "                ],\n", "                [\n", "                    \"2018-12-25 09:45\",\n", "                    9.404104620133403\n", "                ],\n", "                [\n", "                    \"2018-12-25 09:50\",\n", "                    9.183899389126733\n", "                ],\n", "                [\n", "                    \"2018-12-25 09:55\",\n", "                    9.024704419670396\n", "                ],\n", "                [\n", "                    \"2018-12-25 10:00\",\n", "                    8.873469198686877\n", "                ],\n", "                [\n", "                    \"2018-12-25 10:05\",\n", "                    8.929795738752533\n", "                ],\n", "                [\n", "                    \"2018-12-25 10:10\",\n", "                    8.683305951814907\n", "                ],\n", "                [\n", "                    \"2018-12-25 10:30\",\n", "                    8.549140654224162\n", "                ],\n", "                [\n", "                    \"2018-12-25 10:35\",\n", "                    8.671683621512955\n", "                ],\n", "                [\n", "                    \"2018-12-25 10:40\",\n", "                    8.588099440437308\n", "                ],\n", "                [\n", "                    \"2018-12-25 10:45\",\n", "                    8.408694468415442\n", "                ],\n", "                [\n", "                    \"2018-12-25 10:50\",\n", "                    8.53825974499467\n", "                ],\n", "                [\n", "                    \"2018-12-25 10:55\",\n", "                    8.411346757744937\n", "                ],\n", "                [\n", "                    \"2018-12-25 11:00\",\n", "                    8.340779419857691\n", "                ],\n", "                [\n", "                    \"2018-12-25 11:05\",\n", "                    8.173740448864807\n", "                ],\n", "                [\n", "                    \"2018-12-25 11:10\",\n", "                    7.965053426421567\n", "                ],\n", "                [\n", "                    \"2018-12-25 11:15\",\n", "                    7.816800755100488\n", "                ],\n", "                [\n", "                    \"2018-12-25 11:20\",\n", "                    7.675960717345464\n", "                ],\n", "                [\n", "                    \"2018-12-25 11:25\",\n", "                    7.64216268147819\n", "                ],\n", "                [\n", "                    \"2018-12-25 13:30\",\n", "                    7.56005454740428\n", "                ],\n", "                [\n", "                    \"2018-12-25 13:35\",\n", "                    7.532051820034066\n", "                ],\n", "                [\n", "                    \"2018-12-25 13:40\",\n", "                    7.455449229032363\n", "                ],\n", "                [\n", "                    \"2018-12-25 13:45\",\n", "                    7.332676767580745\n", "                ],\n", "                [\n", "                    \"2018-12-25 13:50\",\n", "                    7.366042929201707\n", "                ],\n", "                [\n", "                    \"2018-12-25 13:55\",\n", "                    7.447740782741621\n", "                ],\n", "                [\n", "                    \"2018-12-25 14:00\",\n", "                    7.425353743604541\n", "                ],\n", "                [\n", "                    \"2018-12-25 14:05\",\n", "                    7.304086056424313\n", "                ],\n", "                [\n", "                    \"2018-12-25 14:10\",\n", "                    7.138881753603097\n", "                ],\n", "                [\n", "                    \"2018-12-25 14:15\",\n", "                    7.231937665922942\n", "                ],\n", "                [\n", "                    \"2018-12-25 14:20\",\n", "                    7.0203407826267945\n", "                ],\n", "                [\n", "                    \"2018-12-25 14:25\",\n", "                    6.869323743495455\n", "                ],\n", "                [\n", "                    \"2018-12-25 14:30\",\n", "                    6.775857556320682\n", "                ],\n", "                [\n", "                    \"2018-12-25 14:35\",\n", "                    6.887064678504648\n", "                ],\n", "                [\n", "                    \"2018-12-25 14:40\",\n", "                    6.742711444579415\n", "                ],\n", "                [\n", "                    \"2018-12-25 14:45\",\n", "                    6.705575872350444\n", "                ],\n", "                [\n", "                    \"2018-12-25 14:50\",\n", "                    6.5702970787329225\n", "                ],\n", "                [\n", "                    \"2018-12-25 14:55\",\n", "                    6.591782224796276\n", "                ],\n", "                [\n", "                    \"2018-12-25 21:00\",\n", "                    7.062193113556463\n", "                ],\n", "                [\n", "                    \"2018-12-25 21:05\",\n", "                    6.95908345787864\n", "                ],\n", "                [\n", "                    \"2018-12-25 21:10\",\n", "                    6.861129284984708\n", "                ],\n", "                [\n", "                    \"2018-12-25 21:15\",\n", "                    6.768072820735472\n", "                ],\n", "                [\n", "                    \"2018-12-25 21:20\",\n", "                    6.579669179698698\n", "                ],\n", "                [\n", "                    \"2018-12-25 21:25\",\n", "                    6.400685720713763\n", "                ],\n", "                [\n", "                    \"2018-12-25 21:30\",\n", "                    6.380651434678074\n", "                ],\n", "                [\n", "                    \"2018-12-25 21:35\",\n", "                    6.361618862944171\n", "                ],\n", "                [\n", "                    \"2018-12-25 21:40\",\n", "                    6.343537919796963\n", "                ],\n", "                [\n", "                    \"2018-12-25 21:45\",\n", "                    6.226361023807114\n", "                ],\n", "                [\n", "                    \"2018-12-25 21:50\",\n", "                    6.1150429726167586\n", "                ],\n", "                [\n", "                    \"2018-12-25 21:55\",\n", "                    5.959290823985921\n", "                ],\n", "                [\n", "                    \"2018-12-25 22:00\",\n", "                    6.011326282786625\n", "                ],\n", "                [\n", "                    \"2018-12-25 22:05\",\n", "                    5.860759968647294\n", "                ],\n", "                [\n", "                    \"2018-12-25 22:10\",\n", "                    5.967721970214929\n", "                ],\n", "                [\n", "                    \"2018-12-25 22:15\",\n", "                    5.919335871704183\n", "                ],\n", "                [\n", "                    \"2018-12-25 22:20\",\n", "                    5.723369078118973\n", "                ],\n", "                [\n", "                    \"2018-12-25 22:25\",\n", "                    5.537200624213025\n", "                ],\n", "                [\n", "                    \"2018-12-25 22:30\",\n", "                    5.410340593002373\n", "                ],\n", "                [\n", "                    \"2018-12-25 22:35\",\n", "                    5.389823563352254\n", "                ],\n", "                [\n", "                    \"2018-12-25 22:40\",\n", "                    5.320332385184642\n", "                ],\n", "                [\n", "                    \"2018-12-25 22:45\",\n", "                    5.20431576592541\n", "                ],\n", "                [\n", "                    \"2018-12-25 22:50\",\n", "                    5.0440999776291395\n", "                ],\n", "                [\n", "                    \"2018-12-25 22:55\",\n", "                    4.9418949787476825\n", "                ],\n", "                [\n", "                    \"2018-12-25 23:00\",\n", "                    4.894800229810299\n", "                ],\n", "                [\n", "                    \"2018-12-25 23:05\",\n", "                    4.8500602183197845\n", "                ],\n", "                [\n", "                    \"2018-12-25 23:10\",\n", "                    4.807557207403795\n", "                ],\n", "                [\n", "                    \"2018-12-25 23:15\",\n", "                    4.767179347033605\n", "                ],\n", "                [\n", "                    \"2018-12-25 23:20\",\n", "                    4.728820379681925\n", "                ],\n", "                [\n", "                    \"2018-12-25 23:25\",\n", "                    4.642379360697829\n", "                ],\n", "                [\n", "                    \"2018-12-26 09:00\",\n", "                    5.360260392662938\n", "                ],\n", "                [\n", "                    \"2018-12-26 09:05\",\n", "                    5.342247373029791\n", "                ],\n", "                [\n", "                    \"2018-12-26 09:10\",\n", "                    5.325135004378302\n", "                ],\n", "                [\n", "                    \"2018-12-26 09:15\",\n", "                    5.208878254159386\n", "                ],\n", "                [\n", "                    \"2018-12-26 09:20\",\n", "                    5.198434341451417\n", "                ],\n", "                [\n", "                    \"2018-12-26 09:25\",\n", "                    5.138512624378846\n", "                ],\n", "                [\n", "                    \"2018-12-26 09:30\",\n", "                    5.131586993159904\n", "                ],\n", "                [\n", "                    \"2018-12-26 09:35\",\n", "                    5.075007643501909\n", "                ],\n", "                [\n", "                    \"2018-12-26 09:40\",\n", "                    5.021257261326814\n", "                ],\n", "                [\n", "                    \"2018-12-26 09:45\",\n", "                    4.970194398260473\n", "                ],\n", "                [\n", "                    \"2018-12-26 09:50\",\n", "                    4.921684678347449\n", "                ],\n", "                [\n", "                    \"2018-12-26 09:55\",\n", "                    4.875600444430077\n", "                ],\n", "                [\n", "                    \"2018-12-26 10:00\",\n", "                    4.881820422208573\n", "                ],\n", "                [\n", "                    \"2018-12-26 10:05\",\n", "                    4.837729401098144\n", "                ],\n", "                [\n", "                    \"2018-12-26 10:10\",\n", "                    4.7958429310432376\n", "                ],\n", "                [\n", "                    \"2018-12-26 10:30\",\n", "                    4.656050784491076\n", "                ]\n", "            ],\n", "            \"label\": {\n", "                \"normal\": {\n", "                    \"show\": false,\n", "                    \"position\": \"top\",\n", "                    \"textStyle\": {\n", "                        \"fontSize\": 12\n", "                    }\n", "                },\n", "                \"emphasis\": {\n", "                    \"show\": true,\n", "                    \"textStyle\": {\n", "                        \"fontSize\": 12\n", "                    }\n", "                }\n", "            },\n", "            \"lineStyle\": {\n", "                \"normal\": {\n", "                    \"width\": 1,\n", "                    \"opacity\": 1,\n", "                    \"curveness\": 0,\n", "                    \"type\": \"solid\"\n", "                }\n", "            },\n", "            \"areaStyle\": {\n", "                \"opacity\": 0\n", "            },\n", "            \"markPoint\": {\n", "                \"data\": []\n", "            },\n", "            \"markLine\": {\n", "                \"data\": [\n", "                    {\n", "                        \"type\": \"average\",\n", "                        \"name\": \"mean-Value\"\n", "                    }\n", "                ],\n", "                \"symbolSize\": 10\n", "            },\n", "            \"seriesId\": 4472606,\n", "            \"xAxisIndex\": 0,\n", "            \"yAxisIndex\": 0\n", "        },\n", "        {\n", "            \"type\": \"line\",\n", "            \"name\": \"st<PERSON><PERSON>\",\n", "            \"symbol\": \"emptyCircle\",\n", "            \"symbolSize\": 4,\n", "            \"smooth\": false,\n", "            \"step\": false,\n", "            \"showSymbol\": true,\n", "            \"data\": [\n", "                [\n", "                    \"2018-12-20 21:55\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 22:00\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 22:05\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 22:10\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 22:15\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 22:20\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 22:25\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 22:30\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 22:35\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 22:40\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 22:45\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 22:50\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 22:55\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 23:00\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 23:05\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 23:10\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 23:15\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 23:20\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-20 23:25\",\n", "                    NaN\n", "                ],\n", "                [\n", "                    \"2018-12-21 09:00\",\n", "                    3.9038442590979723\n", "                ],\n", "                [\n", "                    \"2018-12-21 09:05\",\n", "                    4.055551750236354\n", "                ],\n", "                [\n", "                    \"2018-12-21 09:10\",\n", "                    4.247352116323445\n", "                ],\n", "                [\n", "                    \"2018-12-21 09:15\",\n", "                    5.903177110608972\n", "                ],\n", "                [\n", "                    \"2018-12-21 09:20\",\n", "                    6.632495759533022\n", "                ],\n", "                [\n", "                    \"2018-12-21 09:25\",\n", "                    7.26567271489965\n", "                ],\n", "                [\n", "                    \"2018-12-21 09:30\",\n", "                    7.605754400400902\n", "                ],\n", "                [\n", "                    \"2018-12-21 09:35\",\n", "                    7.8396428490648775\n", "                ],\n", "                [\n", "                    \"2018-12-21 09:40\",\n", "                    7.495832175282475\n", "                ],\n", "                [\n", "                    \"2018-12-21 09:45\",\n", "                    7.123903424387503\n", "                ],\n", "                [\n", "                    \"2018-12-21 09:50\",\n", "                    7.196526940196123\n", "                ],\n", "                [\n", "                    \"2018-12-21 09:55\",\n", "                    7.073895673467763\n", "                ],\n", "                [\n", "                    \"2018-12-21 10:00\",\n", "                    7.067531393647025\n", "                ],\n", "                [\n", "                    \"2018-12-21 10:05\",\n", "                    6.8940191470470245\n", "                ],\n", "                [\n", "                    \"2018-12-21 10:10\",\n", "                    7.175479078069543\n", "                ],\n", "                [\n", "                    \"2018-12-21 10:30\",\n", "                    7.031891637308211\n", "                ],\n", "                [\n", "                    \"2018-12-21 10:35\",\n", "                    6.5458001802796435\n", "                ],\n", "                [\n", "                    \"2018-12-21 10:40\",\n", "                    6.043798474457184\n", "                ],\n", "                [\n", "                    \"2018-12-21 10:45\",\n", "                    5.616048432925822\n", "                ],\n", "                [\n", "                    \"2018-12-21 10:50\",\n", "                    4.842261868249982\n", "                ],\n", "                [\n", "                    \"2018-12-21 10:55\",\n", "                    4.63977370136206\n", "                ],\n", "                [\n", "                    \"2018-12-21 11:00\",\n", "                    3.897435053914107\n", "                ],\n", "                [\n", "                    \"2018-12-21 11:05\",\n", "                    3.4292856399996494\n", "                ],\n", "                [\n", "                    \"2018-12-21 11:10\",\n", "                    3.3507461856978327\n", "                ],\n", "                [\n", "                    \"2018-12-21 11:15\",\n", "                    3.368605052700648\n", "                ],\n", "                [\n", "                    \"2018-12-21 11:20\",\n", "                    3.383415433972772\n", "                ],\n", "                [\n", "                    \"2018-12-21 11:25\",\n", "                    3.4099120223855897\n", "                ],\n", "                [\n", "                    \"2018-12-21 13:30\",\n", "                    5.566641716494222\n", "                ],\n", "                [\n", "                    \"2018-12-21 13:35\",\n", "                    6.938839960749497\n", "                ],\n", "                [\n", "                    \"2018-12-21 13:40\",\n", "                    8.108020720206147\n", "                ],\n", "                [\n", "                    \"2018-12-21 13:45\",\n", "                    8.688498144111186\n", "                ],\n", "                [\n", "                    \"2018-12-21 13:50\",\n", "                    9.27833497996125\n", "                ],\n", "                [\n", "                    \"2018-12-21 13:55\",\n", "                    11.442355526735897\n", "                ],\n", "                [\n", "                    \"2018-12-21 14:00\",\n", "                    13.277330303928855\n", "                ],\n", "                [\n", "                    \"2018-12-21 14:05\",\n", "                    14.48956521082828\n", "                ],\n", "                [\n", "                    \"2018-12-21 14:10\",\n", "                    15.614416415564781\n", "                ],\n", "                [\n", "                    \"2018-12-21 14:15\",\n", "                    16.196913286174343\n", "                ],\n", "                [\n", "                    \"2018-12-21 14:20\",\n", "                    16.275441622262196\n", "                ],\n", "                [\n", "                    \"2018-12-21 14:25\",\n", "                    16.754924649190077\n", "                ],\n", "                [\n", "                    \"2018-12-21 14:30\",\n", "                    16.641739692705702\n", "                ],\n", "                [\n", "                    \"2018-12-21 14:35\",\n", "                    16.258766865914186\n", "                ],\n", "                [\n", "                    \"2018-12-21 14:40\",\n", "                    16.116063415115903\n", "                ],\n", "                [\n", "                    \"2018-12-21 14:45\",\n", "                    15.411035007422441\n", "                ],\n", "                [\n", "                    \"2018-12-21 14:50\",\n", "                    14.25798022158489\n", "                ],\n", "                [\n", "                    \"2018-12-21 14:55\",\n", "                    12.658100173371434\n", "                ],\n", "                [\n", "                    \"2018-12-21 21:00\",\n", "                    10.628264204423573\n", "                ],\n", "                [\n", "                    \"2018-12-21 21:05\",\n", "                    8.437268515343103\n", "                ],\n", "                [\n", "                    \"2018-12-21 21:10\",\n", "                    8.000624975632078\n", "                ],\n", "                [\n", "                    \"2018-12-21 21:15\",\n", "                    7.685050422649086\n", "                ],\n", "                [\n", "                    \"2018-12-21 21:20\",\n", "                    7.398648525189732\n", "                ],\n", "                [\n", "                    \"2018-12-21 21:25\",\n", "                    6.82843320245646\n", "                ],\n", "                [\n", "                    \"2018-12-21 21:30\",\n", "                    6.383376849399764\n", "                ],\n", "                [\n", "                    \"2018-12-21 21:35\",\n", "                    6.4844043675010425\n", "                ],\n", "                [\n", "                    \"2018-12-21 21:40\",\n", "                    6.367691889484286\n", "                ],\n", "                [\n", "                    \"2018-12-21 21:45\",\n", "                    6.4257295305698365\n", "                ],\n", "                [\n", "                    \"2018-12-21 21:50\",\n", "                    6.263186090165073\n", "                ],\n", "                [\n", "                    \"2018-12-21 21:55\",\n", "                    6.174746958411315\n", "                ],\n", "                [\n", "                    \"2018-12-21 22:00\",\n", "                    6.299801584177076\n", "                ],\n", "                [\n", "                    \"2018-12-21 22:05\",\n", "                    6.119436248602301\n", "                ],\n", "                [\n", "                    \"2018-12-21 22:10\",\n", "                    5.766064515813324\n", "                ],\n", "                [\n", "                    \"2018-12-21 22:15\",\n", "                    5.361669515986499\n", "                ],\n", "                [\n", "                    \"2018-12-21 22:20\",\n", "                    4.29272640640229\n", "                ],\n", "                [\n", "                    \"2018-12-21 22:25\",\n", "                    3.865229617950072\n", "                ],\n", "                [\n", "                    \"2018-12-21 22:30\",\n", "                    3.456515586373821\n", "                ],\n", "                [\n", "                    \"2018-12-21 22:35\",\n", "                    3.1507935508730998\n", "                ],\n", "                [\n", "                    \"2018-12-21 22:40\",\n", "                    2.9478805946662394\n", "                ],\n", "                [\n", "                    \"2018-12-21 22:45\",\n", "                    2.8613807855648994\n", "                ],\n", "                [\n", "                    \"2018-12-21 22:50\",\n", "                    3.0736785776728768\n", "                ],\n", "                [\n", "                    \"2018-12-21 22:55\",\n", "                    2.290742238108178\n", "                ],\n", "                [\n", "                    \"2018-12-21 23:00\",\n", "                    2.282542442298515\n", "                ],\n", "                [\n", "                    \"2018-12-21 23:05\",\n", "                    2.3021728866038136\n", "                ],\n", "                [\n", "                    \"2018-12-21 23:10\",\n", "                    2.3425413549847387\n", "                ],\n", "                [\n", "                    \"2018-12-21 23:15\",\n", "                    2.5588083164503113\n", "                ],\n", "                [\n", "                    \"2018-12-21 23:20\",\n", "                    2.6697378147448223\n", "                ],\n", "                [\n", "                    \"2018-12-21 23:25\",\n", "                    2.7545417042354847\n", "                ],\n", "                [\n", "                    \"2018-12-24 09:00\",\n", "                    3.7629775445889035\n", "                ],\n", "                [\n", "                    \"2018-12-24 09:05\",\n", "                    4.807286136723637\n", "                ],\n", "                [\n", "                    \"2018-12-24 09:10\",\n", "                    5.181457323882702\n", "                ],\n", "                [\n", "                    \"2018-12-24 09:15\",\n", "                    7.281998352090653\n", "                ],\n", "                [\n", "                    \"2018-12-24 09:20\",\n", "                    8.907300376648976\n", "                ],\n", "                [\n", "                    \"2018-12-24 09:25\",\n", "                    10.890821823918307\n", "                ],\n", "                [\n", "                    \"2018-12-24 09:30\",\n", "                    12.895251063846235\n", "                ],\n", "                [\n", "                    \"2018-12-24 09:35\",\n", "                    14.329951151352507\n", "                ],\n", "                [\n", "                    \"2018-12-24 09:40\",\n", "                    15.000916638685194\n", "                ],\n", "                [\n", "                    \"2018-12-24 09:45\",\n", "                    15.160062664797795\n", "                ],\n", "                [\n", "                    \"2018-12-24 09:50\",\n", "                    15.581639836667625\n", "                ],\n", "                [\n", "                    \"2018-12-24 09:55\",\n", "                    15.548231410694966\n", "                ],\n", "                [\n", "                    \"2018-12-24 10:00\",\n", "                    15.926314702373285\n", "                ],\n", "                [\n", "                    \"2018-12-24 10:05\",\n", "                    15.722913216037014\n", "                ],\n", "                [\n", "                    \"2018-12-24 10:10\",\n", "                    15.059465461948756\n", "                ],\n", "                [\n", "                    \"2018-12-24 10:30\",\n", "                    14.210823339935134\n", "                ],\n", "                [\n", "                    \"2018-12-24 10:35\",\n", "                    13.217412757426708\n", "                ],\n", "                [\n", "                    \"2018-12-24 10:40\",\n", "                    11.903780911970207\n", "                ],\n", "                [\n", "                    \"2018-12-24 10:45\",\n", "                    10.46171592038539\n", "                ],\n", "                [\n", "                    \"2018-12-24 10:50\",\n", "                    8.634089413393438\n", "                ],\n", "                [\n", "                    \"2018-12-24 10:55\",\n", "                    8.493968448310488\n", "                ],\n", "                [\n", "                    \"2018-12-24 11:00\",\n", "                    8.622499637476732\n", "                ],\n", "                [\n", "                    \"2018-12-24 11:05\",\n", "                    8.401785524453697\n", "                ],\n", "                [\n", "                    \"2018-12-24 11:10\",\n", "                    8.471127433860726\n", "                ],\n", "                [\n", "                    \"2018-12-24 11:15\",\n", "                    8.665304380134176\n", "                ],\n", "                [\n", "                    \"2018-12-24 11:20\",\n", "                    8.58545281280458\n", "                ],\n", "                [\n", "                    \"2018-12-24 11:25\",\n", "                    7.8841613377211965\n", "                ],\n", "                [\n", "                    \"2018-12-24 13:30\",\n", "                    7.002856560007299\n", "                ],\n", "                [\n", "                    \"2018-12-24 13:35\",\n", "                    6.665395712200703\n", "                ],\n", "                [\n", "                    \"2018-12-24 13:40\",\n", "                    6.5610593657782585\n", "                ],\n", "                [\n", "                    \"2018-12-24 13:45\",\n", "                    6.292058486757916\n", "                ],\n", "                [\n", "                    \"2018-12-24 13:50\",\n", "                    6.292058486757916\n", "                ],\n", "                [\n", "                    \"2018-12-24 13:55\",\n", "                    5.54504283117725\n", "                ],\n", "                [\n", "                    \"2018-12-24 14:00\",\n", "                    5.480875842506248\n", "                ],\n", "                [\n", "                    \"2018-12-24 14:05\",\n", "                    5.790293602214916\n", "                ],\n", "                [\n", "                    \"2018-12-24 14:10\",\n", "                    5.940538696036406\n", "                ],\n", "                [\n", "                    \"2018-12-24 14:15\",\n", "                    6.293647590976472\n", "                ],\n", "                [\n", "                    \"2018-12-24 14:20\",\n", "                    6.251999680132152\n", "                ],\n", "                [\n", "                    \"2018-12-24 14:25\",\n", "                    6.111260099053744\n", "                ],\n", "                [\n", "                    \"2018-12-24 14:30\",\n", "                    5.99395528847384\n", "                ],\n", "                [\n", "                    \"2018-12-24 14:35\",\n", "                    5.674504383660856\n", "                ],\n", "                [\n", "                    \"2018-12-24 14:40\",\n", "                    5.200721103789738\n", "                ],\n", "                [\n", "                    \"2018-12-24 14:45\",\n", "                    4.748420790248754\n", "                ],\n", "                [\n", "                    \"2018-12-24 14:50\",\n", "                    4.65939910298464\n", "                ],\n", "                [\n", "                    \"2018-12-24 14:55\",\n", "                    4.404543109151337\n", "                ],\n", "                [\n", "                    \"2018-12-24 21:00\",\n", "                    6.525909898272618\n", "                ],\n", "                [\n", "                    \"2018-12-24 21:05\",\n", "                    7.133722730781224\n", "                ],\n", "                [\n", "                    \"2018-12-24 21:10\",\n", "                    8.19390017028497\n", "                ],\n", "                [\n", "                    \"2018-12-24 21:15\",\n", "                    8.778382538865511\n", "                ],\n", "                [\n", "                    \"2018-12-24 21:20\",\n", "                    9.049309365945891\n", "                ],\n", "                [\n", "                    \"2018-12-24 21:25\",\n", "                    9.48933612015438\n", "                ],\n", "                [\n", "                    \"2018-12-24 21:30\",\n", "                    9.82484096557902\n", "                ],\n", "                [\n", "                    \"2018-12-24 21:35\",\n", "                    10.024345365150472\n", "                ],\n", "                [\n", "                    \"2018-12-24 21:40\",\n", "                    10.01948102446752\n", "                ],\n", "                [\n", "                    \"2018-12-24 21:45\",\n", "                    10.141005867262429\n", "                ],\n", "                [\n", "                    \"2018-12-24 21:50\",\n", "                    10.10581515759508\n", "                ],\n", "                [\n", "                    \"2018-12-24 21:55\",\n", "                    10.222524150130436\n", "                ],\n", "                [\n", "                    \"2018-12-24 22:00\",\n", "                    10.012492197250394\n", "                ],\n", "                [\n", "                    \"2018-12-24 22:05\",\n", "                    9.318127494330396\n", "                ],\n", "                [\n", "                    \"2018-12-24 22:10\",\n", "                    8.68490069029137\n", "                ],\n", "                [\n", "                    \"2018-12-24 22:15\",\n", "                    7.970414041991941\n", "                ],\n", "                [\n", "                    \"2018-12-24 22:20\",\n", "                    7.177046746383674\n", "                ],\n", "                [\n", "                    \"2018-12-24 22:25\",\n", "                    6.704289671598909\n", "                ],\n", "                [\n", "                    \"2018-12-24 22:30\",\n", "                    6.253798845528634\n", "                ],\n", "                [\n", "                    \"2018-12-24 22:35\",\n", "                    6.061971626348881\n", "                ],\n", "                [\n", "                    \"2018-12-24 22:40\",\n", "                    5.561249859455354\n", "                ],\n", "                [\n", "                    \"2018-12-24 22:45\",\n", "                    5.435071296598227\n", "                ],\n", "                [\n", "                    \"2018-12-24 22:50\",\n", "                    4.99274473613698\n", "                ],\n", "                [\n", "                    \"2018-12-24 22:55\",\n", "                    4.81559965117635\n", "                ],\n", "                [\n", "                    \"2018-12-24 23:00\",\n", "                    5.018964036479885\n", "                ],\n", "                [\n", "                    \"2018-12-24 23:05\",\n", "                    5.4154870510107145\n", "                ],\n", "                [\n", "                    \"2018-12-24 23:10\",\n", "                    5.4154870510107145\n", "                ],\n", "                [\n", "                    \"2018-12-24 23:15\",\n", "                    5.608029957140588\n", "                ],\n", "                [\n", "                    \"2018-12-24 23:20\",\n", "                    5.836094584610632\n", "                ],\n", "                [\n", "                    \"2018-12-24 23:25\",\n", "                    5.978921307281013\n", "                ],\n", "                [\n", "                    \"2018-12-25 09:00\",\n", "                    12.83510810239725\n", "                ],\n", "                [\n", "                    \"2018-12-25 09:05\",\n", "                    19.38917223605687\n", "                ],\n", "                [\n", "                    \"2018-12-25 09:10\",\n", "                    23.20016163735913\n", "                ],\n", "                [\n", "                    \"2018-12-25 09:15\",\n", "                    26.224559100220706\n", "                ],\n", "                [\n", "                    \"2018-12-25 09:20\",\n", "                    28.46664715065031\n", "                ],\n", "                [\n", "                    \"2018-12-25 09:25\",\n", "                    29.66799622487974\n", "                ],\n", "                [\n", "                    \"2018-12-25 09:30\",\n", "                    31.822122807867977\n", "                ],\n", "                [\n", "                    \"2018-12-25 09:35\",\n", "                    33.121141284666585\n", "                ],\n", "                [\n", "                    \"2018-12-25 09:40\",\n", "                    34.16939420006454\n", "                ],\n", "                [\n", "                    \"2018-12-25 09:45\",\n", "                    34.345123380188525\n", "                ],\n", "                [\n", "                    \"2018-12-25 09:50\",\n", "                    34.36899038377528\n", "                ],\n", "                [\n", "                    \"2018-12-25 09:55\",\n", "                    34.07491746137659\n", "                ],\n", "                [\n", "                    \"2018-12-25 10:00\",\n", "                    33.264508113014124\n", "                ],\n", "                [\n", "                    \"2018-12-25 10:05\",\n", "                    31.551980920377265\n", "                ],\n", "                [\n", "                    \"2018-12-25 10:10\",\n", "                    29.8885847774642\n", "                ],\n", "                [\n", "                    \"2018-12-25 10:30\",\n", "                    28.223926020308568\n", "                ],\n", "                [\n", "                    \"2018-12-25 10:35\",\n", "                    25.601708927312746\n", "                ],\n", "                [\n", "                    \"2018-12-25 10:40\",\n", "                    22.260053908264577\n", "                ],\n", "                [\n", "                    \"2018-12-25 10:45\",\n", "                    17.41378764081187\n", "                ],\n", "                [\n", "                    \"2018-12-25 10:50\",\n", "                    9.594138835737366\n", "                ],\n", "                [\n", "                    \"2018-12-25 10:55\",\n", "                    8.819722217826788\n", "                ],\n", "                [\n", "                    \"2018-12-25 11:00\",\n", "                    8.974825903518832\n", "                ],\n", "                [\n", "                    \"2018-12-25 11:05\",\n", "                    8.771972412111495\n", "                ],\n", "                [\n", "                    \"2018-12-25 11:10\",\n", "                    8.742997197781193\n", "                ],\n", "                [\n", "                    \"2018-12-25 11:15\",\n", "                    8.374216381274715\n", "                ],\n", "                [\n", "                    \"2018-12-25 11:20\",\n", "                    7.328710664291099\n", "                ],\n", "                [\n", "                    \"2018-12-25 11:25\",\n", "                    7.796633889033617\n", "                ],\n", "                [\n", "                    \"2018-12-25 13:30\",\n", "                    7.777531742154674\n", "                ],\n", "                [\n", "                    \"2018-12-25 13:35\",\n", "                    7.764502559702551\n", "                ],\n", "                [\n", "                    \"2018-12-25 13:40\",\n", "                    7.53176606115971\n", "                ],\n", "                [\n", "                    \"2018-12-25 13:45\",\n", "                    7.377499576366792\n", "                ],\n", "                [\n", "                    \"2018-12-25 13:50\",\n", "                    7.584688523588229\n", "                ],\n", "                [\n", "                    \"2018-12-25 13:55\",\n", "                    7.723341245883969\n", "                ],\n", "                [\n", "                    \"2018-12-25 14:00\",\n", "                    7.465085398040484\n", "                ],\n", "                [\n", "                    \"2018-12-25 14:05\",\n", "                    7.2360210061377375\n", "                ],\n", "                [\n", "                    \"2018-12-25 14:10\",\n", "                    7.355949972600681\n", "                ],\n", "                [\n", "                    \"2018-12-25 14:15\",\n", "                    8.102314484201047\n", "                ],\n", "                [\n", "                    \"2018-12-25 14:20\",\n", "                    8.604649905667628\n", "                ],\n", "                [\n", "                    \"2018-12-25 14:25\",\n", "                    8.897190567770126\n", "                ],\n", "                [\n", "                    \"2018-12-25 14:30\",\n", "                    9.13167564032945\n", "                ],\n", "                [\n", "                    \"2018-12-25 14:35\",\n", "                    8.743569065329458\n", "                ],\n", "                [\n", "                    \"2018-12-25 14:40\",\n", "                    8.584870412544749\n", "                ],\n", "                [\n", "                    \"2018-12-25 14:45\",\n", "                    8.27345151676048\n", "                ],\n", "                [\n", "                    \"2018-12-25 14:50\",\n", "                    7.760637860324751\n", "                ],\n", "                [\n", "                    \"2018-12-25 14:55\",\n", "                    7.505997601846118\n", "                ],\n", "                [\n", "                    \"2018-12-25 21:00\",\n", "                    7.0105277975979865\n", "                ],\n", "                [\n", "                    \"2018-12-25 21:05\",\n", "                    5.809259849549702\n", "                ],\n", "                [\n", "                    \"2018-12-25 21:10\",\n", "                    4.972675336278979\n", "                ],\n", "                [\n", "                    \"2018-12-25 21:15\",\n", "                    4.637887450097995\n", "                ],\n", "                [\n", "                    \"2018-12-25 21:20\",\n", "                    4.2576401915156925\n", "                ],\n", "                [\n", "                    \"2018-12-25 21:25\",\n", "                    4.04474968338791\n", "                ],\n", "                [\n", "                    \"2018-12-25 21:30\",\n", "                    4.236744032884954\n", "                ],\n", "                [\n", "                    \"2018-12-25 21:35\",\n", "                    4.521891197457003\n", "                ],\n", "                [\n", "                    \"2018-12-25 21:40\",\n", "                    4.603259714638072\n", "                ],\n", "                [\n", "                    \"2018-12-25 21:45\",\n", "                    4.6000000001174275\n", "                ],\n", "                [\n", "                    \"2018-12-25 21:50\",\n", "                    4.630334761059772\n", "                ],\n", "                [\n", "                    \"2018-12-25 21:55\",\n", "                    4.5122056691085435\n", "                ],\n", "                [\n", "                    \"2018-12-25 22:00\",\n", "                    4.475209492390531\n", "                ],\n", "                [\n", "                    \"2018-12-25 22:05\",\n", "                    4.543126676705818\n", "                ],\n", "                [\n", "                    \"2018-12-25 22:10\",\n", "                    4.488875137534182\n", "                ],\n", "                [\n", "                    \"2018-12-25 22:15\",\n", "                    4.426059195249312\n", "                ],\n", "                [\n", "                    \"2018-12-25 22:20\",\n", "                    4.3255057506644725\n", "                ],\n", "                [\n", "                    \"2018-12-25 22:25\",\n", "                    3.9984371946816593\n", "                ],\n", "                [\n", "                    \"2018-12-25 22:30\",\n", "                    3.709447398419221\n", "                ],\n", "                [\n", "                    \"2018-12-25 22:35\",\n", "                    2.5743931322699063\n", "                ],\n", "                [\n", "                    \"2018-12-25 22:40\",\n", "                    2.5768197454606807\n", "                ],\n", "                [\n", "                    \"2018-12-25 22:45\",\n", "                    2.5768197454606807\n", "                ],\n", "                [\n", "                    \"2018-12-25 22:50\",\n", "                    2.5194245376832254\n", "                ],\n", "                [\n", "                    \"2018-12-25 22:55\",\n", "                    2.5179356623732443\n", "                ],\n", "                [\n", "                    \"2018-12-25 23:00\",\n", "                    2.624404694437629\n", "                ],\n", "                [\n", "                    \"2018-12-25 23:05\",\n", "                    2.706935536665904\n", "                ],\n", "                [\n", "                    \"2018-12-25 23:10\",\n", "                    2.77668867554396\n", "                ],\n", "                [\n", "                    \"2018-12-25 23:15\",\n", "                    2.3722352329416623\n", "                ],\n", "                [\n", "                    \"2018-12-25 23:20\",\n", "                    2.3237900078046057\n", "                ],\n", "                [\n", "                    \"2018-12-25 23:25\",\n", "                    2.3510635892681235\n", "                ],\n", "                [\n", "                    \"2018-12-26 09:00\",\n", "                    4.0183952021788585\n", "                ],\n", "                [\n", "                    \"2018-12-26 09:05\",\n", "                    5.259277517024589\n", "                ],\n", "                [\n", "                    \"2018-12-26 09:10\",\n", "                    5.6610511391732965\n", "                ],\n", "                [\n", "                    \"2018-12-26 09:15\",\n", "                    5.971599450667944\n", "                ],\n", "                [\n", "                    \"2018-12-26 09:20\",\n", "                    6.104096984870719\n", "                ],\n", "                [\n", "                    \"2018-12-26 09:25\",\n", "                    6.192535829616195\n", "                ],\n", "                [\n", "                    \"2018-12-26 09:30\",\n", "                    6.239190652658686\n", "                ],\n", "                [\n", "                    \"2018-12-26 09:35\",\n", "                    6.3324560795656115\n", "                ],\n", "                [\n", "                    \"2018-12-26 09:40\",\n", "                    6.459682654746845\n", "                ],\n", "                [\n", "                    \"2018-12-26 09:45\",\n", "                    6.492110596642006\n", "                ],\n", "                [\n", "                    \"2018-12-26 09:50\",\n", "                    6.642853302571002\n", "                ],\n", "                [\n", "                    \"2018-12-26 09:55\",\n", "                    6.574762353122526\n", "                ],\n", "                [\n", "                    \"2018-12-26 10:00\",\n", "                    6.544272304822342\n", "                ],\n", "                [\n", "                    \"2018-12-26 10:05\",\n", "                    6.430202174054692\n", "                ],\n", "                [\n", "                    \"2018-12-26 10:10\",\n", "                    6.086665753799253\n", "                ],\n", "                [\n", "                    \"2018-12-26 10:30\",\n", "                    5.678688228689694\n", "                ]\n", "            ],\n", "            \"label\": {\n", "                \"normal\": {\n", "                    \"show\": false,\n", "                    \"position\": \"top\",\n", "                    \"textStyle\": {\n", "                        \"fontSize\": 12\n", "                    }\n", "                },\n", "                \"emphasis\": {\n", "                    \"show\": true,\n", "                    \"textStyle\": {\n", "                        \"fontSize\": 12\n", "                    }\n", "                }\n", "            },\n", "            \"lineStyle\": {\n", "                \"normal\": {\n", "                    \"width\": 1,\n", "                    \"opacity\": 1,\n", "                    \"curveness\": 0,\n", "                    \"type\": \"solid\"\n", "                }\n", "            },\n", "            \"areaStyle\": {\n", "                \"opacity\": 0\n", "            },\n", "            \"markPoint\": {\n", "                \"data\": [\n", "                    {\n", "                        \"type\": \"max\",\n", "                        \"name\": \"Maximum\",\n", "                        \"symbol\": \"pin\",\n", "                        \"symbolSize\": 50,\n", "                        \"label\": {\n", "                            \"normal\": {\n", "                                \"textStyle\": {\n", "                                    \"color\": \"#fff\"\n", "                                }\n", "                            }\n", "                        }\n", "                    },\n", "                    {\n", "                        \"type\": \"min\",\n", "                        \"name\": \"<PERSON><PERSON>\",\n", "                        \"symbol\": \"pin\",\n", "                        \"symbolSize\": 50,\n", "                        \"label\": {\n", "                            \"normal\": {\n", "                                \"textStyle\": {\n", "                                    \"color\": \"#fff\"\n", "                                }\n", "                            }\n", "                        }\n", "                    }\n", "                ]\n", "            },\n", "            \"markLine\": {\n", "                \"data\": [\n", "                    {\n", "                        \"type\": \"average\",\n", "                        \"name\": \"mean-Value\"\n", "                    }\n", "                ],\n", "                \"symbolSize\": 10\n", "            },\n", "            \"seriesId\": 4472606,\n", "            \"xAxisIndex\": 0,\n", "            \"yAxisIndex\": 0\n", "        },\n", "        {\n", "            \"type\": \"candlestick\",\n", "            \"name\": \"MA8888.ZC BarSize.min5 K<PERSON>ine (bar count:260)\",\n", "            \"data\": [\n", "                [\n", "                    2495.0,\n", "                    2493.0,\n", "                    2491.0,\n", "                    2496.0\n", "                ],\n", "                [\n", "                    2493.0,\n", "                    2496.0,\n", "                    2489.0,\n", "                    2496.0\n", "                ],\n", "                [\n", "                    2495.0,\n", "                    2497.0,\n", "                    2493.0,\n", "                    2499.0\n", "                ],\n", "                [\n", "                    2496.0,\n", "                    2496.0,\n", "                    2493.0,\n", "                    2497.0\n", "                ],\n", "                [\n", "                    2496.0,\n", "                    2493.0,\n", "                    2491.0,\n", "                    2496.0\n", "                ],\n", "                [\n", "                    2493.0,\n", "                    2492.0,\n", "                    2491.0,\n", "                    2497.0\n", "                ],\n", "                [\n", "                    2493.0,\n", "                    2492.0,\n", "                    2490.0,\n", "                    2495.0\n", "                ],\n", "                [\n", "                    2492.0,\n", "                    2497.0,\n", "                    2492.0,\n", "                    2497.0\n", "                ],\n", "                [\n", "                    2496.0,\n", "                    2495.0,\n", "                    2491.0,\n", "                    2498.0\n", "                ],\n", "                [\n", "                    2494.0,\n", "                    2486.0,\n", "                    2485.0,\n", "                    2495.0\n", "                ],\n", "                [\n", "                    2487.0,\n", "                    2491.0,\n", "                    2486.0,\n", "                    2493.0\n", "                ],\n", "                [\n", "                    2491.0,\n", "                    2491.0,\n", "                    2486.0,\n", "                    2492.0\n", "                ],\n", "                [\n", "                    2491.0,\n", "                    2491.0,\n", "                    2490.0,\n", "                    2493.0\n", "                ],\n", "                [\n", "                    2491.0,\n", "                    2487.0,\n", "                    2484.0,\n", "                    2492.0\n", "                ],\n", "                [\n", "                    2488.0,\n", "                    2492.0,\n", "                    2486.0,\n", "                    2492.0\n", "                ],\n", "                [\n", "                    2491.0,\n", "                    2492.0,\n", "                    2491.0,\n", "                    2498.0\n", "                ],\n", "                [\n", "                    2492.0,\n", "                    2489.0,\n", "                    2488.0,\n", "                    2495.0\n", "                ],\n", "                [\n", "                    2490.0,\n", "                    2489.0,\n", "                    2487.0,\n", "                    2491.0\n", "                ],\n", "                [\n", "                    2489.0,\n", "                    2488.0,\n", "                    2484.0,\n", "                    2491.0\n", "                ],\n", "                [\n", "                    2486.0,\n", "                    2481.0,\n", "                    2478.0,\n", "                    2486.0\n", "                ],\n", "                [\n", "                    2481.0,\n", "                    2486.0,\n", "                    2480.0,\n", "                    2487.0\n", "                ],\n", "                [\n", "                    2487.0,\n", "                    2483.0,\n", "                    2480.0,\n", "                    2487.0\n", "                ],\n", "                [\n", "                    2484.0,\n", "                    2470.0,\n", "                    2466.0,\n", "                    2484.0\n", "                ],\n", "                [\n", "                    2469.0,\n", "                    2473.0,\n", "                    2469.0,\n", "                    2474.0\n", "                ],\n", "                [\n", "                    2473.0,\n", "                    2473.0,\n", "                    2471.0,\n", "                    2475.0\n", "                ],\n", "                [\n", "                    2474.0,\n", "                    2475.0,\n", "                    2472.0,\n", "                    2475.0\n", "                ],\n", "                [\n", "                    2474.0,\n", "                    2475.0,\n", "                    2470.0,\n", "                    2476.0\n", "                ],\n", "                [\n", "                    2476.0,\n", "                    2478.0,\n", "                    2474.0,\n", "                    2479.0\n", "                ],\n", "                [\n", "                    2477.0,\n", "                    2480.0,\n", "                    2477.0,\n", "                    2483.0\n", "                ],\n", "                [\n", "                    2480.0,\n", "                    2478.0,\n", "                    2477.0,\n", "                    2481.0\n", "                ],\n", "                [\n", "                    2478.0,\n", "                    2477.0,\n", "                    2476.0,\n", "                    2480.0\n", "                ],\n", "                [\n", "                    2477.0,\n", "                    2473.0,\n", "                    2471.0,\n", "                    2478.0\n", "                ],\n", "                [\n", "                    2472.0,\n", "                    2474.0,\n", "                    2468.0,\n", "                    2474.0\n", "                ],\n", "                [\n", "                    2474.0,\n", "                    2469.0,\n", "                    2469.0,\n", "                    2475.0\n", "                ],\n", "                [\n", "                    2470.0,\n", "                    2468.0,\n", "                    2463.0,\n", "                    2470.0\n", "                ],\n", "                [\n", "                    2468.0,\n", "                    2470.0,\n", "                    2467.0,\n", "                    2471.0\n", "                ],\n", "                [\n", "                    2470.0,\n", "                    2473.0,\n", "                    2469.0,\n", "                    2473.0\n", "                ],\n", "                [\n", "                    2473.0,\n", "                    2468.0,\n", "                    2468.0,\n", "                    2473.0\n", "                ],\n", "                [\n", "                    2468.0,\n", "                    2475.0,\n", "                    2467.0,\n", "                    2476.0\n", "                ],\n", "                [\n", "                    2475.0,\n", "                    2475.0,\n", "                    2473.0,\n", "                    2478.0\n", "                ],\n", "                [\n", "                    2474.0,\n", "                    2471.0,\n", "                    2471.0,\n", "                    2476.0\n", "                ],\n", "                [\n", "                    2471.0,\n", "                    2469.0,\n", "                    2469.0,\n", "                    2473.0\n", "                ],\n", "                [\n", "                    2469.0,\n", "                    2473.0,\n", "                    2469.0,\n", "                    2474.0\n", "                ],\n", "                [\n", "                    2472.0,\n", "                    2475.0,\n", "                    2470.0,\n", "                    2478.0\n", "                ],\n", "                [\n", "                    2476.0,\n", "                    2475.0,\n", "                    2473.0,\n", "                    2477.0\n", "                ],\n", "                [\n", "                    2475.0,\n", "                    2471.0,\n", "                    2470.0,\n", "                    2475.0\n", "                ],\n", "                [\n", "                    2469.0,\n", "                    2453.0,\n", "                    2451.0,\n", "                    2469.0\n", "                ],\n", "                [\n", "                    2452.0,\n", "                    2452.0,\n", "                    2445.0,\n", "                    2454.0\n", "                ],\n", "                [\n", "                    2451.0,\n", "                    2449.0,\n", "                    2446.0,\n", "                    2453.0\n", "                ],\n", "                [\n", "                    2449.0,\n", "                    2452.0,\n", "                    2446.0,\n", "                    2453.0\n", "                ],\n", "                [\n", "                    2452.0,\n", "                    2450.0,\n", "                    2448.0,\n", "                    2452.0\n", "                ],\n", "                [\n", "                    2450.0,\n", "                    2435.0,\n", "                    2433.0,\n", "                    2451.0\n", "                ],\n", "                [\n", "                    2434.0,\n", "                    2432.0,\n", "                    2424.0,\n", "                    2439.0\n", "                ],\n", "                [\n", "                    2433.0,\n", "                    2435.0,\n", "                    2430.0,\n", "                    2437.0\n", "                ],\n", "                [\n", "                    2435.0,\n", "                    2433.0,\n", "                    2433.0,\n", "                    2444.0\n", "                ],\n", "                [\n", "                    2433.0,\n", "                    2436.0,\n", "                    2431.0,\n", "                    2440.0\n", "                ],\n", "                [\n", "                    2436.0,\n", "                    2439.0,\n", "                    2435.0,\n", "                    2440.0\n", "                ],\n", "                [\n", "                    2440.0,\n", "                    2433.0,\n", "                    2431.0,\n", "                    2441.0\n", "                ],\n", "                [\n", "                    2433.0,\n", "                    2433.0,\n", "                    2428.0,\n", "                    2437.0\n", "                ],\n", "                [\n", "                    2434.0,\n", "                    2433.0,\n", "                    2430.0,\n", "                    2436.0\n", "                ],\n", "                [\n", "                    2433.0,\n", "                    2429.0,\n", "                    2424.0,\n", "                    2433.0\n", "                ],\n", "                [\n", "                    2429.0,\n", "                    2452.0,\n", "                    2428.0,\n", "                    2452.0\n", "                ],\n", "                [\n", "                    2450.0,\n", "                    2451.0,\n", "                    2446.0,\n", "                    2452.0\n", "                ],\n", "                [\n", "                    2451.0,\n", "                    2450.0,\n", "                    2448.0,\n", "                    2451.0\n", "                ],\n", "                [\n", "                    2437.0,\n", "                    2438.0,\n", "                    2432.0,\n", "                    2440.0\n", "                ],\n", "                [\n", "                    2437.0,\n", "                    2440.0,\n", "                    2436.0,\n", "                    2444.0\n", "                ],\n", "                [\n", "                    2439.0,\n", "                    2442.0,\n", "                    2436.0,\n", "                    2442.0\n", "                ],\n", "                [\n", "                    2442.0,\n", "                    2434.0,\n", "                    2433.0,\n", "                    2442.0\n", "                ],\n", "                [\n", "                    2435.0,\n", "                    2441.0,\n", "                    2431.0,\n", "                    2442.0\n", "                ],\n", "                [\n", "                    2440.0,\n", "                    2441.0,\n", "                    2439.0,\n", "                    2443.0\n", "                ],\n", "                [\n", "                    2440.0,\n", "                    2442.0,\n", "                    2440.0,\n", "                    2445.0\n", "                ],\n", "                [\n", "                    2441.0,\n", "                    2445.0,\n", "                    2441.0,\n", "                    2447.0\n", "                ],\n", "                [\n", "                    2445.0,\n", "                    2444.0,\n", "                    2443.0,\n", "                    2448.0\n", "                ],\n", "                [\n", "                    2444.0,\n", "                    2446.0,\n", "                    2443.0,\n", "                    2448.0\n", "                ],\n", "                [\n", "                    2445.0,\n", "                    2444.0,\n", "                    2442.0,\n", "                    2446.0\n", "                ],\n", "                [\n", "                    2444.0,\n", "                    2440.0,\n", "                    2440.0,\n", "                    2445.0\n", "                ],\n", "                [\n", "                    2440.0,\n", "                    2447.0,\n", "                    2440.0,\n", "                    2447.0\n", "                ],\n", "                [\n", "                    2447.0,\n", "                    2447.0,\n", "                    2444.0,\n", "                    2448.0\n", "                ],\n", "                [\n", "                    2446.0,\n", "                    2443.0,\n", "                    2442.0,\n", "                    2447.0\n", "                ],\n", "                [\n", "                    2442.0,\n", "                    2445.0,\n", "                    2442.0,\n", "                    2446.0\n", "                ],\n", "                [\n", "                    2446.0,\n", "                    2445.0,\n", "                    2444.0,\n", "                    2451.0\n", "                ],\n", "                [\n", "                    2444.0,\n", "                    2443.0,\n", "                    2442.0,\n", "                    2447.0\n", "                ],\n", "                [\n", "                    2444.0,\n", "                    2444.0,\n", "                    2441.0,\n", "                    2447.0\n", "                ],\n", "                [\n", "                    2443.0,\n", "                    2446.0,\n", "                    2443.0,\n", "                    2448.0\n", "                ],\n", "                [\n", "                    2445.0,\n", "                    2443.0,\n", "                    2442.0,\n", "                    2446.0\n", "                ],\n", "                [\n", "                    2443.0,\n", "                    2443.0,\n", "                    2441.0,\n", "                    2445.0\n", "                ],\n", "                [\n", "                    2444.0,\n", "                    2438.0,\n", "                    2438.0,\n", "                    2444.0\n", "                ],\n", "                [\n", "                    2439.0,\n", "                    2442.0,\n", "                    2438.0,\n", "                    2443.0\n", "                ],\n", "                [\n", "                    2442.0,\n", "                    2446.0,\n", "                    2440.0,\n", "                    2446.0\n", "                ],\n", "                [\n", "                    2446.0,\n", "                    2447.0,\n", "                    2443.0,\n", "                    2447.0\n", "                ],\n", "                [\n", "                    2446.0,\n", "                    2447.0,\n", "                    2446.0,\n", "                    2450.0\n", "                ],\n", "                [\n", "                    2447.0,\n", "                    2449.0,\n", "                    2446.0,\n", "                    2453.0\n", "                ],\n", "                [\n", "                    2449.0,\n", "                    2448.0,\n", "                    2445.0,\n", "                    2450.0\n", "                ],\n", "                [\n", "                    2448.0,\n", "                    2448.0,\n", "                    2446.0,\n", "                    2450.0\n", "                ],\n", "                [\n", "                    2443.0,\n", "                    2433.0,\n", "                    2426.0,\n", "                    2443.0\n", "                ],\n", "                [\n", "                    2434.0,\n", "                    2430.0,\n", "                    2426.0,\n", "                    2436.0\n", "                ],\n", "                [\n", "                    2430.0,\n", "                    2434.0,\n", "                    2427.0,\n", "                    2436.0\n", "                ],\n", "                [\n", "                    2434.0,\n", "                    2419.0,\n", "                    2415.0,\n", "                    2434.0\n", "                ],\n", "                [\n", "                    2421.0,\n", "                    2418.0,\n", "                    2418.0,\n", "                    2428.0\n", "                ],\n", "                [\n", "                    2418.0,\n", "                    2411.0,\n", "                    2406.0,\n", "                    2419.0\n", "                ],\n", "                [\n", "                    2410.0,\n", "                    2406.0,\n", "                    2394.0,\n", "                    2410.0\n", "                ],\n", "                [\n", "                    2407.0,\n", "                    2407.0,\n", "                    2402.0,\n", "                    2409.0\n", "                ],\n", "                [\n", "                    2408.0,\n", "                    2412.0,\n", "                    2407.0,\n", "                    2412.0\n", "                ],\n", "                [\n", "                    2412.0,\n", "                    2416.0,\n", "                    2410.0,\n", "                    2418.0\n", "                ],\n", "                [\n", "                    2417.0,\n", "                    2411.0,\n", "                    2409.0,\n", "                    2417.0\n", "                ],\n", "                [\n", "                    2412.0,\n", "                    2417.0,\n", "                    2410.0,\n", "                    2417.0\n", "                ],\n", "                [\n", "                    2416.0,\n", "                    2410.0,\n", "                    2410.0,\n", "                    2416.0\n", "                ],\n", "                [\n", "                    2410.0,\n", "                    2417.0,\n", "                    2407.0,\n", "                    2417.0\n", "                ],\n", "                [\n", "                    2416.0,\n", "                    2425.0,\n", "                    2415.0,\n", "                    2428.0\n", "                ],\n", "                [\n", "                    2426.0,\n", "                    2423.0,\n", "                    2422.0,\n", "                    2428.0\n", "                ],\n", "                [\n", "                    2423.0,\n", "                    2426.0,\n", "                    2419.0,\n", "                    2428.0\n", "                ],\n", "                [\n", "                    2426.0,\n", "                    2429.0,\n", "                    2424.0,\n", "                    2432.0\n", "                ],\n", "                [\n", "                    2429.0,\n", "                    2429.0,\n", "                    2426.0,\n", "                    2431.0\n", "                ],\n", "                [\n", "                    2430.0,\n", "                    2428.0,\n", "                    2426.0,\n", "                    2431.0\n", "                ],\n", "                [\n", "                    2429.0,\n", "                    2431.0,\n", "                    2426.0,\n", "                    2431.0\n", "                ],\n", "                [\n", "                    2430.0,\n", "                    2432.0,\n", "                    2430.0,\n", "                    2436.0\n", "                ],\n", "                [\n", "                    2433.0,\n", "                    2431.0,\n", "                    2430.0,\n", "                    2434.0\n", "                ],\n", "                [\n", "                    2432.0,\n", "                    2425.0,\n", "                    2423.0,\n", "                    2432.0\n", "                ],\n", "                [\n", "                    2426.0,\n", "                    2429.0,\n", "                    2424.0,\n", "                    2432.0\n", "                ],\n", "                [\n", "                    2430.0,\n", "                    2430.0,\n", "                    2427.0,\n", "                    2431.0\n", "                ],\n", "                [\n", "                    2430.0,\n", "                    2428.0,\n", "                    2427.0,\n", "                    2430.0\n", "                ],\n", "                [\n", "                    2428.0,\n", "                    2423.0,\n", "                    2422.0,\n", "                    2430.0\n", "                ],\n", "                [\n", "                    2423.0,\n", "                    2417.0,\n", "                    2414.0,\n", "                    2424.0\n", "                ],\n", "                [\n", "                    2417.0,\n", "                    2418.0,\n", "                    2414.0,\n", "                    2420.0\n", "                ],\n", "                [\n", "                    2419.0,\n", "                    2414.0,\n", "                    2412.0,\n", "                    2419.0\n", "                ],\n", "                [\n", "                    2414.0,\n", "                    2417.0,\n", "                    2414.0,\n", "                    2418.0\n", "                ],\n", "                [\n", "                    2416.0,\n", "                    2419.0,\n", "                    2416.0,\n", "                    2421.0\n", "                ],\n", "                [\n", "                    2419.0,\n", "                    2418.0,\n", "                    2415.0,\n", "                    2419.0\n", "                ],\n", "                [\n", "                    2418.0,\n", "                    2416.0,\n", "                    2414.0,\n", "                    2418.0\n", "                ],\n", "                [\n", "                    2416.0,\n", "                    2418.0,\n", "                    2416.0,\n", "                    2420.0\n", "                ],\n", "                [\n", "                    2418.0,\n", "                    2414.0,\n", "                    2414.0,\n", "                    2420.0\n", "                ],\n", "                [\n", "                    2414.0,\n", "                    2418.0,\n", "                    2411.0,\n", "                    2418.0\n", "                ],\n", "                [\n", "                    2417.0,\n", "                    2425.0,\n", "                    2417.0,\n", "                    2428.0\n", "                ],\n", "                [\n", "                    2425.0,\n", "                    2424.0,\n", "                    2421.0,\n", "                    2426.0\n", "                ],\n", "                [\n", "                    2424.0,\n", "                    2424.0,\n", "                    2420.0,\n", "                    2425.0\n", "                ],\n", "                [\n", "                    2423.0,\n", "                    2423.0,\n", "                    2421.0,\n", "                    2424.0\n", "                ],\n", "                [\n", "                    2423.0,\n", "                    2419.0,\n", "                    2419.0,\n", "                    2423.0\n", "                ],\n", "                [\n", "                    2420.0,\n", "                    2420.0,\n", "                    2418.0,\n", "                    2423.0\n", "                ],\n", "                [\n", "                    2420.0,\n", "                    2415.0,\n", "                    2413.0,\n", "                    2421.0\n", "                ],\n", "                [\n", "                    2406.0,\n", "                    2395.0,\n", "                    2389.0,\n", "                    2406.0\n", "                ],\n", "                [\n", "                    2395.0,\n", "                    2401.0,\n", "                    2393.0,\n", "                    2401.0\n", "                ],\n", "                [\n", "                    2401.0,\n", "                    2397.0,\n", "                    2397.0,\n", "                    2402.0\n", "                ],\n", "                [\n", "                    2397.0,\n", "                    2401.0,\n", "                    2395.0,\n", "                    2403.0\n", "                ],\n", "                [\n", "                    2402.0,\n", "                    2404.0,\n", "                    2400.0,\n", "                    2404.0\n", "                ],\n", "                [\n", "                    2404.0,\n", "                    2401.0,\n", "                    2401.0,\n", "                    2405.0\n", "                ],\n", "                [\n", "                    2401.0,\n", "                    2401.0,\n", "                    2401.0,\n", "                    2403.0\n", "                ],\n", "                [\n", "                    2401.0,\n", "                    2401.0,\n", "                    2398.0,\n", "                    2402.0\n", "                ],\n", "                [\n", "                    2401.0,\n", "                    2405.0,\n", "                    2399.0,\n", "                    2406.0\n", "                ],\n", "                [\n", "                    2406.0,\n", "                    2402.0,\n", "                    2401.0,\n", "                    2406.0\n", "                ],\n", "                [\n", "                    2403.0,\n", "                    2403.0,\n", "                    2402.0,\n", "                    2405.0\n", "                ],\n", "                [\n", "                    2403.0,\n", "                    2401.0,\n", "                    2401.0,\n", "                    2405.0\n", "                ],\n", "                [\n", "                    2401.0,\n", "                    2408.0,\n", "                    2401.0,\n", "                    2410.0\n", "                ],\n", "                [\n", "                    2408.0,\n", "                    2412.0,\n", "                    2408.0,\n", "                    2415.0\n", "                ],\n", "                [\n", "                    2413.0,\n", "                    2414.0,\n", "                    2411.0,\n", "                    2414.0\n", "                ],\n", "                [\n", "                    2414.0,\n", "                    2414.0,\n", "                    2408.0,\n", "                    2414.0\n", "                ],\n", "                [\n", "                    2414.0,\n", "                    2412.0,\n", "                    2411.0,\n", "                    2415.0\n", "                ],\n", "                [\n", "                    2413.0,\n", "                    2412.0,\n", "                    2410.0,\n", "                    2413.0\n", "                ],\n", "                [\n", "                    2411.0,\n", "                    2415.0,\n", "                    2411.0,\n", "                    2417.0\n", "                ],\n", "                [\n", "                    2414.0,\n", "                    2412.0,\n", "                    2411.0,\n", "                    2415.0\n", "                ],\n", "                [\n", "                    2411.0,\n", "                    2407.0,\n", "                    2406.0,\n", "                    2413.0\n", "                ],\n", "                [\n", "                    2408.0,\n", "                    2406.0,\n", "                    2403.0,\n", "                    2409.0\n", "                ],\n", "                [\n", "                    2406.0,\n", "                    2406.0,\n", "                    2402.0,\n", "                    2406.0\n", "                ],\n", "                [\n", "                    2405.0,\n", "                    2406.0,\n", "                    2402.0,\n", "                    2406.0\n", "                ],\n", "                [\n", "                    2405.0,\n", "                    2400.0,\n", "                    2398.0,\n", "                    2408.0\n", "                ],\n", "                [\n", "                    2400.0,\n", "                    2396.0,\n", "                    2393.0,\n", "                    2402.0\n", "                ],\n", "                [\n", "                    2396.0,\n", "                    2401.0,\n", "                    2396.0,\n", "                    2403.0\n", "                ],\n", "                [\n", "                    2401.0,\n", "                    2398.0,\n", "                    2396.0,\n", "                    2401.0\n", "                ],\n", "                [\n", "                    2399.0,\n", "                    2399.0,\n", "                    2397.0,\n", "                    2401.0\n", "                ],\n", "                [\n", "                    2399.0,\n", "                    2399.0,\n", "                    2397.0,\n", "                    2403.0\n", "                ],\n", "                [\n", "                    2369.0,\n", "                    2354.0,\n", "                    2338.0,\n", "                    2369.0\n", "                ],\n", "                [\n", "                    2353.0,\n", "                    2337.0,\n", "                    2337.0,\n", "                    2354.0\n", "                ],\n", "                [\n", "                    2338.0,\n", "                    2341.0,\n", "                    2337.0,\n", "                    2347.0\n", "                ],\n", "                [\n", "                    2341.0,\n", "                    2338.0,\n", "                    2332.0,\n", "                    2342.0\n", "                ],\n", "                [\n", "                    2338.0,\n", "                    2337.0,\n", "                    2334.0,\n", "                    2339.0\n", "                ],\n", "                [\n", "                    2337.0,\n", "                    2342.0,\n", "                    2335.0,\n", "                    2342.0\n", "                ],\n", "                [\n", "                    2341.0,\n", "                    2325.0,\n", "                    2321.0,\n", "                    2341.0\n", "                ],\n", "                [\n", "                    2326.0,\n", "                    2327.0,\n", "                    2323.0,\n", "                    2330.0\n", "                ],\n", "                [\n", "                    2328.0,\n", "                    2320.0,\n", "                    2318.0,\n", "                    2329.0\n", "                ],\n", "                [\n", "                    2321.0,\n", "                    2326.0,\n", "                    2320.0,\n", "                    2328.0\n", "                ],\n", "                [\n", "                    2326.0,\n", "                    2325.0,\n", "                    2323.0,\n", "                    2328.0\n", "                ],\n", "                [\n", "                    2325.0,\n", "                    2323.0,\n", "                    2322.0,\n", "                    2328.0\n", "                ],\n", "                [\n", "                    2323.0,\n", "                    2323.0,\n", "                    2322.0,\n", "                    2328.0\n", "                ],\n", "                [\n", "                    2324.0,\n", "                    2332.0,\n", "                    2323.0,\n", "                    2333.0\n", "                ],\n", "                [\n", "                    2332.0,\n", "                    2330.0,\n", "                    2329.0,\n", "                    2333.0\n", "                ],\n", "                [\n", "                    2330.0,\n", "                    2325.0,\n", "                    2324.0,\n", "                    2330.0\n", "                ],\n", "                [\n", "                    2325.0,\n", "                    2320.0,\n", "                    2316.0,\n", "                    2327.0\n", "                ],\n", "                [\n", "                    2320.0,\n", "                    2323.0,\n", "                    2316.0,\n", "                    2323.0\n", "                ],\n", "                [\n", "                    2322.0,\n", "                    2321.0,\n", "                    2321.0,\n", "                    2326.0\n", "                ],\n", "                [\n", "                    2321.0,\n", "                    2312.0,\n", "                    2311.0,\n", "                    2322.0\n", "                ],\n", "                [\n", "                    2311.0,\n", "                    2308.0,\n", "                    2308.0,\n", "                    2314.0\n", "                ],\n", "                [\n", "                    2309.0,\n", "                    2313.0,\n", "                    2306.0,\n", "                    2313.0\n", "                ],\n", "                [\n", "                    2312.0,\n", "                    2311.0,\n", "                    2309.0,\n", "                    2314.0\n", "                ],\n", "                [\n", "                    2310.0,\n", "                    2309.0,\n", "                    2308.0,\n", "                    2312.0\n", "                ],\n", "                [\n", "                    2309.0,\n", "                    2312.0,\n", "                    2308.0,\n", "                    2313.0\n", "                ],\n", "                [\n", "                    2312.0,\n", "                    2309.0,\n", "                    2307.0,\n", "                    2312.0\n", "                ],\n", "                [\n", "                    2310.0,\n", "                    2306.0,\n", "                    2306.0,\n", "                    2313.0\n", "                ],\n", "                [\n", "                    2307.0,\n", "                    2310.0,\n", "                    2306.0,\n", "                    2312.0\n", "                ],\n", "                [\n", "                    2310.0,\n", "                    2317.0,\n", "                    2310.0,\n", "                    2317.0\n", "                ],\n", "                [\n", "                    2317.0,\n", "                    2318.0,\n", "                    2316.0,\n", "                    2322.0\n", "                ],\n", "                [\n", "                    2318.0,\n", "                    2321.0,\n", "                    2317.0,\n", "                    2322.0\n", "                ],\n", "                [\n", "                    2321.0,\n", "                    2327.0,\n", "                    2321.0,\n", "                    2329.0\n", "                ],\n", "                [\n", "                    2327.0,\n", "                    2326.0,\n", "                    2322.0,\n", "                    2331.0\n", "                ],\n", "                [\n", "                    2326.0,\n", "                    2329.0,\n", "                    2323.0,\n", "                    2330.0\n", "                ],\n", "                [\n", "                    2330.0,\n", "                    2327.0,\n", "                    2326.0,\n", "                    2331.0\n", "                ],\n", "                [\n", "                    2326.0,\n", "                    2327.0,\n", "                    2324.0,\n", "                    2328.0\n", "                ],\n", "                [\n", "                    2327.0,\n", "                    2333.0,\n", "                    2327.0,\n", "                    2336.0\n", "                ],\n", "                [\n", "                    2333.0,\n", "                    2332.0,\n", "                    2331.0,\n", "                    2334.0\n", "                ],\n", "                [\n", "                    2331.0,\n", "                    2329.0,\n", "                    2329.0,\n", "                    2333.0\n", "                ],\n", "                [\n", "                    2329.0,\n", "                    2331.0,\n", "                    2326.0,\n", "                    2331.0\n", "                ],\n", "                [\n", "                    2330.0,\n", "                    2323.0,\n", "                    2322.0,\n", "                    2331.0\n", "                ],\n", "                [\n", "                    2324.0,\n", "                    2323.0,\n", "                    2320.0,\n", "                    2324.0\n", "                ],\n", "                [\n", "                    2323.0,\n", "                    2321.0,\n", "                    2320.0,\n", "                    2326.0\n", "                ],\n", "                [\n", "                    2321.0,\n", "                    2322.0,\n", "                    2321.0,\n", "                    2325.0\n", "                ],\n", "                [\n", "                    2323.0,\n", "                    2317.0,\n", "                    2317.0,\n", "                    2324.0\n", "                ],\n", "                [\n", "                    2317.0,\n", "                    2330.0,\n", "                    2317.0,\n", "                    2333.0\n", "                ],\n", "                [\n", "                    2329.0,\n", "                    2328.0,\n", "                    2325.0,\n", "                    2329.0\n", "                ],\n", "                [\n", "                    2328.0,\n", "                    2332.0,\n", "                    2327.0,\n", "                    2332.0\n", "                ],\n", "                [\n", "                    2332.0,\n", "                    2330.0,\n", "                    2329.0,\n", "                    2334.0\n", "                ],\n", "                [\n", "                    2330.0,\n", "                    2329.0,\n", "                    2328.0,\n", "                    2331.0\n", "                ],\n", "                [\n", "                    2329.0,\n", "                    2328.0,\n", "                    2328.0,\n", "                    2331.0\n", "                ],\n", "                [\n", "                    2328.0,\n", "                    2333.0,\n", "                    2327.0,\n", "                    2333.0\n", "                ],\n", "                [\n", "                    2333.0,\n", "                    2335.0,\n", "                    2330.0,\n", "                    2336.0\n", "                ],\n", "                [\n", "                    2335.0,\n", "                    2332.0,\n", "                    2330.0,\n", "                    2336.0\n", "                ],\n", "                [\n", "                    2332.0,\n", "                    2329.0,\n", "                    2328.0,\n", "                    2332.0\n", "                ],\n", "                [\n", "                    2329.0,\n", "                    2331.0,\n", "                    2327.0,\n", "                    2331.0\n", "                ],\n", "                [\n", "                    2331.0,\n", "                    2329.0,\n", "                    2328.0,\n", "                    2331.0\n", "                ],\n", "                [\n", "                    2330.0,\n", "                    2325.0,\n", "                    2323.0,\n", "                    2330.0\n", "                ],\n", "                [\n", "                    2325.0,\n", "                    2324.0,\n", "                    2324.0,\n", "                    2327.0\n", "                ],\n", "                [\n", "                    2324.0,\n", "                    2329.0,\n", "                    2324.0,\n", "                    2332.0\n", "                ],\n", "                [\n", "                    2330.0,\n", "                    2331.0,\n", "                    2328.0,\n", "                    2333.0\n", "                ],\n", "                [\n", "                    2330.0,\n", "                    2331.0,\n", "                    2329.0,\n", "                    2331.0\n", "                ],\n", "                [\n", "                    2331.0,\n", "                    2330.0,\n", "                    2329.0,\n", "                    2331.0\n", "                ],\n", "                [\n", "                    2330.0,\n", "                    2331.0,\n", "                    2328.0,\n", "                    2331.0\n", "                ],\n", "                [\n", "                    2330.0,\n", "                    2326.0,\n", "                    2326.0,\n", "                    2331.0\n", "                ],\n", "                [\n", "                    2326.0,\n", "                    2329.0,\n", "                    2326.0,\n", "                    2330.0\n", "                ],\n", "                [\n", "                    2328.0,\n", "                    2328.0,\n", "                    2327.0,\n", "                    2330.0\n", "                ],\n", "                [\n", "                    2328.0,\n", "                    2329.0,\n", "                    2328.0,\n", "                    2330.0\n", "                ],\n", "                [\n", "                    2328.0,\n", "                    2329.0,\n", "                    2328.0,\n", "                    2331.0\n", "                ],\n", "                [\n", "                    2328.0,\n", "                    2326.0,\n", "                    2326.0,\n", "                    2330.0\n", "                ],\n", "                [\n", "                    2326.0,\n", "                    2326.0,\n", "                    2325.0,\n", "                    2329.0\n", "                ],\n", "                [\n", "                    2326.0,\n", "                    2324.0,\n", "                    2324.0,\n", "                    2328.0\n", "                ],\n", "                [\n", "                    2325.0,\n", "                    2328.0,\n", "                    2324.0,\n", "                    2328.0\n", "                ],\n", "                [\n", "                    2327.0,\n", "                    2325.0,\n", "                    2324.0,\n", "                    2327.0\n", "                ],\n", "                [\n", "                    2326.0,\n", "                    2326.0,\n", "                    2325.0,\n", "                    2328.0\n", "                ],\n", "                [\n", "                    2331.0,\n", "                    2343.0,\n", "                    2331.0,\n", "                    2345.0\n", "                ],\n", "                [\n", "                    2343.0,\n", "                    2344.0,\n", "                    2342.0,\n", "                    2347.0\n", "                ],\n", "                [\n", "                    2344.0,\n", "                    2340.0,\n", "                    2340.0,\n", "                    2345.0\n", "                ],\n", "                [\n", "                    2341.0,\n", "                    2341.0,\n", "                    2339.0,\n", "                    2342.0\n", "                ],\n", "                [\n", "                    2340.0,\n", "                    2337.0,\n", "                    2336.0,\n", "                    2341.0\n", "                ],\n", "                [\n", "                    2337.0,\n", "                    2336.0,\n", "                    2334.0,\n", "                    2338.0\n", "                ],\n", "                [\n", "                    2336.0,\n", "                    2335.0,\n", "                    2334.0,\n", "                    2339.0\n", "                ],\n", "                [\n", "                    2335.0,\n", "                    2337.0,\n", "                    2334.0,\n", "                    2338.0\n", "                ],\n", "                [\n", "                    2337.0,\n", "                    2338.0,\n", "                    2337.0,\n", "                    2341.0\n", "                ],\n", "                [\n", "                    2339.0,\n", "                    2340.0,\n", "                    2337.0,\n", "                    2341.0\n", "                ],\n", "                [\n", "                    2340.0,\n", "                    2341.0,\n", "                    2338.0,\n", "                    2342.0\n", "                ],\n", "                [\n", "                    2341.0,\n", "                    2338.0,\n", "                    2337.0,\n", "                    2341.0\n", "                ],\n", "                [\n", "                    2338.0,\n", "                    2339.0,\n", "                    2334.0,\n", "                    2339.0\n", "                ],\n", "                [\n", "                    2338.0,\n", "                    2337.0,\n", "                    2335.0,\n", "                    2339.0\n", "                ],\n", "                [\n", "                    2337.0,\n", "                    2336.0,\n", "                    2334.0,\n", "                    2338.0\n", "                ],\n", "                [\n", "                    2336.0,\n", "                    2336.0,\n", "                    2336.0,\n", "                    2338.0\n", "                ]\n", "            ],\n", "            \"markPoint\": {\n", "                \"data\": [\n", "                    {\n", "                        \"type\": \"max\",\n", "                        \"name\": \"Maximum\",\n", "                        \"symbol\": \"pin\",\n", "                        \"symbolSize\": 50,\n", "                        \"label\": {\n", "                            \"normal\": {\n", "                                \"textStyle\": {\n", "                                    \"color\": \"#fff\"\n", "                                }\n", "                            }\n", "                        }\n", "                    }\n", "                ]\n", "            },\n", "            \"markLine\": {\n", "                \"data\": []\n", "            },\n", "            \"seriesId\": 8769018,\n", "            \"xAxisIndex\": 1,\n", "            \"yAxisIndex\": 1\n", "        }\n", "    ],\n", "    \"legend\": [\n", "        {\n", "            \"data\": [\n", "                \"ATR\",\n", "                \"stddev\"\n", "            ],\n", "            \"selectedMode\": \"multiple\",\n", "            \"show\": true,\n", "            \"left\": \"center\",\n", "            \"top\": \"50%\",\n", "            \"orient\": \"horizontal\",\n", "            \"textStyle\": {\n", "                \"fontSize\": 12\n", "            }\n", "        },\n", "        {\n", "            \"data\": [\n", "                \"MA8888.ZC BarSize.min5 KLine (bar count:260)\"\n", "            ],\n", "            \"selectedMode\": \"multiple\",\n", "            \"show\": true,\n", "            \"left\": \"center\",\n", "            \"top\": \"top\",\n", "            \"orient\": \"horizontal\",\n", "            \"textStyle\": {\n", "                \"fontSize\": 12\n", "            }\n", "        }\n", "    ],\n", "    \"animation\": true,\n", "    \"xAxis\": [\n", "        {\n", "            \"show\": true,\n", "            \"nameLocation\": \"middle\",\n", "            \"nameGap\": 25,\n", "            \"nameTextStyle\": {\n", "                \"fontSize\": 14\n", "            },\n", "            \"axisTick\": {\n", "                \"alignWithLabel\": false\n", "            },\n", "            \"inverse\": false,\n", "            \"boundaryGap\": true,\n", "            \"type\": \"category\",\n", "            \"splitLine\": {\n", "                \"show\": false\n", "            },\n", "            \"axisLine\": {\n", "                \"lineStyle\": {\n", "                    \"width\": 1\n", "                }\n", "            },\n", "            \"axisLabel\": {\n", "                \"interval\": \"auto\",\n", "                \"rotate\": 0,\n", "                \"margin\": 8,\n", "                \"textStyle\": {\n", "                    \"fontSize\": 12\n", "                }\n", "            },\n", "            \"data\": [\n", "                \"2018-12-20 21:55\",\n", "                \"2018-12-20 22:00\",\n", "                \"2018-12-20 22:05\",\n", "                \"2018-12-20 22:10\",\n", "                \"2018-12-20 22:15\",\n", "                \"2018-12-20 22:20\",\n", "                \"2018-12-20 22:25\",\n", "                \"2018-12-20 22:30\",\n", "                \"2018-12-20 22:35\",\n", "                \"2018-12-20 22:40\",\n", "                \"2018-12-20 22:45\",\n", "                \"2018-12-20 22:50\",\n", "                \"2018-12-20 22:55\",\n", "                \"2018-12-20 23:00\",\n", "                \"2018-12-20 23:05\",\n", "                \"2018-12-20 23:10\",\n", "                \"2018-12-20 23:15\",\n", "                \"2018-12-20 23:20\",\n", "                \"2018-12-20 23:25\",\n", "                \"2018-12-21 09:00\",\n", "                \"2018-12-21 09:05\",\n", "                \"2018-12-21 09:10\",\n", "                \"2018-12-21 09:15\",\n", "                \"2018-12-21 09:20\",\n", "                \"2018-12-21 09:25\",\n", "                \"2018-12-21 09:30\",\n", "                \"2018-12-21 09:35\",\n", "                \"2018-12-21 09:40\",\n", "                \"2018-12-21 09:45\",\n", "                \"2018-12-21 09:50\",\n", "                \"2018-12-21 09:55\",\n", "                \"2018-12-21 10:00\",\n", "                \"2018-12-21 10:05\",\n", "                \"2018-12-21 10:10\",\n", "                \"2018-12-21 10:30\",\n", "                \"2018-12-21 10:35\",\n", "                \"2018-12-21 10:40\",\n", "                \"2018-12-21 10:45\",\n", "                \"2018-12-21 10:50\",\n", "                \"2018-12-21 10:55\",\n", "                \"2018-12-21 11:00\",\n", "                \"2018-12-21 11:05\",\n", "                \"2018-12-21 11:10\",\n", "                \"2018-12-21 11:15\",\n", "                \"2018-12-21 11:20\",\n", "                \"2018-12-21 11:25\",\n", "                \"2018-12-21 13:30\",\n", "                \"2018-12-21 13:35\",\n", "                \"2018-12-21 13:40\",\n", "                \"2018-12-21 13:45\",\n", "                \"2018-12-21 13:50\",\n", "                \"2018-12-21 13:55\",\n", "                \"2018-12-21 14:00\",\n", "                \"2018-12-21 14:05\",\n", "                \"2018-12-21 14:10\",\n", "                \"2018-12-21 14:15\",\n", "                \"2018-12-21 14:20\",\n", "                \"2018-12-21 14:25\",\n", "                \"2018-12-21 14:30\",\n", "                \"2018-12-21 14:35\",\n", "                \"2018-12-21 14:40\",\n", "                \"2018-12-21 14:45\",\n", "                \"2018-12-21 14:50\",\n", "                \"2018-12-21 14:55\",\n", "                \"2018-12-21 21:00\",\n", "                \"2018-12-21 21:05\",\n", "                \"2018-12-21 21:10\",\n", "                \"2018-12-21 21:15\",\n", "                \"2018-12-21 21:20\",\n", "                \"2018-12-21 21:25\",\n", "                \"2018-12-21 21:30\",\n", "                \"2018-12-21 21:35\",\n", "                \"2018-12-21 21:40\",\n", "                \"2018-12-21 21:45\",\n", "                \"2018-12-21 21:50\",\n", "                \"2018-12-21 21:55\",\n", "                \"2018-12-21 22:00\",\n", "                \"2018-12-21 22:05\",\n", "                \"2018-12-21 22:10\",\n", "                \"2018-12-21 22:15\",\n", "                \"2018-12-21 22:20\",\n", "                \"2018-12-21 22:25\",\n", "                \"2018-12-21 22:30\",\n", "                \"2018-12-21 22:35\",\n", "                \"2018-12-21 22:40\",\n", "                \"2018-12-21 22:45\",\n", "                \"2018-12-21 22:50\",\n", "                \"2018-12-21 22:55\",\n", "                \"2018-12-21 23:00\",\n", "                \"2018-12-21 23:05\",\n", "                \"2018-12-21 23:10\",\n", "                \"2018-12-21 23:15\",\n", "                \"2018-12-21 23:20\",\n", "                \"2018-12-21 23:25\",\n", "                \"2018-12-24 09:00\",\n", "                \"2018-12-24 09:05\",\n", "                \"2018-12-24 09:10\",\n", "                \"2018-12-24 09:15\",\n", "                \"2018-12-24 09:20\",\n", "                \"2018-12-24 09:25\",\n", "                \"2018-12-24 09:30\",\n", "                \"2018-12-24 09:35\",\n", "                \"2018-12-24 09:40\",\n", "                \"2018-12-24 09:45\",\n", "                \"2018-12-24 09:50\",\n", "                \"2018-12-24 09:55\",\n", "                \"2018-12-24 10:00\",\n", "                \"2018-12-24 10:05\",\n", "                \"2018-12-24 10:10\",\n", "                \"2018-12-24 10:30\",\n", "                \"2018-12-24 10:35\",\n", "                \"2018-12-24 10:40\",\n", "                \"2018-12-24 10:45\",\n", "                \"2018-12-24 10:50\",\n", "                \"2018-12-24 10:55\",\n", "                \"2018-12-24 11:00\",\n", "                \"2018-12-24 11:05\",\n", "                \"2018-12-24 11:10\",\n", "                \"2018-12-24 11:15\",\n", "                \"2018-12-24 11:20\",\n", "                \"2018-12-24 11:25\",\n", "                \"2018-12-24 13:30\",\n", "                \"2018-12-24 13:35\",\n", "                \"2018-12-24 13:40\",\n", "                \"2018-12-24 13:45\",\n", "                \"2018-12-24 13:50\",\n", "                \"2018-12-24 13:55\",\n", "                \"2018-12-24 14:00\",\n", "                \"2018-12-24 14:05\",\n", "                \"2018-12-24 14:10\",\n", "                \"2018-12-24 14:15\",\n", "                \"2018-12-24 14:20\",\n", "                \"2018-12-24 14:25\",\n", "                \"2018-12-24 14:30\",\n", "                \"2018-12-24 14:35\",\n", "                \"2018-12-24 14:40\",\n", "                \"2018-12-24 14:45\",\n", "                \"2018-12-24 14:50\",\n", "                \"2018-12-24 14:55\",\n", "                \"2018-12-24 21:00\",\n", "                \"2018-12-24 21:05\",\n", "                \"2018-12-24 21:10\",\n", "                \"2018-12-24 21:15\",\n", "                \"2018-12-24 21:20\",\n", "                \"2018-12-24 21:25\",\n", "                \"2018-12-24 21:30\",\n", "                \"2018-12-24 21:35\",\n", "                \"2018-12-24 21:40\",\n", "                \"2018-12-24 21:45\",\n", "                \"2018-12-24 21:50\",\n", "                \"2018-12-24 21:55\",\n", "                \"2018-12-24 22:00\",\n", "                \"2018-12-24 22:05\",\n", "                \"2018-12-24 22:10\",\n", "                \"2018-12-24 22:15\",\n", "                \"2018-12-24 22:20\",\n", "                \"2018-12-24 22:25\",\n", "                \"2018-12-24 22:30\",\n", "                \"2018-12-24 22:35\",\n", "                \"2018-12-24 22:40\",\n", "                \"2018-12-24 22:45\",\n", "                \"2018-12-24 22:50\",\n", "                \"2018-12-24 22:55\",\n", "                \"2018-12-24 23:00\",\n", "                \"2018-12-24 23:05\",\n", "                \"2018-12-24 23:10\",\n", "                \"2018-12-24 23:15\",\n", "                \"2018-12-24 23:20\",\n", "                \"2018-12-24 23:25\",\n", "                \"2018-12-25 09:00\",\n", "                \"2018-12-25 09:05\",\n", "                \"2018-12-25 09:10\",\n", "                \"2018-12-25 09:15\",\n", "                \"2018-12-25 09:20\",\n", "                \"2018-12-25 09:25\",\n", "                \"2018-12-25 09:30\",\n", "                \"2018-12-25 09:35\",\n", "                \"2018-12-25 09:40\",\n", "                \"2018-12-25 09:45\",\n", "                \"2018-12-25 09:50\",\n", "                \"2018-12-25 09:55\",\n", "                \"2018-12-25 10:00\",\n", "                \"2018-12-25 10:05\",\n", "                \"2018-12-25 10:10\",\n", "                \"2018-12-25 10:30\",\n", "                \"2018-12-25 10:35\",\n", "                \"2018-12-25 10:40\",\n", "                \"2018-12-25 10:45\",\n", "                \"2018-12-25 10:50\",\n", "                \"2018-12-25 10:55\",\n", "                \"2018-12-25 11:00\",\n", "                \"2018-12-25 11:05\",\n", "                \"2018-12-25 11:10\",\n", "                \"2018-12-25 11:15\",\n", "                \"2018-12-25 11:20\",\n", "                \"2018-12-25 11:25\",\n", "                \"2018-12-25 13:30\",\n", "                \"2018-12-25 13:35\",\n", "                \"2018-12-25 13:40\",\n", "                \"2018-12-25 13:45\",\n", "                \"2018-12-25 13:50\",\n", "                \"2018-12-25 13:55\",\n", "                \"2018-12-25 14:00\",\n", "                \"2018-12-25 14:05\",\n", "                \"2018-12-25 14:10\",\n", "                \"2018-12-25 14:15\",\n", "                \"2018-12-25 14:20\",\n", "                \"2018-12-25 14:25\",\n", "                \"2018-12-25 14:30\",\n", "                \"2018-12-25 14:35\",\n", "                \"2018-12-25 14:40\",\n", "                \"2018-12-25 14:45\",\n", "                \"2018-12-25 14:50\",\n", "                \"2018-12-25 14:55\",\n", "                \"2018-12-25 21:00\",\n", "                \"2018-12-25 21:05\",\n", "                \"2018-12-25 21:10\",\n", "                \"2018-12-25 21:15\",\n", "                \"2018-12-25 21:20\",\n", "                \"2018-12-25 21:25\",\n", "                \"2018-12-25 21:30\",\n", "                \"2018-12-25 21:35\",\n", "                \"2018-12-25 21:40\",\n", "                \"2018-12-25 21:45\",\n", "                \"2018-12-25 21:50\",\n", "                \"2018-12-25 21:55\",\n", "                \"2018-12-25 22:00\",\n", "                \"2018-12-25 22:05\",\n", "                \"2018-12-25 22:10\",\n", "                \"2018-12-25 22:15\",\n", "                \"2018-12-25 22:20\",\n", "                \"2018-12-25 22:25\",\n", "                \"2018-12-25 22:30\",\n", "                \"2018-12-25 22:35\",\n", "                \"2018-12-25 22:40\",\n", "                \"2018-12-25 22:45\",\n", "                \"2018-12-25 22:50\",\n", "                \"2018-12-25 22:55\",\n", "                \"2018-12-25 23:00\",\n", "                \"2018-12-25 23:05\",\n", "                \"2018-12-25 23:10\",\n", "                \"2018-12-25 23:15\",\n", "                \"2018-12-25 23:20\",\n", "                \"2018-12-25 23:25\",\n", "                \"2018-12-26 09:00\",\n", "                \"2018-12-26 09:05\",\n", "                \"2018-12-26 09:10\",\n", "                \"2018-12-26 09:15\",\n", "                \"2018-12-26 09:20\",\n", "                \"2018-12-26 09:25\",\n", "                \"2018-12-26 09:30\",\n", "                \"2018-12-26 09:35\",\n", "                \"2018-12-26 09:40\",\n", "                \"2018-12-26 09:45\",\n", "                \"2018-12-26 09:50\",\n", "                \"2018-12-26 09:55\",\n", "                \"2018-12-26 10:00\",\n", "                \"2018-12-26 10:05\",\n", "                \"2018-12-26 10:10\",\n", "                \"2018-12-26 10:30\"\n", "            ]\n", "        },\n", "        {\n", "            \"show\": true,\n", "            \"nameLocation\": \"middle\",\n", "            \"nameGap\": 25,\n", "            \"nameTextStyle\": {\n", "                \"fontSize\": 14\n", "            },\n", "            \"axisTick\": {\n", "                \"alignWithLabel\": false\n", "            },\n", "            \"inverse\": false,\n", "            \"boundaryGap\": true,\n", "            \"type\": \"category\",\n", "            \"splitLine\": {\n", "                \"show\": false\n", "            },\n", "            \"axisLine\": {\n", "                \"lineStyle\": {\n", "                    \"width\": 1\n", "                }\n", "            },\n", "            \"axisLabel\": {\n", "                \"interval\": \"auto\",\n", "                \"rotate\": 0,\n", "                \"margin\": 8,\n", "                \"textStyle\": {\n", "                    \"fontSize\": 12\n", "                }\n", "            },\n", "            \"data\": [\n", "                \"2018-12-20 21:55\",\n", "                \"2018-12-20 22:00\",\n", "                \"2018-12-20 22:05\",\n", "                \"2018-12-20 22:10\",\n", "                \"2018-12-20 22:15\",\n", "                \"2018-12-20 22:20\",\n", "                \"2018-12-20 22:25\",\n", "                \"2018-12-20 22:30\",\n", "                \"2018-12-20 22:35\",\n", "                \"2018-12-20 22:40\",\n", "                \"2018-12-20 22:45\",\n", "                \"2018-12-20 22:50\",\n", "                \"2018-12-20 22:55\",\n", "                \"2018-12-20 23:00\",\n", "                \"2018-12-20 23:05\",\n", "                \"2018-12-20 23:10\",\n", "                \"2018-12-20 23:15\",\n", "                \"2018-12-20 23:20\",\n", "                \"2018-12-20 23:25\",\n", "                \"2018-12-21 09:00\",\n", "                \"2018-12-21 09:05\",\n", "                \"2018-12-21 09:10\",\n", "                \"2018-12-21 09:15\",\n", "                \"2018-12-21 09:20\",\n", "                \"2018-12-21 09:25\",\n", "                \"2018-12-21 09:30\",\n", "                \"2018-12-21 09:35\",\n", "                \"2018-12-21 09:40\",\n", "                \"2018-12-21 09:45\",\n", "                \"2018-12-21 09:50\",\n", "                \"2018-12-21 09:55\",\n", "                \"2018-12-21 10:00\",\n", "                \"2018-12-21 10:05\",\n", "                \"2018-12-21 10:10\",\n", "                \"2018-12-21 10:30\",\n", "                \"2018-12-21 10:35\",\n", "                \"2018-12-21 10:40\",\n", "                \"2018-12-21 10:45\",\n", "                \"2018-12-21 10:50\",\n", "                \"2018-12-21 10:55\",\n", "                \"2018-12-21 11:00\",\n", "                \"2018-12-21 11:05\",\n", "                \"2018-12-21 11:10\",\n", "                \"2018-12-21 11:15\",\n", "                \"2018-12-21 11:20\",\n", "                \"2018-12-21 11:25\",\n", "                \"2018-12-21 13:30\",\n", "                \"2018-12-21 13:35\",\n", "                \"2018-12-21 13:40\",\n", "                \"2018-12-21 13:45\",\n", "                \"2018-12-21 13:50\",\n", "                \"2018-12-21 13:55\",\n", "                \"2018-12-21 14:00\",\n", "                \"2018-12-21 14:05\",\n", "                \"2018-12-21 14:10\",\n", "                \"2018-12-21 14:15\",\n", "                \"2018-12-21 14:20\",\n", "                \"2018-12-21 14:25\",\n", "                \"2018-12-21 14:30\",\n", "                \"2018-12-21 14:35\",\n", "                \"2018-12-21 14:40\",\n", "                \"2018-12-21 14:45\",\n", "                \"2018-12-21 14:50\",\n", "                \"2018-12-21 14:55\",\n", "                \"2018-12-21 21:00\",\n", "                \"2018-12-21 21:05\",\n", "                \"2018-12-21 21:10\",\n", "                \"2018-12-21 21:15\",\n", "                \"2018-12-21 21:20\",\n", "                \"2018-12-21 21:25\",\n", "                \"2018-12-21 21:30\",\n", "                \"2018-12-21 21:35\",\n", "                \"2018-12-21 21:40\",\n", "                \"2018-12-21 21:45\",\n", "                \"2018-12-21 21:50\",\n", "                \"2018-12-21 21:55\",\n", "                \"2018-12-21 22:00\",\n", "                \"2018-12-21 22:05\",\n", "                \"2018-12-21 22:10\",\n", "                \"2018-12-21 22:15\",\n", "                \"2018-12-21 22:20\",\n", "                \"2018-12-21 22:25\",\n", "                \"2018-12-21 22:30\",\n", "                \"2018-12-21 22:35\",\n", "                \"2018-12-21 22:40\",\n", "                \"2018-12-21 22:45\",\n", "                \"2018-12-21 22:50\",\n", "                \"2018-12-21 22:55\",\n", "                \"2018-12-21 23:00\",\n", "                \"2018-12-21 23:05\",\n", "                \"2018-12-21 23:10\",\n", "                \"2018-12-21 23:15\",\n", "                \"2018-12-21 23:20\",\n", "                \"2018-12-21 23:25\",\n", "                \"2018-12-24 09:00\",\n", "                \"2018-12-24 09:05\",\n", "                \"2018-12-24 09:10\",\n", "                \"2018-12-24 09:15\",\n", "                \"2018-12-24 09:20\",\n", "                \"2018-12-24 09:25\",\n", "                \"2018-12-24 09:30\",\n", "                \"2018-12-24 09:35\",\n", "                \"2018-12-24 09:40\",\n", "                \"2018-12-24 09:45\",\n", "                \"2018-12-24 09:50\",\n", "                \"2018-12-24 09:55\",\n", "                \"2018-12-24 10:00\",\n", "                \"2018-12-24 10:05\",\n", "                \"2018-12-24 10:10\",\n", "                \"2018-12-24 10:30\",\n", "                \"2018-12-24 10:35\",\n", "                \"2018-12-24 10:40\",\n", "                \"2018-12-24 10:45\",\n", "                \"2018-12-24 10:50\",\n", "                \"2018-12-24 10:55\",\n", "                \"2018-12-24 11:00\",\n", "                \"2018-12-24 11:05\",\n", "                \"2018-12-24 11:10\",\n", "                \"2018-12-24 11:15\",\n", "                \"2018-12-24 11:20\",\n", "                \"2018-12-24 11:25\",\n", "                \"2018-12-24 13:30\",\n", "                \"2018-12-24 13:35\",\n", "                \"2018-12-24 13:40\",\n", "                \"2018-12-24 13:45\",\n", "                \"2018-12-24 13:50\",\n", "                \"2018-12-24 13:55\",\n", "                \"2018-12-24 14:00\",\n", "                \"2018-12-24 14:05\",\n", "                \"2018-12-24 14:10\",\n", "                \"2018-12-24 14:15\",\n", "                \"2018-12-24 14:20\",\n", "                \"2018-12-24 14:25\",\n", "                \"2018-12-24 14:30\",\n", "                \"2018-12-24 14:35\",\n", "                \"2018-12-24 14:40\",\n", "                \"2018-12-24 14:45\",\n", "                \"2018-12-24 14:50\",\n", "                \"2018-12-24 14:55\",\n", "                \"2018-12-24 21:00\",\n", "                \"2018-12-24 21:05\",\n", "                \"2018-12-24 21:10\",\n", "                \"2018-12-24 21:15\",\n", "                \"2018-12-24 21:20\",\n", "                \"2018-12-24 21:25\",\n", "                \"2018-12-24 21:30\",\n", "                \"2018-12-24 21:35\",\n", "                \"2018-12-24 21:40\",\n", "                \"2018-12-24 21:45\",\n", "                \"2018-12-24 21:50\",\n", "                \"2018-12-24 21:55\",\n", "                \"2018-12-24 22:00\",\n", "                \"2018-12-24 22:05\",\n", "                \"2018-12-24 22:10\",\n", "                \"2018-12-24 22:15\",\n", "                \"2018-12-24 22:20\",\n", "                \"2018-12-24 22:25\",\n", "                \"2018-12-24 22:30\",\n", "                \"2018-12-24 22:35\",\n", "                \"2018-12-24 22:40\",\n", "                \"2018-12-24 22:45\",\n", "                \"2018-12-24 22:50\",\n", "                \"2018-12-24 22:55\",\n", "                \"2018-12-24 23:00\",\n", "                \"2018-12-24 23:05\",\n", "                \"2018-12-24 23:10\",\n", "                \"2018-12-24 23:15\",\n", "                \"2018-12-24 23:20\",\n", "                \"2018-12-24 23:25\",\n", "                \"2018-12-25 09:00\",\n", "                \"2018-12-25 09:05\",\n", "                \"2018-12-25 09:10\",\n", "                \"2018-12-25 09:15\",\n", "                \"2018-12-25 09:20\",\n", "                \"2018-12-25 09:25\",\n", "                \"2018-12-25 09:30\",\n", "                \"2018-12-25 09:35\",\n", "                \"2018-12-25 09:40\",\n", "                \"2018-12-25 09:45\",\n", "                \"2018-12-25 09:50\",\n", "                \"2018-12-25 09:55\",\n", "                \"2018-12-25 10:00\",\n", "                \"2018-12-25 10:05\",\n", "                \"2018-12-25 10:10\",\n", "                \"2018-12-25 10:30\",\n", "                \"2018-12-25 10:35\",\n", "                \"2018-12-25 10:40\",\n", "                \"2018-12-25 10:45\",\n", "                \"2018-12-25 10:50\",\n", "                \"2018-12-25 10:55\",\n", "                \"2018-12-25 11:00\",\n", "                \"2018-12-25 11:05\",\n", "                \"2018-12-25 11:10\",\n", "                \"2018-12-25 11:15\",\n", "                \"2018-12-25 11:20\",\n", "                \"2018-12-25 11:25\",\n", "                \"2018-12-25 13:30\",\n", "                \"2018-12-25 13:35\",\n", "                \"2018-12-25 13:40\",\n", "                \"2018-12-25 13:45\",\n", "                \"2018-12-25 13:50\",\n", "                \"2018-12-25 13:55\",\n", "                \"2018-12-25 14:00\",\n", "                \"2018-12-25 14:05\",\n", "                \"2018-12-25 14:10\",\n", "                \"2018-12-25 14:15\",\n", "                \"2018-12-25 14:20\",\n", "                \"2018-12-25 14:25\",\n", "                \"2018-12-25 14:30\",\n", "                \"2018-12-25 14:35\",\n", "                \"2018-12-25 14:40\",\n", "                \"2018-12-25 14:45\",\n", "                \"2018-12-25 14:50\",\n", "                \"2018-12-25 14:55\",\n", "                \"2018-12-25 21:00\",\n", "                \"2018-12-25 21:05\",\n", "                \"2018-12-25 21:10\",\n", "                \"2018-12-25 21:15\",\n", "                \"2018-12-25 21:20\",\n", "                \"2018-12-25 21:25\",\n", "                \"2018-12-25 21:30\",\n", "                \"2018-12-25 21:35\",\n", "                \"2018-12-25 21:40\",\n", "                \"2018-12-25 21:45\",\n", "                \"2018-12-25 21:50\",\n", "                \"2018-12-25 21:55\",\n", "                \"2018-12-25 22:00\",\n", "                \"2018-12-25 22:05\",\n", "                \"2018-12-25 22:10\",\n", "                \"2018-12-25 22:15\",\n", "                \"2018-12-25 22:20\",\n", "                \"2018-12-25 22:25\",\n", "                \"2018-12-25 22:30\",\n", "                \"2018-12-25 22:35\",\n", "                \"2018-12-25 22:40\",\n", "                \"2018-12-25 22:45\",\n", "                \"2018-12-25 22:50\",\n", "                \"2018-12-25 22:55\",\n", "                \"2018-12-25 23:00\",\n", "                \"2018-12-25 23:05\",\n", "                \"2018-12-25 23:10\",\n", "                \"2018-12-25 23:15\",\n", "                \"2018-12-25 23:20\",\n", "                \"2018-12-25 23:25\",\n", "                \"2018-12-26 09:00\",\n", "                \"2018-12-26 09:05\",\n", "                \"2018-12-26 09:10\",\n", "                \"2018-12-26 09:15\",\n", "                \"2018-12-26 09:20\",\n", "                \"2018-12-26 09:25\",\n", "                \"2018-12-26 09:30\",\n", "                \"2018-12-26 09:35\",\n", "                \"2018-12-26 09:40\",\n", "                \"2018-12-26 09:45\",\n", "                \"2018-12-26 09:50\",\n", "                \"2018-12-26 09:55\",\n", "                \"2018-12-26 10:00\",\n", "                \"2018-12-26 10:05\",\n", "                \"2018-12-26 10:10\",\n", "                \"2018-12-26 10:30\"\n", "            ],\n", "            \"scale\": true,\n", "            \"gridIndex\": 2\n", "        }\n", "    ],\n", "    \"yAxis\": [\n", "        {\n", "            \"show\": true,\n", "            \"nameLocation\": \"middle\",\n", "            \"nameGap\": 25,\n", "            \"nameTextStyle\": {\n", "                \"fontSize\": 14\n", "            },\n", "            \"axisTick\": {\n", "                \"alignWithLabel\": false\n", "            },\n", "            \"inverse\": false,\n", "            \"boundaryGap\": true,\n", "            \"type\": \"value\",\n", "            \"splitLine\": {\n", "                \"show\": true\n", "            },\n", "            \"axisLine\": {\n", "                \"lineStyle\": {\n", "                    \"width\": 1\n", "                }\n", "            },\n", "            \"axisLabel\": {\n", "                \"interval\": \"auto\",\n", "                \"formatter\": \"{value} \",\n", "                \"rotate\": 0,\n", "                \"margin\": 8,\n", "                \"textStyle\": {\n", "                    \"fontSize\": 12\n", "                }\n", "            }\n", "        },\n", "        {\n", "            \"show\": true,\n", "            \"nameLocation\": \"middle\",\n", "            \"nameGap\": 25,\n", "            \"nameTextStyle\": {\n", "                \"fontSize\": 14\n", "            },\n", "            \"axisTick\": {\n", "                \"alignWithLabel\": false\n", "            },\n", "            \"inverse\": false,\n", "            \"boundaryGap\": true,\n", "            \"type\": \"value\",\n", "            \"splitLine\": {\n", "                \"show\": true\n", "            },\n", "            \"axisLine\": {\n", "                \"lineStyle\": {\n", "                    \"width\": 1\n", "                }\n", "            },\n", "            \"axisLabel\": {\n", "                \"interval\": \"auto\",\n", "                \"formatter\": \"{value} \",\n", "                \"rotate\": 0,\n", "                \"margin\": 8,\n", "                \"textStyle\": {\n", "                    \"fontSize\": 12\n", "                }\n", "            },\n", "            \"scale\": true,\n", "            \"splitArea\": {\n", "                \"show\": true\n", "            },\n", "            \"gridIndex\": 2\n", "        }\n", "    ],\n", "    \"color\": [\n", "        \"#c23531\",\n", "        \"#2f4554\",\n", "        \"#61a0a8\",\n", "        \"#d48265\",\n", "        \"#749f83\",\n", "        \"#ca8622\",\n", "        \"#bda29a\",\n", "        \"#6e7074\",\n", "        \"#546570\",\n", "        \"#c4ccd3\",\n", "        \"#f05b72\",\n", "        \"#ef5b9c\",\n", "        \"#f47920\",\n", "        \"#905a3d\",\n", "        \"#fab27b\",\n", "        \"#2a5caa\",\n", "        \"#444693\",\n", "        \"#726930\",\n", "        \"#b2d235\",\n", "        \"#6d8346\",\n", "        \"#ac6767\",\n", "        \"#1d953f\",\n", "        \"#6950a1\",\n", "        \"#918597\",\n", "        \"#f6f5ec\"\n", "    ],\n", "    \"dataZoom\": [\n", "        {\n", "            \"show\": true,\n", "            \"type\": \"slider\",\n", "            \"start\": 50,\n", "            \"end\": 100,\n", "            \"orient\": \"horizontal\",\n", "            \"xAxisIndex\": [\n", "                0,\n", "                1\n", "            ]\n", "        }\n", "    ],\n", "    \"grid\": [\n", "        {\n", "            \"top\": \"60%\"\n", "        },\n", "        {\n", "            \"top\": \"60%\"\n", "        },\n", "        {\n", "            \"bottom\": \"55%\"\n", "        }\n", "    ]\n", "};\n", "myChart_8140804aa45047b3a52dbfd1853e66c8.setOption(option_8140804aa45047b3a52dbfd1853e66c8);\n", "\n", "    });\n", "</script>\n"], "text/plain": ["<pyecharts.custom.grid.Grid at 0x219db3feb38>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["cnt=260\n", "symbol='MA8888.ZC'\n", "barsize=BarSize.min5\n", "hist=ds.get_history_data(symbol, cnt, [BarData.datetime,BarData.open,BarData.high,BarData.low,BarData.close], barsize)\n", "\n", "dt_str = []\n", "cnt=hist.shape[1]\n", "if cnt == 0:\n", "    print(cnt)\n", "\n", "for i in range(cnt):\n", "    dt_str.append(datetime.datetime.fromtimestamp(int(hist[0][i])).strftime('%Y-%m-%d %H:%M'))\n", "\n", "    # draw ATR and stddev\n", "period = 20\n", "atr = ta.ATR(hist[2], hist[3], hist[4], period)\n", "# atr = np.nan_to_num(atr)\n", "stddev = ta.STDDEV(hist[4], period)\n", "# stddev = np.nan_to_num(stddev)\n", "\n", "line = Line(\"Index ATR/STDDEV\", title_top=\"50%\")\n", "line.add(\"ATR\", dt_str, atr, mark_line=[\"average\"])\n", "line.add(\"stddev\", dt_str, stddev,\n", "         mark_point=[\"max\", \"min\"],\n", "         legend_top=\"50%\",\n", "         mark_line=[\"average\"],\n", "         is_datazoom_show=True,\n", "         datazoom_xaxis_index=[0, 1], )\n", "# line\n", "\n", "# draw kline\n", "bars = []\n", "for i in range(cnt):\n", "    bar = []\n", "    bar.append(hist[1][i])\n", "    bar.append(hist[4][i])\n", "    bar.append(hist[3][i])\n", "    bar.append(hist[2][i])\n", "    bars.append(bar)\n", "\n", "kline = Kline(\"K 线图 {} {}\".format(symbol, barsize))\n", "kline.add(\"{} {} KLine (bar count:{})\".format(symbol, barsize, cnt), dt_str, bars, mark_point=[\"max\"],\n", "          is_datazoom_show=True)\n", "# kline.show_config()\n", "#kline\n", "\n", "grid = Grid(width=980, height=700)\n", "grid.add(line, grid_top=\"60%\")\n", "grid.add(kline, grid_bottom=\"55%\")\n", "grid #.render()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python [default]", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.0"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 2}