# Copyright (c) Microsoft Corporation.
# Licensed under the MIT License.
# coding=utf-8

from __future__ import division
from __future__ import print_function

import numpy as np
import pandas as pd
from typing import Text, Union
import copy
from qlib.utils import get_or_create_path
from qlib.log import get_module_logger, set_log_with_config

import torch
import torch.nn as nn
import torch.optim as optim
# from torch.nn.utils import weight_norm
from torch.optim import lr_scheduler
import torch.nn.functional as F
from torch.utils.data import DataLoader, TensorDataset, random_split

# from .pytorch_utils import count_parameters
from qlib.model.base import Model
from qlib.data.dataset import DatasetH
from qlib.data.dataset.handler import DataHandlerLP
from pyqlab.const import ALL_FACTOR_NAMES, TWO_VAL_FACTOR_NAMES


LOGGING_CONFIG = {
    "version": 1,
    "formatters": {
        "default": {
            # 'format':'%(asctime)s %(filename)s %(lineno)s %(levelname)s %(message)s',
            # 'format':'%(asctime)s %(levelname)s %(message)s',
            'format':'%(levelname)s:%(name)s:%(message)s'
        },
        "plain": {
            "format": "%(message)s",
        },
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "level": "INFO",
            "formatter": "default",
        },
        "console_plain": {
            "class": "logging.StreamHandler",
            "level": "INFO",
            "formatter": "plain"
        },
        "file":{
            "class": "logging.FileHandler",
            "level":20,
            "filename": "./log.txt",
            "formatter": "default",
        }
    },
    "loggers": {
        "console_logger": {
            "handlers": ["console"],
            "level": "INFO",
            "propagate": False,
        },
        "console_plain_logger": {
            "handlers": ["console_plain"],
            "level": "DEBUG",
            "propagate": False,
        },
        "file_logger":{
            "handlers": ["file"],
            "level": "INFO",
            "propagate": False,
        }
    },
    "disable_existing_loggers": True,
}

class Conv1dModelPytorch(Model):
    """MLP Model

    Parameters
    ----------
    d_feat : int
        input dimension for each time step
    n_chans: int
        number of channels
    metric: str
        the evaluate metric used in early stop
    optimizer : str
        optimizer name
    GPU : str
        the GPU ID(s) used for training
    """

    def __init__(
        self,
        num_code=50,
        num_channel=5,
        num_input=95, #87
        layers=(96, 96, 64),
        dropout=0.5,
        n_epochs=200,
        lr=0.0001,
        lr_decay=0.1,
        lr_decay_steps=10,
        metric="",
        batch_size=64,
        early_stop=30,
        loss="mse",
        optimizer="adam",
        GPU=0,
        seed=None,
        best_cond="loss",
        **kwargs
    ):
        # Set logger.
        self.logger = get_module_logger("M")
        # set_log_with_config(LOGGING_CONFIG)
        self.logger.info("=======================================")

        # set hyper-parameters.
        self.num_code = num_code
        self.num_channel = num_channel
        self.num_input = num_input
        self.layers = layers
        self.dropout = dropout
        self.n_epochs = n_epochs
        self.lr = lr
        self.lr_decay = lr_decay
        self.lr_decay_steps = lr_decay_steps
        self.metric = metric
        self.batch_size = batch_size
        self.early_stop = early_stop
        self.optimizer = optimizer.lower()
        self.loss = loss
        self.device = torch.device("cuda:%d" % (GPU) if torch.cuda.is_available() and GPU >= 0 else "cpu")
        self.seed = seed
        self.best_cond = best_cond

        self.logger.info(
            "MLP parameters setting:"
            "\nnum_code : {}"
            "\nnum_input : {}"
            "\nlayers : {}"
            "\ndropout : {}"
            "\nn_epochs : {}"
            "\nlr : {}"
            "\nmetric : {}"
            "\nbatch_size : {}"
            "\nearly_stop : {}"
            "\noptimizer : {}"
            "\nloss_type : {}"
            "\nvisible_GPU : {}"
            "\nuse_GPU : {}"
            "\nseed : {}"
            "\nbest_cond : {}".format(
                num_code,
                num_input,
                layers,
                dropout,
                n_epochs,
                lr,
                metric,
                batch_size,
                early_stop,
                optimizer.lower(),
                loss,
                GPU,
                self.use_gpu,
                seed,
                best_cond,
            )
        )

        if self.seed is not None:
            np.random.seed(self.seed)
            torch.manual_seed(self.seed)

        # TODO: batch_norm drop_out
        # self.conv_model = TimeSeriesModel(
        self.conv_model = TimeSeriesModel2(
        # self.conv_model = Conv1DNet(
        # self.conv_model = ConvNet(
            num_code=self.num_code,
            num_input=self.num_input,
            num_channel=self.num_channel,
            # output_size=1,
            dropout=self.dropout,
            # layers=self.layers,
        )
        self.logger.info("model:\n{:}".format(self.conv_model))
        # self.logger.info("model size: {:.4f} MB".format(count_parameters(self.conv_model)))

        if optimizer.lower() == "adam":
            self.train_optimizer = optim.Adam(self.conv_model.parameters(), lr=self.lr)
        elif optimizer.lower() == "gd":
            self.train_optimizer = optim.SGD(self.conv_model.parameters(), lr=self.lr)
        else:
            raise NotImplementedError("optimizer {} is not supported!".format(optimizer))

        self.fitted = False
        self.conv_model.to(self.device)

    @property
    def use_gpu(self):
        return self.device != torch.device("cpu")

    def mse(self, pred, label):
        loss = (pred - label) ** 2
        return torch.mean(loss)

    def loss_fn(self, pred, label):
        # mask = ~torch.isnan(label)
        # if self.loss == "mse":
        #     return self.mse(pred[mask], label[mask])
        if self.loss == "mse":
            sqr_loss = torch.mul(pred - label, pred - label)
            loss = sqr_loss.mean()
            return loss
        elif self.loss == "binary":
            loss = nn.BCELoss()
            return loss(pred, label)
        raise ValueError("unknown loss `%s`" % self.loss)

    def accuracy(self, pred, label):
        if self.use_gpu:
            preds = (pred>0.5).type(torch.IntTensor).cuda()
        else:
            preds = (pred>0.5).type(torch.IntTensor)
        return (preds == label).float().mean()

   

    def loader(self, dataset, batch_size=32, shuffle=True):
        # 将数据集划分为训练集和验证集
        train_size = int(0.8 * len(dataset))
        val_size = len(dataset) - train_size
        train_dataset, val_dataset = random_split(dataset, [train_size, val_size])

        train_dataloader = DataLoader(train_dataset, batch_size=batch_size, shuffle=shuffle)
        val_dataloader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
        return train_dataloader, val_dataloader
    
    def train_epoch(self, dataloader):

        self.conv_model.train()
        running_loss = 0.0

        for inputs, targets, encodeds in dataloader:
            inputs, targets, encodeds = inputs.to(self.device), targets.to(self.device), encodeds.to(self.device)

            # print("inputs.shape: ", inputs.shape)
            self.train_optimizer.zero_grad()
            outputs = self.conv_model(encodeds, inputs)
            loss = self.loss_fn(outputs, targets.float())
            # l2_reg = self.conv_model.L2_regularization(weight_decay=0.01)
            # loss = loss + l2_reg
            loss.backward()
            nn.utils.clip_grad_value_(self.conv_model.parameters(), 3.0)
            self.train_optimizer.step()

            running_loss += loss.item()

        return running_loss / len(dataloader)


    def test_epoch(self, dataloader):

        self.conv_model.eval()
        losses = []
        correct = 0
        total = 0
        with torch.no_grad():
            for inputs, targets, encodeds in dataloader:
                inputs, targets, encodeds = inputs.to(self.device), targets.to(self.device), encodeds.to(self.device)

                outputs = self.conv_model(encodeds, inputs)
                loss = self.loss_fn(outputs, targets.float())
                losses.append(loss.item())
                correct += ((outputs>0.5).type(torch.IntTensor).to(self.device) == targets).sum().item()
                total += targets.size(0)

        epoch_acc = correct / total
        return np.mean(losses), epoch_acc # np.mean(accs)
    

    def fit(
        self,
        dataset,
        evals_result=dict(),
        save_path=None,
    ):
        x_data, y_data, encoded_data = dataset.prepare(
            ["train", "valid"],
            col_set=["feature", "label", "encoded"],
            data_key=DataHandlerLP.DK_L,
        )
        # print(len(x_data), len(y_data), len(encoded_data))
        dataset = TensorDataset(torch.tensor(x_data), torch.tensor(y_data), torch.tensor(encoded_data))

        train_dl, val_dl = self.loader(dataset, self.batch_size)

        save_path = get_or_create_path(save_path)
        stop_steps = 0
        train_loss = 0
        best_loss = np.inf
        best_acc = -np.inf
        best_epoch = 0
        evals_result["loss"] = {}
        evals_result["accuracy"] = {}
        evals_result["loss"]["train"] = []
        evals_result["loss"]["valid"] = []
        evals_result["accuracy"]["train"] = []
        evals_result["accuracy"]["valid"] = []

        # train
        self.logger.info("training...")
        self.fitted = True

        # 学习速率衰减设置
        exp_lr_scheduler = lr_scheduler.StepLR(self.train_optimizer,
                                               step_size=self.lr_decay_steps,
                                               gamma=self.lr_decay) # 按步数
        # exp_lr_scheduler = lr_scheduler.MultiStepLR(opt, milestones=[50, 100, 150], gamma=0.1) # 按里程碑
        # exp_lr_scheduler = lr_scheduler.ExponentialLR(opt, gamma=0.1) # 按系数每步

        for step in range(self.n_epochs):
            self.logger.info("Epoch %d:", step)
            # self.logger.info("training...")
            self.train_epoch(train_dl)
            exp_lr_scheduler.step()

            # self.logger.info("evaluating...")
            train_loss, train_acc = self.test_epoch(train_dl)
            val_loss, val_acc = self.test_epoch(val_dl)
            self.logger.info("loss: train %.3f, valid %.3f" % (train_loss, val_loss))
            self.logger.info("accuracy: train %.3f, valid %.3f" % (train_acc, val_acc))
            evals_result["loss"]["train"].append(train_loss)
            evals_result["loss"]["valid"].append(val_loss)
            evals_result["accuracy"]["train"].append(train_acc)
            evals_result["accuracy"]["valid"].append(val_acc)

            # TODO: best model cond.
            if self.best_cond == "loss":
                if val_loss < best_loss:
                    best_loss = val_loss

                    if val_acc > best_acc:
                        best_acc = val_acc

                    stop_steps = 0
                    best_epoch = step
                    best_param = copy.deepcopy(self.conv_model.state_dict())
                else:
                    stop_steps += 1
                    if stop_steps >= self.early_stop:
                        self.logger.info("early stop")
                        break
            else:
                if val_acc > best_acc:
                    best_acc = val_acc

                    if val_loss < best_loss:
                        best_loss = val_loss

                    stop_steps = 0
                    best_epoch = step
                    best_param = copy.deepcopy(self.conv_model.state_dict())
                else:
                    stop_steps += 1
                    if stop_steps >= self.early_stop:
                        self.logger.info("early stop")
                        break

        self.logger.info("best epoch: %d loss: %.6lf accuracy: %.3lf" % (best_epoch, best_loss, best_acc))
        self.conv_model.load_state_dict(best_param)
        # save model
        # torch.save(best_param, save_path)
        model = self.conv_model.cpu()
        model.eval() # 如果要使用，要调用eval()表明运行模式
        sm = torch.jit.script(model)
        sm.save(save_path)

        if self.use_gpu:
            torch.cuda.empty_cache()

        return best_epoch, best_loss, best_acc

    def predict(self, dataset: DatasetH, segment: Union[Text, slice] = "test"):
        """
        Predict the result of the given dataset.
        """
        if not self.fitted:
            raise ValueError("model is not fitted yet!")

        x_data, y_data, encoded_data = dataset.prepare(
            segment,
            col_set=["feature", "label", "encoded"],
            data_key=DataHandlerLP.DK_L,
        )
        # print(len(x_data), len(y_data), len(encoded_data))
        dataset = TensorDataset(torch.tensor(x_data), torch.tensor(y_data), torch.tensor(encoded_data))

        train_size = int(0.9 * len(dataset))
        test_size = len(dataset) - train_size
        train_dataset, test_dataset = random_split(dataset, [train_size, test_size])
        test_dataloader = DataLoader(test_dataset, batch_size=self.batch_size, shuffle=False)

        preds = []
        self.conv_model.eval()
        for inputs, targets, encodeds in test_dataloader:
            inputs, targets, encodeds = inputs.to(self.device), targets.to(self.device), encodeds.to(self.device)

            with torch.no_grad():
                outputs = self.conv_model(encodeds, inputs)
            preds.append(outputs.detach().cpu().numpy())

        return pd.Series(np.concatenate(preds))


class Conv1DNet(nn.Module):
    def __init__(self):
        super(Conv1DNet, self).__init__()
        self.conv1 = nn.Conv1d(5, 16, kernel_size=3, stride=1, padding=1)
        self.pool1 = nn.MaxPool1d(kernel_size=2)
        self.conv2 = nn.Conv1d(16, 32, kernel_size=3, stride=1, padding=1)
        self.pool2 = nn.MaxPool1d(kernel_size=2)
        self.fc1 = nn.Linear(32 * 11, 64)
        self.fc2 = nn.Linear(64, 1)

    def forward(self, x):
        x = F.relu(self.conv1(x))
        x = self.pool1(x)
        x = F.relu(self.conv2(x))
        x = self.pool2(x)
        x = x.view(-1, 32 * 11)
        x = F.relu(self.fc1(x))
        x = self.fc2(x)
        x = torch.sigmoid(x)
        return x.view(-1)


class TimeSeriesModel(nn.Module):
    """
    通常输入: 5x45矩阵,5个通道,每个通道45个数据,然后增加一个code embedding通道
    """
    def __init__(self, num_code=60, num_channel=5, num_input=45, dropout=0.5):
        super(TimeSeriesModel, self).__init__()
        self.code_embeddings = nn.Embedding(num_embeddings=num_code, embedding_dim=num_input)
        self.conv1 = nn.Conv1d(in_channels=num_channel+1, out_channels=32, kernel_size=3, stride=1, padding=1)
        self.pool1 = nn.AvgPool1d(kernel_size=2)
        self.conv2 = nn.Conv1d(in_channels=32, out_channels=64, kernel_size=3, stride=1, padding=1)
        self.pool2 = nn.AvgPool1d(kernel_size=2)
        self.flatten = nn.Flatten()
        self.fc1 = nn.Linear(in_features=(64*((num_input//2)//2)), out_features=64)
        self.drop = nn.Dropout(dropout)
        self.fc2 = nn.Linear(in_features=64, out_features=1)

    def forward(self, code_ids, x):
        embedded_code_ids = self.code_embeddings(code_ids)
        embedded_code_ids = torch.unsqueeze(embedded_code_ids, dim=1)
        x = torch.cat([x, embedded_code_ids], dim=1)
        x = self.conv1(x)
        x = nn.functional.relu(x)
        x = self.pool1(x)
        x = self.conv2(x)
        x = nn.functional.relu(x)
        x = self.pool2(x)
        x = self.flatten(x)
        x = self.fc1(x)
        x = nn.functional.relu(x)
        x = self.drop(x)
        x = self.fc2(x)
        x = torch.sigmoid(x)
        return x.view(-1)

    

class TimeSeriesModel2(nn.Module):
    """
    通常输入: 5x45矩阵,5个通道,每个通道45个数据,然后增加一个code embedding通道
    """
    def __init__(self, num_code=60, num_channel=5, num_input=45, dropout=0.5):
        super(TimeSeriesModel2, self).__init__()
        self.code_embeddings = nn.Embedding(num_embeddings=num_code, embedding_dim=num_input)
        self.conv1 = nn.Conv1d(in_channels=num_channel+1, out_channels=16, kernel_size=3, stride=1, padding=1)
        self.pool1 = nn.AvgPool1d(kernel_size=2)
        self.conv2 = nn.Conv1d(in_channels=16, out_channels=32, kernel_size=3, stride=1, padding=1)
        self.pool2 = nn.AvgPool1d(kernel_size=2)
        self.flatten = nn.Flatten()
        self.fc1 = nn.Linear(in_features=(32*((num_input//2)//2)), out_features=64)
        # self.fc1 = nn.Linear(in_features=(32*(num_input-4)), out_features=64)
        self.drop = nn.Dropout(dropout)
        self.fc2 = nn.Linear(in_features=64, out_features=1)

    def forward(self, code_ids, x):
        embedded_code_ids = self.code_embeddings(code_ids)
        embedded_code_ids = torch.unsqueeze(embedded_code_ids, dim=1)
        x = torch.cat([x, embedded_code_ids], dim=1)
        x = self.conv1(x)
        x = nn.functional.relu(x)
        x = self.pool1(x)
        x = self.conv2(x)
        x = nn.functional.relu(x)
        x = self.pool2(x)
        x = self.flatten(x)
        x = self.fc1(x)
        x = nn.functional.relu(x)
        x = self.drop(x)
        x = self.fc2(x)
        x = torch.sigmoid(x)
        return x.view(-1)

    
class ConvNet(nn.Module):
    def __init__(self, num_code=60, num_channel=5, num_input=45):
        super(ConvNet, self).__init__()
        self.embedding = nn.Embedding(num_code, num_channel)
        self.conv1d = nn.Conv1d(in_channels=num_channel+1, out_channels=64, kernel_size=3, padding=1)
        self.pool1d = nn.AvgPool1d(kernel_size=3, padding=1)
        self.fc = nn.Linear(64*num_input, 1)

    def forward(self, code_ids, x):
        embed = self.embedding(code_ids)
        embed = torch.unsqueeze(embed, dim=1).permute(0, 2, 1)
        print(embed.shape)
        print(x.shape)
        out = torch.cat([x, embed], dim=-1)
        out = out.permute(0, 2, 1)
        out = self.conv1d(out)
        out = F.relu(out)
        out = self.pool1d(out)
        out = out.view(out.size(0), -1)
        out = self.fc(out)
        out = torch.sigmoid(out)
        return out.view(-1)