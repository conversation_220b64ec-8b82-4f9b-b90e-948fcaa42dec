import logging
import random
import numpy as np
import gymnasium as gym
from gymnasium.spaces import Box, Discrete, Tuple

# from pyqlab.rl.data.tickdata_fut import PreprocessingPipeline
from pyqlab.rl.data.markdata_fut import MarkDataDataset

logger = logging.getLogger(__name__)

"""
- state的含义、设置,如account,price,technical indicator,
  具体看代码 https://github.com/AI4Finance-Foundation/FinRL/blob/master/finrl/finrl_meta/env_stock_trading/env_stocktrading.py
- action的含义,买入卖出,以及小额交易忽略
- reward function的设置(直接影响 能否训练出更加谨慎,回撤更小的智能体)交易滑点,
- turbulence达到阈值就强制卖出,等
- env reset 里的随机性对于强化学习训练的益处

金融强化学习算法与其他自动交易算法的区别如下：
1. 强化学习算法中的无模型算法 不需要对环境进行建模（也就是它不预测市场）。
   这与部分深度学习的交易算法对市场进行一定程度的预测不同。市场的预测一直是一个难题,而深度强化学习
   有机会在不对市场进行预测的情况下,直接学习交易策略。
2. 数据导向,增量学习。
   深度强化学习算法和其他深度学习算法一样,都是数据导向的。这与一些基于基本逻辑语句写就的交易算法明显不同。
   市场每时每刻都在产出大量数据,依靠人类经验总结出来的交易策略大家都在使用。而深度学习可以使用这些数据,
   提高交易策略的自动化程度。

"""

class MarketTimingEnv(gym.Env):
    """
    def __init__(self, 
        initial_amount=1e7,  # 初始本金
        max_stock=1e2,  # 最大交易额度,买入或卖出100个单位
        buy_cost_pct=1e-3,  # 交易损耗率设为 0.001
        sell_cost_pct=1e-3,  # 交易损耗率设为 0.001
        gamma=0.99,  # 强化学习的折扣比率,给人为设置的终止状态的reward进行补偿的时候会用到
        beg_idx=0,   # 使用数据集的这个位置之后的数据
        end_idx=1113  # 使用数据集的这个位置之前的数据
     ):

    """
    def __init__(self, env_config):
        self.md = MarkDataDataset(
            data_path=f"{env_config['data_path']}/{env_config['data_file']}",
            version=env_config["version"]
        )
        self.md.load()
        _, _, mk_data = self.md.getitem(0)

        self.initial_amount = env_config["initial_amount"]
        self.gamma = env_config["gamma"]

        # Environment information
        self.action_space = Discrete(3)
        self.state_dim = len(mk_data)
        self.observation_space = Box(low=-4.0, high=4.0, shape=(self.state_dim,), dtype=np.float32)
        self.max_step = self.md.len()

        # Initialize variables
        self.reset()

    def reset(self, *, seed=None, options=None):
        super().reset(seed=seed)
        self.cur_steps = 0
        self.amount = self.initial_amount
        self.rewards = []
        self.total_asset = self.amount

        _, _, mk_data = self.md.getitem(0)
        new_state = self.get_state(mk_data)
        return new_state, {}

    def get_state(self, data):
        return np.array(data, dtype=np.float32)

    def step(self, action):
        self.cur_steps += 1

        pos_side, pnl, mk_data = self.md.getitem(self.cur_steps)

        if (action == 1 and pos_side == "L") or (action == 2 and pos_side == "S"):
            self.amount += pnl

        state = self.get_state(mk_data)

        returns = (self.amount - self.total_asset) / self.total_asset
        reward = np.log1p(returns) * 100
        # print(f"returns: {returns}, reward: {reward}")

        # Apply small penalty for not trading
        if action == 0:
            reward -= 0.0001

        self.rewards.append(reward)
        self.total_asset = self.amount

        if self.total_asset <= 10000:
            reward = -1  # 设置最大损失为-100%
            done = True  # 结束当前回合
            print(f"*** Total asset: {self.total_asset}. Ending current episode. ***")
        else:
            done = self.cur_steps == self.max_step - 1

        if done:
            self._handle_episode_end(reward)
        elif self.cur_steps % 2000 == 0:
            self._log_progress(reward)

        return state, reward, done, False, {}

    def _handle_episode_end(self, reward):
        mean_reward = np.mean(self.rewards)
        final_reward = reward + 1 / (1 - self.gamma) * mean_reward
        self.cumulative_returns = self.total_asset / self.initial_amount
        print(f"=== Final reward: {final_reward:.6f}, cumulative_returns: {self.cumulative_returns:.6f} ===")

    def _log_progress(self, reward):
        print(f"Step: {self.cur_steps}/{self.max_step}, reward: {reward:.6f}, total_asset: {self.total_asset:.0f}")



