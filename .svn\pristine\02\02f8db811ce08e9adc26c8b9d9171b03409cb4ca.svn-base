{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# AICM\n", "- 功能：数据维护、盘面分析、策略开发\n", "- 反复模拟演练，场景恢复"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import datetime\n", "import talib as ta\n", "import pandas as pd\n", "import numpy as np\n", "import sys\n", "import matplotlib as mpl\n", "import matplotlib.pyplot as plt\n", "# from pyecharts import Kline,Line,Overlap,Grid,online\n", "# online()\n", "sys.path.append(\"d:/QuantTunnel\")\n", "from qtunnel import DataSource,Backtest,BarData,BarSize,DoRight,RunMode\n", "from qtunnel import DB,Mode,Cursor,Transaction,create_db,registered_dbs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 波动量化分析\n", "\n", "计算ATR指标和STDDEV指标，用于对单个合约的波动性量化分析。根据波动性特性，选择目标投资合约。\n", "为了合约间方便比较，用指标与价格间的比率做统计分析处理。"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def atr_stats(ds, barsize):\n", "    '''\n", "    \n", "    '''\n", "    col_atr = []\n", "    col_stddev = []\n", "    col_atr_ratio = []\n", "    col_stddev_ratio = []\n", "    col_stddev_atr_ratio = []\n", "    exist_codes = []\n", "    \n", "    fut_codes = ds.get_block_data(\"ZLLX\") #ZLQH\n", "    for symbol in fut_codes:\n", "        real_hist = ds.get_history_data(symbol, 260, [BarData.high, BarData.low, BarData.close], barsize)\n", "        if real_hist is None:\n", "            print(\"{} history bars is None.\".format(symbol))\n", "            continue\n", "        exist_codes.append(symbol)\n", "        real_atr = ta.ATR(real_hist[0], real_hist[1], real_hist[2], period)\n", "        real_stddev = ta.STDDEV(real_hist[2], period)\n", "        real_ma = ta.SMA(real_hist[2], period)\n", "        col_atr.append(real_atr[-1])\n", "        col_stddev.append(real_stddev[-1])\n", "        col_atr_ratio.append(col_atr[-1] / real_ma[-1])\n", "        col_stddev_ratio.append(col_stddev[-1] / real_ma[-1])\n", "        col_stddev_atr_ratio.append(col_stddev[-1] / col_atr[-1])\n", "\n", "    df = pd.DataFrame(\n", "        {\"ATR\": col_atr, \"ATR Ratio\": col_atr_ratio, \"stddev\": col_stddev, \"STDDEV Ratio\": col_stddev_ratio,\n", "         \"STDDEV ATR Ratio\": col_stddev_atr_ratio},\n", "        index=exist_codes\n", "    )\n", "    df = df.sort_values(by=\"STDDEV ATR Ratio\", ascending=False)\n", "    # print(df.describe())\n", "    # df.to_excel(r\".\\store\\fut_atr_rank_{0}_{1}_{2}.xlsx\".format(frequency, atr_bar_count, datetime.datetime.now().strftime(\"%Y%m%d\")), \"Sheet1\")\n", "    return df\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ds=DataSource(RunMode.passive)\n", "df=atr_stats(ds, BarSize.day)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3.7.4 64-bit ('base': conda)", "language": "python", "name": "python37464bitbaseconda785f576371554898b0cb22a5a77815ec"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4-final"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 2}