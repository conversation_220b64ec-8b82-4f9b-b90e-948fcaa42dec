{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import optuna\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def get_hp_result(name, db_path):\n", "    # accuracy:max  loss:min\n", "    study = optuna.create_study(study_name=name, direction='maximize', storage=db_path, load_if_exists=True)\n", "    df = study.trials_dataframe(attrs=('number', 'value', 'params', 'state'))\n", "    # print(df)\n", "    return study.best_value, study.best_params\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["names=[\"MLP\", \"GBDT\"]\n", "dbs=[\n", "    # \"sqlite:///db_gbdt_ALL_short.sqlite3\",\n", "    \"sqlite:///db_MLP_MIX_long.sqlite3\",\n", "    \"sqlite:///db_MLP_MIX_short.sqlite3\",\n", "    \"sqlite:///db_MLP_5R_long.sqlite3\",\n", "    \"sqlite:///db_MLP_5R_short.sqlite3\",\n", "    \"sqlite:///db_MLP_7R_long.sqlite3\",\n", "    \"sqlite:///db_MLP_7R_short.sqlite3\",\n", "    \"sqlite:///db_MLP_ALL_long.sqlite3\",\n", "    \"sqlite:///db_MLP_ALL_short.sqlite3\",\n", "]\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m[I 2022-01-26 18:38:46,044]\u001b[0m Using an existing study with name 'MLP' instead of creating a new one.\u001b[0m\n", "\u001b[32m[I 2022-01-26 18:38:46,549]\u001b[0m Using an existing study with name 'MLP' instead of creating a new one.\u001b[0m\n", "\u001b[32m[I 2022-01-26 18:38:46,633]\u001b[0m Using an existing study with name 'MLP' instead of creating a new one.\u001b[0m\n", "\u001b[32m[I 2022-01-26 18:38:46,735]\u001b[0m Using an existing study with name 'MLP' instead of creating a new one.\u001b[0m\n", "\u001b[32m[I 2022-01-26 18:38:46,828]\u001b[0m Using an existing study with name 'MLP' instead of creating a new one.\u001b[0m\n", "\u001b[32m[I 2022-01-26 18:38:46,912]\u001b[0m Using an existing study with name 'MLP' instead of creating a new one.\u001b[0m\n", "\u001b[32m[I 2022-01-26 18:38:46,996]\u001b[0m Using an existing study with name 'MLP' instead of creating a new one.\u001b[0m\n", "\u001b[32m[I 2022-01-26 18:38:47,086]\u001b[0m Using an existing study with name 'MLP' instead of creating a new one.\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["               best_value  dropout       lr\n", "model_name                                 \n", "MLP_MIX_long     0.729167     0.06  0.00092\n", "MLP_MIX_short    0.671875     0.07  0.00026\n", "MLP_5R_long      0.597656     0.37  0.00016\n", "MLP_5R_short     0.605469     0.09  0.00040\n", "MLP_7R_long      0.585938     0.20  0.00064\n", "MLP_7R_short     0.599609     0.09  0.00078\n", "MLP_ALL_long     0.622768     0.10  0.00072\n", "MLP_ALL_short    0.659598     0.06  0.00058\n"]}], "source": ["data=[]\n", "for db in dbs:\n", "    bv, bp = get_hp_result(\"MLP\", db)\n", "    data.append([db[13:db.find('.')], bv, bp['dropout'], bp['lr']])\n", "df=pd.DataFrame(data, columns=['model_name', 'best_value', 'dropout', 'lr'])\n", "df=df.set_index(\"model_name\")\n", "df.to_csv(\"../data/HP_MLP.csv\")\n", "print(df)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.0009200000000000001\n"]}], "source": ["print(df.loc[\"MLP_MIX_long\", \"lr\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "af09bc94d41e018aa4bb791c06386d7d2a0d085b02fa573368449120a8cb3c6e"}, "kernelspec": {"display_name": "Python 3.8.8 64-bit ('base': conda)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}