{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["99 3338.2445037866073\n", "199 2225.6381079794837\n", "299 1485.4731860126194\n", "399 992.8866711506726\n", "499 664.9330020481934\n", "599 446.4949879862836\n", "699 300.9357193112604\n", "799 203.8941142143419\n", "899 139.16592768824862\n", "999 95.96853666016455\n", "1099 67.12411855809438\n", "1199 47.85250331448381\n", "1299 34.96886021602115\n", "1399 26.35027650415804\n", "1499 20.58098607729074\n", "1599 16.716328839824758\n", "1699 14.12564405381743\n", "1799 12.387658391364187\n", "1899 11.220798113360962\n", "1999 10.436744852305855\n", "Result: y = -0.022706447808146627 + 0.8236413214852779 x + 0.003917240083827723 x^2 + -0.08862227032780369 x^3\n"]}], "source": ["import numpy as np\n", "import math\n", "\n", "# Create random input and output data\n", "x = np.linspace(-math.pi, math.pi, 2000)\n", "y = np.sin(x)\n", "\n", "# Randomly initialize weights\n", "a = np.random.randn()\n", "b = np.random.randn()\n", "c = np.random.randn()\n", "d = np.random.randn()\n", "\n", "learning_rate = 1e-6\n", "for t in range(2000):\n", "    # Forward pass: compute predicted y\n", "    # y = a + b x + c x^2 + d x^3\n", "    y_pred = a + b * x + c * x ** 2 + d * x ** 3\n", "\n", "    # Compute and print loss\n", "    loss = np.square(y_pred - y).sum()\n", "    if t % 100 == 99:\n", "        print(t, loss)\n", "\n", "    # Backprop to compute gradients of a, b, c, d with respect to loss\n", "    grad_y_pred = 2.0 * (y_pred - y)\n", "    grad_a = grad_y_pred.sum()\n", "    grad_b = (grad_y_pred * x).sum()\n", "    grad_c = (grad_y_pred * x ** 2).sum()\n", "    grad_d = (grad_y_pred * x ** 3).sum()\n", "\n", "    # Update weights\n", "    a -= learning_rate * grad_a\n", "    b -= learning_rate * grad_b\n", "    c -= learning_rate * grad_c\n", "    d -= learning_rate * grad_d\n", "\n", "print(f'Result: y = {a} + {b} x + {c} x^2 + {d} x^3')"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from skimpy import skim, generate_test_data"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>length</th>\n", "      <th>width</th>\n", "      <th>depth</th>\n", "      <th>rnd</th>\n", "      <th>class</th>\n", "      <th>location</th>\n", "      <th>booly_col</th>\n", "      <th>text</th>\n", "      <th>date</th>\n", "      <th>date_no_freq</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.762796</td>\n", "      <td>1.468082</td>\n", "      <td>9</td>\n", "      <td>-0.423534</td>\n", "      <td>virtginica</td>\n", "      <td>UK</td>\n", "      <td>False</td>\n", "      <td>What weather!</td>\n", "      <td>2018-01-31</td>\n", "      <td>NaT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.031203</td>\n", "      <td>0.267769</td>\n", "      <td>10</td>\n", "      <td>2.102890</td>\n", "      <td>virtginica</td>\n", "      <td>UK</td>\n", "      <td>False</td>\n", "      <td>How are you?</td>\n", "      <td>2018-02-28</td>\n", "      <td>1992-01-05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0.044075</td>\n", "      <td>3.571043</td>\n", "      <td>12</td>\n", "      <td>0.147606</td>\n", "      <td>setosa</td>\n", "      <td>UK</td>\n", "      <td>True</td>\n", "      <td>How are you?</td>\n", "      <td>2018-03-31</td>\n", "      <td>2022-01-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0.914088</td>\n", "      <td>2.838664</td>\n", "      <td>15</td>\n", "      <td>-0.997567</td>\n", "      <td>virtginica</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>2018-04-30</td>\n", "      <td>NaT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0.555878</td>\n", "      <td>2.214629</td>\n", "      <td>5</td>\n", "      <td>0.329828</td>\n", "      <td>setosa</td>\n", "      <td>UK</td>\n", "      <td>False</td>\n", "      <td>How are you?</td>\n", "      <td>2018-05-31</td>\n", "      <td>2022-01-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>995</th>\n", "      <td>0.999685</td>\n", "      <td>3.941814</td>\n", "      <td>9</td>\n", "      <td>-1.602732</td>\n", "      <td>virtginica</td>\n", "      <td>UK</td>\n", "      <td>True</td>\n", "      <td>How are you?</td>\n", "      <td>2100-12-31</td>\n", "      <td>2022-01-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>996</th>\n", "      <td>0.741045</td>\n", "      <td>1.894116</td>\n", "      <td>8</td>\n", "      <td>0.617328</td>\n", "      <td>setosa</td>\n", "      <td>UK</td>\n", "      <td>False</td>\n", "      <td>Indeed, it was the most outrageously pompous c...</td>\n", "      <td>2101-01-31</td>\n", "      <td>2023-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>997</th>\n", "      <td>0.079299</td>\n", "      <td>1.183011</td>\n", "      <td>9</td>\n", "      <td>NaN</td>\n", "      <td>virtginica</td>\n", "      <td>Mexico</td>\n", "      <td>True</td>\n", "      <td>How are you?</td>\n", "      <td>2101-02-28</td>\n", "      <td>2022-01-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>998</th>\n", "      <td>0.997800</td>\n", "      <td>0.452186</td>\n", "      <td>11</td>\n", "      <td>0.698113</td>\n", "      <td>virtginica</td>\n", "      <td>UK</td>\n", "      <td>False</td>\n", "      <td>Indeed, it was the most outrageously pompous c...</td>\n", "      <td>2101-03-31</td>\n", "      <td>2022-01-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>999</th>\n", "      <td>0.860041</td>\n", "      <td>0.478546</td>\n", "      <td>10</td>\n", "      <td>-0.983313</td>\n", "      <td>virtginica</td>\n", "      <td>UK</td>\n", "      <td>True</td>\n", "      <td>What weather!</td>\n", "      <td>2101-04-30</td>\n", "      <td>2022-01-01</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1000 rows × 10 columns</p>\n", "</div>"], "text/plain": ["       length     width  depth       rnd       class location  booly_col  \\\n", "0    0.762796  1.468082      9 -0.423534  virtginica       UK      False   \n", "1    0.031203  0.267769     10  2.102890  virtginica       UK      False   \n", "2    0.044075  3.571043     12  0.147606      setosa       UK       True   \n", "3    0.914088  2.838664     15 -0.997567  virtginica      NaN       True   \n", "4    0.555878  2.214629      5  0.329828      setosa       UK      False   \n", "..        ...       ...    ...       ...         ...      ...        ...   \n", "995  0.999685  3.941814      9 -1.602732  virtginica       UK       True   \n", "996  0.741045  1.894116      8  0.617328      setosa       UK      False   \n", "997  0.079299  1.183011      9       NaN  virtginica   Mexico       True   \n", "998  0.997800  0.452186     11  0.698113  virtginica       UK      False   \n", "999  0.860041  0.478546     10 -0.983313  virtginica       UK       True   \n", "\n", "                                                  text       date date_no_freq  \n", "0                                        What weather! 2018-01-31          NaT  \n", "1                                         How are you? 2018-02-28   1992-01-05  \n", "2                                         How are you? 2018-03-31   2022-01-01  \n", "3                                                 <NA> 2018-04-30          NaT  \n", "4                                         How are you? 2018-05-31   2022-01-01  \n", "..                                                 ...        ...          ...  \n", "995                                       How are you? 2100-12-31   2022-01-01  \n", "996  Indeed, it was the most outrageously pompous c... 2101-01-31   2023-03-04  \n", "997                                       How are you? 2101-02-28   2022-01-01  \n", "998  Indeed, it was the most outrageously pompous c... 2101-03-31   2022-01-01  \n", "999                                      What weather! 2101-04-30   2022-01-01  \n", "\n", "[1000 rows x 10 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df = generate_test_data()\n", "df"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">╭───────────────────────────────────── skimpy summary ──────────────────────────────────────╮\n", "│ <span style=\"font-style: italic\">         Data Summary         </span> <span style=\"font-style: italic\">      Data Types       </span> <span style=\"font-style: italic\">       Categories        </span>          │\n", "│ ┏━━━━━━━━━━━━━━━━━━━┳━━━━━━━━┓ ┏━━━━━━━━━━━━━┳━━━━━━━┓ ┏━━━━━━━━━━━━━━━━━━━━━━━┓          │\n", "│ ┃<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\"> dataframe         </span>┃<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\"> Values </span>┃ ┃<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\"> Column Type </span>┃<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\"> Count </span>┃ ┃<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\"> Categorical Variables </span>┃          │\n", "│ ┡━━━━━━━━━━━━━━━━━━━╇━━━━━━━━┩ ┡━━━━━━━━━━━━━╇━━━━━━━┩ ┡━━━━━━━━━━━━━━━━━━━━━━━┩          │\n", "│ │ Number of rows    │ 1000   │ │ float64     │ 3     │ │ class                 │          │\n", "│ │ Number of columns │ 10     │ │ category    │ 2     │ │ location              │          │\n", "│ └───────────────────┴────────┘ │ datetime64  │ 2     │ └───────────────────────┘          │\n", "│                                │ int64       │ 1     │                                    │\n", "│                                │ bool        │ 1     │                                    │\n", "│                                │ string      │ 1     │                                    │\n", "│                                └─────────────┴───────┘                                    │\n", "│ <span style=\"font-style: italic\">                                         number                                         </span>  │\n", "│ ┏━━━━━━━━┳━━━━━━━━━┳━━━━━━━━━━━┳━━━━━━━┳━━━━━━┳━━━━━━━━━┳━━━━━━━┳━━━━━━┳━━━━━━┳━━━━━━━━┓  │\n", "│ ┃<span style=\"font-weight: bold\">        </span>┃<span style=\"font-weight: bold\"> missing </span>┃<span style=\"font-weight: bold\"> complete  </span>┃<span style=\"font-weight: bold\"> mean  </span>┃<span style=\"font-weight: bold\"> sd   </span>┃<span style=\"font-weight: bold\"> p0      </span>┃<span style=\"font-weight: bold\"> p25   </span>┃<span style=\"font-weight: bold\"> p75  </span>┃<span style=\"font-weight: bold\"> p100 </span>┃<span style=\"font-weight: bold\"> hist   </span>┃  │\n", "│ ┃        ┃         ┃<span style=\"font-weight: bold\"> rate      </span>┃       ┃      ┃         ┃       ┃      ┃      ┃        ┃  │\n", "│ ┡━━━━━━━━╇━━━━━━━━━╇━━━━━━━━━━━╇━━━━━━━╇━━━━━━╇━━━━━━━━━╇━━━━━━━╇━━━━━━╇━━━━━━╇━━━━━━━━┩  │\n", "│ │ <span style=\"color: #af87ff; text-decoration-color: #af87ff\">length</span> │ <span style=\"color: #008080; text-decoration-color: #008080\">      0</span> │ <span style=\"color: #008080; text-decoration-color: #008080\">        1</span> │ <span style=\"color: #008080; text-decoration-color: #008080\">  0.5</span> │ <span style=\"color: #008080; text-decoration-color: #008080\">0.36</span> │ <span style=\"color: #008080; text-decoration-color: #008080\">1.6e-06</span> │ <span style=\"color: #008080; text-decoration-color: #008080\"> 0.13</span> │ <span style=\"color: #008080; text-decoration-color: #008080\">0.86</span> │ <span style=\"color: #008080; text-decoration-color: #008080\">   1</span> │ <span style=\"color: #008000; text-decoration-color: #008000\">█▃▃▃▄█</span> │  │\n", "│ │ <span style=\"color: #af87ff; text-decoration-color: #af87ff\">width </span> │ <span style=\"color: #008080; text-decoration-color: #008080\">      0</span> │ <span style=\"color: #008080; text-decoration-color: #008080\">        1</span> │ <span style=\"color: #008080; text-decoration-color: #008080\">    2</span> │ <span style=\"color: #008080; text-decoration-color: #008080\"> 1.9</span> │ <span style=\"color: #008080; text-decoration-color: #008080\"> 0.0021</span> │ <span style=\"color: #008080; text-decoration-color: #008080\">  0.6</span> │ <span style=\"color: #008080; text-decoration-color: #008080\">   3</span> │ <span style=\"color: #008080; text-decoration-color: #008080\">  14</span> │ <span style=\"color: #008000; text-decoration-color: #008000\"> █▃▁  </span> │  │\n", "│ │ <span style=\"color: #af87ff; text-decoration-color: #af87ff\">depth </span> │ <span style=\"color: #008080; text-decoration-color: #008080\">      0</span> │ <span style=\"color: #008080; text-decoration-color: #008080\">        1</span> │ <span style=\"color: #008080; text-decoration-color: #008080\">   10</span> │ <span style=\"color: #008080; text-decoration-color: #008080\"> 3.2</span> │ <span style=\"color: #008080; text-decoration-color: #008080\">      2</span> │ <span style=\"color: #008080; text-decoration-color: #008080\">    8</span> │ <span style=\"color: #008080; text-decoration-color: #008080\">  12</span> │ <span style=\"color: #008080; text-decoration-color: #008080\">  20</span> │ <span style=\"color: #008000; text-decoration-color: #008000\">▁▄█▆▃▁</span> │  │\n", "│ │ <span style=\"color: #af87ff; text-decoration-color: #af87ff\">rnd   </span> │ <span style=\"color: #008080; text-decoration-color: #008080\">    120</span> │ <span style=\"color: #008080; text-decoration-color: #008080\">     0.88</span> │ <span style=\"color: #008080; text-decoration-color: #008080\">-0.02</span> │ <span style=\"color: #008080; text-decoration-color: #008080\">   1</span> │ <span style=\"color: #008080; text-decoration-color: #008080\">   -2.8</span> │ <span style=\"color: #008080; text-decoration-color: #008080\">-0.74</span> │ <span style=\"color: #008080; text-decoration-color: #008080\">0.66</span> │ <span style=\"color: #008080; text-decoration-color: #008080\"> 3.7</span> │ <span style=\"color: #008000; text-decoration-color: #008000\">▁▄█▅▁ </span> │  │\n", "│ └────────┴─────────┴───────────┴───────┴──────┴─────────┴───────┴──────┴──────┴────────┘  │\n", "│ <span style=\"font-style: italic\">                                        category                                        </span>  │\n", "│ ┏━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━┳━━━━━━━━━━━━┓  │\n", "│ ┃<span style=\"font-weight: bold\">                 </span>┃<span style=\"font-weight: bold\"> missing       </span>┃<span style=\"font-weight: bold\"> complete rate          </span>┃<span style=\"font-weight: bold\"> ordered      </span>┃<span style=\"font-weight: bold\"> unique     </span>┃  │\n", "│ ┡━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━╇━━━━━━━━━━━━┩  │\n", "│ │ <span style=\"color: #af87ff; text-decoration-color: #af87ff\">class          </span> │ <span style=\"color: #008080; text-decoration-color: #008080\">            0</span> │ <span style=\"color: #008080; text-decoration-color: #008080\">                     1</span> │ <span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">False       </span> │ <span style=\"color: #008080; text-decoration-color: #008080\">         2</span> │  │\n", "│ │ <span style=\"color: #af87ff; text-decoration-color: #af87ff\">location       </span> │ <span style=\"color: #008080; text-decoration-color: #008080\">            1</span> │ <span style=\"color: #008080; text-decoration-color: #008080\">                     1</span> │ <span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">False       </span> │ <span style=\"color: #008080; text-decoration-color: #008080\">         5</span> │  │\n", "│ └─────────────────┴───────────────┴────────────────────────┴──────────────┴────────────┘  │\n", "│ <span style=\"font-style: italic\">                                        datetime                                        </span>  │\n", "│ ┏━━━━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━━┓  │\n", "│ ┃<span style=\"font-weight: bold\">                </span>┃<span style=\"font-weight: bold\"> missing  </span>┃<span style=\"font-weight: bold\"> complete rate   </span>┃<span style=\"font-weight: bold\"> first        </span>┃<span style=\"font-weight: bold\"> last        </span>┃<span style=\"font-weight: bold\"> frequency </span>┃  │\n", "│ ┡━━━━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━━┩  │\n", "│ │ <span style=\"color: #af87ff; text-decoration-color: #af87ff\">date          </span> │ <span style=\"color: #008080; text-decoration-color: #008080\">       0</span> │ <span style=\"color: #008080; text-decoration-color: #008080\">              1</span> │ <span style=\"color: #800000; text-decoration-color: #800000\"> 2018-01-31 </span> │ <span style=\"color: #800000; text-decoration-color: #800000\">2101-04-30 </span> │ <span style=\"color: #af87ff; text-decoration-color: #af87ff\">M        </span> │  │\n", "│ │ <span style=\"color: #af87ff; text-decoration-color: #af87ff\">date_no_freq  </span> │ <span style=\"color: #008080; text-decoration-color: #008080\">       3</span> │ <span style=\"color: #008080; text-decoration-color: #008080\">              1</span> │ <span style=\"color: #800000; text-decoration-color: #800000\"> 1992-01-05 </span> │ <span style=\"color: #800000; text-decoration-color: #800000\">2023-03-04 </span> │ <span style=\"color: #af87ff; text-decoration-color: #af87ff\">None     </span> │  │\n", "│ └────────────────┴──────────┴─────────────────┴──────────────┴─────────────┴───────────┘  │\n", "│ <span style=\"font-style: italic\">                                         string                                         </span>  │\n", "│ ┏━━━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━┓  │\n", "│ ┃<span style=\"font-weight: bold\">         </span>┃<span style=\"font-weight: bold\"> missing     </span>┃<span style=\"font-weight: bold\"> complete rate       </span>┃<span style=\"font-weight: bold\"> words per row       </span>┃<span style=\"font-weight: bold\"> total words      </span>┃  │\n", "│ ┡━━━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━┩  │\n", "│ │ <span style=\"color: #af87ff; text-decoration-color: #af87ff\">text   </span> │ <span style=\"color: #008080; text-decoration-color: #008080\">          6</span> │ <span style=\"color: #008080; text-decoration-color: #008080\">               0.99</span> │ <span style=\"color: #008080; text-decoration-color: #008080\">                5.8</span> │ <span style=\"color: #008080; text-decoration-color: #008080\">            5800</span> │  │\n", "│ └─────────┴─────────────┴─────────────────────┴─────────────────────┴──────────────────┘  │\n", "│ <span style=\"font-style: italic\">                                          bool                                          </span>  │\n", "│ ┏━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━┓  │\n", "│ ┃<span style=\"font-weight: bold\">                          </span>┃<span style=\"font-weight: bold\"> true         </span>┃<span style=\"font-weight: bold\"> true rate               </span>┃<span style=\"font-weight: bold\"> hist             </span>┃  │\n", "│ ┡━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━┩  │\n", "│ │ <span style=\"color: #af87ff; text-decoration-color: #af87ff\">booly_col               </span> │ <span style=\"color: #008080; text-decoration-color: #008080\">         520</span> │ <span style=\"color: #008080; text-decoration-color: #008080\">                   0.52</span> │ <span style=\"color: #008000; text-decoration-color: #008000\">     █    █     </span> │  │\n", "│ └──────────────────────────┴──────────────┴─────────────────────────┴──────────────────┘  │\n", "╰─────────────────────────────────────────── End ───────────────────────────────────────────╯\n", "</pre>\n"], "text/plain": ["╭───────────────────────────────────── skimpy summary ──────────────────────────────────────╮\n", "│ \u001b[3m         Data Summary         \u001b[0m \u001b[3m      Data Types       \u001b[0m \u001b[3m       Categories        \u001b[0m          │\n", "│ ┏━━━━━━━━━━━━━━━━━━━┳━━━━━━━━┓ ┏━━━━━━━━━━━━━┳━━━━━━━┓ ┏━━━━━━━━━━━━━━━━━━━━━━━┓          │\n", "│ ┃\u001b[1;36m \u001b[0m\u001b[1;36mdataframe        \u001b[0m\u001b[1;36m \u001b[0m┃\u001b[1;36m \u001b[0m\u001b[1;36mValues\u001b[0m\u001b[1;36m \u001b[0m┃ ┃\u001b[1;36m \u001b[0m\u001b[1;36mColumn Type\u001b[0m\u001b[1;36m \u001b[0m┃\u001b[1;36m \u001b[0m\u001b[1;36mCount\u001b[0m\u001b[1;36m \u001b[0m┃ ┃\u001b[1;36m \u001b[0m\u001b[1;36mCategorical Variables\u001b[0m\u001b[1;36m \u001b[0m┃          │\n", "│ ┡━━━━━━━━━━━━━━━━━━━╇━━━━━━━━┩ ┡━━━━━━━━━━━━━╇━━━━━━━┩ ┡━━━━━━━━━━━━━━━━━━━━━━━┩          │\n", "│ │ Number of rows    │ 1000   │ │ float64     │ 3     │ │ class                 │          │\n", "│ │ Number of columns │ 10     │ │ category    │ 2     │ │ location              │          │\n", "│ └───────────────────┴────────┘ │ datetime64  │ 2     │ └───────────────────────┘          │\n", "│                                │ int64       │ 1     │                                    │\n", "│                                │ bool        │ 1     │                                    │\n", "│                                │ string      │ 1     │                                    │\n", "│                                └─────────────┴───────┘                                    │\n", "│ \u001b[3m                                         number                                         \u001b[0m  │\n", "│ ┏━━━━━━━━┳━━━━━━━━━┳━━━━━━━━━━━┳━━━━━━━┳━━━━━━┳━━━━━━━━━┳━━━━━━━┳━━━━━━┳━━━━━━┳━━━━━━━━┓  │\n", "│ ┃\u001b[1m \u001b[0m\u001b[1m      \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mmissing\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mcomplete \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mmean \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1msd  \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mp0     \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mp25  \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mp75 \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mp100\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mhist  \u001b[0m\u001b[1m \u001b[0m┃  │\n", "│ ┃        ┃         ┃\u001b[1m \u001b[0m\u001b[1mrate     \u001b[0m\u001b[1m \u001b[0m┃       ┃      ┃         ┃       ┃      ┃      ┃        ┃  │\n", "│ ┡━━━━━━━━╇━━━━━━━━━╇━━━━━━━━━━━╇━━━━━━━╇━━━━━━╇━━━━━━━━━╇━━━━━━━╇━━━━━━╇━━━━━━╇━━━━━━━━┩  │\n", "│ │ \u001b[38;5;141mlength\u001b[0m │ \u001b[36m      0\u001b[0m │ \u001b[36m        1\u001b[0m │ \u001b[36m  0.5\u001b[0m │ \u001b[36m0.36\u001b[0m │ \u001b[36m1.6e-06\u001b[0m │ \u001b[36m 0.13\u001b[0m │ \u001b[36m0.86\u001b[0m │ \u001b[36m   1\u001b[0m │ \u001b[32m█▃▃▃▄█\u001b[0m │  │\n", "│ │ \u001b[38;5;141mwidth \u001b[0m │ \u001b[36m      0\u001b[0m │ \u001b[36m        1\u001b[0m │ \u001b[36m    2\u001b[0m │ \u001b[36m 1.9\u001b[0m │ \u001b[36m 0.0021\u001b[0m │ \u001b[36m  0.6\u001b[0m │ \u001b[36m   3\u001b[0m │ \u001b[36m  14\u001b[0m │ \u001b[32m █▃▁  \u001b[0m │  │\n", "│ │ \u001b[38;5;141mdepth \u001b[0m │ \u001b[36m      0\u001b[0m │ \u001b[36m        1\u001b[0m │ \u001b[36m   10\u001b[0m │ \u001b[36m 3.2\u001b[0m │ \u001b[36m      2\u001b[0m │ \u001b[36m    8\u001b[0m │ \u001b[36m  12\u001b[0m │ \u001b[36m  20\u001b[0m │ \u001b[32m▁▄█▆▃▁\u001b[0m │  │\n", "│ │ \u001b[38;5;141mrnd   \u001b[0m │ \u001b[36m    120\u001b[0m │ \u001b[36m     0.88\u001b[0m │ \u001b[36m-0.02\u001b[0m │ \u001b[36m   1\u001b[0m │ \u001b[36m   -2.8\u001b[0m │ \u001b[36m-0.74\u001b[0m │ \u001b[36m0.66\u001b[0m │ \u001b[36m 3.7\u001b[0m │ \u001b[32m▁▄█▅▁ \u001b[0m │  │\n", "│ └────────┴─────────┴───────────┴───────┴──────┴─────────┴───────┴──────┴──────┴────────┘  │\n", "│ \u001b[3m                                        category                                        \u001b[0m  │\n", "│ ┏━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━┳━━━━━━━━━━━━┓  │\n", "│ ┃\u001b[1m \u001b[0m\u001b[1m               \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mmissing      \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mcomplete rate         \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mordered     \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1munique    \u001b[0m\u001b[1m \u001b[0m┃  │\n", "│ ┡━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━╇━━━━━━━━━━━━┩  │\n", "│ │ \u001b[38;5;141mclass          \u001b[0m │ \u001b[36m            0\u001b[0m │ \u001b[36m                     1\u001b[0m │ \u001b[38;5;45m<PERSON><PERSON><PERSON>       \u001b[0m │ \u001b[36m         2\u001b[0m │  │\n", "│ │ \u001b[38;5;141mlocation       \u001b[0m │ \u001b[36m            1\u001b[0m │ \u001b[36m                     1\u001b[0m │ \u001b[38;5;45m<PERSON><PERSON><PERSON>       \u001b[0m │ \u001b[36m         5\u001b[0m │  │\n", "│ └─────────────────┴───────────────┴────────────────────────┴──────────────┴────────────┘  │\n", "│ \u001b[3m                                        datetime                                        \u001b[0m  │\n", "│ ┏━━━━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━━┓  │\n", "│ ┃\u001b[1m \u001b[0m\u001b[1m              \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mmissing \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mcomplete rate  \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mfirst       \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mlast       \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mfrequency\u001b[0m\u001b[1m \u001b[0m┃  │\n", "│ ┡━━━━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━━┩  │\n", "│ │ \u001b[38;5;141mdate          \u001b[0m │ \u001b[36m       0\u001b[0m │ \u001b[36m              1\u001b[0m │ \u001b[31m 2018-01-31 \u001b[0m │ \u001b[31m2101-04-30 \u001b[0m │ \u001b[38;5;141mM        \u001b[0m │  │\n", "│ │ \u001b[38;5;141mdate_no_freq  \u001b[0m │ \u001b[36m       3\u001b[0m │ \u001b[36m              1\u001b[0m │ \u001b[31m 1992-01-05 \u001b[0m │ \u001b[31m2023-03-04 \u001b[0m │ \u001b[38;5;141mN<PERSON>     \u001b[0m │  │\n", "│ └────────────────┴──────────┴─────────────────┴──────────────┴─────────────┴───────────┘  │\n", "│ \u001b[3m                                         string                                         \u001b[0m  │\n", "│ ┏━━━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━┓  │\n", "│ ┃\u001b[1m \u001b[0m\u001b[1m       \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mmissing    \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mcomplete rate      \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mwords per row      \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mtotal words     \u001b[0m\u001b[1m \u001b[0m┃  │\n", "│ ┡━━━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━┩  │\n", "│ │ \u001b[38;5;141mtext   \u001b[0m │ \u001b[36m          6\u001b[0m │ \u001b[36m               0.99\u001b[0m │ \u001b[36m                5.8\u001b[0m │ \u001b[36m            5800\u001b[0m │  │\n", "│ └─────────┴─────────────┴─────────────────────┴─────────────────────┴──────────────────┘  │\n", "│ \u001b[3m                                          bool                                          \u001b[0m  │\n", "│ ┏━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━┓  │\n", "│ ┃\u001b[1m \u001b[0m\u001b[1m                        \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mtrue        \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mtrue rate              \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mhist            \u001b[0m\u001b[1m \u001b[0m┃  │\n", "│ ┡━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━┩  │\n", "│ │ \u001b[38;5;141mbo<PERSON>_col               \u001b[0m │ \u001b[36m         520\u001b[0m │ \u001b[36m                   0.52\u001b[0m │ \u001b[32m     █    █     \u001b[0m │  │\n", "│ └──────────────────────────┴──────────────┴─────────────────────────┴──────────────────┘  │\n", "╰─────────────────────────────────────────── End ───────────────────────────────────────────╯\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["skim(df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "af09bc94d41e018aa4bb791c06386d7d2a0d085b02fa573368449120a8cb3c6e"}, "kernelspec": {"display_name": "Python 3.7.6 64-bit ('base': conda)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}