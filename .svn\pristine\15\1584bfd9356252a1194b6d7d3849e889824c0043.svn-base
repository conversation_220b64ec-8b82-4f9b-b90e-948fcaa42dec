# %%
from copy import copy
import os
import pandas as pd
import numpy as np
import datetime
import copy
import json

import random
from torch._C import device
from tqdm import tqdm

import matplotlib.pyplot as plt
import seaborn as sns

from sklearn.model_selection import StratifiedKFold # 将全部训练集S分成k个不相交的子集
from sklearn.preprocessing import LabelEncoder # 标签编码LabelEncoder 作用： 利用LabelEncoder() 将转换成连续的数值型变量。即是对不连续的数字或者文本进行编号

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader, SequentialSampler, RandomSampler
import torch.optim as optim
from torch.optim import lr_scheduler
# from fastai.layers import SigmoidRange
from torch.optim import Adam
from torch.optim.lr_scheduler import ExponentialLR
import pytorch_lightning as pl
from pytorch_lightning.callbacks import ModelCheckpoint
from pytorch_lightning.callbacks.early_stopping import EarlyStopping
import ipywidgets as widgets

TRAD_FUT_CODES = ['M', 'Y', 'A', 'P', 'JM', 'I', 'V', 'EG', 'EB', 'SR', 'CF', 'FG', 'TA', 'MA', 'OI',\
    'RM', 'RS', 'SF', 'SM', 'AP', 'UR', 'SA', 'RB', 'HC', 'AG', 'SP', 'BU', 'SS', 'RU', 'LH', 'PK']


class PreprocessingPipeline:
    
    def __init__(self, n_splits, shuffle, random_state, only_trading_code, data_path, portfolios, interface_params):
        
        self.n_splits = n_splits
        self.shuffle = shuffle
        self.random_state = random_state
        self.only_trading_code = only_trading_code
        self.data_path = data_path
        self.portfolios = portfolios
        self.interface_params = interface_params
        self.lb_df = None
        self.lf_df = None
        self.sf_df = None
        self.ct_df = None

    def _load_data(self, direct):

        self.lb_df = pd.DataFrame()
        self.lf_df = pd.DataFrame()
        self.sf_df = pd.DataFrame()
        self.ct_df = pd.DataFrame()

        for pf in self.portfolios:
            if os.path.isfile('%s/factors_%s_lf.%s.csv'%(self.data_path, direct, pf)):
                self.lf_df = self.lf_df.append(pd.read_csv('%s/factors_%s_lf.%s.csv'%(self.data_path, direct, pf)))
            if os.path.isfile('%s/factors_%s_sf.%s.csv'%(self.data_path, direct, pf)):
                self.sf_df = self.sf_df.append(pd.read_csv('%s/factors_%s_sf.%s.csv'%(self.data_path, direct, pf)))
            if os.path.isfile('%s/factors_%s_ct.%s.csv'%(self.data_path, direct, pf)):
                self.ct_df = self.ct_df.append(pd.read_csv('%s/factors_%s_ct.%s.csv'%(self.data_path, direct, pf)))
            if os.path.isfile('%s/orders_%s_label.%s.csv'%(self.data_path, direct, pf)):
                self.lb_df = self.lb_df.append(pd.read_csv('%s/orders_%s_label.%s.csv'%(self.data_path, direct, pf)), ignore_index=True)

        # 清除不需要的数据
        if self.only_trading_code:
            self.lb_df = self.lb_df[self.lb_df['CODE'].isin(TRAD_FUT_CODES)]
        self.lb_df.drop_duplicates(subset=['ord_id'], keep=False, inplace=True)
        self.sf_df.drop_duplicates(subset=['ord_id'], keep=False, inplace=True)
        self.lf_df.drop_duplicates(subset=['ord_id'], keep=False, inplace=True)
        self.ct_df.drop_duplicates(subset=['ord_id'], keep=False, inplace=True)
        # lb_df的排序和重新索引步骤不能少，否则FOLD标记有问题
        self.lb_df.sort_values(by='ord_id', inplace=True)
        self.lb_df.reset_index(drop=True, inplace=True)
        self.lf_df = self.lf_df[self.lf_df['ord_id'].isin(self.lb_df['ord_id'])]
        self.sf_df = self.sf_df[self.sf_df['ord_id'].isin(self.lb_df['ord_id'])]
        self.ct_df = self.ct_df[self.ct_df['ord_id'].isin(self.lb_df['ord_id'])]

        # 汇总后保存
        self.lf_df.to_csv("%s/factors_%s_lf.csv"%(self.data_path, direct), index=0)
        self.sf_df.to_csv("%s/factors_%s_sf.csv"%(self.data_path, direct), index=0)
        self.ct_df.to_csv("%s/factors_%s_ct.csv"%(self.data_path, direct), index=0)
        self.lb_df.to_csv("%s/orders_%s_label.csv"%(self.data_path, direct), index=0)

        self.lf_df.set_index("ord_id", inplace=True)
        self.sf_df.set_index("ord_id", inplace=True)
        self.ct_df.set_index("ord_id", inplace=True)
        print(f'Feature total: {len(self.lf_df)}')
        # print(self.lb_df.label.value_counts())
        print(f"Today add {direct} count: {(self.lb_df['datetime'] >= datetime.datetime.now().strftime('%Y%m%d 00:00:00')).sum()}")
        
    def _label_encode(self):

        # Encoding instrument_id for embeddings
        le = LabelEncoder()
        self.lb_df['code_encoded'] = le.fit_transform(self.lb_df['CODE'].values)
    
    def _get_folds(self):
        skf = StratifiedKFold(n_splits=self.n_splits, shuffle=self.shuffle, random_state=self.random_state)
        for fold, (_, val_idx) in enumerate(skf.split(X=self.lb_df, y=self.lb_df['label']), 1):
            self.lb_df.loc[val_idx, 'fold'] = fold
        self.lb_df['fold'] = self.lb_df['fold'].astype(np.uint8)

    def _dump_input_param_json(self, direct, model_name, model_path):
        f_sel = {}
        f_sel['codes'] = sorted(self.lb_df.CODE.unique().tolist())

        f_sel['mean'] = self.lf_df.values.mean(axis=0).tolist()
        f_sel['mean'] += (self.sf_df.values.mean(axis=0).tolist())
        f_sel['mean'] += (self.ct_df.values.mean(axis=0).tolist())
        f_sel['std'] = self.lf_df.values.std(axis=0).tolist()
        f_sel['std'] += (self.sf_df.values.std(axis=0).tolist())
        f_sel['std'] += (self.ct_df.values.std(axis=0).tolist())

        f_sel['lf_len'] = self.lf_df.shape[1]
        f_sel['sf_len'] = self.sf_df.shape[1]
        f_sel['ct_len'] = self.ct_df.shape[1]

        with open(f'{self.data_path}/using_factor.json', 'r') as using_file:
            using_factor = json.load(using_file)
            f_sel.update(using_factor)
        f_sel.update(self.interface_params)
        with open(f'{model_path}/{model_name}_{direct}.json', 'w') as factor_sel_file:
            json.dump(f_sel, factor_sel_file)
                        
    def transform(self, direct, model_name, model_path):
        self._load_data(direct)
        self._label_encode()
        self._get_folds()
        self._dump_input_param_json(direct, model_name, model_path)
        
        return self.lb_df, self.lf_df, self.sf_df, self.ct_df

    def get_num_embeddings(self):
        return len(self.lb_df.CODE.unique())

class Factor1DDataset(Dataset):

    def __init__(self, lb_df, lf_df, sf_df, ct_df):
        """
        关键点：要保证几个表基于ord_id的顺序一致
        """

        self.lb_df = lb_df
        # 这里lb_df经FOLD选择后是子集，排序和重新索引步骤不能少，否则有问题
        self.lb_df.sort_values(by='ord_id', inplace=True)
        self.lb_df.reset_index(drop=True, inplace=True)
        lf_df = lf_df[lf_df.index.isin(self.lb_df['ord_id'])]
        sf_df = sf_df[sf_df.index.isin(self.lb_df['ord_id'])]
        ct_df = ct_df[ct_df.index.isin(self.lb_df['ord_id'])]

        df = pd.merge(lf_df, sf_df, how='inner', left_index=True, right_index=True)
        self.data = pd.merge(df, ct_df, how='inner', left_index=True, right_index=True)
        self.data = self.data[self.data.index.isin(self.lb_df['ord_id'])]
        self.data.sort_index(inplace=True)

        assert len(self.lb_df) == len(self.data) and self.lb_df.iloc[-1]['ord_id'] == self.data.index[-1], 'dataset processing error!'

        self.data = self.data.values
        
        self.data = (self.data - self.data.mean(axis=0)) /self.data.std(axis=0)

    def __len__(self):
        return len(self.lb_df)

    def __getitem__(self, idx):

        sequences = torch.as_tensor(np.array(self.data[idx]), dtype=torch.float)
        code_encoded = torch.as_tensor(self.lb_df.iloc[idx]['code_encoded'], dtype=torch.long)
        target = self.lb_df.iloc[idx]['label']
        target = torch.as_tensor(target, dtype=torch.float)
        return code_encoded, sequences, target

class Factor2DDataset(Dataset):

    def __init__(self, lb_df, lf_df, sf_df, ct_df, flip_probability=0.):
        """
        关键点：要保证几个表基于ord_id的顺序一致
        """

        self.lb_df = lb_df
        self.lb_df.sort_values(by='ord_id', inplace=True)
        self.lb_df.reset_index(drop=True, inplace=True)
        self.lf_df = lf_df[lf_df.index.isin(self.lb_df['ord_id'])]
        self.sf_df = sf_df[sf_df.index.isin(self.lb_df['ord_id'])]
        self.ct_df = ct_df[ct_df.index.isin(self.lb_df['ord_id'])]

        # assert len(self.lb_df) == len(self.lf_df) and self.lb_df.iloc[-1]['ord_id'] == self.lf_df.index[-1], 'dataset processing error!'
        
        self.lf_df = self.lf_df.sort_index().values
        self.sf_df = self.sf_df.sort_index().values
        self.ct_df = self.ct_df.sort_index().values

        
        self.lf_df = (self.lf_df - self.lf_df.mean(axis=0)) /self.lf_df.std(axis=0)
        self.sf_df = (self.sf_df - self.sf_df.mean(axis=0)) /self.sf_df.std(axis=0)
        self.ct_df = (self.ct_df - self.ct_df.mean(axis=0)) /self.ct_df.std(axis=0)

        self.transforms = {
            'flip': flip_probability,
        }

    def __len__(self):
        return len(self.lb_df)

    def __getitem__(self, idx):

        """
        Get the idxth element in the dataset
        """

        item = []
        item.append(self.lf_df[idx].tolist())
        item.append(self.sf_df[idx].tolist())
        item.append(self.ct_df[idx].tolist() + [0]*(len(self.lf_df[idx])-len(self.ct_df[idx])))

        sequences = torch.as_tensor(np.array(item), dtype=torch.float)

        # Flip sequences on zeroth dimension
        if np.random.rand() < self.transforms['flip']:
            sequences = torch.flip(sequences, dims=[0])

        code_encoded = torch.as_tensor(self.lb_df.iloc[idx]['code_encoded'], dtype=torch.long)
        target = self.lb_df.iloc[idx]['label']
        target = torch.as_tensor(target, dtype=torch.float)
        return code_encoded, sequences, target

# %%
# -------------------------------------------------------------------------
# 
# 创建模型
class AICMBaseModel(pl.LightningModule):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.save_hyperparameters()
        self.loss_df = pd.DataFrame()
        self.accuracy_df = pd.DataFrame()

    # 定义计算正确率函数
    def _accuracy(self, out, yb):
        preds = (out>0.5).type(torch.IntTensor).cuda()
        return (preds == yb).float().mean()

    def _common_step(self, batch, stage):
        code_encoded, x, y = batch
        y_pred = self(code_encoded, x)
        loss = F.binary_cross_entropy_with_logits(y_pred, y)
        acc = self._accuracy(y_pred, y)
        self.log(f'{stage}/loss', loss.item(), prog_bar=True, on_step=True, on_epoch=False)
        self.log(f'{stage}/accuracy', acc.item(), prog_bar=True, on_step=True, on_epoch=False)
        return {
            'loss': loss,
            'accuracy': acc
        }

    def training_step(self, batch, batch_idx):
        return self._common_step(batch, 'train')

    def validation_step(self, batch, batch_idx):
        return self._common_step(batch, 'valid')

    def _common_epoch_end(self, outputs, stage):
        loss = np.mean([x['loss'].item() for x in outputs])
        acc = np.mean([x['accuracy'].item() for x in outputs])
        # print(loss, acc)
        self.log(f'{stage}/loss', loss.item(), prog_bar=True, on_step=False, on_epoch=True)
        self.log(f'{stage}/accuracy', acc.item(), prog_bar=True, on_step=False, on_epoch=True)
        self.loss_df.loc[self.trainer.current_epoch, f'{stage}/loss'] = loss
        self.accuracy_df.loc[self.trainer.current_epoch, f'{stage}/accuracy'] = acc
        
    def training_epoch_end(self, outputs):
        self._common_epoch_end(outputs, 'train')

        self.loss_widget.clear_output(wait=True)
        with self.loss_widget:
            ylim = [self.loss_df.min().min(), self.loss_df.quantile(0.95).max()]
            ylim[0] -= (ylim[1] - ylim[0]) * 0.05
            self.loss_df.plot(color=['C1', 'C0'], style=['--', '-'], ylim=ylim)
            plt.show()            

        self.accuracy_widget.clear_output(wait=True)
        with self.accuracy_widget:
            ylim = [self.accuracy_df.min().min(), self.accuracy_df.quantile(0.95).max()]
            ylim[0] -= (ylim[1] - ylim[0]) * 0.05
            self.accuracy_df.plot(color=['C1', 'C0'], style=['--', '-'], ylim=ylim)
            plt.show()            

    def validation_epoch_end(self, outputs):
        self._common_epoch_end(outputs, 'valid')

    def on_fit_start(self):
        self.loss_widget = widgets.Output()
        display(self.loss_widget)
        self.accuracy_widget = widgets.Output()
        display(self.accuracy_widget)

    def configure_optimizers(self):
        opt = Adam(self.parameters(), lr=0.0005)
        sched = {
            'scheduler': ExponentialLR(opt, 0.95), 
            'interval': 'epoch'
        }
        return [opt], [sched]
        

class MLPModel(AICMBaseModel): # nn.Module
    '''多层感知机模型'''
    def __init__(self, code_num, input_num, bn=True, dropout=True):

        super(MLPModel, self).__init__()
        
        self.code_embeddings = nn.Embedding(num_embeddings=code_num, embedding_dim=3)
        self.bn = bn
        self.dropout = dropout
        self.lin_1 = nn.Linear(input_num + 3, 96)
        self.bn_1 = nn.BatchNorm1d(96)
        self.lin_2 = nn.Linear(96, 96)
        self.bn_2 = nn.BatchNorm1d(96)
        self.lin_3 = nn.Linear(96, 96)
        self.bn_3 = nn.BatchNorm1d(96)
        self.lin_4 = nn.Linear(96, 1, bias=True)
        self.drop = nn.Dropout(0.4)
        self.activate = nn.ReLU()
        self.sigmoid = nn.Sigmoid()


    def forward(self, code_ids, input):
        '''
        注意：
        模型不能这样写：self.bn_1(F.dropout(F.relu(self.lin_1(input))))
        模型层嵌套写法的问题，dropout在模型的train时执行，在eval时不执行
        Dropout：放在全连接层防止过拟合，一般放在激活函数层之后
        BatchNorm：归一化放在激活层前后好像都有，最初放在了
        激活层池化层后面，而现在普遍放在激活层前。
        '''
        # input layer
        # 加入code向量
        embedded_code_ids = self.code_embeddings(code_ids)
        input = torch.cat([input, embedded_code_ids], dim=1)
        x = self.lin_1(input)
        # print(x.shape)
        if self.bn:
            x = self.bn_1(x)
        x = self.activate(x)
        if self.dropout:
            x = self.drop(x)
        # hidden layer1
        x = self.lin_2(x)
        if self.bn:
            x = self.bn_2(x)
        x = self.activate(x)
        if self.dropout:
            x = self.drop(x)
        # hidden layer2
        x = self.lin_3(x)
        if self.bn:
            x = self.bn_3(x)
        x = self.activate(x)
        if self.dropout:
            x = self.drop(x)
        # out layer
        x = self.lin_4(x)
        x = self.sigmoid(x)
        return x.view(-1)


#--------------------------------------------------------------------------    

class Conv1dBlock(nn.Module):

    def __init__(self, in_channels, out_channels, kernel_size=(5,), stride=(1,), padding=(2,), skip_connection=False):

        super(Conv1dBlock, self).__init__()

        self.skip_connection = skip_connection
        self.conv_block = nn.Sequential(
            nn.Conv1d(in_channels, out_channels, kernel_size=kernel_size, stride=stride, padding=padding, padding_mode='replicate', bias=True),
            nn.BatchNorm1d(out_channels),
            nn.ReLU(inplace=True),
            nn.Conv1d(out_channels, out_channels, kernel_size=kernel_size, stride=stride, padding=padding, padding_mode='replicate', bias=True),
            nn.BatchNorm1d(out_channels),
        )
        self.downsample = nn.Sequential(
            nn.Conv1d(in_channels, out_channels, kernel_size=(1,), stride=(1,), bias=False),
            nn.BatchNorm1d(out_channels)
        )
        self.relu = nn.ReLU(inplace=True)

    def forward(self, x):

        output = self.conv_block(x)
        if self.skip_connection:
            x = self.downsample(x)
            output += x
        output = self.relu(output)

        return output

class CNN1DModel(AICMBaseModel):

    def __init__(self, num_embeddings, in_channels):

        super(CNN1DModel, self).__init__()

        self.stock_embeddings = nn.Embedding(num_embeddings=num_embeddings, embedding_dim=10)
        self.conv_block1 = Conv1dBlock(in_channels=in_channels, out_channels=32, skip_connection=True)
        self.conv_block2 = Conv1dBlock(in_channels=32, out_channels=64, skip_connection=True)
        self.conv_block3 = Conv1dBlock(in_channels=64, out_channels=128, skip_connection=True)
        self.conv_block4 = Conv1dBlock(in_channels=128, out_channels=64, skip_connection=True)
        self.conv_block5 = Conv1dBlock(in_channels=64, out_channels=32, skip_connection=True)
        self.conv_block6 = Conv1dBlock(in_channels=32, out_channels=16, skip_connection=True)
        self.conv_block7 = Conv1dBlock(in_channels=16, out_channels=8, skip_connection=True)
        self.conv_block8 = Conv1dBlock(in_channels=8, out_channels=1, skip_connection=True)
        self.pooling = nn.AvgPool1d(kernel_size=(3,), stride=(1,), padding=(1,))
        self.linear = nn.Linear(3 + 10, 32, bias=True)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.25)
        self.head = nn.Sequential(
            nn.Linear(32, 1, bias=True),
            nn.Sigmoid()
        )

    def forward(self, stock_ids, sequences):

        x = torch.transpose(sequences, 1, 2)
        x = self.conv_block1(x)
        x = self.pooling(x)
        x = self.conv_block2(x)
        x = self.pooling(x)
        x = self.conv_block3(x)
        x = self.pooling(x)
        x = self.conv_block4(x)
        x = self.pooling(x)
        x = self.conv_block5(x)
        x = self.pooling(x)
        x = self.conv_block6(x)
        x = self.pooling(x)
        x = self.conv_block7(x)
        x = self.pooling(x)
        x = self.conv_block8(x)
        x = self.pooling(x)
        x = x.view(x.size(0), -1)
        embedded_stock_ids = self.stock_embeddings(stock_ids)
        x = torch.cat([x, self.dropout(embedded_stock_ids)], dim=1)
        x = self.relu(self.linear(x))
        output = self.head(x)
        
        return output.view(-1)



#--------------------------------------------------------------------------
def trainer(name, early_stop=False, save_model=False):

    model_params = {
        'cnn1d': {
            'model_name': 'cnn1d',
            'model_path': 'e:/lab/RoboQuant/pylab/model',
            'model_parameters': {
                'num_embeddings': 38,
                'in_channels': 41,
            }
        },
        'mlp': {
            'model_name': "MLP2",
            'model_path': 'e:/lab/RoboQuant/pylab/model',
            'model_parameters': {
                'batch_norm': True,
                'drop_out': True,
            },
        }
    }

    training_params = {
        'amp': False,
        'learning_rate': 0.001,
        'weight_decay': 0,
        'epochs': 60,
        'batch_size': 64,
        'reduce_lr_patience': 5,
        'reduce_lr_factor': 0.25,
        'reduce_lr_min': 0.000001,
        'early_stopping_patience': 20,
        'random_state': 42,
        'deterministic_cudnn': False,
        'valid_fold': 1,
    }

    preprocessing_params = {
        'n_splits': 5,
        'shuffle': True,
        'random_state': 42,
        'only_trading_code': True,
        'data_path': 'e:/lab/RoboQuant/pylab/data',
        'portfolios': ['00200910081133001', '00171106132928000'],
        'interface_params': {
            'input_dim': 2,
            'code_encoding': 2, # 0:unsing, 1:onehot, 2:embedding
        }
    }

    if name == 'mlp':
        preprocessing_params['interface_params']['input_dim'] = 1
    else:
        preprocessing_params['interface_params']['input_dim'] = 2

    ppp = PreprocessingPipeline(**preprocessing_params)
    directs = ['long', 'short']
    for direct in directs:
        print(f'\n{"*" * 30}\n{direct}\n{"*" * 30}')
        lb_df, lf_df, sf_df, ct_df = ppp.transform(direct, model_params[name]['model_name'], model_params[name]['model_path'])
  
        trn_idx, val_idx = lb_df.loc[lb_df['fold'] != training_params['valid_fold']].index, lb_df.loc[lb_df['fold'] == training_params['valid_fold']].index
        if name=='mlp':
            train_ds = Factor1DDataset(lb_df=lb_df.loc[trn_idx, :], lf_df=lf_df, sf_df=sf_df, ct_df=ct_df)
        else:
            train_ds = Factor2DDataset(lb_df=lb_df.loc[trn_idx, :], lf_df=lf_df, sf_df=sf_df, ct_df=ct_df, flip_probability=0.)
        train_dl = DataLoader(
            train_ds,
            batch_size=training_params['batch_size'],
            # sampler=RandomSampler(train_ds),
            # pin_memory=True,
            # drop_last=False,
            # num_workers=training_params['num_workers'],
        )
        if name== 'mlp':
            valid_ds = Factor1DDataset(lb_df=lb_df.loc[val_idx, :], lf_df=lf_df, sf_df=sf_df, ct_df=ct_df)
        else:
            valid_ds = Factor2DDataset(lb_df=lb_df.loc[val_idx, :], lf_df=lf_df, sf_df=sf_df, ct_df=ct_df, flip_probability=0.)
        valid_dl = DataLoader(
            valid_ds,
            batch_size=training_params['batch_size'],
            # sampler=SequentialSampler(valid_ds),
            # pin_memory=True,
            # drop_last=False,
            # num_workers=training_params['num_workers'],
        )
        if name == 'mlp':
            input_num= lf_df.shape[1] + sf_df.shape[1] + ct_df.shape[1]
            model = MLPModel(len(lb_df.CODE.unique().tolist()), input_num, True, True)
        elif name == 'cnn1d':
            model_params[name]['model_parameters']['num_embeddings'] = ppp.get_num_embeddings()
            model = CNN1DModel(**model_params[name]['model_parameters'])
        else:
            print(f'unknow model name: {name}')
            return

        model.summarize(max_depth=1)
        cb = []
        # checkpoint_callback = ModelCheckpoint(monitor='valid/loss', mode='min')
        checkpoint_callback = ModelCheckpoint(monitor='valid/accuracy', mode='max')
        cb.append(checkpoint_callback)
        if early_stop:
            early_stop_callback = EarlyStopping(monitor="valid/accuracy", min_delta=0.00, patience=3, verbose=False, mode="max")
            cb.append(early_stop_callback)
        trainer = pl.Trainer(callbacks=cb, gpus=1, precision=16, max_epochs=training_params['epochs'], log_every_n_steps=5)
        trainer.fit(model, train_dl, valid_dl)

        # 训练完成之后，保存了多个模型，下面是获得最好的模型，也就是将原来保存的模型中最好的模型权重apply到当前的网络上
        print(f'Best loss epoch {model.loss_df["valid/loss"].argmin()}: {model.loss_df["valid/loss"].min()}')
        print(f'Best accuracy epoch {model.accuracy_df["valid/accuracy"].argmax()}: {model.accuracy_df["valid/accuracy"].max()}')

        print(checkpoint_callback.best_model_path)
        if save_model:
            '''
            if name == 'mlp':
                model = MLPModel.load_from_checkpoint(checkpoint_callback.best_model_path)
            else:
                model = CNN1DModel.load_from_checkpoint(checkpoint_callback.best_model_path)
            '''
            checkpoint = torch.load(checkpoint_callback.best_model_path, map_location=lambda storage, loc: storage)
            model.load_state_dict(checkpoint['state_dict'])
            model.eval()
            sm = torch.jit.script(model)
            sm.save(f'{model_params[name]["model_path"]}/{model_params[name]["model_name"]}_{direct}.model')

import warnings
warnings.filterwarnings("ignore")

if __name__ == '__main__':
    trainer(name='mlp', save_model=True)
    # trainer(name='cnn1d', save_model=True)




# %%
