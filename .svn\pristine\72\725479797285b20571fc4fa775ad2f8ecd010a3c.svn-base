2022-01-18 20:37:55: -- 分析开始 User Command
2022-01-18 20:37:55: 变更：22， 冲突：0， 复制时间：0， 复制状态：18， 错误： 0, All: 56
2022-01-18 20:37:55: Left to Right: Copy File: 22 
2022-01-18 20:37:55: -- 分析已结束。历时 00:00:00, 速度： Many 文件/秒
2022-01-18 20:37:55: 
2022-01-18 20:38:05: === User Changed Action from 'COPY_FILE_LTOR' to 'NO_COPY' on item /MLP_long.json
2022-01-18 20:38:05: Done.
2022-01-18 20:38:07: === User Changed Action from 'COPY_FILE_LTOR' to 'NO_COPY' on item /MLP_long.model
2022-01-18 20:38:07: Done.
2022-01-18 20:38:07: === User Changed Action from 'COPY_FILE_LTOR' to 'NO_COPY' on item /MLP_short.json
2022-01-18 20:38:07: Done.
2022-01-18 20:38:09: === User Changed Action from 'COPY_FILE_LTOR' to 'NO_COPY' on item /MLP_short.model
2022-01-18 20:38:09: Done.
2022-01-18 20:38:20: === User Changed Action from 'COPY_FILE_LTOR' to 'NO_COPY' on item /MLP_5R_short.model
2022-01-18 20:38:20: Done.
2022-01-18 20:38:20: === User Changed Action from 'COPY_FILE_LTOR' to 'NO_COPY' on item /MLP_5R_short.json
2022-01-18 20:38:20: Done.
2022-01-18 20:38:21: === User Changed Action from 'COPY_FILE_LTOR' to 'NO_COPY' on item /MLP_5R_long.model
2022-01-18 20:38:22: Done.
2022-01-18 20:38:23: === User Changed Action from 'COPY_FILE_LTOR' to 'NO_COPY' on item /MLP_5R_long.json
2022-01-18 20:38:23: Done.
2022-01-18 20:38:30: == 同步开始由 User Command
2022-01-18 20:38:30: 覆盖复制 'E:/lab/RoboQuant/pylab/model/gbdt_short.json' -> 'D:/RoboQuant/model/gbdt_short.json' (4,470)
2022-01-18 20:38:30: 覆盖复制 'E:/lab/RoboQuant/pylab/model/gbdt_short.txt' -> 'D:/RoboQuant/model/gbdt_short.txt' (2,658,736)
2022-01-18 20:38:30: 新建复制 'E:/lab/RoboQuant/pylab/model/MLP2_5R_long.json' -> 'D:/RoboQuant/model/MLP2_5R_long.json' (4,474)
2022-01-18 20:38:30: 新建复制 'E:/lab/RoboQuant/pylab/model/MLP2_5R_long.model' -> 'D:/RoboQuant/model/MLP2_5R_long.model' (137,195)
2022-01-18 20:38:30: 新建复制 'E:/lab/RoboQuant/pylab/model/MLP2_5R_short.json' -> 'D:/RoboQuant/model/MLP2_5R_short.json' (4,503)
2022-01-18 20:38:30: 新建复制 'E:/lab/RoboQuant/pylab/model/MLP2_5R_short.model' -> 'D:/RoboQuant/model/MLP2_5R_short.model' (137,240)
2022-01-18 20:38:30: 新建复制 'E:/lab/RoboQuant/pylab/model/MLP2_7R_long.json' -> 'D:/RoboQuant/model/MLP2_7R_long.json' (4,479)
2022-01-18 20:38:30: 新建复制 'E:/lab/RoboQuant/pylab/model/MLP2_7R_long.model' -> 'D:/RoboQuant/model/MLP2_7R_long.model' (137,195)
2022-01-18 20:38:30: 新建复制 'E:/lab/RoboQuant/pylab/model/MLP2_7R_short.json' -> 'D:/RoboQuant/model/MLP2_7R_short.json' (4,481)
2022-01-18 20:38:30: 新建复制 'E:/lab/RoboQuant/pylab/model/MLP2_7R_short.model' -> 'D:/RoboQuant/model/MLP2_7R_short.model' (137,240)
2022-01-18 20:38:30: 新建复制 'E:/lab/RoboQuant/pylab/model/MLP2_MIX_long.json' -> 'D:/RoboQuant/model/MLP2_MIX_long.json' (4,481)
2022-01-18 20:38:30: 新建复制 'E:/lab/RoboQuant/pylab/model/MLP2_MIX_long.model' -> 'D:/RoboQuant/model/MLP2_MIX_long.model' (137,240)
2022-01-18 20:38:30: 新建复制 'E:/lab/RoboQuant/pylab/model/MLP2_MIX_short.json' -> 'D:/RoboQuant/model/MLP2_MIX_short.json' (4,487)
2022-01-18 20:38:30: 新建复制 'E:/lab/RoboQuant/pylab/model/MLP2_MIX_short.model' -> 'D:/RoboQuant/model/MLP2_MIX_short.model' (137,285)
2022-01-18 20:38:30: == 同步完成. 历时: 00:00:00, 速度: 0 字节/s, 完成: 32, 错误: 0
2022-01-18 20:38:30: 
