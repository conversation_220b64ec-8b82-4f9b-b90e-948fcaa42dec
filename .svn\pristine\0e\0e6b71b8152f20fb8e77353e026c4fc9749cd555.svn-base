{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 500 entries, 0 to 499\n", "Data columns (total 35 columns):\n", "user_id          500 non-null object\n", "shop_id          500 non-null object\n", "time_stamp       500 non-null datetime64[ns]\n", "longitude        500 non-null float64\n", "latitude         500 non-null float64\n", "wifi_id1         500 non-null object\n", "wifi_strong1     500 non-null int64\n", "con_sta1         500 non-null bool\n", "wifi_id2         500 non-null object\n", "wifi_strong2     500 non-null int64\n", "con_sta2         500 non-null object\n", "wifi_id3         499 non-null object\n", "wifi_strong3     499 non-null float64\n", "con_sta3         499 non-null object\n", "wifi_id4         497 non-null object\n", "wifi_strong4     497 non-null float64\n", "con_sta4         497 non-null object\n", "wifi_id5         496 non-null object\n", "wifi_strong5     496 non-null float64\n", "con_sta5         496 non-null object\n", "wifi_id6         495 non-null object\n", "wifi_strong6     495 non-null float64\n", "con_sta6         495 non-null object\n", "wifi_id7         494 non-null object\n", "wifi_strong7     494 non-null float64\n", "con_sta7         494 non-null object\n", "wifi_id8         486 non-null object\n", "wifi_strong8     486 non-null float64\n", "con_sta8         486 non-null object\n", "wifi_id9         478 non-null object\n", "wifi_strong9     478 non-null float64\n", "con_sta9         478 non-null object\n", "wifi_id10        467 non-null object\n", "wifi_strong10    467 non-null float64\n", "con_sta10        467 non-null object\n", "dtypes: bool(1), datetime64[ns](1), float64(10), int64(2), object(21)\n", "memory usage: 133.4+ KB\n", "None\n", "user_id\n", "wifi_id1\n", "wifi_id2\n", "con_sta2\n", "wifi_id3\n", "con_sta3\n", "wifi_id4\n", "con_sta4\n", "wifi_id5\n", "con_sta5\n", "wifi_id6\n", "con_sta6\n", "wifi_id7\n", "con_sta7\n", "wifi_id8\n", "con_sta8\n", "wifi_id9\n", "con_sta9\n", "wifi_id10\n", "con_sta10\n", "time\n", "user_id\n", "wifi_id1\n", "wifi_id2\n", "wifi_id3\n", "con_sta3\n", "wifi_id4\n", "wifi_id5\n", "con_sta5\n", "wifi_id6\n", "wifi_id7\n", "con_sta7\n", "wifi_id8\n", "con_sta8\n", "wifi_id9\n", "con_sta9\n", "wifi_id10\n", "con_sta10\n", "time\n"]}, {"name": "stderr", "output_type": "stream", "text": ["d:\\Anaconda3\\lib\\site-packages\\ipykernel_launcher.py:55: FutureWarning: Method .as_matrix will be removed in a future version. Use .values instead.\n", "d:\\Anaconda3\\lib\\site-packages\\ipykernel_launcher.py:56: FutureWarning: Method .as_matrix will be removed in a future version. Use .values instead.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["     row_id    shop_id\n", "0    118742   s_330608\n", "1    118743   s_552320\n", "2    118744   s_481614\n", "3    118745  s_2887645\n", "4    118746   s_293805\n", "5    118747    s_10859\n", "6    118748   s_461573\n", "7    118749   s_411850\n", "8    118750    s_70790\n", "9    118751  s_3795241\n", "10   118752   s_293082\n", "11   118753   s_938918\n", "12   118754  s_1334527\n", "13   118755   s_411850\n", "14   118756    s_80293\n", "15   118757   s_386448\n", "16   118758   s_298312\n", "17   118759    s_75184\n", "18   118760   s_296179\n", "19   118761   s_491340\n", "20   118762  s_3686420\n", "21   118763   s_382453\n", "22   118764  s_3816766\n", "23   118765  s_1191698\n", "24   118766  s_3210888\n", "25   118767    s_52283\n", "26   118768   s_330608\n", "27   118769   s_520703\n", "28   118770  s_3210888\n", "29   118771   s_330608\n", "..      ...        ...\n", "470  119212    s_70790\n", "471  119213  s_1925717\n", "472  119214    s_52949\n", "473  119215  s_2916543\n", "474  119216  s_1385338\n", "475  119217     s_7301\n", "476  119218   s_725518\n", "477  119219  s_2796281\n", "478  119220   s_471346\n", "479  119221   s_363090\n", "480  119222    s_54892\n", "481  119223  s_3816766\n", "482  119224  s_3001995\n", "483  119225   s_165228\n", "484  119226   s_938918\n", "485  119227   s_382453\n", "486  119228   s_176656\n", "487  119229  s_3210888\n", "488  119230   s_410892\n", "489  119231  s_3835693\n", "490  119232   s_382453\n", "491  119233   s_298312\n", "492  119234    s_53617\n", "493  119235   s_143304\n", "494  119236   s_240723\n", "495  119237   s_221357\n", "496  119238   s_390053\n", "497  119239   s_591653\n", "498  119240    s_12499\n", "499  119241   s_384764\n", "\n", "[500 rows x 2 columns]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["d:\\Anaconda3\\lib\\site-packages\\sklearn\\preprocessing\\label.py:151: DeprecationWarning: The truth value of an empty array is ambiguous. Returning False, but in future this will result in an error. Use `array.size > 0` to check that an array is not empty.\n", "  if diff:\n"]}], "source": ["import pandas as pd\n", "import xgboost as xgb\n", "from sklearn import preprocessing\n", "\n", "\n", "train = pd.read_csv(r'F:\\BaiduNetdiskDownload\\CBD_Location\\train.csv')\n", "tests = pd.read_csv(r'F:\\BaiduNetdiskDownload\\CBD_Location\\test.csv')\n", "\n", "train['time_stamp'] = pd.to_datetime(pd.Series(train['time_stamp']))\n", "tests['time_stamp'] = pd.to_datetime(pd.Series(tests['time_stamp']))\n", "\n", "print(train.info())\n", "\n", "train['Year'] = train['time_stamp'].apply(lambda x:x.year)\n", "train['Month'] = train['time_stamp'].apply(lambda x: x.month)\n", "train['weekday'] = train['time_stamp'].apply(lambda x: x.weekday())\n", "train['time'] = train['time_stamp'].dt.time\n", "tests['Year'] = tests['time_stamp'].apply(lambda x: x.year)\n", "tests['Month'] = tests['time_stamp'].apply(lambda x: x.month)\n", "tests['weekday'] = tests['time_stamp'].dt.dayofweek\n", "tests['time'] = tests['time_stamp'].dt.time\n", "train = train.drop('time_stamp', axis=1)\n", "train = train.dropna(axis=0)\n", "tests = tests.drop('time_stamp', axis=1)\n", "tests = tests.fillna(method='pad')\n", "for f in train.columns:\n", "    if train[f].dtype=='object':\n", "        if f != 'shop_id':\n", "            print(f)\n", "            lbl = preprocessing.LabelEncoder()\n", "            train[f] = lbl.fit_transform(list(train[f].values))\n", "for f in tests.columns:\n", "    if tests[f].dtype == 'object':\n", "        print(f)\n", "        lbl = preprocessing.LabelEncoder()\n", "        lbl.fit(list(tests[f].values))\n", "        tests[f] = lbl.transform(list(tests[f].values))\n", "\n", "\n", "feature_columns_to_use = ['Year', 'Month', 'weekday',\n", "'time', 'longitude', 'latitude',\n", "'wifi_id1', 'wifi_strong1', 'con_sta1',\n", " 'wifi_id2', 'wifi_strong2', 'con_sta2',\n", "'wifi_id3', 'wifi_strong3', 'con_sta3',\n", "'wifi_id4', 'wifi_strong4', 'con_sta4',\n", "'wifi_id5', 'wifi_strong5', 'con_sta5',\n", "'wifi_id6', 'wifi_strong6', 'con_sta6',\n", "'wifi_id7', 'wifi_strong7', 'con_sta7',\n", "'wifi_id8', 'wifi_strong8', 'con_sta8',\n", "'wifi_id9', 'wifi_strong9', 'con_sta9',\n", "'wifi_id10', 'wifi_strong10', 'con_sta10',]\n", "\n", "big_train = train[feature_columns_to_use]\n", "big_test = tests[feature_columns_to_use]\n", "train_X = big_train.as_matrix()\n", "test_X = big_test.as_matrix()\n", "train_y = train['shop_id']\n", "\n", "gbm = xgb.XGBClassifier(silent=1, max_depth=10,\n", "                    n_estimators=1000, learning_rate=0.05)\n", "gbm.fit(train_X, train_y)\n", "predictions = gbm.predict(test_X)\n", "\n", "submission = pd.DataFrame({'row_id': tests['row_id'],\n", "                            'shop_id': predictions})\n", "print(submission)\n", "submission.to_csv(\"submission.csv\",index=False)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 2}