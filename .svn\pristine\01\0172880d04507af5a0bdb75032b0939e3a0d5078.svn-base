﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" DefaultTargets="Build">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{4290d8a0-2eb3-45da-994d-d12e7569d48e}</ProjectGuid>
    <ProjectHome />
    <StartupFile>./mlab/workflow_mlp.py</StartupFile>
    <SearchPath>.</SearchPath>
    <WorkingDirectory>.</WorkingDirectory>
    <OutputPath>.</OutputPath>
    <ProjectTypeGuids>{888888a0-9f3d-457c-b088-3a5042f75d52}</ProjectTypeGuids>
    <LaunchProvider>Standard Python launcher</LaunchProvider>
    <InterpreterId>Global|ContinuumAnalytics|Anaconda38-64</InterpreterId>
    <IsWindowsApplication>False</IsWindowsApplication>
    <SuppressConfigureTestFrameworkPrompt>true</SuppressConfigureTestFrameworkPrompt>
    <CommandLineArguments>
    </CommandLineArguments>
    <InterpreterArguments>
    </InterpreterArguments>
    <EnableNativeCodeDebugging>False</EnableNativeCodeDebugging>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)' == 'Debug'" />
  <PropertyGroup Condition="'$(Configuration)' == 'Release'" />
  <PropertyGroup>
    <VisualStudioVersion Condition=" '$(VisualStudioVersion)' == '' ">10.0</VisualStudioVersion>
  </PropertyGroup>
  <ItemGroup>
    <Content Include="mlab\benchmarks\ALSTM\README.md" />
    <Content Include="mlab\benchmarks\ALSTM\requirements.txt" />
    <Content Include="mlab\benchmarks\ALSTM\workflow_config_alstm_Alpha158.yaml" />
    <Content Include="mlab\benchmarks\ALSTM\workflow_config_alstm_Alpha360.yaml" />
    <Content Include="mlab\benchmarks\CatBoost\README.md" />
    <Content Include="mlab\benchmarks\CatBoost\requirements.txt" />
    <Content Include="mlab\benchmarks\CatBoost\workflow_config_catboost_Alpha158.yaml" />
    <Content Include="mlab\benchmarks\CatBoost\workflow_config_catboost_Alpha360.yaml" />
    <Content Include="mlab\benchmarks\DoubleEnsemble\README.md" />
    <Content Include="mlab\benchmarks\DoubleEnsemble\requirements.txt" />
    <Content Include="mlab\benchmarks\DoubleEnsemble\workflow_config_doubleensemble_Alpha158.yaml" />
    <Content Include="mlab\benchmarks\DoubleEnsemble\workflow_config_doubleensemble_Alpha360.yaml" />
    <Content Include="mlab\benchmarks\GATs\README.md" />
    <Content Include="mlab\benchmarks\GATs\requirements.txt" />
    <Content Include="mlab\benchmarks\GATs\workflow_config_gats_Alpha158.yaml" />
    <Content Include="mlab\benchmarks\GATs\workflow_config_gats_Alpha360.yaml" />
    <Content Include="mlab\benchmarks\GRU\csi300_gru_ts.pkl" />
    <Content Include="mlab\benchmarks\GRU\model_gru_csi300.pkl" />
    <Content Include="mlab\benchmarks\GRU\README.md" />
    <Content Include="mlab\benchmarks\GRU\requirements.txt" />
    <Content Include="mlab\benchmarks\GRU\workflow_config_gru_Alpha158.yaml" />
    <Content Include="mlab\benchmarks\GRU\workflow_config_gru_Alpha360.yaml" />
    <Content Include="mlab\benchmarks\LightGBM\README.md" />
    <Content Include="mlab\benchmarks\LightGBM\requirements.txt" />
    <Content Include="mlab\benchmarks\LightGBM\workflow_config_lightgbm_Alpha158.yaml" />
    <Content Include="mlab\benchmarks\LightGBM\workflow_config_lightgbm_Alpha158_multi_freq.yaml" />
    <Content Include="mlab\benchmarks\LightGBM\workflow_config_lightgbm_Alpha158_test.yaml" />
    <Content Include="mlab\benchmarks\LightGBM\workflow_config_lightgbm_Alpha360.yaml" />
    <Content Include="mlab\benchmarks\LightGBM\workflow_config_lightgbm_configurable_dataset.yaml" />
    <Content Include="mlab\benchmarks\LightGBM\workflow_config_lightgbm_multi_freq.yaml" />
    <Content Include="mlab\benchmarks\Linear\requirements.txt" />
    <Content Include="mlab\benchmarks\Linear\workflow_config_linear_Alpha158.yaml" />
    <Content Include="mlab\benchmarks\Localformer\README.md" />
    <Content Include="mlab\benchmarks\Localformer\requirements.txt" />
    <Content Include="mlab\benchmarks\Localformer\workflow_config_localformer_Alpha158.yaml" />
    <Content Include="mlab\benchmarks\Localformer\workflow_config_localformer_Alpha360.yaml" />
    <Content Include="mlab\benchmarks\LSTM\csi300_lstm_ts.pkl" />
    <Content Include="mlab\benchmarks\LSTM\model_lstm_csi300.pkl" />
    <Content Include="mlab\benchmarks\LSTM\README.md" />
    <Content Include="mlab\benchmarks\LSTM\requirements.txt" />
    <Content Include="mlab\benchmarks\LSTM\workflow_config_lstm_Alpha158.yaml" />
    <Content Include="mlab\benchmarks\LSTM\workflow_config_lstm_Alpha360.yaml" />
    <Content Include="mlab\benchmarks\MLP\README.md" />
    <Content Include="mlab\benchmarks\MLP\requirements.txt" />
    <Content Include="mlab\benchmarks\MLP\workflow_config_mlp_Alpha360.yaml" />
    <Content Include="mlab\benchmarks\MLP\workflow_config_mlp_train.yaml" />
    <Content Include="mlab\benchmarks\SFM\README.md" />
    <Content Include="mlab\benchmarks\SFM\requirements.txt" />
    <Content Include="mlab\benchmarks\SFM\workflow_config_sfm_Alpha360.yaml" />
    <Content Include="mlab\benchmarks\TabNet\README.md" />
    <Content Include="mlab\benchmarks\TabNet\requirements.txt" />
    <Content Include="mlab\benchmarks\TabNet\workflow_config_TabNet_Alpha360.yaml" />
    <Content Include="mlab\benchmarks\TabNet\workflow_config_TabNet_train.yaml" />
    <Content Include="mlab\benchmarks\TCN\README.md" />
    <Content Include="mlab\benchmarks\TCN\requirements.txt" />
    <Content Include="mlab\benchmarks\TCN\workflow_config_tcn_Alpha158.yaml" />
    <Content Include="mlab\benchmarks\TCN\workflow_config_tcn_Alpha360.yaml" />
    <Content Include="mlab\benchmarks\TCTS\README.md" />
    <Content Include="mlab\benchmarks\TCTS\requirements.txt" />
    <Content Include="mlab\benchmarks\TCTS\workflow.png" />
    <Content Include="mlab\benchmarks\TCTS\workflow_config_tcts_Alpha360.yaml" />
    <Content Include="mlab\benchmarks\TFT\README.md" />
    <Content Include="mlab\benchmarks\TFT\requirements.txt" />
    <Content Include="mlab\benchmarks\TFT\workflow_config_tft_Alpha158.yaml" />
    <Content Include="mlab\benchmarks\Transformer\README.md" />
    <Content Include="mlab\benchmarks\Transformer\requirements.txt" />
    <Content Include="mlab\benchmarks\Transformer\workflow_config_transformer_Alpha158.yaml" />
    <Content Include="mlab\benchmarks\Transformer\workflow_config_transformer_Alpha360.yaml" />
    <Content Include="mlab\benchmarks\TRA\configs\config_alstm.yaml" />
    <Content Include="mlab\benchmarks\TRA\configs\config_alstm_tra.yaml" />
    <Content Include="mlab\benchmarks\TRA\configs\config_alstm_tra_init.yaml" />
    <Content Include="mlab\benchmarks\TRA\configs\config_transformer.yaml" />
    <Content Include="mlab\benchmarks\TRA\configs\config_transformer_tra.yaml" />
    <Content Include="mlab\benchmarks\TRA\configs\config_transformer_tra_init.yaml" />
    <Content Include="mlab\benchmarks\TRA\data\README.md" />
    <Content Include="mlab\benchmarks\TRA\README.md" />
    <Content Include="mlab\benchmarks\TRA\Reports.ipynb" />
    <Content Include="mlab\benchmarks\TRA\requirements.txt" />
    <Content Include="mlab\benchmarks\TRA\run.sh" />
    <Content Include="mlab\benchmarks\TRA\workflow_config_tra_Alpha158.yaml" />
    <Content Include="mlab\benchmarks\TRA\workflow_config_tra_Alpha158_full.yaml" />
    <Content Include="mlab\benchmarks\TRA\workflow_config_tra_Alpha360.yaml" />
    <Content Include="mlab\benchmarks\XGBoost\README.md" />
    <Content Include="mlab\benchmarks\XGBoost\requirements.txt" />
    <Content Include="mlab\benchmarks\XGBoost\workflow_config_xgboost_Alpha158.yaml" />
    <Content Include="mlab\benchmarks\XGBoost\workflow_config_xgboost_Alpha360.yaml" />
    <Content Include="model\cnn1d_fold1_learning_curve.png" />
    <Content Include="model\cnn1d_fold2_learning_curve.png" />
    <Content Include="model\cnn1d_fold3_learning_curve.png" />
    <Content Include="model\cnn1d_fold4_learning_curve.png" />
    <Content Include="model\cnn1d_fold5_learning_curve.png" />
    <Content Include="model\LRS_context_long_accuracy.png" />
    <Content Include="model\LRS_context_long_loss.png" />
    <Content Include="model\LRS_context_short_accuracy.png" />
    <Content Include="model\LRS_context_short_loss.png" />
    <Content Include="model\MLP_accuracy.png" />
    <Content Include="model\MLP_loss.png" />
    <Content Include="pyqlab\VERSION.txt" />
    <Content Include="temp\pyfolioexs\pydata_stack-4-finance.jpg" />
    <Content Include="temp\tt\img\alphastar.gif" />
    <Content Include="temp\tt\img\commits.png" />
    <Content Include="temp\tt\img\primer.jpg" />
    <Content Include="temp\tt\requirements.txt" />
    <Content Include="temp\tt\test_keras_plot_model.png" />
    <Content Include="temp\tt\test_keras_plot_model_1.png" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="mlab\benchmarks\LightGBM\features_resample_N.py" />
    <Compile Include="mlab\benchmarks\LightGBM\features_sample.py" />
    <Compile Include="mlab\benchmarks\LightGBM\multi_freq_handler.py" />
    <Compile Include="mlab\benchmarks\TFT\data_formatters\base.py" />
    <Compile Include="mlab\benchmarks\TFT\data_formatters\qlib_Alpha158.py" />
    <Compile Include="mlab\benchmarks\TFT\data_formatters\__init__.py" />
    <Compile Include="mlab\benchmarks\TFT\expt_settings\configs.py" />
    <Compile Include="mlab\benchmarks\TFT\expt_settings\__init__.py" />
    <Compile Include="mlab\benchmarks\TFT\libs\hyperparam_opt.py" />
    <Compile Include="mlab\benchmarks\TFT\libs\tft_model.py" />
    <Compile Include="mlab\benchmarks\TFT\libs\utils.py" />
    <Compile Include="mlab\benchmarks\TFT\libs\__init__.py" />
    <Compile Include="mlab\benchmarks\TFT\tft.py" />
    <Compile Include="mlab\benchmarks\TRA\example.py" />
    <Compile Include="mlab\benchmarks\TRA\src\dataset.py" />
    <Compile Include="mlab\benchmarks\TRA\src\model.py" />
    <Compile Include="mlab\train_mlp_pl.py" />
    <Compile Include="mlab\workflow.py" />
    <Compile Include="mlab\workflow_mlp.py" />
    <Compile Include="pyqlab\config.py" />
    <Compile Include="pyqlab\contrib\model\pytorch_mlp_pl.py" />
    <Compile Include="pyqlab\contrib\model\pytorch_tabnet.py" />
    <Compile Include="pyqlab\contrib\model\pytorch_utils.py" />
    <Compile Include="pyqlab\contrib\model\__init__.py" />
    <Compile Include="pyqlab\contrib\__init__.py" />
    <Compile Include="pyqlab\data\dataset\factors.py" />
    <Compile Include="pyqlab\data\dataset\handler.py" />
    <Compile Include="pyqlab\data\dataset\loader.py" />
    <Compile Include="pyqlab\data\dataset\__init__.py" />
    <Compile Include="pyqlab\data\_libs\__init__.py" />
    <Compile Include="pyqlab\data\__init__.py" />
    <Compile Include="pyqlab\log.py" />
    <Compile Include="pyqlab\model\base.py" />
    <Compile Include="pyqlab\model\__init__.py" />
    <Compile Include="pyqlab\tests\config.py" />
    <Compile Include="pyqlab\tests\data.py" />
    <Compile Include="pyqlab\tests\__init__.py" />
    <Compile Include="pyqlab\utils\exceptions.py" />
    <Compile Include="pyqlab\utils\file.py" />
    <Compile Include="pyqlab\utils\index_data.py" />
    <Compile Include="pyqlab\utils\objm.py" />
    <Compile Include="pyqlab\utils\paral.py" />
    <Compile Include="pyqlab\utils\resam.py" />
    <Compile Include="pyqlab\utils\serial.py" />
    <Compile Include="pyqlab\utils\time.py" />
    <Compile Include="pyqlab\utils\__init__.py" />
    <Compile Include="pyqlab\__init__.py" />
    <Compile Include="temp\bars\bars.py" />
    <Compile Include="temp\CCRP\count_dataset.py" />
    <Compile Include="temp\CCRP\dataset.py" />
    <Compile Include="temp\CCRP\dataset_traditional.py" />
    <Compile Include="temp\CCRP\generatebigdata.py" />
    <Compile Include="temp\CCRP\get_data.py" />
    <Compile Include="temp\CCRP\knn.py" />
    <Compile Include="temp\CCRP\model_evaluate.py" />
    <Compile Include="temp\CCRP\myDeepCNN.py" />
    <Compile Include="temp\CCRP\myVgg16.py" />
    <Compile Include="temp\CCRP\myVgg19.py" />
    <Compile Include="temp\CCRP\naivebayes.py" />
    <Compile Include="temp\CCRP\predictme.py" />
    <Compile Include="temp\CCRP\preproccess_binclass.py" />
    <Compile Include="temp\CCRP\preprocess.py" />
    <Compile Include="temp\CCRP\randomforest.py" />
    <Compile Include="temp\CCRP\removezeroimage.py" />
    <Compile Include="temp\CCRP\resnet50.py" />
    <Compile Include="temp\CCRP\runallfromlist.py" />
    <Compile Include="temp\CCRP\run_binary_preprocessing.py" />
    <Compile Include="temp\CCRP\run_categorical_preprocessing.py" />
    <Compile Include="temp\CCRP\svm.py" />
    <Compile Include="temp\CCRP\visualme.py" />
    <Compile Include="temp\CCRP\whoisempty.py" />
    <Compile Include="temp\esn\pyESN.py" />
    <Compile Include="temp\esn\testing.py" />
    <Compile Include="temp\factors\Alpha101.py" />
    <Compile Include="temp\factors\Alpha101a.py" />
    <Compile Include="temp\factors\Alpha191.py" />
    <Compile Include="temp\LightGBM\advanced_example.py" />
    <Compile Include="temp\LightGBM\logistic_regression.py" />
    <Compile Include="temp\LightGBM\plot_example.py" />
    <Compile Include="temp\LightGBM\simple_example.py" />
    <Compile Include="temp\LightGBM\sklearn_example.py" />
    <Compile Include="tests\test_dataset.py" />
    <Compile Include="tests\test_pyqlab.py" />
    <Compile Include="torch\aicm_eda.py" />
    <Compile Include="torch\aicm_factors.py" />
    <Compile Include="torch\aicm_models.py" />
    <Compile Include="torch\aicm_pl_train_models.py" />
    <Compile Include="torch\aicm_tabnet_model.py" />
    <Compile Include="torch\aicm_train_models.py" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="mlab\" />
    <Folder Include="mlab\benchmarks\" />
    <Folder Include="mlab\benchmarks\ALSTM\" />
    <Folder Include="mlab\benchmarks\CatBoost\" />
    <Folder Include="mlab\benchmarks\DoubleEnsemble\" />
    <Folder Include="mlab\benchmarks\GATs\" />
    <Folder Include="mlab\benchmarks\GRU\" />
    <Folder Include="mlab\benchmarks\LightGBM\" />
    <Folder Include="mlab\benchmarks\Linear\" />
    <Folder Include="mlab\benchmarks\Localformer\" />
    <Folder Include="mlab\benchmarks\LSTM\" />
    <Folder Include="mlab\benchmarks\MLP\" />
    <Folder Include="mlab\benchmarks\SFM\" />
    <Folder Include="mlab\benchmarks\TabNet\" />
    <Folder Include="mlab\benchmarks\TCN\" />
    <Folder Include="mlab\benchmarks\TCTS\" />
    <Folder Include="mlab\benchmarks\TFT\" />
    <Folder Include="mlab\benchmarks\TFT\data_formatters\" />
    <Folder Include="mlab\benchmarks\TFT\expt_settings\" />
    <Folder Include="mlab\benchmarks\TFT\libs\" />
    <Folder Include="mlab\benchmarks\Transformer\" />
    <Folder Include="mlab\benchmarks\TRA\" />
    <Folder Include="mlab\benchmarks\TRA\configs\" />
    <Folder Include="mlab\benchmarks\TRA\data\" />
    <Folder Include="mlab\benchmarks\TRA\src\" />
    <Folder Include="mlab\benchmarks\XGBoost\" />
    <Folder Include="model" />
    <Folder Include="pyqlab" />
    <Folder Include="pyqlab\contrib" />
    <Folder Include="pyqlab\contrib\model" />
    <Folder Include="pyqlab\data" />
    <Folder Include="pyqlab\data\dataset" />
    <Folder Include="pyqlab\data\_libs" />
    <Folder Include="pyqlab\model" />
    <Folder Include="pyqlab\tests" />
    <Folder Include="pyqlab\utils" />
    <Folder Include="temp\" />
    <Folder Include="temp\bars" />
    <Folder Include="temp\CCRP" />
    <Folder Include="temp\esn" />
    <Folder Include="temp\factors" />
    <Folder Include="temp\LightGBM" />
    <Folder Include="temp\pyfolioexs" />
    <Folder Include="temp\tt" />
    <Folder Include="temp\tt\img" />
    <Folder Include="tests" />
    <Folder Include="torch" />
  </ItemGroup>
  <ItemGroup>
    <InterpreterReference Include="Global|ContinuumAnalytics|Anaconda38-64" />
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\Python Tools\Microsoft.PythonTools.targets" />
</Project>