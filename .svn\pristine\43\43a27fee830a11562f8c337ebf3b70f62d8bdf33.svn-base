"""
NATR vs STDDEV:
NATR指标使用的是(high,low,close)来计算波动幅度,而STDDEV仅仅使用了(close),
仅这一点NATR就已经胜过了STDDEV
"""
import sys
import numpy as np
import pandas as pd
from datetime import datetime
import talib as ta

sys.path.append("d:/QuantLab")
# from qtunnel import DB,Mode,Cursor,Transaction,create_db,registered_dbs
from qtunnel import DataSource, BarData, BarSize, DoRight, RunMode
# ds=DataSource(RunMode.passive)

def crosssection_rsi(ds, blkname="ZLQH", barsize=BarSize.day, win=20, length=250, primary=True):
    count = 0
    total=None
    blocks = ds.get_block_data(blkname)
    for symbol in blocks:
        if primary:
            symbol=ds.get_fut_lx_label(symbol)
        hist=ds.get_history_data(symbol, length, [BarData.datetime,BarData.close], barsize)
        if hist.shape[0] < length:
            print(f"{symbol} len {hist.shape[0]}")
            continue
        rsi = ta.RSI(hist[:,1], win) -50
        if total is None:
            total = rsi
        else:
            total += rsi
        count += 1
    avg_rsi=total/count
    return hist[:, 0].copy(), avg_rsi

def crosssection_natr(ds, blkname="ZLQH", barsize=BarSize.day, win=20, length=250, primary=True):
    count = 0
    total=None
    blocks = ds.get_block_data(blkname)
    for symbol in blocks:
        if primary:
            symbol=ds.get_fut_lx_label(symbol)
        hist=ds.get_history_data(symbol, length, [BarData.datetime,BarData.open,BarData.high,BarData.low,BarData.close], barsize)
        if hist.shape[0] < length:
            print(f"{symbol} len {hist.shape[0]}")
            continue
        natr = ta.NATR(hist[:,2], hist[:,3], hist[:,4], 5)
        if total is None:
            total = natr
        else:
            total += natr
        count += 1
    avg_natr=total/count
    return hist[:, 0].copy(), avg_natr

def crosssection_std(ds: DataSource, blkname="ZLQH", barsize=BarSize.day, win=20, length=250, primary=True):
    count = 0
    total=None
    blocks = ds.get_block_data(blkname)
    for symbol in blocks:
        if primary:
            symbol=ds.get_fut_lx_label(symbol)
        hist=ds.get_history_data(symbol, length, [BarData.datetime,BarData.close], barsize)
        if hist.shape[0] < length:
            print(f"{symbol} len {hist.shape[0]}")
            continue
        nstddev = ta.STDDEV(hist[:,1], win) / hist[:,1] * 100
        if total is None:
            total = nstddev
        else:
            total += nstddev
        count += 1
    avg_nstddev=total/count
    return hist[:, 0].copy(), avg_nstddev

def crosssection_mom(ds: DataSource, blkname="ZLQH", barsize=BarSize.day, win=20, length=250, primary=True):
    count = 0
    total=None
    blocks = ds.get_block_data(blkname)
    for symbol in blocks:
        if primary:
            symbol=ds.get_fut_lx_label(symbol)
        hist=ds.get_history_data(symbol, length, [BarData.datetime,BarData.close], barsize)
        if hist.shape[0] < length:
            print(f"{symbol} len {hist.shape[0]}")
            continue
        mom = ta.MOM(hist[:,1], win) / hist[:,1] * 100
        if total is None:
            total = mom
        else:
            total += mom
        count += 1
    avg_mom=total/count
    return hist[:, 0].copy(), avg_mom

def zscore(series):
    nonans = series[~np.isnan(series)]
    zscore = (series - np.mean(nonans))/np.std(nonans)
    return zscore

def trend_level(series, win=15, ma_win=5):
    ma=ta.EMA(series, ma_win)
    ls=ta.LINEARREG_SLOPE(ma, win)
    nonans=ls[~np.isnan(ls)]
    std=np.std(nonans)
    direct=[]
    for i in range(ls.shape[0]):
        if np.isnan(ls[i]):
            direct.append(ls[i])
        else:
            drt=0
            if ls[i]>0 and ls[i]<=0.5*std:
                drt=1
            elif ls[i]>0.5*std and ls[i]<=std:
                drt=2
            elif ls[i]>std:
                if ls[i-1]>=ls[i]:
                    drt=3
                else:
                    drt=4
            elif ls[i]<0 and ls[i]>=-0.5*std:
                drt=-1
            elif ls[i]<-0.5*std and ls[i]>=-1*std:
                drt=-2
            elif ls[i]<-1*std:
                if ls[i-1]<=ls[i]:
                    drt=-3
                else:
                    drt=-4
            direct.append(drt)
    return direct

def draw_cs_trend_chart(blkname='主选期货', period='day', win=15, length=120, primary=True):
    import mplfinance as mpf
    from qtunnel import DataSource,BarData,BarSize,DoRight,RunMode

    if period=='min5':
        barsize=BarSize.min5
        dt_format='%Y-%m-%d'
    elif period=='range':
        barsize=BarSize.range
        dt_format='%Y-%m-%d'
    else:
        barsize=BarSize.day
        dt_format='%Y-%m'
    ds=DataSource(RunMode.passive)
    _, rsi = crosssection_rsi(ds, blkname, barsize, win, length, primary)
    _, natr = crosssection_natr(ds, blkname, barsize, win, length, primary)
    # dates, std = crosssection_std(ds, blkname, barsize, win, length, primary)
    _, mom = crosssection_mom(ds, blkname, barsize, win, length, primary)
    zs_rsi = zscore(rsi)
    zs_natr = zscore(natr)
    # zs_std = zscore(std)
    zs_mom = zscore(mom)

    wfi_hist=ds.get_history_data("WFI999.IX", length, [BarData.datetime,BarData.open,BarData.high,BarData.low,BarData.close,BarData.volume], barsize)
    if zs_rsi.shape[0] != wfi_hist.shape[0]:
        raise ValueError(f"Wenhua futures index data length: {wfi_hist.shape[0]} <> contract history data length:{zs_rsi.shape[0]}")
    df=pd.DataFrame(wfi_hist, columns=["date", "open", "high", "low", "close", "volume"])
    df["date"] = df["date"].apply(datetime.fromtimestamp) #.apply(lambda x: x.strftime("%Y-%m-%d"))
    df.set_index(["date"], inplace=True, drop=True)
    df["natr"] = zs_natr
    df["mom"] = zs_mom
    df["rsi"] = zs_rsi

    tl_natr=trend_level(zs_natr, win=10)
    tl_mom=trend_level(zs_mom, win=10)

    # mpf.available_styles()
    add_plot = [
        mpf.make_addplot(df[["natr","rsi","mom"]], panel=1),
        mpf.make_addplot(tl_mom, panel=2, color='r'),
        mpf.make_addplot(tl_natr, panel=3, color='y'),
    ]
    mpf.plot(df, type='candle',
        mav=(5, 10, 20, 60),
        # volume=True,
        style='nightclouds',
        addplot=add_plot,
        datetime_format=dt_format,
        figscale=1.2, #设置图像的缩小或放大,1.5就是放大50%
        # figratio=(6.5, 3.5), # 设置图形纵横比
    )