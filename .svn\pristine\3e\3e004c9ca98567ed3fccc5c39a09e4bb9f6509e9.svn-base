{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Workflow"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[1228:MainThread](2023-03-22 16:32:14,547) INFO - qlib.Initialization - [config.py:413] - default_conf: client.\n", "[1228:MainThread](2023-03-22 16:32:14,550) INFO - qlib.Initialization - [__init__.py:74] - qlib successfully initialized based on client settings.\n", "[1228:MainThread](2023-03-22 16:32:14,552) INFO - qlib.Initialization - [__init__.py:76] - data_path={'__DEFAULT_FREQ': WindowsPath('C:/Users/<USER>/.qlib/qlib_data/cn_data')}\n"]}], "source": ["import qlib\n", "from qlib.config import REG_CN\n", "from qlib.utils import init_instance_by_config, flatten_dict\n", "from qlib.workflow import R\n", "\n", "# from pyqlab.data.dataset.handler import DataHandlerAHF\n", "\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import ipywidgets as widgets\n", "\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "provider_uri = \"~/.qlib/qlib_data/cn_data\"  # target_dir\n", "qlib.init(provider_uri=provider_uri, region=REG_CN)\n", "\n", "###################################\n", "# train model\n", "###################################\n", "SEL_LONG_FACTOR_NAMES = [ # Slow period factor\n", "    # \"MACD\", \"MACD_DIFF\", \"MACD_DEA\", \"MOM\", \"RSI\",\n", "\n", "    # \"LR_SLOPE_FAST\", \"LR_SLOPE_MIDD\", \"LR_SLOPE_SLOW\",\n", "    # \"LR_SLOPE_FAST_THRESHOLD\", \"LR_SLOPE_SLOW_THRESHOLD\",\n", "\n", "    # \"SQUEEZE_ZERO_BARS\", \n", "    # \"SQUEEZE_GAP\", \"SQUEEZE_GAP_FAST\", \"SQUEEZE_GAP_SLOW\",\n", "    # \"SQUEEZE_GAP_THRESHOLD\", \"SQUEEZE_NARROW_BARS\",\n", "\n", "    # \"BAND_POSITION\", \"BAND_WIDTH\",\n", "    # \"BAND_EXPAND\", \"<PERSON>ND_GRADIENT\", \"BAND_GRADIENT_THRESHOLD\", \"BAND_GAP\",\n", "\n", "    # \"TL_FAST\", \"TL_SLOW\", \"TL_THRESHOLD\",\n", "\n", "    # \"TREND_VALUE\", \"TREND_BARS\", \"TREND_INBARS\", \"TREND_INPOSR\", \"TREND_HLR\",\n", "    # \"TREND_LEVEL\"\n", "]\n", "\n", "SEL_SHORT_FACTOR_NAMES = [ # Fast period factor\n", "    \"MACD\", \"MACD_DIFF\", \"MACD_DEA\", \"MOM\", \"RSI\",\n", "\n", "    \"LR_SLOPE_FAST\", \"LR_SLOPE_MIDD\", \"LR_SLOPE_SLOW\",\n", "    \"LR_SLOPE_FAST_THRESHOLD\", \"LR_SLOPE_SLOW_THRESHOLD\",\n", "\n", "    \"SQUEEZE_ZERO_BARS\", \n", "    \"SQUEEZE_GAP\", \"SQUEEZE_GAP_FAST\", \"SQUEEZE_GAP_SLOW\",\n", "    \"SQUEEZE_GAP_THRESHOLD\", \"SQUEEZE_NARROW_BARS\",\n", "\n", "    \"BAND_POSITION\", \"BAND_WIDTH\",\n", "    \"BAND_EXPAND\", \"<PERSON>ND_GRADIENT\", \"BAND_GRADIENT_THRESHOLD\", \"BAND_GAP\",\n", "\n", "    \"TL_FAST\", \"TL_SLOW\", \"TL_THRESHOLD\",\n", "\n", "    \"TREND_VALUE\",\n", "    \"TREND_BARS\",\n", "    \"TREND_INBARS\",\n", "    \"TREND_INPOSR\", \"TREND_HLR\",\n", "    \"TREND_LEVEL\"\n", "]\n", "\n", "SEL_CONTEXT_FACTOR_NAMES = [\n", "  \"STDDEV_RNG\", \"SHORT_RANGE\",\n", "  \"FAST_QH_RSI\", \"FAST_QH_ZSCORE\", \"FAST_QH_DIRECT\",\n", "  \"FAST_QH_NATR\", \"FAST_QH_NATR_ZSCORE\", \"FAST_QH_NATR_DIRECT\",\n", "  \"FAST_QH_MOM\", \"FAST_QH_MOM_ZSCORE\", \"FAST_QH_MOM_DIRECT\",\n", "]\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["\n", "data_handler_config = {\n", "    \"start_time\": \"\",\n", "    \"end_time\": \"\",\n", "    \"instruments\": ['2020',],\n", "    \"data_loader\": {\n", "        \"class\": \"AHFDataLoader\",\n", "        \"module_path\": \"pyqlab.data.dataset.loader\",\n", "        \"kwargs\": {\n", "            \"direct\": \"L\",\n", "            \"model_name\": \"CONV1D\",\n", "            \"model_name_suff\": \"\",\n", "            \"model_path\": \"e:/lab/RoboQuant/pylab/model\",\n", "            \"data_path\": \"e:/featdata\",\n", "            \"sel_lf_names\": SEL_LONG_FACTOR_NAMES,\n", "            \"sel_sf_names\": SEL_SHORT_FACTOR_NAMES,\n", "            \"sel_ct_names\": SEL_CONTEXT_FACTOR_NAMES,\n", "        }\n", "    },\n", "}\n", "\n", "dataset_config = {\n", "    \"class\": \"AHFDatasetH\",\n", "    \"module_path\": \"pyqlab.data.dataset\",\n", "    \"kwargs\": {\n", "        \"handler\": {\n", "            \"class\": \"DataHandlerAHF\",\n", "            \"module_path\": \"pyqlab.data.dataset.handler\",\n", "            \"kwargs\": data_handler_config,\n", "        },\n", "        \"segments\": [\"train\", \"valid\"],\n", "        \"col_set\": [\"feature\", \"label\", \"encoded\"],\n", "    },\n", "}\n", "\n", "task = {\n", "    \"model\": {\n", "        \"class\": \"Conv1dModelPytorch\",\n", "        \"module_path\": \"pyqlab.contrib.model.pytorch_conv1d\",\n", "        \"kwargs\": {\n", "            \"loss\": \"binary\",\n", "            \"num_code\": 60,\n", "            \"num_input\": 56,\n", "            \"output_dim\": 1,\n", "            \"dropout\": 0.2,\n", "            \"lr\": 0.001,\n", "            \"lr_decay\": 0.99,\n", "            \"lr_decay_steps\": 100,\n", "            \"optimizer\": \"adam\",\n", "            \"batch_size\": 32,\n", "            \"GPU\": 0,\n", "            \"early_stop\": 50,\n", "            \"best_cond\": \"accuracy\", # or loss\n", "            \"layers\": (96, 64, 16), # (128, 256, 128, 64),\n", "        },\n", "    },\n", "}\n", "\n", "def plt_show(df, title=\"\"):\n", "    wdt = widgets.Output()\n", "    wdt.clear_output(wait=False)\n", "    with wdt:\n", "        ylim = [df.min().min(), df.quantile(0.95).max()]\n", "        ylim[0] -= (ylim[1] - ylim[0]) * 0.05\n", "        df.plot(color=['C1', 'C0'], style=['--', '-'], ylim=ylim, title=title)\n", "        plt.show()\n", "\n", "def display_result(evals_result):\n", "    for key, val in evals_result.items():\n", "        if not isinstance(val, dict):\n", "            plt_show(pd.DataFrame(evals_result), key)\n", "            break\n", "        else:\n", "            plt_show(pd.DataFrame(val), key)\n", "            \n", "pfs = {\n", "    '5HF': ['2020',],\n", "}\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"ename": "AssertionError", "evalue": "load raw data is empty.", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAssertionError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[4], line 5\u001b[0m\n\u001b[0;32m      2\u001b[0m \u001b[39m# 优化: datahandler可以重复使用，提高运行效率\u001b[39;00m\n\u001b[0;32m      3\u001b[0m \u001b[39m# prepare processed data in memory.\u001b[39;00m\n\u001b[0;32m      4\u001b[0m data_handler_config[\u001b[39m\"\u001b[39m\u001b[39mdata_loader\u001b[39m\u001b[39m\"\u001b[39m][\u001b[39m\"\u001b[39m\u001b[39mkwargs\u001b[39m\u001b[39m\"\u001b[39m][\u001b[39m\"\u001b[39m\u001b[39mdirect\u001b[39m\u001b[39m\"\u001b[39m] \u001b[39m=\u001b[39m \u001b[39m\"\u001b[39m\u001b[39mlong\u001b[39m\u001b[39m\"\u001b[39m\n\u001b[1;32m----> 5\u001b[0m hd_long: DataHandlerAHF \u001b[39m=\u001b[39m init_instance_by_config(dataset_config[\u001b[39m\"\u001b[39;49m\u001b[39mkwargs\u001b[39;49m\u001b[39m\"\u001b[39;49m][\u001b[39m\"\u001b[39;49m\u001b[39mhandler\u001b[39;49m\u001b[39m\"\u001b[39;49m])\n\u001b[0;32m      6\u001b[0m data_handler_config[\u001b[39m\"\u001b[39m\u001b[39mdata_loader\u001b[39m\u001b[39m\"\u001b[39m][\u001b[39m\"\u001b[39m\u001b[39mkwargs\u001b[39m\u001b[39m\"\u001b[39m][\u001b[39m\"\u001b[39m\u001b[39mdirect\u001b[39m\u001b[39m\"\u001b[39m] \u001b[39m=\u001b[39m \u001b[39m\"\u001b[39m\u001b[39mshort\u001b[39m\u001b[39m\"\u001b[39m\n\u001b[0;32m      7\u001b[0m hd_short: DataHandlerAHF \u001b[39m=\u001b[39m init_instance_by_config(dataset_config[\u001b[39m\"\u001b[39m\u001b[39mkwargs\u001b[39m\u001b[39m\"\u001b[39m][\u001b[39m\"\u001b[39m\u001b[39mhandler\u001b[39m\u001b[39m\"\u001b[39m])\n", "File \u001b[1;32md:\\Miniconda3\\lib\\site-packages\\qlib\\utils\\__init__.py:452\u001b[0m, in \u001b[0;36minit_instance_by_config\u001b[1;34m(config, default_module, accept_types, try_kwargs, **kwargs)\u001b[0m\n\u001b[0;32m    449\u001b[0m klass, cls_kwargs \u001b[39m=\u001b[39m get_callable_kwargs(config, default_module\u001b[39m=\u001b[39mdefault_module)\n\u001b[0;32m    451\u001b[0m \u001b[39mtry\u001b[39;00m:\n\u001b[1;32m--> 452\u001b[0m     \u001b[39mreturn\u001b[39;00m klass(\u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49mcls_kwargs, \u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49mtry_kwargs, \u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49mkwargs)\n\u001b[0;32m    453\u001b[0m \u001b[39mexcept\u001b[39;00m (\u001b[39mTypeError\u001b[39;00m,):\n\u001b[0;32m    454\u001b[0m     \u001b[39m# TypeError for handling errors like\u001b[39;00m\n\u001b[0;32m    455\u001b[0m     \u001b[39m# 1: `XXX() got multiple values for keyword argument 'YYY'`\u001b[39;00m\n\u001b[0;32m    456\u001b[0m     \u001b[39m# 2: `XXX() got an unexpected keyword argument 'YYY'\u001b[39;00m\n\u001b[0;32m    457\u001b[0m     \u001b[39mreturn\u001b[39;00m klass(\u001b[39m*\u001b[39m\u001b[39m*\u001b[39mcls_kwargs, \u001b[39m*\u001b[39m\u001b[39m*\u001b[39mkwargs)\n", "File \u001b[1;32me:\\lab\\RoboQuant\\pylab\\pyqlab\\data\\dataset\\handler.py:200\u001b[0m, in \u001b[0;36mDataHandlerAHF.__init__\u001b[1;34m(self, instruments, start_time, end_time, data_loader, valid_fold, is_normal, init_data, fetch_orig)\u001b[0m\n\u001b[0;32m    197\u001b[0m     \u001b[39mraise\u001b[39;00m \u001b[39mValueError\u001b[39;00m(\u001b[39m\"\u001b[39m\u001b[39mDataHandlerAF init parameter data_loader is None\u001b[39m\u001b[39m\"\u001b[39m)\n\u001b[0;32m    198\u001b[0m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mlogger \u001b[39m=\u001b[39m get_module_logger(\u001b[39m\"\u001b[39m\u001b[39mDataHandlerAF\u001b[39m\u001b[39m\"\u001b[39m)\n\u001b[1;32m--> 200\u001b[0m \u001b[39msuper\u001b[39;49m()\u001b[39m.\u001b[39;49m\u001b[39m__init__\u001b[39;49m(\n\u001b[0;32m    201\u001b[0m     instruments\u001b[39m=\u001b[39;49minstruments,\n\u001b[0;32m    202\u001b[0m     start_time\u001b[39m=\u001b[39;49mstart_time,\n\u001b[0;32m    203\u001b[0m     end_time\u001b[39m=\u001b[39;49mend_time,\n\u001b[0;32m    204\u001b[0m     data_loader\u001b[39m=\u001b[39;49mdata_loader,\n\u001b[0;32m    205\u001b[0m )\n", "File \u001b[1;32md:\\Miniconda3\\lib\\site-packages\\qlib\\data\\dataset\\handler.py:97\u001b[0m, in \u001b[0;36mDataHandler.__init__\u001b[1;34m(self, instruments, start_time, end_time, data_loader, init_data, fetch_orig)\u001b[0m\n\u001b[0;32m     95\u001b[0m \u001b[39mif\u001b[39;00m init_data:\n\u001b[0;32m     96\u001b[0m     \u001b[39mwith\u001b[39;00m TimeInspector\u001b[39m.\u001b[39mlogt(\u001b[39m\"\u001b[39m\u001b[39mInit data\u001b[39m\u001b[39m\"\u001b[39m):\n\u001b[1;32m---> 97\u001b[0m         \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49msetup_data()\n\u001b[0;32m     98\u001b[0m \u001b[39msuper\u001b[39m()\u001b[39m.\u001b[39m\u001b[39m__init__\u001b[39m()\n", "File \u001b[1;32me:\\lab\\RoboQuant\\pylab\\pyqlab\\data\\dataset\\handler.py:224\u001b[0m, in \u001b[0;36mDataHandlerAHF.setup_data\u001b[1;34m(self, enable_cache)\u001b[0m\n\u001b[0;32m    219\u001b[0m     \u001b[39mreturn\u001b[39;00m\n\u001b[0;32m    222\u001b[0m \u001b[39mwith\u001b[39;00m TimeInspector\u001b[39m.\u001b[39mlogt(\u001b[39m\"\u001b[39m\u001b[39mLoading data\u001b[39m\u001b[39m\"\u001b[39m):\n\u001b[0;32m    223\u001b[0m     \u001b[39m# 加载原始数据\u001b[39;00m\n\u001b[1;32m--> 224\u001b[0m     lf_df, sf_df, ct_df \u001b[39m=\u001b[39m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mdata_loader\u001b[39m.\u001b[39;49mload(\u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49minstruments, \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mstart_time, \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mend_time)\n\u001b[0;32m    225\u001b[0m     \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mlogger\u001b[39m.\u001b[39minfo(\u001b[39mf\u001b[39m\u001b[39m\"\u001b[39m\u001b[39m\\n\u001b[39;00m\u001b[39m============================\u001b[39m\u001b[39m\\n\u001b[39;00m\u001b[39m\\n\u001b[39;00m\u001b[39msf\u001b[39m\u001b[39m{\u001b[39;00msf_df\u001b[39m.\u001b[39mshape\u001b[39m}\u001b[39;00m\u001b[39m lf\u001b[39m\u001b[39m{\u001b[39;00mlf_df\u001b[39m.\u001b[39mshape\u001b[39m}\u001b[39;00m\u001b[39m ct\u001b[39m\u001b[39m{\u001b[39;00mct_df\u001b[39m.\u001b[39mshape\u001b[39m}\u001b[39;00m\u001b[39m\\n\u001b[39;00m\u001b[39m\\n\u001b[39;00m\u001b[39m============================\u001b[39m\u001b[39m\"\u001b[39m)\n\u001b[0;32m    227\u001b[0m     \u001b[39m# todo： 当前未做col_sel选择，直接合并特征数据\u001b[39;00m\n", "File \u001b[1;32me:\\lab\\RoboQuant\\pylab\\pyqlab\\data\\dataset\\loader.py:320\u001b[0m, in \u001b[0;36mAHFDataLoader.load\u001b[1;34m(self, instruments, start_time, end_time)\u001b[0m\n\u001b[0;32m    317\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mload\u001b[39m(\u001b[39mself\u001b[39m, instruments, start_time\u001b[39m=\u001b[39m\u001b[39mNone\u001b[39;00m, end_time\u001b[39m=\u001b[39m\u001b[39mNone\u001b[39;00m) \u001b[39m-\u001b[39m\u001b[39m>\u001b[39m pd\u001b[39m.\u001b[39mDataFrame:\n\u001b[0;32m    318\u001b[0m     \u001b[39m# TODO: ...\u001b[39;00m\n\u001b[0;32m    319\u001b[0m     \u001b[39mself\u001b[39m\u001b[39m.\u001b[39myears\u001b[39m=\u001b[39minstruments\n\u001b[1;32m--> 320\u001b[0m     \u001b[39mreturn\u001b[39;00m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mtransform()\n", "File \u001b[1;32me:\\lab\\RoboQuant\\pylab\\pyqlab\\data\\dataset\\loader.py:308\u001b[0m, in \u001b[0;36mAHFDataLoader.transform\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    307\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mtransform\u001b[39m(\u001b[39mself\u001b[39m):\n\u001b[1;32m--> 308\u001b[0m     \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_prepare_data()\n\u001b[0;32m    309\u001b[0m     \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_label_encode()\n\u001b[0;32m    310\u001b[0m     \u001b[39m# self._get_folds()\u001b[39;00m\n", "File \u001b[1;32me:\\lab\\RoboQuant\\pylab\\pyqlab\\data\\dataset\\loader.py:235\u001b[0m, in \u001b[0;36mAHFDataLoader._prepare_data\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    233\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39m_prepare_data\u001b[39m(\u001b[39mself\u001b[39m):\n\u001b[0;32m    234\u001b[0m     \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_load_data_from_file()\n\u001b[1;32m--> 235\u001b[0m     \u001b[39massert\u001b[39;00m \u001b[39mnot\u001b[39;00m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39msf_df\u001b[39m.\u001b[39mempty, \u001b[39m\"\u001b[39m\u001b[39mload raw data is empty.\u001b[39m\u001b[39m\"\u001b[39m\n\u001b[0;32m    237\u001b[0m     \u001b[39m# 清除不需要的数据\u001b[39;00m\n\u001b[0;32m    238\u001b[0m     \u001b[39mif\u001b[39;00m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39monly_trading_code:\n", "\u001b[1;31mAssertionError\u001b[0m: load raw data is empty."]}], "source": ["from pyqlab.data.dataset.handler import DataHandlerAHF\n", "# 优化: datahandler可以重复使用，提高运行效率\n", "# prepare processed data in memory.\n", "data_handler_config[\"data_loader\"][\"kwargs\"][\"direct\"] = \"long\"\n", "hd_long: DataHandlerAHF = init_instance_by_config(dataset_config[\"kwargs\"][\"handler\"])\n", "data_handler_config[\"data_loader\"][\"kwargs\"][\"direct\"] = \"short\"\n", "hd_short: DataHandlerAHF = init_instance_by_config(dataset_config[\"kwargs\"][\"handler\"])\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## ConvNet"]}, {"cell_type": "code", "execution_count": 116, "metadata": {}, "outputs": [], "source": ["# Copyright (c) Microsoft Corporation.\n", "# Licensed under the MIT License.\n", "\n", "from __future__ import division\n", "from __future__ import print_function\n", "\n", "import numpy as np\n", "import pandas as pd\n", "from typing import Text, Union\n", "import copy\n", "from qlib.utils import get_or_create_path\n", "from qlib.log import get_module_logger, set_log_with_config\n", "\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "# from torch.nn.utils import weight_norm\n", "from torch.optim import lr_scheduler\n", "import torch.nn.functional as F\n", "from torch.utils.data import DataLoader, TensorDataset, random_split\n", "\n", "# from .pytorch_utils import count_parameters\n", "from qlib.model.base import Model\n", "from qlib.data.dataset import DatasetH\n", "from qlib.data.dataset.handler import DataHandlerLP\n", "from pyqlab.const import ALL_FACTOR_NAMES, TWO_VAL_FACTOR_NAMES\n", "\n", "SEL_SHORT_FACTOR_NAMES = [ # Fast period factor\n", "    \"MACD\", \"MACD_DIFF\", \"MACD_DEA\", \"MOM\", \"RSI\",\n", "\n", "    \"LR_SLOPE_FAST\", \"LR_SLOPE_MIDD\", \"LR_SLOPE_SLOW\",\n", "    \"LR_SLOPE_FAST_THRESHOLD\", \"LR_SLOPE_SLOW_THRESHOLD\",\n", "\n", "    \"SQUEEZE_ZERO_BARS\", \n", "    \"SQUEEZE_GAP\", \"SQUEEZE_GAP_FAST\", \"SQUEEZE_GAP_SLOW\",\n", "    \"SQUEEZE_GAP_THRESHOLD\", \"SQUEEZE_NARROW_BARS\",\n", "\n", "    \"BAND_POSITION\", \"BAND_WIDTH\",\n", "    \"BAND_EXPAND\", \"<PERSON>ND_GRADIENT\", \"BAND_GRADIENT_THRESHOLD\", \"BAND_GAP\",\n", "\n", "    \"TL_FAST\", \"TL_SLOW\", \"TL_THRESHOLD\",\n", "\n", "    \"TREND_VALUE\",\n", "    \"TREND_BARS\",\n", "    \"TREND_INBARS\",\n", "    \"TREND_INPOSR\", \"TREND_HLR\",\n", "    \"TREND_LEVEL\"\n", "]\n", "\n", "LOGGING_CONFIG = {\n", "    \"version\": 1,\n", "    \"formatters\": {\n", "        \"default\": {\n", "            # 'format':'%(asctime)s %(filename)s %(lineno)s %(levelname)s %(message)s',\n", "            'format':'%(asctime)s %(levelname)s %(message)s',\n", "        },\n", "        \"plain\": {\n", "            \"format\": \"%(message)s\",\n", "        },\n", "    },\n", "    \"handlers\": {\n", "        \"console\": {\n", "            \"class\": \"logging.StreamHandler\",\n", "            \"level\": \"INFO\",\n", "            \"formatter\": \"default\",\n", "        },\n", "        \"console_plain\": {\n", "            \"class\": \"logging.StreamHandler\",\n", "            \"level\": \"INFO\",\n", "            \"formatter\": \"plain\"\n", "        },\n", "        \"file\":{\n", "            \"class\": \"logging.FileHandler\",\n", "            \"level\":20,\n", "            \"filename\": \"./log.txt\",\n", "            \"formatter\": \"default\",\n", "        }\n", "    },\n", "    \"loggers\": {\n", "        \"console_logger\": {\n", "            \"handlers\": [\"console\"],\n", "            \"level\": \"INFO\",\n", "            \"propagate\": <PERSON><PERSON><PERSON>,\n", "        },\n", "        \"console_plain_logger\": {\n", "            \"handlers\": [\"console_plain\"],\n", "            \"level\": \"DEBUG\",\n", "            \"propagate\": <PERSON><PERSON><PERSON>,\n", "        },\n", "        \"file_logger\":{\n", "            \"handlers\": [\"file\"],\n", "            \"level\": \"INFO\",\n", "            \"propagate\": <PERSON><PERSON><PERSON>,\n", "        }\n", "    },\n", "    \"disable_existing_loggers\": True,\n", "}\n"]}, {"cell_type": "code", "execution_count": 156, "metadata": {}, "outputs": [], "source": ["\n", "class Conv1dModelPytorch(Model):\n", "    \"\"\"MLP Model\n", "\n", "    Parameters\n", "    ----------\n", "    d_feat : int\n", "        input dimension for each time step\n", "    n_chans: int\n", "        number of channels\n", "    metric: str\n", "        the evaluate metric used in early stop\n", "    optimizer : str\n", "        optimizer name\n", "    GPU : str\n", "        the GPU ID(s) used for training\n", "    \"\"\"\n", "\n", "    def __init__(\n", "        self,\n", "        num_code=50,\n", "        num_input=95, #87\n", "        layers=(96, 96, 64),\n", "        dropout=0.5,\n", "        n_epochs=50,\n", "        lr=0.0001,\n", "        metric=\"\",\n", "        batch_size=64,\n", "        early_stop=30,\n", "        loss=\"mse\",\n", "        optimizer=\"adam\",\n", "        GPU=0,\n", "        seed=None,\n", "        best_cond=\"loss\",\n", "        **kwargs\n", "    ):\n", "        # Set logger.\n", "        self.logger = get_module_logger(\"MLP\")\n", "        # set_log_with_config(LOGGING_CONFIG)\n", "        self.logger.info(\"=======================================\")\n", "\n", "        # set hyper-parameters.\n", "        self.num_code = num_code\n", "        self.num_input = num_input\n", "        self.layers = layers\n", "        self.dropout = dropout\n", "        self.n_epochs = n_epochs\n", "        self.lr = lr\n", "        self.metric = metric\n", "        self.batch_size = batch_size\n", "        self.early_stop = early_stop\n", "        self.optimizer = optimizer.lower()\n", "        self.loss = loss\n", "        self.device = torch.device(\"cuda:%d\" % (GPU) if torch.cuda.is_available() and GPU >= 0 else \"cpu\")\n", "        self.seed = seed\n", "        self.best_cond = best_cond\n", "\n", "        self.logger.info(\n", "            \"MLP parameters setting:\"\n", "            \"\\nnum_code : {}\"\n", "            \"\\nnum_input : {}\"\n", "            \"\\nlayers : {}\"\n", "            \"\\ndropout : {}\"\n", "            \"\\nn_epochs : {}\"\n", "            \"\\nlr : {}\"\n", "            \"\\nmetric : {}\"\n", "            \"\\nbatch_size : {}\"\n", "            \"\\nearly_stop : {}\"\n", "            \"\\noptimizer : {}\"\n", "            \"\\nloss_type : {}\"\n", "            \"\\nvisible_GPU : {}\"\n", "            \"\\nuse_GPU : {}\"\n", "            \"\\nseed : {}\"\n", "            \"\\nbest_cond : {}\".format(\n", "                num_code,\n", "                num_input,\n", "                layers,\n", "                dropout,\n", "                n_epochs,\n", "                lr,\n", "                metric,\n", "                batch_size,\n", "                early_stop,\n", "                optimizer.lower(),\n", "                loss,\n", "                GPU,\n", "                self.use_gpu,\n", "                seed,\n", "                best_cond,\n", "            )\n", "        )\n", "\n", "        if self.seed is not None:\n", "            np.random.seed(self.seed)\n", "            torch.manual_seed(self.seed)\n", "\n", "        # TODO: batch_norm drop_out\n", "        # self.mlp_model = MLPModel(\n", "        self.conv_model = Conv1DNet(\n", "            # num_code=self.num_code,\n", "            # num_input=self.num_input,\n", "            # output_size=1,\n", "            # dropout=self.dropout,\n", "            # layers=self.layers,\n", "        )\n", "        self.logger.info(\"model:\\n{:}\".format(self.conv_model))\n", "        # self.logger.info(\"model size: {:.4f} MB\".format(count_parameters(self.conv_model)))\n", "\n", "        if optimizer.lower() == \"adam\":\n", "            self.train_optimizer = optim.Adam(self.conv_model.parameters(), lr=self.lr)\n", "        elif optimizer.lower() == \"gd\":\n", "            self.train_optimizer = optim.SGD(self.conv_model.parameters(), lr=self.lr)\n", "        else:\n", "            raise NotImplementedError(\"optimizer {} is not supported!\".format(optimizer))\n", "\n", "        self.fitted = False\n", "        self.conv_model.to(self.device)\n", "\n", "    @property\n", "    def use_gpu(self):\n", "        return self.device != torch.device(\"cpu\")\n", "\n", "    def mse(self, pred, label):\n", "        loss = (pred - label) ** 2\n", "        return torch.mean(loss)\n", "\n", "    def loss_fn(self, pred, label):\n", "        # mask = ~torch.isnan(label)\n", "        # if self.loss == \"mse\":\n", "        #     return self.mse(pred[mask], label[mask])\n", "        if self.loss == \"mse\":\n", "            sqr_loss = torch.mul(pred - label, pred - label)\n", "            loss = sqr_loss.mean()\n", "            return loss\n", "        elif self.loss == \"binary\":\n", "            loss = nn.<PERSON><PERSON><PERSON>()\n", "            return loss(pred, label)\n", "        raise ValueError(\"unknown loss `%s`\" % self.loss)\n", "\n", "    def accuracy(self, pred, label):\n", "        if self.use_gpu:\n", "            preds = (pred>0.5).type(torch.IntTensor).cuda()\n", "        else:\n", "            preds = (pred>0.5).type(torch.IntTensor)\n", "        return (preds == label).float().mean()\n", "\n", "    def get_factor_cols(self):\n", "        \"\"\"\n", "        因子列名称\n", "        \"\"\"\n", "        col_names = ['code', 'date', 'change']\n", "        for name in SEL_SHORT_FACTOR_NAMES:\n", "            if name in TWO_VAL_FACTOR_NAMES:\n", "                col_names.append(f\"{name}_1\")\n", "                col_names.append(f\"{name}_2\")\n", "            else:\n", "                col_names.append(f\"{name}_2\")\n", "        return col_names\n", "    \n", "    def preprocess(self):\n", "        self.logger.info(\"preprocess data...\")\n", "        sdf = pd.read_parquet('e:/featdata/sfd.parquet', engine='fastparquet')\n", "        sdf.sort_values(by=['code', 'date'], ascending=True, inplace=True)\n", "        sdf['change'] = sdf['change'].astype(np.float32)\n", "        sdf['change'] = sdf['change'].shift(-1)\n", "        sdf = sdf[:-1]\n", "        sdf['RSI_2'] = sdf['RSI_2'].astype(np.float32)\n", "        sdf = sdf[sdf['RSI_2'] != 0.0]\n", "        # for code in sdf.code.unique():\n", "        #     df = sdf[(sdf['code'] == code)]\n", "\n", "        col_names = self.get_factor_cols()\n", "        df = sdf[col_names]\n", "        df['label'] = df.loc[:, 'change'].apply(lambda x: 1 if x > 0.002 else 0)\n", "\n", "        df.drop(['code', 'date', 'change'], axis=1, inplace=True)\n", "        df = df.astype(np.float32)\n", "        data = df.values\n", "        data1 = data[:, :-1]\n", "        data2 = data[:, -1]\n", "        win_size = 5\n", "        step_len = 3\n", "        x_data = [data1[i:i+win_size] for i in range(0, len(data1) - win_size, step_len)]\n", "        y_data = [data2[i+win_size] for i in range(0, len(data2) - win_size, step_len)]\n", "\n", "        dataset = TensorDataset(torch.tensor(x_data), torch.tensor(y_data))\n", "        print(\"dataset_size: {}\".format(len(dataset)))\n", "\n", "    def loader(self, dataset, batch_size=32, shuffle=True):\n", "\n", "        # 将数据集划分为训练集和验证集\n", "        train_size = int(0.8 * len(dataset))\n", "        val_size = len(dataset) - train_size\n", "        train_dataset, val_dataset = random_split(dataset, [train_size, val_size])\n", "        print(\"train_size: {}, val_size: {}\".format(train_size, val_size))\n", "        \n", "        train_dataloader = DataLoader(train_dataset, batch_size=batch_size, shuffle=shuffle)\n", "        val_dataloader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)\n", "        return train_dataloader, val_dataloader\n", "    \n", "    def train_epoch(self, dataloader):\n", "\n", "        self.conv_model.train()\n", "        running_loss = 0.0\n", "\n", "        for inputs, targets in dataloader:\n", "            inputs, targets = inputs.to(self.device), targets.to(self.device)\n", "\n", "            # optimizer.zero_grad()\n", "            self.train_optimizer.zero_grad()\n", "            outputs = self.conv_model(inputs)\n", "            # loss = criterion(outputs.view(-1), targets.float())\n", "            loss = self.loss_fn(outputs.view(-1), targets.float())\n", "            loss.backward()\n", "            # optimizer.step()\n", "            # torch.nn.utils.clip_grad_value_(self.conv_model.parameters(), 3.0)\n", "            self.train_optimizer.step()\n", "\n", "            running_loss += loss.item()\n", "\n", "        return running_loss / len(dataloader)\n", "\n", "\n", "    def test_epoch(self, dataloader):\n", "\n", "        self.conv_model.eval()\n", "        losses = []\n", "        # accs = []\n", "        correct = 0\n", "        total = 0\n", "        with torch.no_grad():\n", "            for inputs, targets in dataloader:\n", "                inputs, targets = inputs.to(self.device), targets.to(self.device)\n", "\n", "                outputs = self.conv_model(inputs)\n", "                # loss = criterion(outputs.view(-1), targets.float())\n", "                loss = self.loss_fn(outputs.view(-1), targets.float())\n", "                losses.append(loss.item())\n", "\n", "                correct += ((outputs.view(-1)>0.5).type(torch.IntTensor).to(self.device) == targets).sum().item()\n", "                total += targets.size(0)\n", "\n", "                # acc = self.accuracy(pred, label)\n", "                # accs.append(acc.item())\n", "        epoch_acc = correct / total\n", "        return np.mean(losses), epoch_acc # np.mean(accs)\n", "    \n", "\n", "    def fit(\n", "        self,\n", "        evals_result=dict(),\n", "        save_path=None,\n", "    ):\n", "        dataset = self.preprocess()\n", "        train_dl, val_dl = self.loader(dataset, 32)\n", "\n", "        save_path = get_or_create_path(save_path)\n", "        stop_steps = 0\n", "        train_loss = 0\n", "        best_loss = np.inf\n", "        best_acc = -np.inf\n", "        best_epoch = 0\n", "        evals_result[\"loss\"] = {}\n", "        evals_result[\"accuracy\"] = {}\n", "        evals_result[\"loss\"][\"train\"] = []\n", "        evals_result[\"loss\"][\"valid\"] = []\n", "        evals_result[\"accuracy\"][\"train\"] = []\n", "        evals_result[\"accuracy\"][\"valid\"] = []\n", "\n", "        # train\n", "        self.logger.info(\"training...\")\n", "        self.fitted = True\n", "\n", "        # 学习速率衰减设置\n", "        exp_lr_scheduler = lr_scheduler.StepLR(self.train_optimizer, step_size=30, gamma=0.5) # 按步数\n", "        # exp_lr_scheduler = lr_scheduler.MultiStepLR(opt, milestones=[50, 100, 150], gamma=0.1) # 按里程碑\n", "        # exp_lr_scheduler = lr_scheduler.ExponentialLR(opt, gamma=0.1) # 按系数每步\n", "\n", "        for step in range(self.n_epochs):\n", "            self.logger.info(\"Epoch %d:\", step)\n", "            # self.logger.info(\"training...\")\n", "            self.train_epoch(train_dl)\n", "            exp_lr_scheduler.step()\n", "\n", "            # self.logger.info(\"evaluating...\")\n", "            train_loss, train_acc = self.test_epoch(train_dl)\n", "            val_loss, val_acc = self.test_epoch(val_dl)\n", "            print(\"loss: train %.3f, valid %.3f\" % (train_loss, val_loss))\n", "            print(\"accuracy: train %.3f, valid %.3f\" % (train_acc, val_acc))\n", "            self.logger.info(\"loss: train %.3f, valid %.3f\" % (train_loss, val_loss))\n", "            self.logger.info(\"accuracy: train %.3f, valid %.3f\" % (train_acc, val_acc))\n", "            evals_result[\"loss\"][\"train\"].append(train_loss)\n", "            evals_result[\"loss\"][\"valid\"].append(val_loss)\n", "            evals_result[\"accuracy\"][\"train\"].append(train_acc)\n", "            evals_result[\"accuracy\"][\"valid\"].append(val_acc)\n", "\n", "            # TODO: best model cond.\n", "            if self.best_cond == \"loss\":\n", "                if val_loss < best_loss:\n", "                    best_loss = val_loss\n", "\n", "                    if val_acc > best_acc:\n", "                        best_acc = val_acc\n", "\n", "                    stop_steps = 0\n", "                    best_epoch = step\n", "                    best_param = copy.deepcopy(self.conv_model.state_dict())\n", "                else:\n", "                    stop_steps += 1\n", "                    if stop_steps >= self.early_stop:\n", "                        self.logger.info(\"early stop\")\n", "                        break\n", "            else:\n", "                if val_acc > best_acc:\n", "                    best_acc = val_acc\n", "\n", "                    if val_loss < best_loss:\n", "                        best_loss = val_loss\n", "\n", "                    stop_steps = 0\n", "                    best_epoch = step\n", "                    best_param = copy.deepcopy(self.mlp_model.state_dict())\n", "                else:\n", "                    stop_steps += 1\n", "                    if stop_steps >= self.early_stop:\n", "                        self.logger.info(\"early stop\")\n", "                        break\n", "\n", "        self.logger.info(\"best epoch: %d loss: %.6lf accuracy: %.3lf\" % (best_epoch, best_loss, best_acc))\n", "        self.conv_model.load_state_dict(best_param)\n", "        # save model\n", "        # torch.save(best_param, save_path)\n", "        model = self.conv_model.cpu()\n", "        model.eval() # 如果要使用，要调用eval()表明运行模式\n", "        sm = torch.jit.script(model)\n", "        sm.save(save_path)\n", "\n", "        if self.use_gpu:\n", "            torch.cuda.empty_cache()\n", "\n", "        return best_epoch, best_loss, best_acc\n", "\n", "    def predict(self, dataset: DatasetH, segment: Union[Text, slice] = \"test\"):\n", "        if not self.fitted:\n", "            raise ValueError(\"model is not fitted yet!\")\n", "\n", "        x_test = dataset.prepare(segment, col_set=\"feature\", data_key=DataHandlerLP.DK_I)\n", "        # x_test = torch.from_numpy(x_test_pd.values).float().to(self.device)\n", "        index = x_test.index\n", "        self.conv_model.eval()\n", "        x_values = x_test.values\n", "        sample_num = x_values.shape[0]\n", "        preds = []\n", "\n", "        for begin in range(sample_num)[:: self.batch_size]:\n", "\n", "            if sample_num - begin < self.batch_size:\n", "                end = sample_num\n", "            else:\n", "                end = begin + self.batch_size\n", "\n", "            x_batch = torch.from_numpy(x_values[begin:end]).float().to(self.device)\n", "\n", "            with torch.no_grad():\n", "                pred = self.mlp_model(x_batch).detach().cpu().numpy()\n", "\n", "            preds.append(pred)\n", "\n", "        return pd.Series(np.concatenate(preds), index=index)\n", "\n", "\n", "\n", "class Conv1DNet(nn.Module):\n", "    def __init__(self):\n", "        super(Conv1DNet, self).__init__()\n", "        self.conv1 = nn.Conv1d(5, 16, kernel_size=3, stride=1, padding=1)\n", "        self.conv2 = nn.Conv1d(16, 32, kernel_size=3, stride=1, padding=1)\n", "        self.fc1 = nn.<PERSON>ar(32 * 45, 64)\n", "        self.fc2 = nn.<PERSON>ar(64, 1)\n", "\n", "    def forward(self, x):\n", "        x = <PERSON>.relu(self.conv1(x))\n", "        x = <PERSON>.relu(self.conv2(x))\n", "        x = x.view(-1, 32 * 45)\n", "        x = F.relu(self.fc1(x))\n", "        x = self.fc2(x)\n", "        x = torch.sigmoid(x)\n", "        return x\n", "\n", "class Net(nn.Module):\n", "    '''\n", "    layers=(256, 512, 768, 512, 256, 128, 64)\n", "    '''\n", "    def __init__(self, num_code, num_input, dropout=0.05, output_dim=1, layers=(96, 128, 96, 64), loss=\"binary\"):\n", "        super(Net, self).__init__()\n", "        self.code_embeddings = nn.Embedding(num_embeddings=num_code, embedding_dim=1)\n", "        num_input+=1\n", "        layers = [num_input] + list(layers)\n", "        dnn_layers = []\n", "        drop_input = nn.Dropout(dropout)\n", "        dnn_layers.append(drop_input)\n", "        for i, (num_input, hidden_units) in enumerate(zip(layers[:-1], layers[1:])):\n", "            fc = nn.Linear(num_input, hidden_units)\n", "            activation = nn.LeakyReLU(negative_slope=0.1, inplace=False)\n", "            bn = nn.BatchNorm1d(hidden_units)\n", "            seq = nn.Sequential(fc, bn, activation)\n", "            dnn_layers.append(seq)\n", "        drop_input = nn.Dropout(dropout)\n", "        dnn_layers.append(drop_input)\n", "        fc = nn.Linear(hidden_units, output_dim)\n", "        dnn_layers.append(fc)\n", "        # add sigmoid layer\n", "        dnn_layers.append(nn.Sigmo<PERSON>())\n", "        # optimizer\n", "        self.dnn_layers = nn.ModuleList(dnn_layers)\n", "        self._weight_init()\n", "\n", "    def _weight_init(self):\n", "        for m in self.modules():\n", "            if isinstance(m, nn.Linear):\n", "                nn.init.kaiming_normal_(m.weight, a=0.1, mode=\"fan_in\", nonlinearity=\"leaky_relu\")\n", "\n", "    def forward(self, code_ids, x):\n", "        # 加入code向量\n", "        embedded_code_ids = self.code_embeddings(code_ids)\n", "        cur_output = torch.cat([x, embedded_code_ids], dim=1)\n", "        for i, now_layer in enumerate(self.dnn_layers):\n", "            cur_output = now_layer(cur_output)\n", "        return cur_output.view(-1)\n"]}, {"cell_type": "code", "execution_count": 157, "metadata": {}, "outputs": [], "source": ["model = Conv1dModelPytorch()\n"]}, {"cell_type": "code", "execution_count": 158, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_16392\\2965532364.py:173: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df['label'] = df.loc[:, 'change'].apply(lambda x: 1 if x > 0.002 else 0)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_16392\\2965532364.py:175: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df.drop(['code', 'date', 'change'], axis=1, inplace=True)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["dataset_size: 14362\n", "train_size: 11489, val_size: 2873\n", "loss: train 0.153, valid 0.152\n", "accuracy: train 0.769, valid 0.767\n", "loss: train 0.129, valid 0.134\n", "accuracy: train 0.824, valid 0.815\n", "loss: train 0.121, valid 0.124\n", "accuracy: train 0.837, valid 0.832\n", "loss: train 0.116, valid 0.120\n", "accuracy: train 0.838, valid 0.833\n", "loss: train 0.111, valid 0.114\n", "accuracy: train 0.848, valid 0.844\n", "loss: train 0.108, valid 0.114\n", "accuracy: train 0.850, valid 0.840\n", "loss: train 0.107, valid 0.115\n", "accuracy: train 0.853, valid 0.845\n", "loss: train 0.126, valid 0.133\n", "accuracy: train 0.822, valid 0.818\n", "loss: train 0.104, valid 0.115\n", "accuracy: train 0.858, valid 0.838\n", "loss: train 0.105, valid 0.115\n", "accuracy: train 0.856, valid 0.842\n", "loss: train 0.113, valid 0.123\n", "accuracy: train 0.839, valid 0.833\n", "loss: train 0.097, valid 0.109\n", "accuracy: train 0.872, valid 0.844\n", "loss: train 0.104, valid 0.117\n", "accuracy: train 0.858, valid 0.832\n", "loss: train 0.101, valid 0.114\n", "accuracy: train 0.861, valid 0.845\n", "loss: train 0.094, valid 0.108\n", "accuracy: train 0.875, valid 0.854\n", "loss: train 0.092, valid 0.107\n", "accuracy: train 0.877, valid 0.851\n", "loss: train 0.093, valid 0.109\n", "accuracy: train 0.874, valid 0.851\n", "loss: train 0.097, valid 0.113\n", "accuracy: train 0.866, valid 0.841\n", "loss: train 0.104, valid 0.118\n", "accuracy: train 0.856, valid 0.840\n", "loss: train 0.093, valid 0.109\n", "accuracy: train 0.873, valid 0.851\n", "loss: train 0.091, valid 0.107\n", "accuracy: train 0.879, valid 0.852\n", "loss: train 0.090, valid 0.109\n", "accuracy: train 0.880, valid 0.847\n", "loss: train 0.093, valid 0.111\n", "accuracy: train 0.876, valid 0.841\n", "loss: train 0.088, valid 0.107\n", "accuracy: train 0.884, valid 0.853\n", "loss: train 0.091, valid 0.110\n", "accuracy: train 0.882, valid 0.845\n", "loss: train 0.101, valid 0.121\n", "accuracy: train 0.858, valid 0.824\n", "loss: train 0.088, valid 0.107\n", "accuracy: train 0.887, valid 0.849\n", "loss: train 0.096, valid 0.115\n", "accuracy: train 0.868, valid 0.832\n", "loss: train 0.087, valid 0.106\n", "accuracy: train 0.884, valid 0.854\n", "loss: train 0.086, valid 0.108\n", "accuracy: train 0.888, valid 0.849\n", "loss: train 0.085, valid 0.107\n", "accuracy: train 0.890, valid 0.845\n", "loss: train 0.084, valid 0.107\n", "accuracy: train 0.889, valid 0.847\n", "loss: train 0.085, valid 0.107\n", "accuracy: train 0.890, valid 0.847\n", "loss: train 0.088, valid 0.110\n", "accuracy: train 0.888, valid 0.840\n", "loss: train 0.083, valid 0.107\n", "accuracy: train 0.893, valid 0.847\n", "loss: train 0.082, valid 0.106\n", "accuracy: train 0.894, valid 0.846\n", "loss: train 0.082, valid 0.107\n", "accuracy: train 0.893, valid 0.850\n", "loss: train 0.083, valid 0.107\n", "accuracy: train 0.892, valid 0.849\n", "loss: train 0.084, valid 0.107\n", "accuracy: train 0.888, valid 0.851\n", "loss: train 0.081, valid 0.107\n", "accuracy: train 0.893, valid 0.846\n", "loss: train 0.081, valid 0.107\n", "accuracy: train 0.895, valid 0.848\n", "loss: train 0.083, valid 0.107\n", "accuracy: train 0.891, valid 0.851\n", "loss: train 0.084, valid 0.109\n", "accuracy: train 0.888, valid 0.849\n", "loss: train 0.083, valid 0.109\n", "accuracy: train 0.892, valid 0.845\n", "loss: train 0.079, valid 0.107\n", "accuracy: train 0.898, valid 0.849\n", "loss: train 0.080, valid 0.107\n", "accuracy: train 0.896, valid 0.847\n", "loss: train 0.093, valid 0.121\n", "accuracy: train 0.871, valid 0.827\n", "loss: train 0.079, valid 0.107\n", "accuracy: train 0.899, valid 0.850\n", "loss: train 0.079, valid 0.107\n", "accuracy: train 0.895, valid 0.846\n", "loss: train 0.079, valid 0.105\n", "accuracy: train 0.898, valid 0.851\n", "best epoch: 49 loss: 0.105419 accuracy: 0.854\n"]}], "source": ["best_epoch, best_loss, best_acc = model.fit()\n", "print(\"best epoch: %d loss: %.6lf accuracy: %.3lf\" % (best_epoch, best_loss, best_acc))"]}, {"cell_type": "code", "execution_count": 155, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["best epoch: 39 loss: 0.099359 accuracy: 0.863\n"]}], "source": []}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## 草稿部分"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [], "source": ["import datetime\n", "import time\n", "import pytz\n", "import pandas as pd\n", "import sys\n", "import numpy as np\n", "import json"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["#### 数据预处理"]}, {"cell_type": "code", "execution_count": 134, "metadata": {}, "outputs": [], "source": ["sdf = pd.read_parquet('e:/featdata/sfd.parquet', engine='fastparquet')"]}, {"cell_type": "code", "execution_count": 135, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(43090, 235)\n"]}], "source": ["sdf['change'] = sdf['change'].astype(np.float32)\n", "sdf['change'] = sdf['change'].shift(-1)\n", "sdf = sdf[:-1]\n", "sdf['RSI_2'] = sdf['RSI_2'].astype(np.float32)\n", "sdf = sdf.loc[sdf['RSI_2'] != 0.0]\n", "print(sdf.shape)"]}, {"cell_type": "code", "execution_count": 108, "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['A', 'AG', 'AL', 'AP', 'B', 'BU', 'C', 'CF', 'CJ', 'CS', 'CU',\n", "       'CY', 'EG', 'FG', 'HC', 'I', 'J', 'J<PERSON>', 'J<PERSON>', 'L', 'M', 'MA', 'NI',\n", "       'OI', 'P', 'PB', 'PP', 'RB', 'RM', 'RU', 'SC', 'SF', 'SM', 'SN',\n", "       'SP', 'SR', 'TA', 'V', 'Y', 'ZC', 'ZN'], dtype=object)"]}, "execution_count": 108, "metadata": {}, "output_type": "execute_result"}], "source": ["sdf.code.unique()"]}, {"cell_type": "code", "execution_count": 136, "metadata": {}, "outputs": [], "source": ["asdf = sdf[(sdf['code'] == 'A')]"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["- 选择列"]}, {"cell_type": "code", "execution_count": 137, "metadata": {}, "outputs": [], "source": ["from pyqlab.const import ALL_FACTOR_NAMES, TWO_VAL_FACTOR_NAMES"]}, {"cell_type": "code", "execution_count": 138, "metadata": {}, "outputs": [], "source": ["SEL_SHORT_FACTOR_NAMES = [ # Fast period factor\n", "    \"MACD\", \"MACD_DIFF\", \"MACD_DEA\", \"MOM\", \"RSI\",\n", "\n", "    \"LR_SLOPE_FAST\", \"LR_SLOPE_MIDD\", \"LR_SLOPE_SLOW\",\n", "    \"LR_SLOPE_FAST_THRESHOLD\", \"LR_SLOPE_SLOW_THRESHOLD\",\n", "\n", "    \"SQUEEZE_ZERO_BARS\", \n", "    \"SQUEEZE_GAP\", \"SQUEEZE_GAP_FAST\", \"SQUEEZE_GAP_SLOW\",\n", "    \"SQUEEZE_GAP_THRESHOLD\", \"SQUEEZE_NARROW_BARS\",\n", "\n", "    \"BAND_POSITION\", \"BAND_WIDTH\",\n", "    \"BAND_EXPAND\", \"<PERSON>ND_GRADIENT\", \"BAND_GRADIENT_THRESHOLD\", \"BAND_GAP\",\n", "\n", "    \"TL_FAST\", \"TL_SLOW\", \"TL_THRESHOLD\",\n", "\n", "    \"TREND_VALUE\",\n", "    \"TREND_BARS\",\n", "    \"TREND_INBARS\",\n", "    \"TREND_INPOSR\", \"TREND_HLR\",\n", "    \"TREND_LEVEL\"\n", "]"]}, {"cell_type": "code", "execution_count": 139, "metadata": {}, "outputs": [], "source": ["def get_factor_cols():\n", "    \"\"\"\n", "    因子列名称\n", "    \"\"\"\n", "    col_names = ['code', 'date', 'change']\n", "    for name in SEL_SHORT_FACTOR_NAMES:\n", "        if name in TWO_VAL_FACTOR_NAMES:\n", "            col_names.append(f\"{name}_1\")\n", "            col_names.append(f\"{name}_2\")\n", "        else:\n", "            col_names.append(f\"{name}_2\")\n", "    return col_names"]}, {"cell_type": "code", "execution_count": 140, "metadata": {}, "outputs": [], "source": ["col_names = get_factor_cols()"]}, {"cell_type": "code", "execution_count": 141, "metadata": {}, "outputs": [], "source": ["df = asdf[col_names]"]}, {"cell_type": "code", "execution_count": 147, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_16392\\1471519246.py:12: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df['label'] = df.loc[:,('change')].apply(lambda x: 1 if x > 0.002 else 0)\n"]}], "source": ["# 根据条件生成新列的函数\n", "def value_to_number(val):\n", "    if val > 0.002:\n", "        return 1\n", "    elif val < -0.002:\n", "        return -1\n", "    else:\n", "        return 0\n", "\n", "# 应用函数并生成新列\n", "# df['label'] = df['change'].apply(value_to_number)\n", "df['label'] = df.loc[:,'change'].apply(lambda x: 1 if x > 0.002 else 0)\n"]}, {"cell_type": "code", "execution_count": 146, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>code</th>\n", "      <th>date</th>\n", "      <th>change</th>\n", "      <th>MACD_2</th>\n", "      <th>MACD_DIFF_2</th>\n", "      <th>MACD_DEA_2</th>\n", "      <th>MOM_2</th>\n", "      <th>RSI_1</th>\n", "      <th>RSI_2</th>\n", "      <th>LR_SLOPE_FAST_1</th>\n", "      <th>...</th>\n", "      <th>TREND_VALUE_2</th>\n", "      <th>TREND_BARS_1</th>\n", "      <th>TREND_BARS_2</th>\n", "      <th>TREND_INBARS_1</th>\n", "      <th>TREND_INBARS_2</th>\n", "      <th>TREND_INPOSR_1</th>\n", "      <th>TREND_INPOSR_2</th>\n", "      <th>TREND_HLR_2</th>\n", "      <th>TREND_LEVEL_2</th>\n", "      <th>label</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>50</th>\n", "      <td>A</td>\n", "      <td>1593572864</td>\n", "      <td>0.0017</td>\n", "      <td>-0.031521</td>\n", "      <td>-3.730357</td>\n", "      <td>-3.698836</td>\n", "      <td>0.0</td>\n", "      <td>40.297722</td>\n", "      <td>41.190926</td>\n", "      <td>-0.1</td>\n", "      <td>...</td>\n", "      <td>-1.0</td>\n", "      <td>0.0</td>\n", "      <td>41.0</td>\n", "      <td>3.0</td>\n", "      <td>3.0</td>\n", "      <td>2.285714</td>\n", "      <td>0.857143</td>\n", "      <td>8.285714</td>\n", "      <td>-2.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51</th>\n", "      <td>A</td>\n", "      <td>1593573385</td>\n", "      <td>0.0021</td>\n", "      <td>0.430462</td>\n", "      <td>-2.908844</td>\n", "      <td>-3.339305</td>\n", "      <td>0.0</td>\n", "      <td>45.283953</td>\n", "      <td>45.283951</td>\n", "      <td>1.2</td>\n", "      <td>...</td>\n", "      <td>-1.0</td>\n", "      <td>0.0</td>\n", "      <td>42.0</td>\n", "      <td>3.0</td>\n", "      <td>4.0</td>\n", "      <td>2.285714</td>\n", "      <td>1.571429</td>\n", "      <td>8.285714</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52</th>\n", "      <td>A</td>\n", "      <td>1593573449</td>\n", "      <td>0.0025</td>\n", "      <td>0.993587</td>\n", "      <td>-1.675764</td>\n", "      <td>-2.669351</td>\n", "      <td>0.0</td>\n", "      <td>49.778010</td>\n", "      <td>49.778011</td>\n", "      <td>2.0</td>\n", "      <td>...</td>\n", "      <td>-1.0</td>\n", "      <td>0.0</td>\n", "      <td>43.0</td>\n", "      <td>3.0</td>\n", "      <td>5.0</td>\n", "      <td>2.285714</td>\n", "      <td>2.428571</td>\n", "      <td>8.285714</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>53</th>\n", "      <td>A</td>\n", "      <td>1593573469</td>\n", "      <td>0.0037</td>\n", "      <td>0.953694</td>\n", "      <td>-1.267671</td>\n", "      <td>-2.221364</td>\n", "      <td>0.0</td>\n", "      <td>49.058478</td>\n", "      <td>49.058479</td>\n", "      <td>2.2</td>\n", "      <td>...</td>\n", "      <td>-1.0</td>\n", "      <td>0.0</td>\n", "      <td>44.0</td>\n", "      <td>3.0</td>\n", "      <td>6.0</td>\n", "      <td>2.285714</td>\n", "      <td>2.285714</td>\n", "      <td>8.285714</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>54</th>\n", "      <td>A</td>\n", "      <td>1593573646</td>\n", "      <td>0.0025</td>\n", "      <td>0.734138</td>\n", "      <td>-1.177878</td>\n", "      <td>-1.912016</td>\n", "      <td>0.0</td>\n", "      <td>47.584566</td>\n", "      <td>47.584564</td>\n", "      <td>2.3</td>\n", "      <td>...</td>\n", "      <td>-1.0</td>\n", "      <td>0.0</td>\n", "      <td>45.0</td>\n", "      <td>3.0</td>\n", "      <td>7.0</td>\n", "      <td>2.285714</td>\n", "      <td>2.000000</td>\n", "      <td>8.285714</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2905</th>\n", "      <td>A</td>\n", "      <td>1595602262</td>\n", "      <td>0.0026</td>\n", "      <td>0.025894</td>\n", "      <td>0.994258</td>\n", "      <td>0.968364</td>\n", "      <td>0.0</td>\n", "      <td>53.828998</td>\n", "      <td>53.828999</td>\n", "      <td>1.7</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>6.0</td>\n", "      <td>48.0</td>\n", "      <td>2.0</td>\n", "      <td>31.0</td>\n", "      <td>1.142857</td>\n", "      <td>1.142857</td>\n", "      <td>11.142857</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2906</th>\n", "      <td>A</td>\n", "      <td>1595602598</td>\n", "      <td>0.0028</td>\n", "      <td>0.576267</td>\n", "      <td>2.005925</td>\n", "      <td>1.429658</td>\n", "      <td>0.0</td>\n", "      <td>57.256887</td>\n", "      <td>57.256886</td>\n", "      <td>2.4</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>6.0</td>\n", "      <td>49.0</td>\n", "      <td>2.0</td>\n", "      <td>32.0</td>\n", "      <td>1.142857</td>\n", "      <td>0.285714</td>\n", "      <td>11.142857</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2907</th>\n", "      <td>A</td>\n", "      <td>1595602622</td>\n", "      <td>0.0046</td>\n", "      <td>1.085169</td>\n", "      <td>3.230572</td>\n", "      <td>2.145403</td>\n", "      <td>0.0</td>\n", "      <td>60.406414</td>\n", "      <td>60.406414</td>\n", "      <td>3.7</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>6.0</td>\n", "      <td>50.0</td>\n", "      <td>6.0</td>\n", "      <td>0.0</td>\n", "      <td>2.714286</td>\n", "      <td>0.000000</td>\n", "      <td>11.714286</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2908</th>\n", "      <td>A</td>\n", "      <td>1595602664</td>\n", "      <td>0.0030</td>\n", "      <td>0.633115</td>\n", "      <td>2.950775</td>\n", "      <td>2.317660</td>\n", "      <td>0.0</td>\n", "      <td>56.677584</td>\n", "      <td>56.677586</td>\n", "      <td>4.0</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>6.0</td>\n", "      <td>51.0</td>\n", "      <td>6.0</td>\n", "      <td>1.0</td>\n", "      <td>3.000000</td>\n", "      <td>1.000000</td>\n", "      <td>12.000000</td>\n", "      <td>0.0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2909</th>\n", "      <td>A</td>\n", "      <td>1595602700</td>\n", "      <td>0.0000</td>\n", "      <td>0.062528</td>\n", "      <td>2.267152</td>\n", "      <td>2.204624</td>\n", "      <td>0.0</td>\n", "      <td>53.161571</td>\n", "      <td>53.161572</td>\n", "      <td>0.5</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>6.0</td>\n", "      <td>52.0</td>\n", "      <td>6.0</td>\n", "      <td>2.0</td>\n", "      <td>3.000000</td>\n", "      <td>1.714286</td>\n", "      <td>12.000000</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2860 rows × 49 columns</p>\n", "</div>"], "text/plain": ["     code        date  change    MACD_2  MACD_DIFF_2  MACD_DEA_2  MOM_2  \\\n", "50      A  1593572864  0.0017 -0.031521    -3.730357   -3.698836    0.0   \n", "51      A  1593573385  0.0021  0.430462    -2.908844   -3.339305    0.0   \n", "52      A  1593573449  0.0025  0.993587    -1.675764   -2.669351    0.0   \n", "53      A  1593573469  0.0037  0.953694    -1.267671   -2.221364    0.0   \n", "54      A  1593573646  0.0025  0.734138    -1.177878   -1.912016    0.0   \n", "...   ...         ...     ...       ...          ...         ...    ...   \n", "2905    A  1595602262  0.0026  0.025894     0.994258    0.968364    0.0   \n", "2906    A  1595602598  0.0028  0.576267     2.005925    1.429658    0.0   \n", "2907    A  1595602622  0.0046  1.085169     3.230572    2.145403    0.0   \n", "2908    A  1595602664  0.0030  0.633115     2.950775    2.317660    0.0   \n", "2909    A  1595602700  0.0000  0.062528     2.267152    2.204624    0.0   \n", "\n", "          RSI_1      RSI_2  LR_SLOPE_FAST_1  ...  TREND_VALUE_2  TREND_BARS_1  \\\n", "50    40.297722  41.190926             -0.1  ...           -1.0           0.0   \n", "51    45.283953  45.283951              1.2  ...           -1.0           0.0   \n", "52    49.778010  49.778011              2.0  ...           -1.0           0.0   \n", "53    49.058478  49.058479              2.2  ...           -1.0           0.0   \n", "54    47.584566  47.584564              2.3  ...           -1.0           0.0   \n", "...         ...        ...              ...  ...            ...           ...   \n", "2905  53.828998  53.828999              1.7  ...            1.0           6.0   \n", "2906  57.256887  57.256886              2.4  ...            1.0           6.0   \n", "2907  60.406414  60.406414              3.7  ...            1.0           6.0   \n", "2908  56.677584  56.677586              4.0  ...            1.0           6.0   \n", "2909  53.161571  53.161572              0.5  ...            1.0           6.0   \n", "\n", "      TREND_BARS_2  TREND_INBARS_1  TREND_INBARS_2  TREND_INPOSR_1  \\\n", "50            41.0             3.0             3.0        2.285714   \n", "51            42.0             3.0             4.0        2.285714   \n", "52            43.0             3.0             5.0        2.285714   \n", "53            44.0             3.0             6.0        2.285714   \n", "54            45.0             3.0             7.0        2.285714   \n", "...            ...             ...             ...             ...   \n", "2905          48.0             2.0            31.0        1.142857   \n", "2906          49.0             2.0            32.0        1.142857   \n", "2907          50.0             6.0             0.0        2.714286   \n", "2908          51.0             6.0             1.0        3.000000   \n", "2909          52.0             6.0             2.0        3.000000   \n", "\n", "      TREND_INPOSR_2  TREND_HLR_2  TREND_LEVEL_2  label  \n", "50          0.857143     8.285714           -2.0      0  \n", "51          1.571429     8.285714            0.0      1  \n", "52          2.428571     8.285714            0.0      1  \n", "53          2.285714     8.285714            0.0      1  \n", "54          2.000000     8.285714            0.0      1  \n", "...              ...          ...            ...    ...  \n", "2905        1.142857    11.142857            0.0      1  \n", "2906        0.285714    11.142857            0.0      1  \n", "2907        0.000000    11.714286            0.0      1  \n", "2908        1.000000    12.000000            0.0      1  \n", "2909        1.714286    12.000000            0.0      0  \n", "\n", "[2860 rows x 49 columns]"]}, "execution_count": 146, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 148, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_16392\\2613770004.py:1: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df.drop(['code', 'date', 'change'], axis=1, inplace=True)\n"]}], "source": ["df.drop(['code', 'date', 'change'], axis=1, inplace=True)\n", "df = df.astype(np.float32)"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"data": {"text/plain": ["(2860, 46)"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["df.shape"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [], "source": ["df.to_csv('e:/featdata/feat.csv', index=False)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["#### CONV MODEL"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import torch\n", "from torch import nn, optim"]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [], "source": ["# 定义卷积神经网络模型\n", "class ConvNet(nn.Module):\n", "    def __init__(self):\n", "        super(ConvNet, self).__init__()\n", "        self.conv1 = nn.Conv1d(in_channels=5, out_channels=4, kernel_size=3)\n", "        self.relu1 = nn.ReLU()\n", "        self.pool1 = nn.MaxPool1d(kernel_size=2)\n", "        self.conv2 = nn.Conv1d(in_channels=4, out_channels=8, kernel_size=3)\n", "        self.relu2 = nn.ReLU()\n", "        self.pool2 = nn.MaxPool1d(kernel_size=2)\n", "        self.fc1 = nn.Linear(in_features=144, out_features=64)\n", "        self.relu3 = nn.ReLU()\n", "        self.fc2 = nn.Linear(in_features=64, out_features=1)\n", "        self.sigmoid = nn.Sigmoid()\n", "        \n", "    def forward(self, x):\n", "        out = self.conv1(x)\n", "        out = self.relu1(out)\n", "        out = self.pool1(out)\n", "        out = self.conv2(out)\n", "        out = self.relu2(out)\n", "        out = self.pool2(out)\n", "        out = out.view(-1, 144)\n", "        out = self.fc1(out)\n", "        out = self.relu3(out)\n", "        out = self.fc2(out)\n", "        out = self.sigmoid(out)\n", "        return out\n", "\n", "# 定义训练函数\n", "def train(model, device, train_loader, criterion, optimizer, epoch):\n", "    model.train()\n", "    for batch_idx, (data, target) in enumerate(train_loader):\n", "        # 将数据和目标移到GPU上\n", "        data, target = data.to(device), target.to(device)\n", "        print(data.shape, target.shape)\n", "        print(target)\n", "        # 将梯度置零\n", "        optimizer.zero_grad()\n", "        # 前向传播\n", "        output = model(data)\n", "        print(output.view(-1))\n", "        # 计算损失\n", "        loss = criterion(output.view(-1), target)\n", "        # 反向传播\n", "        loss.backward()\n", "        # 更新权重\n", "        optimizer.step()\n", "        # 打印训练进度\n", "        if batch_idx % 10 == 0:\n", "            print('Train Epoch: {} [{}/{} ({:.0f}%)]\\tLoss: {:.6f}'.format(\n", "                epoch, batch_idx * len(data), len(train_loader.dataset),\n", "                100. * batch_idx / len(train_loader), loss.item()))\n", "\n", "# 定义测试函数\n", "def test(model, device, test_loader, criterion):\n", "    model.eval()\n", "    test_loss = 0\n", "    correct = 0\n", "    with torch.no_grad():\n", "        for data, target in test_loader:\n", "            # 将数据和目标移到GPU上\n", "            data, target = data.to(device), target.to(device)\n", "            # 前向传播\n", "            output = model(data)\n", "            # 计算损失\n", "            test_loss += criterion(output, target).item()\n", "            # 计算准确率\n", "            pred = output.argmax(dim=1, keepdim=True)\n", "            correct += pred.eq(target.view_as(pred)).sum().item()\n", "\n", "    test_loss /= len(test_loader.dataset)\n", "    accuracy = 100. * correct / len(test_loader.dataset)\n", "    print('Test set: Average loss: {:.4f}, Accuracy: {}/{} ({:.0f}%)'.format(\n", "        test_loss, correct, len(test_loader.dataset), accuracy))\n", "\n", "# 加载数据\n", "from torch.utils.data import Dataset, DataLoader\n", "\n", "class MyDataset(Dataset):\n", "    def __init__(self, data):\n", "        self.win_size = 5\n", "        self.batched_data = [data[i:i+self.win_size] for i in range(0, len(data) - self.win_size, 1)]\n", "    \n", "    def __len__(self):\n", "        return len(self.batched_data)\n", "    \n", "    def __getitem__(self, idx):\n", "        return self.batched_data[idx][:, :-1], self.batched_data[idx][:,-1][-1]\n"]}, {"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [], "source": ["\n", "# data = torch.randn(5, 45)\n", "# target = torch.tensor([0, 1, 2, 3, 4])\n", "dataset = MyDataset(df.values)\n", "train_loader = DataLoader(dataset, batch_size=2, shuffle=True)\n"]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [], "source": ["\n", "# 定义超参数\n", "num_epochs = 10\n", "learning_rate = 0.001\n", "# num_classes = 3\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "\n", "# 初始化模型\n", "model = ConvNet().to(device)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# 定义损失函数和优化器\n", "# criterion = nn.CrossEntropyLoss()\n", "criterion = nn.<PERSON><PERSON><PERSON>()\n", "optimizer = optim.Adam(model.parameters(), lr=learning_rate)\n", "\n", "# 训练模型\n", "for epoch in range(num_epochs):\n", "    train(model, device, train_loader, criterion, optimizer, epoch)\n", "    test(model, device, train_loader, criterion)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "\n", "# 测试模型\n", "test_data = torch.randn(5, 45)\n", "test_target = torch.tensor([0, 1, 2, 3, 4])\n", "test_dataset = MyDataset(test_data, test_target)\n", "test_loader = DataLoader(test_dataset, batch_size=2, shuffle=True)\n", "test(model, device, test_loader, criterion)\n", "\n", "# 保存模型\n", "torch.save(model.state_dict(), 'convnet.pth')\n", "print('Model saved successfully.')"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [], "source": ["# 这是一个简单的一维卷积神经网络（CNN）模型，用于处理5 * 45的数据输入\n", "# 并输出单一概率预测。我们将使用PyTorch构建模型，并生成完整的训练和验\n", "# 证代码。请注意，这里的训练数据和验证数据需要您自己准备。\n", "\n", "# 首先，创建一个名为`Conv1DNet`的模型类。这个类将继承自`torch.nn.Module`，\n", "# 并实现模型的前向传播过程。\n", "\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import torch.nn.functional as F\n", "from torch.utils.data import DataLoader, TensorDataset"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [], "source": ["\n", "class Conv1DNet(nn.Module):\n", "    def __init__(self):\n", "        super(Conv1DNet, self).__init__()\n", "        self.conv1 = nn.Conv1d(5, 16, kernel_size=3, stride=1, padding=1)\n", "        self.conv2 = nn.Conv1d(16, 32, kernel_size=3, stride=1, padding=1)\n", "        self.fc1 = nn.<PERSON>ar(32 * 45, 64)\n", "        self.fc2 = nn.<PERSON>ar(64, 1)\n", "\n", "    def forward(self, x):\n", "        x = <PERSON>.relu(self.conv1(x))\n", "        x = <PERSON>.relu(self.conv2(x))\n", "        x = x.view(-1, 32 * 45)\n", "        x = F.relu(self.fc1(x))\n", "        x = self.fc2(x)\n", "        x = torch.sigmoid(x)\n", "        return x"]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([2855, 5, 45]) torch.<PERSON><PERSON>([2855, 5, 45]) torch.<PERSON><PERSON>([2855]) torch.<PERSON><PERSON>([2855])\n"]}], "source": ["# 接下来，准备训练和验证数据集。您需要将您的数据转换为PyTorch张量，\n", "# 然后使用`TensorDataset`和`DataLoader`创建数据加载器。\n", "x_data = df.values[:, :-1]\n", "y_data = df.values[:, -1]\n", "win_size = 5\n", "X_train = [x_data[i:i+win_size] for i in range(0, len(x_data) - win_size, 1)]\n", "y_train = [y_data[i+win_size] for i in range(0, len(x_data) - win_size, 1)]\n", "\n", "y_val = X_train\n", "y_val = y_train\n", "# 假设X_train, X_val, y_train, y_val是您的训练和验证数据\n", "X_train, X_val = torch.tensor(X_train), torch.tensor(X_train)\n", "y_train, y_val = torch.tensor(y_train), torch.tensor(y_val)\n", "print(X_train.shape, X_val.shape, y_train.shape, y_val.shape)\n", "\n", "train_dataset = TensorDataset(X_train, y_train)\n", "val_dataset = TensorDataset(X_val, y_val)\n", "\n", "train_dataloader = DataLoader(train_dataset, batch_size=32, shuffle=True)\n", "val_dataloader = DataLoader(val_dataset, batch_size=32, shuffle=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([7, 5, 45]) torch.<PERSON><PERSON>([7])\n", "torch.<PERSON><PERSON>([7, 1])\n", "Epoch 1/10 | Train Loss: 0.4713 | Validation Loss: 0.3952\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([7, 5, 45]) torch.<PERSON><PERSON>([7])\n", "torch.<PERSON><PERSON>([7, 1])\n", "Epoch 2/10 | Train Loss: 0.3339 | Validation Loss: 0.2826\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([7, 5, 45]) torch.<PERSON><PERSON>([7])\n", "torch.<PERSON><PERSON>([7, 1])\n", "Epoch 3/10 | Train Loss: 0.2971 | Validation Loss: 0.2626\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([7, 5, 45]) torch.<PERSON><PERSON>([7])\n", "torch.<PERSON><PERSON>([7, 1])\n", "Epoch 4/10 | Train Loss: 0.2677 | Validation Loss: 0.2414\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([7, 5, 45]) torch.<PERSON><PERSON>([7])\n", "torch.<PERSON><PERSON>([7, 1])\n", "Epoch 5/10 | Train Loss: 0.2588 | Validation Loss: 0.2368\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([7, 5, 45]) torch.<PERSON><PERSON>([7])\n", "torch.<PERSON><PERSON>([7, 1])\n", "Epoch 6/10 | Train Loss: 0.2535 | Validation Loss: 0.2912\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([7, 5, 45]) torch.<PERSON><PERSON>([7])\n", "torch.<PERSON><PERSON>([7, 1])\n", "Epoch 7/10 | Train Loss: 0.2441 | Validation Loss: 0.2328\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([7, 5, 45]) torch.<PERSON><PERSON>([7])\n", "torch.<PERSON><PERSON>([7, 1])\n", "Epoch 8/10 | Train Loss: 0.2406 | Validation Loss: 0.2161\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([7, 5, 45]) torch.<PERSON><PERSON>([7])\n", "torch.<PERSON><PERSON>([7, 1])\n", "Epoch 9/10 | Train Loss: 0.2285 | Validation Loss: 0.2272\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([32, 5, 45]) torch.<PERSON><PERSON>([32])\n", "torch.<PERSON><PERSON>([32, 1])\n", "torch.<PERSON><PERSON>([7, 5, 45]) torch.<PERSON><PERSON>([7])\n", "torch.<PERSON><PERSON>([7, 1])\n", "Epoch 10/10 | Train Loss: 0.2250 | Validation Loss: 0.2088\n"]}], "source": ["\n", "# 接下来，定义训练和验证函数。\n", "\n", "def train(model, dataloader, criterion, optimizer, device):\n", "    model.train()\n", "    running_loss = 0.0\n", "\n", "    for inputs, targets in dataloader:\n", "        inputs, targets = inputs.to(device), targets.to(device)\n", "        print(inputs.shape, targets.shape)\n", "\n", "        optimizer.zero_grad()\n", "        outputs = model(inputs)\n", "        print(outputs.shape)\n", "        loss = criterion(outputs.view(-1), targets.float())\n", "        loss.backward()\n", "        optimizer.step()\n", "\n", "        running_loss += loss.item()\n", "\n", "    return running_loss / len(dataloader)\n", "\n", "def validate(model, dataloader, criterion, device):\n", "    model.eval()\n", "    running_loss = 0.0\n", "\n", "    with torch.no_grad():\n", "        for inputs, targets in dataloader:\n", "            inputs, targets = inputs.to(device), targets.to(device)\n", "\n", "            outputs = model(inputs)\n", "            loss = criterion(outputs.view(-1), targets.float())\n", "\n", "            running_loss += loss.item()\n", "\n", "    return running_loss / len(dataloader)\n", "\n", "# 最后，设置训练和验证循环。\n", "\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "model = Conv1DNet().to(device)\n", "criterion = nn.<PERSON><PERSON><PERSON>()\n", "optimizer = optim.<PERSON>(model.parameters(), lr=1e-3)\n", "epochs = 10\n", "\n", "for epoch in range(epochs):\n", "    train_loss = train(model, train_dataloader, criterion, optimizer, device)\n", "    val_loss = validate(model, val_dataloader, criterion, device)\n", "\n", "    print(f\"Epoch {epoch + 1}/{epochs} | Train Loss: {train_loss:.4f} | Validation Loss: {val_loss:.4f}\")\n", "\n", "# 请注意，这里的示例代码使用了二进制交叉熵损失（BCELoss），\n", "# 但您可以根据您的具体任务选择其他损失函数。此外，您可能需\n", "# 要根据您的数据集调整模型结构和超参数。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}