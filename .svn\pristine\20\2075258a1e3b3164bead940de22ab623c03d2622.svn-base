{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import sys\n", "import time\n", "import datetime\n", "import json\n", "import sqlite3\n", "import pandas as pd\n", "import numpy as np\n", "sys.path.append(\"d:/QuantLab\")\n", "from qtunnel import DB,Mode,Cursor,Transaction,create_db,registered_dbs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pfs_name_ids = {\n", "    \"zxjt_pjj\": {\n", "        \"FUT-ZXJT-P21\": \"00210102215917000\",\n", "        \"FUT-ZXJT-JMO\": \"00171009141918000\",\n", "        \"FUT-ZXJT-JHL\": \"*****************\",\n", "    },\n", "    \"zxjt_xzy\": {\n", "        \"FUT-ZXJT-X21\": \"00210102224248000\",\n", "        \"FUT-ZXJT-XMO\": \"00171009141918000\",\n", "        \"FUT-ZXJT-XHL\": \"00210419180454000\",\n", "    },\n", "    \"nhqh_xzy\": {\n", "        \"FUT-NHQH-X21\": \"00210102223323000\",\n", "        \"FUT-NHQH-W1B\": \"00170908115033000\",\n", "        \"FUT-NHQH-NN1\": \"00220123224825000\",\n", "    },\n", "    \"gtja_xzy\": {\n", "        \"FUT-GTJA-X21\": \"00210102225821000\",\n", "        \"FUT-GTJA-W1B\": \"00171009141918000\",\n", "    }\n", "}\n", "\n", "pfs_main = ['00211229152555000', '00170623114649000']\n", "pfs_zxjt_pjj = ['00210102215917000', '00171009141918000', '*****************']\n", "pfs_zxjt_xzy = ['00210102224248000', '00171009141918000', '00210419180454000']\n", "pfs_zxjt_nhqh = ['00210102223323000', '00170908115033000', '00220123224825000']\n", "pfs_zxjt_gtja = ['00210102225821000', '00171009141918000']\n", "pfs_local = ['00200910081133001', '00171106132928000']"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>account</th>\n", "      <th>date</th>\n", "      <th>settlement</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>中信建投实盘CTP交易-SE</td>\n", "      <td>********</td>\n", "      <td>中信建投期货...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          account      date                                         settlement\n", "0  中信建投实盘CTP交易-SE  ********                                          中信建投期货..."]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_parquet(f\"../data/trading/中信建投实盘CTP交易-SE.settlement.parquet\")\n", "df"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ord_id</th>\n", "      <th>account_id</th>\n", "      <th>label</th>\n", "      <th>datetime</th>\n", "      <th>oprater</th>\n", "      <th>direct</th>\n", "      <th>cost_atr</th>\n", "      <th>pnl</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>***************</td>\n", "      <td>*****************</td>\n", "      <td>M2105.DC</td>\n", "      <td>******** 21:11:54</td>\n", "      <td>open</td>\n", "      <td>S</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>***************</td>\n", "      <td>*****************</td>\n", "      <td>RM2105.ZC</td>\n", "      <td>******** 21:12:08</td>\n", "      <td>open</td>\n", "      <td>S</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>***************</td>\n", "      <td>*****************</td>\n", "      <td>AG2106.SC</td>\n", "      <td>******** 21:14:03</td>\n", "      <td>open</td>\n", "      <td>S</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>***************</td>\n", "      <td>*****************</td>\n", "      <td>AG2106.SC</td>\n", "      <td>******** 21:19:38</td>\n", "      <td>stop</td>\n", "      <td>S</td>\n", "      <td>0.36</td>\n", "      <td>60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>***************</td>\n", "      <td>*****************</td>\n", "      <td>AG2106.SC</td>\n", "      <td>******** 21:46:11</td>\n", "      <td>open</td>\n", "      <td>S</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1720</th>\n", "      <td>***************</td>\n", "      <td>*****************</td>\n", "      <td>SR2205.ZC</td>\n", "      <td>******** 22:37:48</td>\n", "      <td>open</td>\n", "      <td>L</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1721</th>\n", "      <td>***************</td>\n", "      <td>*****************</td>\n", "      <td>SP2205.SC</td>\n", "      <td>******** 22:45:01</td>\n", "      <td>open</td>\n", "      <td>L</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1722</th>\n", "      <td>***************</td>\n", "      <td>*****************</td>\n", "      <td>SP2205.SC</td>\n", "      <td>******** 22:45:02</td>\n", "      <td>exit</td>\n", "      <td>L</td>\n", "      <td>0.12</td>\n", "      <td>20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1723</th>\n", "      <td>***************</td>\n", "      <td>*****************</td>\n", "      <td>SR2205.ZC</td>\n", "      <td>******** 22:59:00</td>\n", "      <td>close</td>\n", "      <td>L</td>\n", "      <td>-0.17</td>\n", "      <td>-10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1724</th>\n", "      <td>***************</td>\n", "      <td>*****************</td>\n", "      <td>C2205.DC</td>\n", "      <td>******** 22:59:00</td>\n", "      <td>close</td>\n", "      <td>L</td>\n", "      <td>0.47</td>\n", "      <td>40</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1725 rows × 8 columns</p>\n", "</div>"], "text/plain": ["               ord_id         account_id      label           datetime  \\\n", "0     ***************  *****************   M2105.DC  ******** 21:11:54   \n", "1     ***************  *****************  RM2105.ZC  ******** 21:12:08   \n", "2     ***************  *****************  AG2106.SC  ******** 21:14:03   \n", "3     ***************  *****************  AG2106.SC  ******** 21:19:38   \n", "4     ***************  *****************  AG2106.SC  ******** 21:46:11   \n", "...               ...                ...        ...                ...   \n", "1720  ***************  *****************  SR2205.ZC  ******** 22:37:48   \n", "1721  ***************  *****************  SP2205.SC  ******** 22:45:01   \n", "1722  ***************  *****************  SP2205.SC  ******** 22:45:02   \n", "1723  ***************  *****************  SR2205.ZC  ******** 22:59:00   \n", "1724  ***************  *****************   C2205.DC  ******** 22:59:00   \n", "\n", "     oprater direct cost_atr  pnl  \n", "0       open      S                \n", "1       open      S                \n", "2       open      S                \n", "3       stop      S     0.36   60  \n", "4       open      S                \n", "...      ...    ...      ...  ...  \n", "1720    open      L                \n", "1721    open      L                \n", "1722    exit      L     0.12   20  \n", "1723   close      L    -0.17  -10  \n", "1724   close      L     0.47   40  \n", "\n", "[1725 rows x 8 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_parquet(f\"../data/trading/*****************.all.order.parquet\")\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "a72f68cc148b279a81ef0488079e33db58bd3e723d7638e46009e47145dcc430"}, "kernelspec": {"display_name": "Python 3.8.10 ('base')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}