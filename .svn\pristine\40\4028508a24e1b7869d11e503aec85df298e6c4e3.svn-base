{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\Anaconda3\\lib\\site-packages\\sklearn\\cross_validation.py:41: DeprecationWarning: This module was deprecated in version 0.18 in favor of the model_selection module into which all the refactored classes and functions are moved. Also note that the interface of the new CV iterators are different from that of this module. This module will be removed in 0.20.\n", "  \"This module will be removed in 0.20.\", DeprecationWarning)\n"]}], "source": ["import pandas as pd\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "from sklearn.cross_validation import train_test_split"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["      0       3       4   5        7        9    11      13      15      16  \\\n", "2486   1  0.9728  0.9401   9  28.3520  65.3523  0.0  0.0018  0.0270  0.0046   \n", "2783   0  0.8880  0.8710   9  18.6892  66.1501  0.0  0.0017  0.0249  0.0038   \n", "2785   0  0.8917  0.8746   9  29.6549  58.7785  0.0  0.0017  0.0248  0.0021   \n", "2786   0  0.8720  0.8506   9  50.9219  59.7798  0.0  0.0020  0.0232  0.0048   \n", "2788   0  0.9272  0.8960   9  25.2474  28.7834  0.0  0.0017  0.0235  0.0044   \n", "\n", "       ...        37       40       41      43      45      46      47  \\\n", "2486   ...   -0.6864  -6.8382  -5.7752 -0.1976 -0.3544  0.9746 -0.0073   \n", "2783   ...   -7.4697   5.2148   3.5673 -0.9470  0.1931  0.2303 -0.7239   \n", "2785   ...   -3.2188  -0.4661  -0.8312 -0.3398 -0.1840 -3.6929 -2.3553   \n", "2786   ...    3.4173 -15.9782 -18.4415  0.3623 -1.9300 -4.1143 -0.5792   \n", "2788   ...   -6.5679   4.1824   1.2230 -0.7008 -0.7179 -8.8911 -3.1102   \n", "\n", "          48      49      51  \n", "2486 -0.0006  0.0027 -0.0053  \n", "2783  0.0004  0.0078  0.0108  \n", "2785 -0.0016  0.0013  0.0135  \n", "2786 -0.0013 -0.0001  0.0163  \n", "2788  0.0009  0.0046  0.0187  \n", "\n", "[5 rows x 34 columns]\n", "      0       3       4   5        7        9       11      13      15  \\\n", "3700   0  1.0367  1.0029  10  25.0152  84.5462  0.0153  0.0018  0.0153   \n", "423    1  1.1611  1.1418  10  83.4013  53.3427  0.0000  0.0012  0.0184   \n", "3701   0  0.8954  0.8782  14  61.5854  56.9208  0.0000  0.0017  0.0204   \n", "3708   0  0.9078  0.8651  21  63.5451  25.8800  0.0000  0.0020  0.0218   \n", "3707   0  0.7442  0.7259  13  76.2460  26.9007  0.0000  0.0017  0.0251   \n", "\n", "          16   ...         37       40       41      43      45      46  \\\n", "3700  0.0029   ...    -2.3109  15.0085  16.1576 -0.4414  4.3569 -1.8519   \n", "423   0.0041   ...     0.7130  -3.2870  -3.0685  0.3193 -1.0776  2.4721   \n", "3701  0.0034   ...     0.0248   0.7285  -0.3312 -0.0111 -0.2211  0.0946   \n", "3708  0.0026   ...    -0.0668  -1.3915  -1.4190  0.0497 -2.4509 -4.0221   \n", "3707  0.0043   ...    17.8730 -41.0491 -40.3403  1.4746 -2.9473 -6.3412   \n", "\n", "          47      48      49      51  \n", "3700  0.7748  0.0000  0.0049 -0.0346  \n", "423  -6.8187 -0.0006 -0.0037  0.0040  \n", "3701  0.0597 -0.0007 -0.0044  0.0038  \n", "3708 -0.6302  0.0007 -0.0039  0.0266  \n", "3707  0.6567  0.0003 -0.0105  0.0089  \n", "\n", "[5 rows x 34 columns]\n"]}], "source": ["FD_PATH = \"d:/QuantLab/log/\"\n", "\n", "#用pandas将时间转为标准格式\n", "dateparse = lambda dates: pd.datetime.strptime(dates,' %Y-%m-%d %H:%M:%S')\n", "df_train = pd.read_csv(FD_PATH + 'featuresdata.201701_201905.log', header=None, sep=']|,', skipinitialspace=True,\n", "                       parse_dates={'datetime': [5]},date_parser=dateparse)\n", "# df_train = pd.read_csv(FD_PATH + 'featuresdata.201701_201905.log', header=None, sep=']|,', skipinitialspace=True)\n", "\n", "temp=pd.DatetimeIndex(df_train['datetime'])\n", "# df_train['date'] = temp.date\n", "# df_train['time'] = temp.time\n", "#由于时间的部分最小粒度为小时，所以把time变为hour更加简洁\n", "# df_train['hour']=pd.to_datetime(temp.time,format=\"%H:%M:%S\")#变换格式\n", "# df_train['hour']=pd.Index(df_train[\"hour\"]).hour\n", "# df_train['dayofweek']=pd.DatetimeIndex(temp.date).dayofweek #提取出星期几这个特征\n", "\n", "# df_train[3].apply(lambda x: x.str.strip())\n", "# df_obj = df_train.select_dtypes([3])\n", "df_train[3] = df_train[3].str.strip()\n", "df_train = df_train[df_train[3] != \"IF1905.SF\"]\n", "df_train = df_train[df_train[3] != \"IH1905.SF\"]\n", "df_train = df_train[df_train[3] != \"IC1905.SF\"]\n", "# df_train = df_train[df_train[3].str != \"IH1905.SF\"]\n", "# df_train = df_train[df_train[3].str != \"IC1905.SF\"]\n", "\n", "df_train = df_train.drop([0,1,2], axis=1)\n", "df_train = df_train.sort_values(4,ascending=1)\n", "\n", "df_train_long, df_train_short = [x for _, x in df_train.groupby(df_train[4])]\n", "\n", "train_long = df_train_long.drop([3,4,6,52,54,'datetime'], axis=1)\n", "train_long = train_long[[60] + [c for c in train_long if c not in [60]]]\n", "train_long.columns = [n for n in range(0, (train_long.shape[1]))]\n", "train_long = train_long.drop([1,2,3,6,8,10,12,14,24,26,30,32,35,36,38,39,42,44,50], axis=1)\n", "print(train_long.head())\n", "\n", "train_long, test_long = train_test_split(train_long, random_state=42)\n", "train_long.to_csv(FD_PATH + 'long2.train', header=False, index=False)\n", "test_long.to_csv(FD_PATH + 'long2.test', header=False, index=False)\n", "# print(train_long)\n", "# print(test_long)\n", "\n", "train_short = df_train_short.drop([3,4,6,52,54,'datetime'], axis=1)\n", "train_short = train_short[[60] + [c for c in train_short if c not in [60]]]\n", "train_short.columns = [n for n in range(0, (train_short.shape[1]))]\n", "train_short = train_short.drop([1,2,3,6,8,10,12,14,24,26,30,32,35,36,38,39,42,44,50], axis=1)\n", "print(train_short.head())\n", "\n", "train_short, test_short = train_test_split(train_short, random_state=42)\n", "train_short.to_csv(FD_PATH + 'short2.train', header=False, index=False)\n", "test_short.to_csv(FD_PATH + 'short2.test', header=False, index=False)\n", "# print(train_short)\n", "# print(test_short)\n", "\n", "# y_train = df_train[0]\n", "# y_test = df_test[0]\n", "# X_train = df_train.drop(0, axis=1)\n", "# X_test = df_test.drop(0, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 2}