{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import datetime\n", "import talib as ta\n", "import pandas as pd\n", "import numpy as np\n", "import sys\n", "import matplotlib as mpl\n", "import matplotlib.pyplot as plt\n", "from pyecharts import Kline,Line,Overlap,Grid,online\n", "online()\n", "sys.path.append(\"d:/QuantLab\")\n", "from qtunnel import DataSource,Backtest,BarData,BarSize,DoRight,RunMode\n", "from qtunnel import DB,Mode,Cursor,Transaction,create_db,registered_dbs"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import factors.Alpha101 as a101"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["ds=DataSource(RunMode.passive)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["cnt=260\n", "symbol='MA8888.ZC'\n", "barsize=BarSize.min5\n", "hist=ds.get_history_data(symbol, cnt, [BarData.datetime,BarData.open,BarData.high,BarData.low,BarData.close], barsize)\n", "\n", "dt_str = []\n", "cnt=hist.shape[1]\n", "if cnt == 0:\n", "    print(cnt)"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\Anaconda3\\lib\\site-packages\\ipykernel\\__main__.py:7: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame\n", "\n", "See the caveats in the documentation: http://pandas.pydata.org/pandas-docs/stable/indexing.html#indexing-view-versus-copy\n", "D:\\QuantLab\\pylab\\factors\\Alpha101.py:287: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame\n", "\n", "See the caveats in the documentation: http://pandas.pydata.org/pandas-docs/stable/indexing.html#indexing-view-versus-copy\n", "  inner[self.returns < 0] = stddev(self.returns, 20)\n", "D:\\QuantLab\\pylab\\factors\\Alpha101.py:150: FutureWarning: Currently, 'apply' passes the values as ndarrays to the applied function. In the future, this will change to passing it as Series objects. You need to specify 'raw=True' to keep the current behaviour, and you can pass 'raw=False' to silence this warning\n", "  return df.rolling(window).apply(np.argmax) + 1\n", "D:\\QuantLab\\pylab\\factors\\Alpha101.py:70: FutureWarning: Currently, 'apply' passes the values as ndarrays to the applied function. In the future, this will change to passing it as Series objects. You need to specify 'raw=True' to keep the current behaviour, and you can pass 'raw=False' to silence this warning\n", "  return df.rolling(window).apply(rolling_rank)\n", "D:\\QuantLab\\pylab\\factors\\Alpha101.py:316: RuntimeWarning: invalid value encountered in sign\n", "  alpha = -1 * ts_rank(abs(delta(self.close, 7)), 60) * sign(delta(self.close, 7))\n", "D:\\QuantLab\\pylab\\factors\\Alpha101.py:349: RuntimeWarning: invalid value encountered in sign\n", "  return sign(delta(self.volume, 1)) * (-1 * delta(self.close, 1))\n", "D:\\QuantLab\\pylab\\factors\\Alpha101.py:387: RuntimeWarning: invalid value encountered in sign\n", "  return ((-1 * sign((self.close - delay(self.close, 7)) + delta(self.close, 7))) *\n", "D:\\QuantLab\\pylab\\factors\\Alpha101.py:461: RuntimeWarning: invalid value encountered in sign\n", "  inner = sign(delta_close) + sign(delay(delta_close, 1)) + sign(delay(delta_close, 2))\n", "D:\\QuantLab\\pylab\\factors\\Alpha101.py:175: FutureWarning: Method .as_matrix will be removed in a future version. Use .values instead.\n", "  na_series = df.as_matrix()\n", "D:\\QuantLab\\pylab\\factors\\Alpha101.py:470: RuntimeWarning: invalid value encountered in sign\n", "  p3=sign(scale(df))\n", "D:\\QuantLab\\pylab\\factors\\Alpha101.py:87: FutureWarning: Currently, 'apply' passes the values as ndarrays to the applied function. In the future, this will change to passing it as Series objects. You need to specify 'raw=True' to keep the current behaviour, and you can pass 'raw=False' to silence this warning\n", "  return df.rolling(window).apply(rolling_prod)\n", "D:\\QuantLab\\pylab\\factors\\Alpha101.py:159: FutureWarning: Currently, 'apply' passes the values as ndarrays to the applied function. In the future, this will change to passing it as Series objects. You need to specify 'raw=True' to keep the current behaviour, and you can pass 'raw=False' to silence this warning\n", "  return df.rolling(window).apply(np.argmin) + 1\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DATETIME</th>\n", "      <th>OPEN</th>\n", "      <th>HIGH</th>\n", "      <th>LOW</th>\n", "      <th>CLOSE</th>\n", "      <th>VOLUME</th>\n", "      <th>AMOUNT</th>\n", "      <th>PCTCHANGE</th>\n", "      <th>alpha001</th>\n", "      <th>alpha002</th>\n", "      <th>...</th>\n", "      <th>alpha085</th>\n", "      <th>alpha086</th>\n", "      <th>alpha088</th>\n", "      <th>alpha092</th>\n", "      <th>alpha094</th>\n", "      <th>alpha095</th>\n", "      <th>alpha096</th>\n", "      <th>alpha098</th>\n", "      <th>alpha099</th>\n", "      <th>alpha101</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.552960e+09</td>\n", "      <td>2475.0</td>\n", "      <td>2476.0</td>\n", "      <td>2468.0</td>\n", "      <td>2473.000000</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>-0.249969</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1.552960e+09</td>\n", "      <td>2473.0</td>\n", "      <td>2474.0</td>\n", "      <td>2470.0</td>\n", "      <td>NaN</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>-0.000404</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1.552961e+09</td>\n", "      <td>2472.0</td>\n", "      <td>2474.0</td>\n", "      <td>2469.0</td>\n", "      <td>2474.000000</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>0.000809</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.017308</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.399920</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1.552961e+09</td>\n", "      <td>2473.0</td>\n", "      <td>2476.0</td>\n", "      <td>2473.0</td>\n", "      <td>NaN</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>-0.000404</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.017308</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1.552961e+09</td>\n", "      <td>2474.0</td>\n", "      <td>2475.0</td>\n", "      <td>2473.0</td>\n", "      <td>2474.000000</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>0.000404</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.265385</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>1.552963e+09</td>\n", "      <td>2473.0</td>\n", "      <td>2475.0</td>\n", "      <td>2472.0</td>\n", "      <td>2474.000000</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.123077</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.333222</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>1.552963e+09</td>\n", "      <td>2474.0</td>\n", "      <td>2477.0</td>\n", "      <td>2474.0</td>\n", "      <td>2476.000000</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>0.000808</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.130769</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.666445</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>1.552963e+09</td>\n", "      <td>2476.0</td>\n", "      <td>2477.0</td>\n", "      <td>2474.0</td>\n", "      <td>NaN</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>-0.000404</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.084615</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>1.552964e+09</td>\n", "      <td>2475.0</td>\n", "      <td>2475.0</td>\n", "      <td>2470.0</td>\n", "      <td>NaN</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>-0.000808</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.111538</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>1.552964e+09</td>\n", "      <td>2473.0</td>\n", "      <td>2475.0</td>\n", "      <td>2471.0</td>\n", "      <td>NaN</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>-0.000809</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.126923</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>1.552964e+09</td>\n", "      <td>2471.0</td>\n", "      <td>2472.0</td>\n", "      <td>2470.0</td>\n", "      <td>2472.000000</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>0.000405</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.134615</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.499750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>1.552964e+09</td>\n", "      <td>2472.0</td>\n", "      <td>2475.0</td>\n", "      <td>2472.0</td>\n", "      <td>2475.000000</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>0.001214</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.096154</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.999667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>1.552965e+09</td>\n", "      <td>2474.0</td>\n", "      <td>2476.0</td>\n", "      <td>2473.0</td>\n", "      <td>2476.000000</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>0.000404</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.088462</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.666445</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>1.552965e+09</td>\n", "      <td>2475.0</td>\n", "      <td>2477.0</td>\n", "      <td>2473.0</td>\n", "      <td>NaN</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>-0.000404</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.100000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>1.552965e+09</td>\n", "      <td>2473.0</td>\n", "      <td>2474.0</td>\n", "      <td>2471.0</td>\n", "      <td>NaN</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>-0.001212</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.103846</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>1.552966e+09</td>\n", "      <td>2473.0</td>\n", "      <td>2473.0</td>\n", "      <td>2464.0</td>\n", "      <td>NaN</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>-0.000809</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.107692</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>1.552966e+09</td>\n", "      <td>2469.0</td>\n", "      <td>2471.0</td>\n", "      <td>2469.0</td>\n", "      <td>2470.000000</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.092308</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.499750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>1.552973e+09</td>\n", "      <td>2470.0</td>\n", "      <td>2470.0</td>\n", "      <td>2463.0</td>\n", "      <td>NaN</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>-0.002834</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.080769</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>1.552974e+09</td>\n", "      <td>2463.0</td>\n", "      <td>2469.0</td>\n", "      <td>2459.0</td>\n", "      <td>2469.000000</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>0.002436</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.053846</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.599940</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>1.552974e+09</td>\n", "      <td>2469.0</td>\n", "      <td>2469.0</td>\n", "      <td>2465.0</td>\n", "      <td>0.001086</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>-0.001215</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.165385</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>-617.095455</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>1.552974e+09</td>\n", "      <td>2467.0</td>\n", "      <td>2470.0</td>\n", "      <td>2466.0</td>\n", "      <td>2470.000000</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>0.001622</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.138462</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.749813</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>1.552975e+09</td>\n", "      <td>2469.0</td>\n", "      <td>2470.0</td>\n", "      <td>2465.0</td>\n", "      <td>0.001205</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>-0.001619</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.200000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>-493.701019</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>1.552975e+09</td>\n", "      <td>2466.0</td>\n", "      <td>2466.0</td>\n", "      <td>2451.0</td>\n", "      <td>0.001469</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>-0.004055</td>\n", "      <td>0.478992</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.230769</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>-164.388943</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>1.552975e+09</td>\n", "      <td>2456.0</td>\n", "      <td>2458.0</td>\n", "      <td>2454.0</td>\n", "      <td>0.001469</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>-0.000407</td>\n", "      <td>0.285714</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.234615</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>-613.846171</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>1.552976e+09</td>\n", "      <td>2454.0</td>\n", "      <td>2456.0</td>\n", "      <td>2450.0</td>\n", "      <td>2455.000000</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>0.000000</td>\n", "      <td>0.096639</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.173077</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.166639</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>1.552976e+09</td>\n", "      <td>2455.0</td>\n", "      <td>2458.0</td>\n", "      <td>2452.0</td>\n", "      <td>2456.000000</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>0.000407</td>\n", "      <td>0.880252</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.115385</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.166639</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>1.552976e+09</td>\n", "      <td>2456.0</td>\n", "      <td>2457.0</td>\n", "      <td>2452.0</td>\n", "      <td>0.001468</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>-0.001629</td>\n", "      <td>0.668067</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.146154</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>-491.101486</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>1.552976e+09</td>\n", "      <td>2452.0</td>\n", "      <td>2454.0</td>\n", "      <td>2450.0</td>\n", "      <td>0.001470</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>-0.000816</td>\n", "      <td>0.478992</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.150000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>-612.846421</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>1.552977e+09</td>\n", "      <td>2450.0</td>\n", "      <td>2453.0</td>\n", "      <td>2447.0</td>\n", "      <td>2450.000000</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>0.000000</td>\n", "      <td>0.285714</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.073077</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>1.552977e+09</td>\n", "      <td>2449.0</td>\n", "      <td>2450.0</td>\n", "      <td>2438.0</td>\n", "      <td>0.001637</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>-0.003673</td>\n", "      <td>0.096639</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.065385</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>-204.066191</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>230</th>\n", "      <td>1.553222e+09</td>\n", "      <td>2479.0</td>\n", "      <td>2479.0</td>\n", "      <td>2476.0</td>\n", "      <td>0.000854</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>-0.000404</td>\n", "      <td>0.096639</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.725819</td>\n", "      <td>0</td>\n", "      <td>0.711538</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>-826.057696</td>\n", "    </tr>\n", "    <tr>\n", "      <th>231</th>\n", "      <td>1.553222e+09</td>\n", "      <td>2478.0</td>\n", "      <td>2479.0</td>\n", "      <td>2476.0</td>\n", "      <td>2477.000000</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>0.000000</td>\n", "      <td>0.285714</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.607692</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>-0.333222</td>\n", "    </tr>\n", "    <tr>\n", "      <th>232</th>\n", "      <td>1.553222e+09</td>\n", "      <td>2477.0</td>\n", "      <td>2479.0</td>\n", "      <td>2476.0</td>\n", "      <td>2479.000000</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>0.000807</td>\n", "      <td>0.880252</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.484615</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.666445</td>\n", "    </tr>\n", "    <tr>\n", "      <th>233</th>\n", "      <td>1.553223e+09</td>\n", "      <td>2479.0</td>\n", "      <td>2479.0</td>\n", "      <td>2476.0</td>\n", "      <td>0.000854</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>-0.000403</td>\n", "      <td>0.668067</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.619231</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>-826.057696</td>\n", "    </tr>\n", "    <tr>\n", "      <th>234</th>\n", "      <td>1.553223e+09</td>\n", "      <td>2479.0</td>\n", "      <td>2479.0</td>\n", "      <td>2474.0</td>\n", "      <td>0.000854</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>-0.001614</td>\n", "      <td>0.478992</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.725819</td>\n", "      <td>0</td>\n", "      <td>0.761538</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>-495.700689</td>\n", "    </tr>\n", "    <tr>\n", "      <th>235</th>\n", "      <td>1.553223e+09</td>\n", "      <td>2474.0</td>\n", "      <td>2477.0</td>\n", "      <td>2470.0</td>\n", "      <td>0.000791</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>-0.001213</td>\n", "      <td>0.285714</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.725819</td>\n", "      <td>0</td>\n", "      <td>0.773077</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>-353.377976</td>\n", "    </tr>\n", "    <tr>\n", "      <th>236</th>\n", "      <td>1.553224e+09</td>\n", "      <td>2471.0</td>\n", "      <td>2476.0</td>\n", "      <td>2470.0</td>\n", "      <td>2476.000000</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>0.002023</td>\n", "      <td>0.096639</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.530769</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.833194</td>\n", "    </tr>\n", "    <tr>\n", "      <th>237</th>\n", "      <td>1.553224e+09</td>\n", "      <td>2476.0</td>\n", "      <td>2478.0</td>\n", "      <td>2475.0</td>\n", "      <td>2476.000000</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>0.000000</td>\n", "      <td>0.668067</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.885079</td>\n", "      <td>0</td>\n", "      <td>0.453846</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>238</th>\n", "      <td>1.553224e+09</td>\n", "      <td>2476.0</td>\n", "      <td>2476.0</td>\n", "      <td>2474.0</td>\n", "      <td>0.000849</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>-0.000404</td>\n", "      <td>0.478992</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.550000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>-1237.380885</td>\n", "    </tr>\n", "    <tr>\n", "      <th>239</th>\n", "      <td>1.553224e+09</td>\n", "      <td>2474.0</td>\n", "      <td>2481.0</td>\n", "      <td>2474.0</td>\n", "      <td>2481.000000</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>0.002424</td>\n", "      <td>0.880252</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.396154</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.999857</td>\n", "    </tr>\n", "    <tr>\n", "      <th>240</th>\n", "      <td>1.553225e+09</td>\n", "      <td>2481.0</td>\n", "      <td>2481.0</td>\n", "      <td>2478.0</td>\n", "      <td>0.001009</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>-0.000403</td>\n", "      <td>0.668067</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.546154</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>-826.724089</td>\n", "    </tr>\n", "    <tr>\n", "      <th>241</th>\n", "      <td>1.553225e+09</td>\n", "      <td>2480.0</td>\n", "      <td>2482.0</td>\n", "      <td>2480.0</td>\n", "      <td>2482.000000</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>0.000806</td>\n", "      <td>0.880252</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.496154</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.999500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>242</th>\n", "      <td>1.553233e+09</td>\n", "      <td>2477.0</td>\n", "      <td>2494.0</td>\n", "      <td>2477.0</td>\n", "      <td>2492.000000</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>0.004029</td>\n", "      <td>0.880252</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.323077</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.882301</td>\n", "    </tr>\n", "    <tr>\n", "      <th>243</th>\n", "      <td>1.553233e+09</td>\n", "      <td>2493.0</td>\n", "      <td>2502.0</td>\n", "      <td>2490.0</td>\n", "      <td>2500.000000</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>0.003210</td>\n", "      <td>0.880252</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.725819</td>\n", "      <td>0</td>\n", "      <td>0.184615</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.583285</td>\n", "    </tr>\n", "    <tr>\n", "      <th>244</th>\n", "      <td>1.553233e+09</td>\n", "      <td>2499.0</td>\n", "      <td>2503.0</td>\n", "      <td>2497.0</td>\n", "      <td>0.001476</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>-0.000800</td>\n", "      <td>0.668067</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.442308</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>-416.430349</td>\n", "    </tr>\n", "    <tr>\n", "      <th>245</th>\n", "      <td>1.553234e+09</td>\n", "      <td>2498.0</td>\n", "      <td>2504.0</td>\n", "      <td>2497.0</td>\n", "      <td>2503.000000</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>0.002002</td>\n", "      <td>0.880252</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.411538</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.714184</td>\n", "    </tr>\n", "    <tr>\n", "      <th>246</th>\n", "      <td>1.553234e+09</td>\n", "      <td>2504.0</td>\n", "      <td>2514.0</td>\n", "      <td>2504.0</td>\n", "      <td>2512.000000</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>0.003596</td>\n", "      <td>0.880252</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.407692</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.799920</td>\n", "    </tr>\n", "    <tr>\n", "      <th>247</th>\n", "      <td>1.553234e+09</td>\n", "      <td>2513.0</td>\n", "      <td>2518.0</td>\n", "      <td>2512.0</td>\n", "      <td>2514.000000</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>0.000796</td>\n", "      <td>0.880252</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.438462</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.166639</td>\n", "    </tr>\n", "    <tr>\n", "      <th>248</th>\n", "      <td>1.553234e+09</td>\n", "      <td>2515.0</td>\n", "      <td>2515.0</td>\n", "      <td>2509.0</td>\n", "      <td>0.001731</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>-0.001989</td>\n", "      <td>0.668067</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.665385</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>-419.096529</td>\n", "    </tr>\n", "    <tr>\n", "      <th>249</th>\n", "      <td>1.553235e+09</td>\n", "      <td>2509.0</td>\n", "      <td>2512.0</td>\n", "      <td>2509.0</td>\n", "      <td>2511.000000</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>0.000797</td>\n", "      <td>0.478992</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.684615</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.666445</td>\n", "    </tr>\n", "    <tr>\n", "      <th>250</th>\n", "      <td>1.553235e+09</td>\n", "      <td>2510.0</td>\n", "      <td>2512.0</td>\n", "      <td>2508.0</td>\n", "      <td>2511.000000</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>0.000000</td>\n", "      <td>0.285714</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.680769</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.249938</td>\n", "    </tr>\n", "    <tr>\n", "      <th>251</th>\n", "      <td>1.553235e+09</td>\n", "      <td>2510.0</td>\n", "      <td>2511.0</td>\n", "      <td>2506.0</td>\n", "      <td>0.001783</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>-0.001593</td>\n", "      <td>0.096639</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.876923</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>-501.899263</td>\n", "    </tr>\n", "    <tr>\n", "      <th>252</th>\n", "      <td>1.553236e+09</td>\n", "      <td>2507.0</td>\n", "      <td>2508.0</td>\n", "      <td>2503.0</td>\n", "      <td>0.001810</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>-0.000798</td>\n", "      <td>0.285714</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.961538</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>-501.299378</td>\n", "    </tr>\n", "    <tr>\n", "      <th>253</th>\n", "      <td>1.553236e+09</td>\n", "      <td>2505.0</td>\n", "      <td>2506.0</td>\n", "      <td>2502.0</td>\n", "      <td>2505.000000</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>0.000000</td>\n", "      <td>0.096639</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.885079</td>\n", "      <td>0</td>\n", "      <td>0.923077</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>254</th>\n", "      <td>1.553236e+09</td>\n", "      <td>2504.0</td>\n", "      <td>2505.0</td>\n", "      <td>2494.0</td>\n", "      <td>0.002064</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>-0.004391</td>\n", "      <td>0.096639</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.885079</td>\n", "      <td>0</td>\n", "      <td>0.946154</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>-227.615484</td>\n", "    </tr>\n", "    <tr>\n", "      <th>255</th>\n", "      <td>1.553236e+09</td>\n", "      <td>2493.0</td>\n", "      <td>2498.0</td>\n", "      <td>2489.0</td>\n", "      <td>2494.000000</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>0.000000</td>\n", "      <td>0.478992</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.834615</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.111099</td>\n", "    </tr>\n", "    <tr>\n", "      <th>256</th>\n", "      <td>1.553237e+09</td>\n", "      <td>2493.0</td>\n", "      <td>2496.0</td>\n", "      <td>2492.0</td>\n", "      <td>2494.000000</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>0.000000</td>\n", "      <td>0.285714</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.692308</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.249938</td>\n", "    </tr>\n", "    <tr>\n", "      <th>257</th>\n", "      <td>1.553237e+09</td>\n", "      <td>2494.0</td>\n", "      <td>2498.0</td>\n", "      <td>2494.0</td>\n", "      <td>2498.000000</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>0.001604</td>\n", "      <td>0.096639</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>0.885079</td>\n", "      <td>0</td>\n", "      <td>0.565385</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.999750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>258</th>\n", "      <td>1.553237e+09</td>\n", "      <td>2498.0</td>\n", "      <td>2498.0</td>\n", "      <td>2492.0</td>\n", "      <td>0.002083</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>-0.002002</td>\n", "      <td>0.668067</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.688462</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>-416.263609</td>\n", "    </tr>\n", "    <tr>\n", "      <th>259</th>\n", "      <td>1.553238e+09</td>\n", "      <td>2494.0</td>\n", "      <td>2497.0</td>\n", "      <td>2493.0</td>\n", "      <td>2495.000000</td>\n", "      <td>10000</td>\n", "      <td>500000</td>\n", "      <td>0.000000</td>\n", "      <td>0.478992</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0.573077</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>-7.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.249938</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>260 rows × 90 columns</p>\n", "</div>"], "text/plain": ["         DATETIME    OPEN    HIGH     LOW        CLOSE  VOLUME  AMOUNT  \\\n", "0    1.552960e+09  2475.0  2476.0  2468.0  2473.000000   10000  500000   \n", "1    1.552960e+09  2473.0  2474.0  2470.0          NaN   10000  500000   \n", "2    1.552961e+09  2472.0  2474.0  2469.0  2474.000000   10000  500000   \n", "3    1.552961e+09  2473.0  2476.0  2473.0          NaN   10000  500000   \n", "4    1.552961e+09  2474.0  2475.0  2473.0  2474.000000   10000  500000   \n", "5    1.552963e+09  2473.0  2475.0  2472.0  2474.000000   10000  500000   \n", "6    1.552963e+09  2474.0  2477.0  2474.0  2476.000000   10000  500000   \n", "7    1.552963e+09  2476.0  2477.0  2474.0          NaN   10000  500000   \n", "8    1.552964e+09  2475.0  2475.0  2470.0          NaN   10000  500000   \n", "9    1.552964e+09  2473.0  2475.0  2471.0          NaN   10000  500000   \n", "10   1.552964e+09  2471.0  2472.0  2470.0  2472.000000   10000  500000   \n", "11   1.552964e+09  2472.0  2475.0  2472.0  2475.000000   10000  500000   \n", "12   1.552965e+09  2474.0  2476.0  2473.0  2476.000000   10000  500000   \n", "13   1.552965e+09  2475.0  2477.0  2473.0          NaN   10000  500000   \n", "14   1.552965e+09  2473.0  2474.0  2471.0          NaN   10000  500000   \n", "15   1.552966e+09  2473.0  2473.0  2464.0          NaN   10000  500000   \n", "16   1.552966e+09  2469.0  2471.0  2469.0  2470.000000   10000  500000   \n", "17   1.552973e+09  2470.0  2470.0  2463.0          NaN   10000  500000   \n", "18   1.552974e+09  2463.0  2469.0  2459.0  2469.000000   10000  500000   \n", "19   1.552974e+09  2469.0  2469.0  2465.0     0.001086   10000  500000   \n", "20   1.552974e+09  2467.0  2470.0  2466.0  2470.000000   10000  500000   \n", "21   1.552975e+09  2469.0  2470.0  2465.0     0.001205   10000  500000   \n", "22   1.552975e+09  2466.0  2466.0  2451.0     0.001469   10000  500000   \n", "23   1.552975e+09  2456.0  2458.0  2454.0     0.001469   10000  500000   \n", "24   1.552976e+09  2454.0  2456.0  2450.0  2455.000000   10000  500000   \n", "25   1.552976e+09  2455.0  2458.0  2452.0  2456.000000   10000  500000   \n", "26   1.552976e+09  2456.0  2457.0  2452.0     0.001468   10000  500000   \n", "27   1.552976e+09  2452.0  2454.0  2450.0     0.001470   10000  500000   \n", "28   1.552977e+09  2450.0  2453.0  2447.0  2450.000000   10000  500000   \n", "29   1.552977e+09  2449.0  2450.0  2438.0     0.001637   10000  500000   \n", "..            ...     ...     ...     ...          ...     ...     ...   \n", "230  1.553222e+09  2479.0  2479.0  2476.0     0.000854   10000  500000   \n", "231  1.553222e+09  2478.0  2479.0  2476.0  2477.000000   10000  500000   \n", "232  1.553222e+09  2477.0  2479.0  2476.0  2479.000000   10000  500000   \n", "233  1.553223e+09  2479.0  2479.0  2476.0     0.000854   10000  500000   \n", "234  1.553223e+09  2479.0  2479.0  2474.0     0.000854   10000  500000   \n", "235  1.553223e+09  2474.0  2477.0  2470.0     0.000791   10000  500000   \n", "236  1.553224e+09  2471.0  2476.0  2470.0  2476.000000   10000  500000   \n", "237  1.553224e+09  2476.0  2478.0  2475.0  2476.000000   10000  500000   \n", "238  1.553224e+09  2476.0  2476.0  2474.0     0.000849   10000  500000   \n", "239  1.553224e+09  2474.0  2481.0  2474.0  2481.000000   10000  500000   \n", "240  1.553225e+09  2481.0  2481.0  2478.0     0.001009   10000  500000   \n", "241  1.553225e+09  2480.0  2482.0  2480.0  2482.000000   10000  500000   \n", "242  1.553233e+09  2477.0  2494.0  2477.0  2492.000000   10000  500000   \n", "243  1.553233e+09  2493.0  2502.0  2490.0  2500.000000   10000  500000   \n", "244  1.553233e+09  2499.0  2503.0  2497.0     0.001476   10000  500000   \n", "245  1.553234e+09  2498.0  2504.0  2497.0  2503.000000   10000  500000   \n", "246  1.553234e+09  2504.0  2514.0  2504.0  2512.000000   10000  500000   \n", "247  1.553234e+09  2513.0  2518.0  2512.0  2514.000000   10000  500000   \n", "248  1.553234e+09  2515.0  2515.0  2509.0     0.001731   10000  500000   \n", "249  1.553235e+09  2509.0  2512.0  2509.0  2511.000000   10000  500000   \n", "250  1.553235e+09  2510.0  2512.0  2508.0  2511.000000   10000  500000   \n", "251  1.553235e+09  2510.0  2511.0  2506.0     0.001783   10000  500000   \n", "252  1.553236e+09  2507.0  2508.0  2503.0     0.001810   10000  500000   \n", "253  1.553236e+09  2505.0  2506.0  2502.0  2505.000000   10000  500000   \n", "254  1.553236e+09  2504.0  2505.0  2494.0     0.002064   10000  500000   \n", "255  1.553236e+09  2493.0  2498.0  2489.0  2494.000000   10000  500000   \n", "256  1.553237e+09  2493.0  2496.0  2492.0  2494.000000   10000  500000   \n", "257  1.553237e+09  2494.0  2498.0  2494.0  2498.000000   10000  500000   \n", "258  1.553237e+09  2498.0  2498.0  2492.0     0.002083   10000  500000   \n", "259  1.553238e+09  2494.0  2497.0  2493.0  2495.000000   10000  500000   \n", "\n", "     PCTCHANGE  alpha001  alpha002     ...       alpha085  alpha086  alpha088  \\\n", "0     0.000000       NaN       0.0     ...            NaN         0       NaN   \n", "1    -0.000404       NaN       0.0     ...            NaN         0       NaN   \n", "2     0.000809       NaN       0.0     ...            NaN         0  0.017308   \n", "3    -0.000404       NaN       0.0     ...            NaN         0  0.017308   \n", "4     0.000404       NaN       0.0     ...            NaN         0  0.265385   \n", "5     0.000000       NaN       0.0     ...            NaN         0  0.123077   \n", "6     0.000808       NaN       0.0     ...            NaN         0  0.130769   \n", "7    -0.000404       NaN       0.0     ...            NaN         0  0.084615   \n", "8    -0.000808       NaN       0.0     ...            NaN         0  0.111538   \n", "9    -0.000809       NaN       0.0     ...            NaN         0  0.126923   \n", "10    0.000405       NaN       0.0     ...            NaN         0  0.134615   \n", "11    0.001214       NaN       0.0     ...            NaN         0  0.096154   \n", "12    0.000404       NaN       0.0     ...            NaN         0  0.088462   \n", "13   -0.000404       NaN       0.0     ...            NaN         0  0.100000   \n", "14   -0.001212       NaN       0.0     ...            NaN         0  0.103846   \n", "15   -0.000809       NaN       0.0     ...            NaN         0  0.107692   \n", "16    0.000000       NaN       0.0     ...            NaN         0  0.092308   \n", "17   -0.002834       NaN       0.0     ...            NaN         0  0.080769   \n", "18    0.002436       NaN       0.0     ...            NaN         0  0.053846   \n", "19   -0.001215       NaN       0.0     ...            NaN         0  0.165385   \n", "20    0.001622       NaN       0.0     ...            NaN         0  0.138462   \n", "21   -0.001619       NaN       0.0     ...            NaN         0  0.200000   \n", "22   -0.004055  0.478992       0.0     ...            NaN         0  0.230769   \n", "23   -0.000407  0.285714       0.0     ...            NaN         0  0.234615   \n", "24    0.000000  0.096639       0.0     ...            NaN         0  0.173077   \n", "25    0.000407  0.880252       0.0     ...            NaN         0  0.115385   \n", "26   -0.001629  0.668067       0.0     ...            NaN         0  0.146154   \n", "27   -0.000816  0.478992       0.0     ...            NaN         0  0.150000   \n", "28    0.000000  0.285714       0.0     ...            NaN         0  0.073077   \n", "29   -0.003673  0.096639       0.0     ...            NaN         0  0.065385   \n", "..         ...       ...       ...     ...            ...       ...       ...   \n", "230  -0.000404  0.096639       0.0     ...       0.725819         0  0.711538   \n", "231   0.000000  0.285714       0.0     ...            NaN         0  0.607692   \n", "232   0.000807  0.880252       0.0     ...            NaN         0  0.484615   \n", "233  -0.000403  0.668067       0.0     ...            NaN         0  0.619231   \n", "234  -0.001614  0.478992       0.0     ...       0.725819         0  0.761538   \n", "235  -0.001213  0.285714       0.0     ...       0.725819         0  0.773077   \n", "236   0.002023  0.096639       0.0     ...            NaN         0  0.530769   \n", "237   0.000000  0.668067       0.0     ...       0.885079         0  0.453846   \n", "238  -0.000404  0.478992       0.0     ...            NaN         0  0.550000   \n", "239   0.002424  0.880252       0.0     ...            NaN         0  0.396154   \n", "240  -0.000403  0.668067       0.0     ...            NaN         0  0.546154   \n", "241   0.000806  0.880252       0.0     ...            NaN         0  0.496154   \n", "242   0.004029  0.880252       0.0     ...            NaN         0  0.323077   \n", "243   0.003210  0.880252       0.0     ...       0.725819         0  0.184615   \n", "244  -0.000800  0.668067       0.0     ...            NaN         0  0.442308   \n", "245   0.002002  0.880252       0.0     ...            NaN         0  0.411538   \n", "246   0.003596  0.880252       0.0     ...            NaN         0  0.407692   \n", "247   0.000796  0.880252       0.0     ...            NaN         0  0.438462   \n", "248  -0.001989  0.668067       0.0     ...            NaN         0  0.665385   \n", "249   0.000797  0.478992       0.0     ...            NaN         0  0.684615   \n", "250   0.000000  0.285714       0.0     ...            NaN         0  0.680769   \n", "251  -0.001593  0.096639       0.0     ...            NaN         0  0.876923   \n", "252  -0.000798  0.285714       0.0     ...            NaN         0  0.961538   \n", "253   0.000000  0.096639       0.0     ...       0.885079         0  0.923077   \n", "254  -0.004391  0.096639       0.0     ...       0.885079         0  0.946154   \n", "255   0.000000  0.478992       0.0     ...            NaN         0  0.834615   \n", "256   0.000000  0.285714       0.0     ...            NaN         0  0.692308   \n", "257   0.001604  0.096639       0.0     ...       0.885079         0  0.565385   \n", "258  -0.002002  0.668067       0.0     ...            NaN         0  0.688462   \n", "259   0.000000  0.478992       0.0     ...            NaN         0  0.573077   \n", "\n", "     alpha092  alpha094  alpha095  alpha096  alpha098  alpha099     alpha101  \n", "0         NaN       NaN     False       NaN       0.0         0    -0.249969  \n", "1         NaN       NaN     False       NaN       0.0         0          NaN  \n", "2         NaN       NaN     False       NaN       0.0         0     0.399920  \n", "3         NaN       NaN     False       NaN       0.0         0          NaN  \n", "4         NaN       NaN     False       NaN       0.0         0     0.000000  \n", "5         NaN       NaN     False       NaN       0.0         0     0.333222  \n", "6         NaN       NaN     False       NaN       0.0         0     0.666445  \n", "7         NaN       NaN     False       NaN       0.0         0          NaN  \n", "8         NaN       NaN     False       NaN       0.0         0          NaN  \n", "9         NaN       NaN     False       NaN       0.0         0          NaN  \n", "10        NaN       NaN     False       NaN       0.0         0     0.499750  \n", "11        NaN       NaN     False       NaN       0.0         0     0.999667  \n", "12        NaN       NaN     False      -7.0       0.0         0     0.666445  \n", "13        NaN       NaN     False      -7.0       0.0         0          NaN  \n", "14        NaN       NaN     False      -7.0       0.0         0          NaN  \n", "15        NaN       NaN     False      -7.0       0.0         0          NaN  \n", "16        NaN       NaN     False      -7.0       0.0         0     0.499750  \n", "17        NaN       NaN     False      -7.0       0.0         0          NaN  \n", "18        NaN       NaN     False      -7.0       0.0         0     0.599940  \n", "19        NaN       NaN     False      -7.0       0.0         0  -617.095455  \n", "20        NaN       NaN     False      -7.0       0.0         0     0.749813  \n", "21        NaN       NaN     False      -7.0       0.0         0  -493.701019  \n", "22        NaN       NaN     False      -7.0       0.0         0  -164.388943  \n", "23        NaN       NaN     False      -7.0       0.0         0  -613.846171  \n", "24        NaN       NaN     False      -7.0       0.0         0     0.166639  \n", "25        NaN       NaN     False      -7.0       0.0         0     0.166639  \n", "26        NaN       NaN     False      -7.0       0.0         0  -491.101486  \n", "27        NaN       NaN     False      -7.0       0.0         0  -612.846421  \n", "28        NaN       NaN     False      -7.0       0.0         0     0.000000  \n", "29        NaN       NaN     False      -7.0       0.0         0  -204.066191  \n", "..        ...       ...       ...       ...       ...       ...          ...  \n", "230       NaN       NaN      True      -7.0       0.0         0  -826.057696  \n", "231       NaN       NaN      True      -7.0       0.0         0    -0.333222  \n", "232       NaN       NaN      True      -7.0       0.0         0     0.666445  \n", "233       NaN       NaN      True      -7.0       0.0         0  -826.057696  \n", "234       NaN       NaN      True      -7.0       0.0         0  -495.700689  \n", "235       NaN       NaN      True      -7.0       0.0         0  -353.377976  \n", "236       NaN       NaN      True      -7.0       0.0         0     0.833194  \n", "237       NaN       NaN      True      -7.0       0.0         0     0.000000  \n", "238       NaN       NaN      True      -7.0       0.0         0 -1237.380885  \n", "239       NaN       NaN      True      -7.0       0.0         0     0.999857  \n", "240       NaN       NaN      True      -7.0       0.0         0  -826.724089  \n", "241       NaN       NaN      True      -7.0       0.0         0     0.999500  \n", "242       NaN       NaN      True      -7.0       0.0         0     0.882301  \n", "243       NaN       NaN      True      -7.0       0.0         0     0.583285  \n", "244       NaN       NaN      True      -7.0       0.0         0  -416.430349  \n", "245       NaN       NaN      True      -7.0       0.0         0     0.714184  \n", "246       NaN       NaN      True      -7.0       0.0         0     0.799920  \n", "247       NaN       NaN      True      -7.0       0.0         0     0.166639  \n", "248       NaN       NaN      True      -7.0       0.0         0  -419.096529  \n", "249       NaN       NaN      True      -7.0       0.0         0     0.666445  \n", "250       NaN       NaN      True      -7.0       0.0         0     0.249938  \n", "251       NaN       NaN      True      -7.0       0.0         0  -501.899263  \n", "252       NaN       NaN      True      -7.0       0.0         0  -501.299378  \n", "253       NaN       NaN      True      -7.0       0.0         0     0.000000  \n", "254       NaN       NaN      True      -7.0       0.0         0  -227.615484  \n", "255       NaN       NaN      True      -7.0       0.0         0     0.111099  \n", "256       NaN       NaN      True      -7.0       0.0         0     0.249938  \n", "257       NaN       NaN      True      -7.0       0.0         0     0.999750  \n", "258       NaN       NaN      True      -7.0       0.0         0  -416.263609  \n", "259       NaN       NaN      True      -7.0       0.0         0     0.249938  \n", "\n", "[260 rows x 90 columns]"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["df=pd.DataFrame({'DATETIME':hist[0,:],'OPEN':hist[1,:],'HIGH':hist[2,:],'LOW':hist[3,:],'CLOSE':hist[4,:]})\n", "df['VOLUME']=10000\n", "df['AMOUNT']=500000\n", "df['PCTCHANGE']=0.0\n", "for i in range(1, len(df['CLOSE'])-1):\n", "    df['PCTCHANGE'][i]=float(df['CLOSE'][i])/float(df['CLOSE'][i-1]) - 1.0\n", "\n", "factor=a101.get_alpha(df)\n", "factor"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python [default]", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.0"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 2}