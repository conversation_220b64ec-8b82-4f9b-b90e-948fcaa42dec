import qlib
from qlib.config import REG_CN
from qlib.utils import init_instance_by_config
import pandas as pd

import optuna

'''
param_dist = {
    'boosting_type': ['gbdt', 'dart', 'rf'],
    'num_leaves': sp.stats.randint(2, 1001),
    'subsample_for_bin': sp.stats.randint(10, 1001),
    'min_split_gain': sp.stats.uniform(0, 5.0),
    'min_child_weight': sp.stats.uniform(1e-6, 1e-2),
    'reg_alpha': sp.stats.uniform(0, 1e-2),
    'reg_lambda': sp.stats.uniform(0, 1e-2),
    'tree_learner': ['data', 'feature', 'serial', 'voting' ],
    'application': ['regression_l1', 'regression_l2', 'regression'],
    'bagging_freq': sp.stats.randint(1, 11),
    'bagging_fraction': sp.stats.uniform(1e-3, 0.99),
    'feature_fraction': sp.stats.uniform(1e-3, 0.99),
    'learning_rate': sp.stats.uniform(1e-6, 0.99),
    'max_depth': sp.stats.randint(1, 501),
    'n_estimators': sp.stats.randint(100, 20001),
    'gpu_use_dp': [True, False],
}
'''

data_handler_config = {
    "start_time": "",
    "end_time": "",
    "instruments": "",
    "data_loader": {
        "class": "AFDataLoader",
        "module_path": "pyqlab.data.dataset.loader",
        "kwargs": {
            "direct": "long",
            "model_name": "GBDT",
            "model_name_suff": "",
            "model_path": "e:/lab/RoboQuant/pylab/model",
            "data_path": "e:/lab/RoboQuant/pylab/data",
            'portfolios': ['00200910081133001', '00171106132928000'],  
        }
    },
}

dataset_config = {
    "class": "AFDatasetH",
    "module_path": "pyqlab.data.dataset",
    "kwargs": {
        "handler": {
            "class": "DataHandlerAF",
            "module_path": "pyqlab.data.dataset.handler",
            "kwargs": data_handler_config,
        },
        "segments": ["train", "valid"],
        "col_set": ["feature", "label", "encoded"],
    }
}

def objective(trial):
    model_config = {
        "class": "MLPModelPytorch",
        "module_path": "pyqlab.contrib.model.pytorch_mlp",
        "kwargs": {
            "loss": "binary",
            "num_code": 60,
            "num_input": 87,
            "output_dim": 1,
            # "dropout": trial.suggest_uniform('dropout', 0.0, 1.0),
            "dropout": trial.suggest_discrete_uniform('dropout', 0.01, 0.5, 0.01),
            # "lr": trial.suggest_loguniform('lr', 1e-5, 1e-3),
            "lr": trial.suggest_discrete_uniform('lr', 1e-4, 1e-3, 2e-5),
            "lr_decay": 0.96,
            "lr_decay_steps": 10,
            "optimizer": "adam",
            "batch_size": 128,
            "GPU": 0,
            "early_stop": 30,
            "layers": (128, 256, 128, 64),
        },
    }
    evals_result = dict()
    dataset = init_instance_by_config(dataset_config)
    model = init_instance_by_config(model_config)
    model.fit(dataset, evals_result=evals_result)
    # return min(evals_result["loss"]["valid"])
    return max(evals_result["accuracy"]["valid"])
    

if __name__=="__main__":

    provider_uri = "~/.qlib/qlib_data/cn_data"  # target_dir
    qlib.init(provider_uri=provider_uri, region=REG_CN)

    pfs = {
        'MIX': ['00200910081133001', '00171106132928000'],
        '5R': ['00211229152555000', '00171106132928000'],
        '7R': ['00200910081133001', '00170623114649000'],
        'ALL': ['00211229152555000', '00171106132928000', '00200910081133001', '00170623114649000'],
    }
    directions=['long', 'short']
    trials=500

    '''
    item = input("select DATASET:\n 1: MIX\n 2: 5R\n 3: 7R\n 4: ALL\n")
    if item == '1':
        pfs_name='MIX'
    elif item == '2':
        pfs_name='5R'
    elif item == '3':
        pfs_name='7R'
    else:
        pfs_name='ALL'

    item = input("select DIRECT:\n 1: long\n 2: short\n")
    if item == '1':
        direct='long'
    else:
        direct='short'
        
    '''

    trials = input("input a number for trials[10 ~ 3000]\n")
    trials=int(trials)
    if trials < 10 or trials > 3000:
        print(f"input trials={trials} out range, set to 100.")
        trials = 300

    for pfs_name in pfs.keys():
        for direct in directions:
            data_handler_config["data_loader"]["kwargs"]["direct"] = direct
            if pfs_name:
                data_handler_config["data_loader"]["kwargs"]["portfolios"] = pfs[pfs_name]
                data_handler_config["data_loader"]["kwargs"]["model_name_suff"] = pfs_name
            study = optuna.create_study(study_name="MLP", direction="maximize", storage=f"sqlite:///db_MLP_{pfs_name}_{direct}.sqlite3", load_if_exists=True)
            study.optimize(objective, n_trials=trials, n_jobs=8)
            print("\n===============================================")
            print(f">>>BEST VALUE: {study.best_value}")
            print(f">>>BEST PARAMS: {study.best_params}")
            print("===============================================\n")
