{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import csv\n", "import json\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["              name          stategy    pnl  times  long_atr_mult  \\\n", "label                                                              \n", "A1909.DC    豆一1909  FUT-STR-BK-a-RL  21600      3            0.4   \n", "AP1910.ZC   苹果1910     FUT-GAP-b-RL   6440      1            0.6   \n", "BU1912.SC   沥青1912  FUT-STR-BK-a-RL  21760      1            0.8   \n", "CF1909.ZC   郑棉1909  FUT-STR-BK-a-RL  34440      2            0.6   \n", "CU1907.SC   沪铜1907     FUT-GAP-b-RL   1200      1            0.4   \n", "CY2001.ZC   棉纱2001     FUT-GAP-b-RL  28750      7            0.4   \n", "FG1909.ZC   玻璃1909  FUT-STR-BK-a-RL  16440      4            0.8   \n", "HC1910.SC   热卷1910     FUT-GAP-b-RL   4480      2            0.8   \n", "I1909.DC    铁矿1909  FUT-STR-BK-a-RL  91850      2            0.8   \n", "J1909.DC    焦炭1909  FUT-STR-BK-a-RL  31200      3            0.4   \n", "JD1909.DC   鸡蛋1909     FUT-GAP-b-RL  11680      1            0.4   \n", "JM1909.DC   焦煤1909  FUT-STR-BK-a-RL   4980      1            0.6   \n", "L1909.DC    乙烯1909  FUT-STR-BK-a-RL  12750      1            0.4   \n", "M1909.DC    豆粕1909     FUT-GAP-b-RL  37750      2            0.4   \n", "MA1909.ZC   甲醇1909  FUT-STR-BK-a-RL  28730      4            0.6   \n", "NI1907.SC   沪镍1907  FUT-STR-BK-a-RL  10085      4            0.4   \n", "OI1909.ZC   菜油1909     FUT-GAP-b-RL   4800      1            1.0   \n", "P1909.DC    棕榈1909     FUT-GAP-b-RL   5700      3            0.8   \n", "PB1907.SC   沪铅1907  FUT-STR-BK-a-RL   5730      5            0.4   \n", "PP1909.DC   丙烯1909  FUT-STR-BK-a-RL  15050      2            0.4   \n", "RB1910.SC   螺纹1910  FUT-STR-BK-a-RL  16520      2            0.8   \n", "RM1909.ZC   菜粕1909  FUT-STR-BK-a-RL  26400      1            0.6   \n", "RU1909.SC   橡胶1909  FUT-STR-BK-a-RL  11720      3            0.6   \n", "SF1909.ZC   硅铁1909  FUT-STR-BK-a-RL  10600      1            0.8   \n", "SM1909.ZC   锰硅1909     FUT-GAP-b-RL  10560      1            0.6   \n", "SP1909.SC   纸浆1909  FUT-STR-BK-a-RL  10480      6            0.4   \n", "SR1909.Z<PERSON>   白糖1909  FUT-STR-BK-a-RL  11460      2            0.8   \n", "TA1909.ZC  PTA1909     FUT-GAP-b-RL  36350      3            0.6   \n", "V1909.DC   PVC1909  FUT-STR-BK-a-RL  -1275      1            0.8   \n", "Y1909.DC    豆油1909  FUT-STR-BK-a-RL  23430      4            0.4   \n", "ZC1909.ZC   动煤1909  FUT-STR-BK-a-RL  27000      1            1.0   \n", "ZN1907.SC   沪锌1907     FUT-GAP-b-RL  10725      1            1.0   \n", "\n", "           short_atr_mult  long_atr  short_atr  \n", "label                                           \n", "A1909.DC              1.4        18         21  \n", "AP1910.ZC             1.0       133         81  \n", "BU1912.SC             1.2        60         24  \n", "CF1909.ZC             1.0       131         43  \n", "CU1907.SC             1.8       145        144  \n", "CY2001.ZC             1.0       142         55  \n", "FG1909.ZC             1.0        13          5  \n", "HC1910.SC             1.6        42         22  \n", "I1909.DC              1.0        13          5  \n", "J1909.DC              1.2        17         14  \n", "JD1909.DC             1.0        22         24  \n", "JM1909.DC             1.2        12          7  \n", "L1909.DC              1.2        38         34  \n", "M1909.DC              1.0        16         13  \n", "MA1909.ZC             1.0        26         12  \n", "NI1907.SC             1.2       580        294  \n", "OI1909.ZC             1.0        72         21  \n", "P1909.DC              1.2        39         17  \n", "PB1907.SC             1.2        70         46  \n", "PP1909.DC             1.4        41         36  \n", "RB1910.SC             1.8        41         25  \n", "RM1909.ZC             1.4        25         20  \n", "RU1909.SC             1.2       134         77  \n", "SF1909.ZC             1.4        48         27  \n", "SM1909.ZC             1.8        50         45  \n", "SP1909.SC             1.0        27         17  \n", "SR1909.ZC             1.4        50         18  \n", "TA1909.ZC             1.4        58         28  \n", "V1909.DC              1.8        67         47  \n", "Y1909.DC              1.0        21         17  \n", "ZC1909.ZC             1.2         6          2  \n", "ZN1907.SC             1.0       274         55  \n"]}], "source": ["CSV_PATH = \"d:/QuantLab/rpt/\"\n", "JSON_PATH = \"d:/QuantLab/\"\n", "\n", "def read_backtest_range(csv_file):\n", "    df = pd.read_csv(CSV_PATH + csv_file, encoding=\"gbk\")\n", "    df.set_index('label',inplace=True)\n", "    return df\n", "\n", "csv_file1 = \"FUT-STR-BK-a.225733.ord2.csv\"\n", "csv_file2 = \"FUT-GAP-b.223544.ord2.csv\"\n", "df1 = pd.read_csv(CSV_PATH + csv_file1, encoding=\"gbk\")\n", "df2 = pd.read_csv(CSV_PATH + csv_file2, encoding=\"gbk\")\n", "\n", "df1.set_index('label',inplace=True)\n", "df2.set_index('label',inplace=True)\n", "\n", "df = df1\n", "for index,row in df2.iterrows():\n", "    if index in df.index:\n", "        if row['pnl'] > df.loc[index, 'pnl']:\n", "            df.loc[index] = row\n", "    else:\n", "        df.loc[index] = row\n", "        print(index)\n", "print(df)\n", "df.to_csv(CSV_PATH + \"combine.rangebar.csv\", encoding=\"gbk\")"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'JSON_PATH' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[1;32m<ipython-input-1-216e9bd26eda>\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[1;32m----> 1\u001b[1;33m \u001b[1;32mwith\u001b[0m \u001b[0mopen\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mJSON_PATH\u001b[0m \u001b[1;33m+\u001b[0m \u001b[1;34m\"RangeBar.json\"\u001b[0m\u001b[1;33m,\u001b[0m\u001b[1;34m'r'\u001b[0m\u001b[1;33m)\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0mjson_f\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      2\u001b[0m     \u001b[0mrange_dict\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mjson\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mload\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mjson_f\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      3\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      4\u001b[0m \u001b[1;32mfor\u001b[0m \u001b[0mindex\u001b[0m\u001b[1;33m,\u001b[0m\u001b[0mrow\u001b[0m \u001b[1;32min\u001b[0m \u001b[0mdf\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0miterrows\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      5\u001b[0m     \u001b[0mrange_dict\u001b[0m\u001b[1;33m[\u001b[0m\u001b[1;34m'fut'\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m[\u001b[0m\u001b[1;34m'long_range_bar'\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m[\u001b[0m\u001b[0mindex\u001b[0m\u001b[1;33m]\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mrow\u001b[0m\u001b[1;33m[\u001b[0m\u001b[1;34m'long_atr'\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;31mNameError\u001b[0m: name 'JSON_PATH' is not defined"]}], "source": ["with open(JSO<PERSON>_PATH + \"RangeBar.json\",'r') as json_f:\n", "    range_dict = json.load(json_f)\n", "\n", "for index,row in df.iterrows():\n", "    range_dict['fut']['long_range_bar'][index] = row['long_atr']\n", "    range_dict['fut']['short_range_bar'][index] = row['short_atr']\n", "    range_dict['fut']['long_range_bar'][index[:-7]] = row['long_atr']\n", "    range_dict['fut']['short_range_bar'][index[:-7]] = row['short_atr']\n", "    if index in range_dict['fut']['long_range_bar']:\n", "        del range_dict['fut']['long_range_bar'][index]\n", "    if index in range_dict['fut']['short_range_bar']:\n", "        del range_dict['fut']['short_range_bar'][index]\n", "\n", "with open(J<PERSON><PERSON>_PATH + \"RangeBar.com.\"+ df.iloc[1, 1][:-2] + csv_file1[-15:-9] + \".json\",\"w\") as json_f:\n", "    json.dump(range_dict,json_f,indent=4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 2}