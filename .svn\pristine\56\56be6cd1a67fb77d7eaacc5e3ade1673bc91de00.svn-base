qlib_init:
    provider_uri: "~/.qlib/qlib_data/cn_data"
    region: cn
market: &market csi300
benchmark: &benchmark SH000300
data_loader_config: &data_loader_config
    class: AFDataLoader
    module_path: pyqlab.data.dataset.loader
    kwargs: 
        direct: long
        model_name: MLP2
        model_path: "e:/lab/RoboQuant/pylab/model"
        data_path: "e:/lab/RoboQuant/pylab/data"
        portfolios: ["00200910081133001", "00171106132928000", "00170623114649000"]
data_handler_config: &data_handler_config
    start_time: 2008-01-01
    end_time: 2020-08-01
    instruments: *market
    data_loader: *data_loader_config

task:
    model:
        class: TabnetModel
        module_path: pyqlab.contrib.model.pytorch_tabnet
        kwargs:
            d_feat: 87
            out_dim: 1
            batch_size: 128
            n_epochs: 100
            early_stop: 50
            pretrain: False
            seed: 993
            GPU: 1
    dataset:
        class: AFDatasetH
        module_path: pyqlab.data.dataset
        kwargs:
            handler:
                class: DataHandlerAF
                module_path: pyqlab.data.dataset.handler
                kwargs: *data_handler_config
            segments: [train, valid]
            col_set: [feature, label]

