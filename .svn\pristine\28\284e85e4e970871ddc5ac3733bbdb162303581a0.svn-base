{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from pyecharts import online\n", "import datetime\n", "import talib as ta\n", "import pandas as pd\n", "from pyecharts import Kline\n", "online()\n", "import sys\n", "import matplotlib as mpl\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "sys.path.append(\"d:/QuantLab\")\n", "from qtunnel import DataSource,Backtest,BarData,BarSize,DoRight,RunMode\n", "from qtunnel import DB,Mode,Cursor,Transaction,create_db,registered_dbs"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["ds = DataSource(RunMode.simulation)\n", "if not ds.start_providers():\n", "    print(\"data source initlize failed.\")\n", "btest = Backtest()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"scrolled": true}, "outputs": [], "source": ["param = \"\"\"\n", "{\n", "    \"begin_date\": \"2018-12-08 01:36:40\",\n", "    \"block_name\": \"ZLQH\",\n", "    \"end_date\": \"2018-12-15 01:36:40\",\n", "    \"instrument_range\": 2,\n", "    \"last_n_bars\": 350,\n", "    \"long_starting_atr\": 0.025,\n", "    \"long_step_number\": 1,\n", "    \"long_step_size\": 0.001,\n", "    \"long_strategy_id\": \"00171106131645000\",\n", "    \"long_strategy_name\": \"FUT-GAP-R-LONG\",\n", "    \"price_mode\": 0,\n", "    \"quote_range\": 1,\n", "    \"quote_source\": 0,\n", "    \"short_starting_atr\": 0.003,\n", "    \"short_step_number\": 3,\n", "    \"short_step_size\": 0.001,\n", "    \"short_strategy_id\": \"00171106131805001\",\n", "    \"short_strategy_name\": \"FUT-GAP-R-SHORT\",\n", "    \"single_label\": \"\",\n", "    \"trigger_mode\": 0\n", "}\n", "\"\"\"\n", "if not btest.set_parameter(param):\n", "    print(\"parameter paser failed.\")\n", "    \n", "btest.start()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NO., time, ID, cash, total value, total return, transaction cost, long strategy, short strategy, block, instrument, start date, end date, Long ATR, Short ATR\n", "1, 2018-12-15 01:53:48, 00181215015346000, 99976.00, 100936.00, 0.01, 24.00, FUT-GAP-R-LONG, FUT-GAP-R-SHORT, ZLQH, , 2018-12-08, 2018-12-15, 0.0250, 0.0040\n", "2, 2018-12-15 01:53:49, 00181215015348001, 100000.00, 100000.00, 0.00, 0.00, FUT-GAP-R-LONG, FUT-GAP-R-SHORT, ZLQH, , 2018-12-08, 2018-12-15, 0.0250, 0.0030\n", "3, 2018-12-15 01:53:50, 00181215015349002, 100000.00, 100000.00, 0.00, 0.00, FUT-GAP-R-LONG, FUT-GAP-R-SHORT, ZLQH, , 2018-12-08, 2018-12-15, 0.0250, 0.0050\n", "\n"]}], "source": ["rpt = btest.report()\n", "print(rpt)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python [default]", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.0"}}, "nbformat": 4, "nbformat_minor": 2}