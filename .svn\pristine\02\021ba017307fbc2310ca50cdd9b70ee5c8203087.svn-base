{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'leveldb'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "\u001b[1;32m~\\AppData\\Local\\Temp/ipykernel_5516/2184168563.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[1;32m----> 1\u001b[1;33m \u001b[1;32mimport\u001b[0m \u001b[0mleveldb\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      2\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mos\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0msys\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;31mModuleNotFoundError\u001b[0m: No module named 'leveldb'"]}], "source": ["import leveldb\n", "import os, sys"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["#初始化一个数据库students\n", "def initialize():\n", "    db = leveldb.LevelDB(\"d:/QuantLab/store/historydata.db.bak\")\n", "    return db\n", "\n", "#插入\n", "def insert(db, sid, name):\n", "    db.<PERSON>(str(sid), name)\n", "    \n", "#删除\n", "def delete(db, sid):\n", "    db.<PERSON><PERSON>(str(sid))\n", "    \n", "#更新\n", "def update(db, sid, name):\n", "    db.<PERSON>(str(sid), name)\n", "    \n", "#搜索\n", "def search(db, sid):\n", "    name = db.<PERSON>(str(sid))\n", "    return name\n", "\n", "#遍历\n", "def display(db):\n", "    for key, value in db.RangeIter():\n", "        print (key, value)\n", "\n", "def iter_key_values(db):\n", "    # db = leveldb.LevelDB('d:/QuantLab/store/historydata.db')\n", "    keys = list(db.<PERSON>Iter(include_value = False))\n", "    print(keys)\n", "\n", "    # keys_values = list(db.<PERSON>Iter())\n", "    # print keys_values"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[bytearray(b'day_A8888.DC_2002'), bytearray(b'day_A8888.DC_2003'), bytearray(b'day_A8888.DC_2004'), bytearray(b'day_A8888.DC_2005'), bytearray(b'day_A8888.DC_2006'), bytearray(b'day_A8888.DC_2007'), bytearray(b'day_A8888.DC_2008'), bytearray(b'day_A8888.DC_2009'), bytearray(b'day_A8888.DC_2010'), bytearray(b'day_A8888.DC_2011'), bytearray(b'day_A8888.DC_2012'), bytearray(b'day_A8888.DC_2013'), bytearray(b'day_A8888.DC_2014'), bytearray(b'day_A8888.DC_2015'), bytearray(b'day_A8888.DC_2016'), bytearray(b'day_A8888.DC_2017'), bytearray(b'day_A8888.DC_2018'), bytearray(b'day_AG8888.SC_2012'), bytearray(b'day_AG8888.SC_2013'), bytearray(b'day_AG8888.SC_2014'), bytearray(b'day_AG8888.SC_2015'), bytearray(b'day_AG8888.SC_2016'), bytearray(b'day_AG8888.SC_2017'), bytearray(b'day_AG8888.SC_2018'), bytearray(b'day_AL8888.SC_2011'), bytearray(b'day_AL8888.SC_2012'), bytearray(b'day_AL8888.SC_2013'), bytearray(b'day_AL8888.SC_2014'), bytearray(b'day_AL8888.SC_2015'), bytearray(b'day_AL8888.SC_2016'), bytearray(b'day_AL8888.SC_2017'), bytearray(b'day_AL8888.SC_2018'), bytearray(b'day_AP8888.ZC_2017'), bytearray(b'day_AP8888.ZC_2018'), bytearray(b'day_AU8888.SC_2008'), bytearray(b'day_AU8888.SC_2009'), bytearray(b'day_AU8888.SC_2010'), bytearray(b'day_AU8888.SC_2011'), bytearray(b'day_AU8888.SC_2012'), bytearray(b'day_AU8888.SC_2013'), bytearray(b'day_AU8888.SC_2014'), bytearray(b'day_AU8888.SC_2015'), bytearray(b'day_AU8888.SC_2016'), bytearray(b'day_AU8888.SC_2017'), bytearray(b'day_AU8888.SC_2018'), bytearray(b'day_BU8888.SC_1996'), bytearray(b'day_BU8888.SC_1997'), bytearray(b'day_BU8888.SC_1998'), bytearray(b'day_BU8888.SC_1999'), bytearray(b'day_BU8888.SC_2000'), bytearray(b'day_BU8888.SC_2001'), bytearray(b'day_BU8888.SC_2002'), bytearray(b'day_BU8888.SC_2003'), bytearray(b'day_BU8888.SC_2004'), bytearray(b'day_BU8888.SC_2005'), bytearray(b'day_BU8888.SC_2006'), bytearray(b'day_BU8888.SC_2007'), bytearray(b'day_BU8888.SC_2008'), bytearray(b'day_BU8888.SC_2009'), bytearray(b'day_BU8888.SC_2010'), bytearray(b'day_BU8888.SC_2011'), bytearray(b'day_BU8888.SC_2012'), bytearray(b'day_BU8888.SC_2013'), bytearray(b'day_BU8888.SC_2014'), bytearray(b'day_BU8888.SC_2015'), bytearray(b'day_BU8888.SC_2016'), bytearray(b'day_BU8888.SC_2017'), bytearray(b'day_BU8888.SC_2018'), bytearray(b'day_C8888.DC_2004'), bytearray(b'day_C8888.DC_2005'), bytearray(b'day_C8888.DC_2006'), bytearray(b'day_C8888.DC_2007'), bytearray(b'day_C8888.DC_2008'), bytearray(b'day_C8888.DC_2009'), bytearray(b'day_C8888.DC_2010'), bytearray(b'day_C8888.DC_2011'), bytearray(b'day_C8888.DC_2012'), bytearray(b'day_C8888.DC_2013'), bytearray(b'day_C8888.DC_2014'), bytearray(b'day_C8888.DC_2015'), bytearray(b'day_C8888.DC_2016'), bytearray(b'day_C8888.DC_2017'), bytearray(b'day_C8888.DC_2018'), bytearray(b'day_CF8888.ZC_2005'), bytearray(b'day_CF8888.ZC_2006'), bytearray(b'day_CF8888.ZC_2007'), bytearray(b'day_CF8888.ZC_2008'), bytearray(b'day_CF8888.ZC_2009'), bytearray(b'day_CF8888.ZC_2010'), bytearray(b'day_CF8888.ZC_2011'), bytearray(b'day_CF8888.ZC_2012'), bytearray(b'day_CF8888.ZC_2013'), bytearray(b'day_CF8888.ZC_2014'), bytearray(b'day_CF8888.ZC_2015'), bytearray(b'day_CF8888.ZC_2016'), bytearray(b'day_CF8888.ZC_2017'), bytearray(b'day_CF8888.ZC_2018'), bytearray(b'day_CS8888.DC_2014'), bytearray(b'day_CS8888.DC_2015'), bytearray(b'day_CS8888.DC_2016'), bytearray(b'day_CS8888.DC_2017'), bytearray(b'day_CS8888.DC_2018'), bytearray(b'day_CU8888.SC_1996'), bytearray(b'day_CU8888.SC_1997'), bytearray(b'day_CU8888.SC_1998'), bytearray(b'day_CU8888.SC_1999'), bytearray(b'day_CU8888.SC_2000'), bytearray(b'day_CU8888.SC_2001'), bytearray(b'day_CU8888.SC_2002'), bytearray(b'day_CU8888.SC_2003'), bytearray(b'day_CU8888.SC_2004'), bytearray(b'day_CU8888.SC_2005'), bytearray(b'day_CU8888.SC_2006'), bytearray(b'day_CU8888.SC_2007'), bytearray(b'day_CU8888.SC_2008'), bytearray(b'day_CU8888.SC_2009'), bytearray(b'day_CU8888.SC_2010'), bytearray(b'day_CU8888.SC_2011'), bytearray(b'day_CU8888.SC_2012'), bytearray(b'day_CU8888.SC_2013'), bytearray(b'day_CU8888.SC_2014'), bytearray(b'day_CU8888.SC_2015'), bytearray(b'day_CU8888.SC_2016'), bytearray(b'day_CU8888.SC_2017'), bytearray(b'day_CU8888.SC_2018'), bytearray(b'day_CY8888.ZC_2017'), bytearray(b'day_CY8888.ZC_2018'), bytearray(b'day_FG8888.ZC_2012'), bytearray(b'day_FG8888.ZC_2013'), bytearray(b'day_FG8888.ZC_2014'), bytearray(b'day_FG8888.ZC_2015'), bytearray(b'day_FG8888.ZC_2016'), bytearray(b'day_FG8888.ZC_2017'), bytearray(b'day_FG8888.ZC_2018'), bytearray(b'day_HC8888.SC_2014'), bytearray(b'day_HC8888.SC_2015'), bytearray(b'day_HC8888.SC_2016'), bytearray(b'day_HC8888.SC_2017'), bytearray(b'day_HC8888.SC_2018'), bytearray(b'day_I8888.DC_2013'), bytearray(b'day_I8888.DC_2014'), bytearray(b'day_I8888.DC_2015'), bytearray(b'day_I8888.DC_2016'), bytearray(b'day_I8888.DC_2017'), bytearray(b'day_I8888.DC_2018'), bytearray(b'day_IC8888.SF_2015'), bytearray(b'day_IC8888.SF_2016'), bytearray(b'day_IC8888.SF_2017'), bytearray(b'day_IC8888.SF_2018'), bytearray(b'day_IF8888.SF_2010'), bytearray(b'day_IF8888.SF_2011'), bytearray(b'day_IF8888.SF_2012'), bytearray(b'day_IF8888.SF_2013'), bytearray(b'day_IF8888.SF_2014'), bytearray(b'day_IF8888.SF_2015'), bytearray(b'day_IF8888.SF_2016'), bytearray(b'day_IF8888.SF_2017'), bytearray(b'day_IF8888.SF_2018'), bytearray(b'day_IH8888.SF_2015'), bytearray(b'day_IH8888.SF_2016'), bytearray(b'day_IH8888.SF_2017'), bytearray(b'day_IH8888.SF_2018'), bytearray(b'day_J8888.DC_2011'), bytearray(b'day_J8888.DC_2012'), bytearray(b'day_J8888.DC_2013'), bytearray(b'day_J8888.DC_2014'), bytearray(b'day_J8888.DC_2015'), bytearray(b'day_J8888.DC_2016'), bytearray(b'day_J8888.DC_2017'), bytearray(b'day_J8888.DC_2018'), bytearray(b'day_JD8888.DC_2013'), bytearray(b'day_JD8888.DC_2014'), bytearray(b'day_JD8888.DC_2015'), bytearray(b'day_JD8888.DC_2016'), bytearray(b'day_JD8888.DC_2017'), bytearray(b'day_JD8888.DC_2018'), bytearray(b'day_JM8888.DC_2013'), bytearray(b'day_JM8888.DC_2014'), bytearray(b'day_JM8888.DC_2015'), bytearray(b'day_JM8888.DC_2016'), bytearray(b'day_JM8888.DC_2017'), bytearray(b'day_JM8888.DC_2018'), bytearray(b'day_L8888.DC_2007'), bytearray(b'day_L8888.DC_2008'), bytearray(b'day_L8888.DC_2009'), bytearray(b'day_L8888.DC_2010'), bytearray(b'day_L8888.DC_2011'), bytearray(b'day_L8888.DC_2012'), bytearray(b'day_L8888.DC_2013'), bytearray(b'day_L8888.DC_2014'), bytearray(b'day_L8888.DC_2015'), bytearray(b'day_L8888.DC_2016'), bytearray(b'day_L8888.DC_2017'), bytearray(b'day_L8888.DC_2018'), bytearray(b'day_M8888.DC_2005'), bytearray(b'day_M8888.DC_2006'), bytearray(b'day_M8888.DC_2007'), bytearray(b'day_M8888.DC_2008'), bytearray(b'day_M8888.DC_2009'), bytearray(b'day_M8888.DC_2010'), bytearray(b'day_M8888.DC_2011'), bytearray(b'day_M8888.DC_2012'), bytearray(b'day_M8888.DC_2013'), bytearray(b'day_M8888.DC_2014'), bytearray(b'day_M8888.DC_2015'), bytearray(b'day_M8888.DC_2016'), bytearray(b'day_M8888.DC_2017'), bytearray(b'day_M8888.DC_2018'), bytearray(b'day_MA8888.ZC_2011'), bytearray(b'day_MA8888.ZC_2012'), bytearray(b'day_MA8888.ZC_2013'), bytearray(b'day_MA8888.ZC_2014'), bytearray(b'day_MA8888.ZC_2015'), bytearray(b'day_MA8888.ZC_2016'), bytearray(b'day_MA8888.ZC_2017'), bytearray(b'day_MA8888.ZC_2018'), bytearray(b'day_MHI888.EX_1994'), bytearray(b'day_MHI888.EX_1995'), bytearray(b'day_MHI888.EX_1996'), bytearray(b'day_MHI888.EX_1997'), bytearray(b'day_MHI888.EX_1998'), bytearray(b'day_MHI888.EX_1999'), bytearray(b'day_MHI888.EX_2000'), bytearray(b'day_MHI888.EX_2001'), bytearray(b'day_MHI888.EX_2002'), bytearray(b'day_MHI888.EX_2003'), bytearray(b'day_MHI888.EX_2004'), bytearray(b'day_MHI888.EX_2005'), bytearray(b'day_MHI888.EX_2006'), bytearray(b'day_MHI888.EX_2007'), bytearray(b'day_MHI888.EX_2008'), bytearray(b'day_MHI888.EX_2009'), bytearray(b'day_MHI888.EX_2010'), bytearray(b'day_MHI888.EX_2011'), bytearray(b'day_MHI888.EX_2012'), bytearray(b'day_MHI888.EX_2013'), bytearray(b'day_MHI888.EX_2014'), bytearray(b'day_MHI888.EX_2015'), bytearray(b'day_MHI888.EX_2016'), bytearray(b'day_MHI888.EX_2017'), bytearray(b'day_MHI888.EX_2018'), bytearray(b'day_NI8888.SC_2015'), bytearray(b'day_NI8888.SC_2016'), bytearray(b'day_NI8888.SC_2017'), bytearray(b'day_NI8888.SC_2018'), bytearray(b'day_OI8888.ZC_2007'), bytearray(b'day_OI8888.ZC_2008'), bytearray(b'day_OI8888.ZC_2009'), bytearray(b'day_OI8888.ZC_2010'), bytearray(b'day_OI8888.ZC_2011'), bytearray(b'day_OI8888.ZC_2012'), bytearray(b'day_OI8888.ZC_2013'), bytearray(b'day_OI8888.ZC_2014'), bytearray(b'day_OI8888.ZC_2015'), bytearray(b'day_OI8888.ZC_2016'), bytearray(b'day_OI8888.ZC_2017'), bytearray(b'day_OI8888.ZC_2018'), bytearray(b'day_P8888.DC_2007'), bytearray(b'day_P8888.DC_2008'), bytearray(b'day_P8888.DC_2009'), bytearray(b'day_P8888.DC_2010'), bytearray(b'day_P8888.DC_2011'), bytearray(b'day_P8888.DC_2012'), bytearray(b'day_P8888.DC_2013'), bytearray(b'day_P8888.DC_2014'), bytearray(b'day_P8888.DC_2015'), bytearray(b'day_P8888.DC_2016'), bytearray(b'day_P8888.DC_2017'), bytearray(b'day_P8888.DC_2018'), bytearray(b'day_PB8888.SC_2011'), bytearray(b'day_PB8888.SC_2012'), bytearray(b'day_PB8888.SC_2013'), bytearray(b'day_PB8888.SC_2014'), bytearray(b'day_PB8888.SC_2015'), bytearray(b'day_PB8888.SC_2016'), bytearray(b'day_PB8888.SC_2017'), bytearray(b'day_PB8888.SC_2018'), bytearray(b'day_PP8888.DC_2014'), bytearray(b'day_PP8888.DC_2015'), bytearray(b'day_PP8888.DC_2016'), bytearray(b'day_PP8888.DC_2017'), bytearray(b'day_PP8888.DC_2018'), bytearray(b'day_RB8888.SC_2009'), bytearray(b'day_RB8888.SC_2010'), bytearray(b'day_RB8888.SC_2011'), bytearray(b'day_RB8888.SC_2012'), bytearray(b'day_RB8888.SC_2013'), bytearray(b'day_RB8888.SC_2014'), bytearray(b'day_RB8888.SC_2015'), bytearray(b'day_RB8888.SC_2016'), bytearray(b'day_RB8888.SC_2017'), bytearray(b'day_RB8888.SC_2018'), bytearray(b'day_RM8888.ZC_2012'), bytearray(b'day_RM8888.ZC_2013'), bytearray(b'day_RM8888.ZC_2014'), bytearray(b'day_RM8888.ZC_2015'), bytearray(b'day_RM8888.ZC_2016'), bytearray(b'day_RM8888.ZC_2017'), bytearray(b'day_RM8888.ZC_2018'), bytearray(b'day_RU8888.SC_1997'), bytearray(b'day_RU8888.SC_1998'), bytearray(b'day_RU8888.SC_1999'), bytearray(b'day_RU8888.SC_2000'), bytearray(b'day_RU8888.SC_2001'), bytearray(b'day_RU8888.SC_2002'), bytearray(b'day_RU8888.SC_2003'), bytearray(b'day_RU8888.SC_2004'), bytearray(b'day_RU8888.SC_2005'), bytearray(b'day_RU8888.SC_2006'), bytearray(b'day_RU8888.SC_2007'), bytearray(b'day_RU8888.SC_2008'), bytearray(b'day_RU8888.SC_2009'), bytearray(b'day_RU8888.SC_2010'), bytearray(b'day_RU8888.SC_2011'), bytearray(b'day_RU8888.SC_2012'), bytearray(b'day_RU8888.SC_2013'), bytearray(b'day_RU8888.SC_2014'), bytearray(b'day_RU8888.SC_2015'), bytearray(b'day_RU8888.SC_2016'), bytearray(b'day_RU8888.SC_2017'), bytearray(b'day_RU8888.SC_2018'), bytearray(b'day_SC8888.SC_2018'), bytearray(b'day_SN8888.SC_2015'), bytearray(b'day_SN8888.SC_2016'), bytearray(b'day_SN8888.SC_2017'), bytearray(b'day_SN8888.SC_2018'), bytearray(b'day_SR8888.ZC_2006'), bytearray(b'day_SR8888.ZC_2007'), bytearray(b'day_SR8888.ZC_2008'), bytearray(b'day_SR8888.ZC_2009'), bytearray(b'day_SR8888.ZC_2010'), bytearray(b'day_SR8888.ZC_2011'), bytearray(b'day_SR8888.ZC_2012'), bytearray(b'day_SR8888.ZC_2013'), bytearray(b'day_SR8888.ZC_2014'), bytearray(b'day_SR8888.ZC_2015'), bytearray(b'day_SR8888.ZC_2016'), bytearray(b'day_SR8888.ZC_2017'), bytearray(b'day_SR8888.ZC_2018'), bytearray(b'day_TA8888.ZC_2006'), bytearray(b'day_TA8888.ZC_2007'), bytearray(b'day_TA8888.ZC_2008'), bytearray(b'day_TA8888.ZC_2009'), bytearray(b'day_TA8888.ZC_2010'), bytearray(b'day_TA8888.ZC_2011'), bytearray(b'day_TA8888.ZC_2012'), bytearray(b'day_TA8888.ZC_2013'), bytearray(b'day_TA8888.ZC_2014'), bytearray(b'day_TA8888.ZC_2015'), bytearray(b'day_TA8888.ZC_2016'), bytearray(b'day_TA8888.ZC_2017'), bytearray(b'day_TA8888.ZC_2018'), bytearray(b'day_V8888.DC_2009'), bytearray(b'day_V8888.DC_2010'), bytearray(b'day_V8888.DC_2011'), bytearray(b'day_V8888.DC_2012'), bytearray(b'day_V8888.DC_2013'), bytearray(b'day_V8888.DC_2014'), bytearray(b'day_V8888.DC_2015'), bytearray(b'day_V8888.DC_2016'), bytearray(b'day_V8888.DC_2017'), bytearray(b'day_V8888.DC_2018'), bytearray(b'day_WFI999.B$_2000'), bytearray(b'day_WFI999.B$_2001'), bytearray(b'day_WFI999.B$_2002'), bytearray(b'day_WFI999.B$_2003'), bytearray(b'day_WFI999.B$_2004'), bytearray(b'day_WFI999.B$_2005'), bytearray(b'day_WFI999.B$_2006'), bytearray(b'day_WFI999.B$_2007'), bytearray(b'day_WFI999.B$_2008'), bytearray(b'day_WFI999.B$_2009'), bytearray(b'day_WFI999.B$_2010'), bytearray(b'day_WFI999.B$_2011'), bytearray(b'day_WFI999.B$_2012'), bytearray(b'day_WFI999.B$_2013'), bytearray(b'day_WFI999.B$_2014'), bytearray(b'day_WFI999.B$_2015'), bytearray(b'day_WFI999.B$_2016'), bytearray(b'day_WFI999.B$_2017'), bytearray(b'day_WFI999.B$_2018'), bytearray(b'day_WFI999.EX_1994'), bytearray(b'day_WFI999.EX_1995'), bytearray(b'day_WFI999.EX_1996'), bytearray(b'day_WFI999.EX_1997'), bytearray(b'day_WFI999.EX_1998'), bytearray(b'day_WFI999.EX_1999'), bytearray(b'day_WFI999.EX_2000'), bytearray(b'day_WFI999.EX_2001'), bytearray(b'day_WFI999.EX_2002'), bytearray(b'day_WFI999.EX_2003'), bytearray(b'day_WFI999.EX_2004'), bytearray(b'day_WFI999.EX_2005'), bytearray(b'day_WFI999.EX_2006'), bytearray(b'day_WFI999.EX_2007'), bytearray(b'day_WFI999.EX_2008'), bytearray(b'day_WFI999.EX_2009'), bytearray(b'day_WFI999.EX_2010'), bytearray(b'day_WFI999.EX_2011'), bytearray(b'day_WFI999.EX_2012'), bytearray(b'day_WFI999.EX_2013'), bytearray(b'day_WFI999.EX_2014'), bytearray(b'day_WFI999.EX_2015'), bytearray(b'day_WFI999.EX_2016'), bytearray(b'day_WFI999.EX_2017'), bytearray(b'day_WFI999.EX_2018'), bytearray(b'day_Y8888.DC_2006'), bytearray(b'day_Y8888.DC_2007'), bytearray(b'day_Y8888.DC_2008'), bytearray(b'day_Y8888.DC_2009'), bytearray(b'day_Y8888.DC_2010'), bytearray(b'day_Y8888.DC_2011'), bytearray(b'day_Y8888.DC_2012'), bytearray(b'day_Y8888.DC_2013'), bytearray(b'day_Y8888.DC_2014'), bytearray(b'day_Y8888.DC_2015'), bytearray(b'day_Y8888.DC_2016'), bytearray(b'day_Y8888.DC_2017'), bytearray(b'day_Y8888.DC_2018'), bytearray(b'day_ZC8888.ZC_2013'), bytearray(b'day_ZC8888.ZC_2014'), bytearray(b'day_ZC8888.ZC_2015'), bytearray(b'day_ZC8888.ZC_2016'), bytearray(b'day_ZC8888.ZC_2017'), bytearray(b'day_ZC8888.ZC_2018'), bytearray(b'day_ZN8888.SC_2007'), bytearray(b'day_ZN8888.SC_2008'), bytearray(b'day_ZN8888.SC_2009'), bytearray(b'day_ZN8888.SC_2010'), bytearray(b'day_ZN8888.SC_2011'), bytearray(b'day_ZN8888.SC_2012'), bytearray(b'day_ZN8888.SC_2013'), bytearray(b'day_ZN8888.SC_2014'), bytearray(b'day_ZN8888.SC_2015'), bytearray(b'day_ZN8888.SC_2016'), bytearray(b'day_ZN8888.SC_2017'), bytearray(b'day_ZN8888.SC_2018'), bytearray(b'min5_A8888.DC_2017_01'), bytearray(b'min5_A8888.DC_2017_02'), bytearray(b'min5_A8888.DC_2017_03'), bytearray(b'min5_A8888.DC_2017_04'), bytearray(b'min5_A8888.DC_2017_05'), bytearray(b'min5_A8888.DC_2017_06'), bytearray(b'min5_A8888.DC_2017_07'), bytearray(b'min5_A8888.DC_2017_08'), bytearray(b'min5_A8888.DC_2017_09'), bytearray(b'min5_A8888.DC_2017_10'), bytearray(b'min5_A8888.DC_2017_11'), bytearray(b'min5_A8888.DC_2017_12'), bytearray(b'min5_A8888.DC_2018_01'), bytearray(b'min5_A8888.DC_2018_02'), bytearray(b'min5_A8888.DC_2018_03'), bytearray(b'min5_A8888.DC_2018_04'), bytearray(b'min5_A8888.DC_2018_05'), bytearray(b'min5_A8888.DC_2018_06'), bytearray(b'min5_A8888.DC_2018_07'), bytearray(b'min5_AG8888.SC_2017_01'), bytearray(b'min5_AG8888.SC_2017_02'), bytearray(b'min5_AG8888.SC_2017_03'), bytearray(b'min5_AG8888.SC_2017_04'), bytearray(b'min5_AG8888.SC_2017_05'), bytearray(b'min5_AG8888.SC_2017_06'), bytearray(b'min5_AG8888.SC_2017_07'), bytearray(b'min5_AG8888.SC_2017_08'), bytearray(b'min5_AG8888.SC_2017_09'), bytearray(b'min5_AG8888.SC_2017_10'), bytearray(b'min5_AG8888.SC_2017_11'), bytearray(b'min5_AG8888.SC_2017_12'), bytearray(b'min5_AG8888.SC_2018_01'), bytearray(b'min5_AG8888.SC_2018_02'), bytearray(b'min5_AG8888.SC_2018_03'), bytearray(b'min5_AG8888.SC_2018_04'), bytearray(b'min5_AG8888.SC_2018_05'), bytearray(b'min5_AG8888.SC_2018_06'), bytearray(b'min5_AG8888.SC_2018_07'), bytearray(b'min5_AL8888.SC_2017_01'), bytearray(b'min5_AL8888.SC_2017_02'), bytearray(b'min5_AL8888.SC_2017_03'), bytearray(b'min5_AL8888.SC_2017_04'), bytearray(b'min5_AL8888.SC_2017_05'), bytearray(b'min5_AL8888.SC_2017_06'), bytearray(b'min5_AL8888.SC_2017_07'), bytearray(b'min5_AL8888.SC_2017_08'), bytearray(b'min5_AL8888.SC_2017_09'), bytearray(b'min5_AL8888.SC_2017_10'), bytearray(b'min5_AL8888.SC_2017_11'), bytearray(b'min5_AL8888.SC_2017_12'), bytearray(b'min5_AL8888.SC_2018_01'), bytearray(b'min5_AL8888.SC_2018_02'), bytearray(b'min5_AL8888.SC_2018_03'), bytearray(b'min5_AL8888.SC_2018_04'), bytearray(b'min5_AL8888.SC_2018_05'), bytearray(b'min5_AL8888.SC_2018_06'), bytearray(b'min5_AL8888.SC_2018_07'), bytearray(b'min5_AP8888.ZC_2017_12'), bytearray(b'min5_AP8888.ZC_2018_01'), bytearray(b'min5_AP8888.ZC_2018_02'), bytearray(b'min5_AP8888.ZC_2018_03'), bytearray(b'min5_AP8888.ZC_2018_04'), bytearray(b'min5_AP8888.ZC_2018_05'), bytearray(b'min5_AP8888.ZC_2018_06'), bytearray(b'min5_AP8888.ZC_2018_07'), bytearray(b'min5_AU8888.SC_2017_01'), bytearray(b'min5_AU8888.SC_2017_02'), bytearray(b'min5_AU8888.SC_2017_03'), bytearray(b'min5_AU8888.SC_2017_04'), bytearray(b'min5_AU8888.SC_2017_05'), bytearray(b'min5_AU8888.SC_2017_06'), bytearray(b'min5_AU8888.SC_2017_07'), bytearray(b'min5_AU8888.SC_2017_08'), bytearray(b'min5_AU8888.SC_2017_09'), bytearray(b'min5_AU8888.SC_2017_10'), bytearray(b'min5_AU8888.SC_2017_11'), bytearray(b'min5_AU8888.SC_2017_12'), bytearray(b'min5_AU8888.SC_2018_01'), bytearray(b'min5_AU8888.SC_2018_02'), bytearray(b'min5_AU8888.SC_2018_03'), bytearray(b'min5_AU8888.SC_2018_04'), bytearray(b'min5_AU8888.SC_2018_05'), bytearray(b'min5_AU8888.SC_2018_06'), bytearray(b'min5_AU8888.SC_2018_07'), bytearray(b'min5_BU8888.SC_2017_01'), bytearray(b'min5_BU8888.SC_2017_02'), bytearray(b'min5_BU8888.SC_2017_03'), bytearray(b'min5_BU8888.SC_2017_04'), bytearray(b'min5_BU8888.SC_2017_05'), bytearray(b'min5_BU8888.SC_2017_06'), bytearray(b'min5_BU8888.SC_2017_07'), bytearray(b'min5_BU8888.SC_2017_08'), bytearray(b'min5_BU8888.SC_2017_09'), bytearray(b'min5_BU8888.SC_2017_10'), bytearray(b'min5_BU8888.SC_2017_11'), bytearray(b'min5_BU8888.SC_2017_12'), bytearray(b'min5_BU8888.SC_2018_01'), bytearray(b'min5_BU8888.SC_2018_02'), bytearray(b'min5_BU8888.SC_2018_03'), bytearray(b'min5_BU8888.SC_2018_04'), bytearray(b'min5_BU8888.SC_2018_05'), bytearray(b'min5_BU8888.SC_2018_06'), bytearray(b'min5_BU8888.SC_2018_07'), bytearray(b'min5_C8888.DC_2017_01'), bytearray(b'min5_C8888.DC_2017_02'), bytearray(b'min5_C8888.DC_2017_03'), bytearray(b'min5_C8888.DC_2017_04'), bytearray(b'min5_C8888.DC_2017_05'), bytearray(b'min5_C8888.DC_2017_06'), bytearray(b'min5_C8888.DC_2017_07'), bytearray(b'min5_C8888.DC_2017_08'), bytearray(b'min5_C8888.DC_2017_09'), bytearray(b'min5_C8888.DC_2017_10'), bytearray(b'min5_C8888.DC_2017_11'), bytearray(b'min5_C8888.DC_2017_12'), bytearray(b'min5_C8888.DC_2018_01'), bytearray(b'min5_C8888.DC_2018_02'), bytearray(b'min5_C8888.DC_2018_03'), bytearray(b'min5_C8888.DC_2018_04'), bytearray(b'min5_C8888.DC_2018_05'), bytearray(b'min5_C8888.DC_2018_06'), bytearray(b'min5_C8888.DC_2018_07'), bytearray(b'min5_CF8888.ZC_2017_01'), bytearray(b'min5_CF8888.ZC_2017_02'), bytearray(b'min5_CF8888.ZC_2017_03'), bytearray(b'min5_CF8888.ZC_2017_04'), bytearray(b'min5_CF8888.ZC_2017_05'), bytearray(b'min5_CF8888.ZC_2017_06'), bytearray(b'min5_CF8888.ZC_2017_07'), bytearray(b'min5_CF8888.ZC_2017_08'), bytearray(b'min5_CF8888.ZC_2017_09'), bytearray(b'min5_CF8888.ZC_2017_10'), bytearray(b'min5_CF8888.ZC_2017_11'), bytearray(b'min5_CF8888.ZC_2017_12'), bytearray(b'min5_CF8888.ZC_2018_01'), bytearray(b'min5_CF8888.ZC_2018_02'), bytearray(b'min5_CF8888.ZC_2018_03'), bytearray(b'min5_CF8888.ZC_2018_04'), bytearray(b'min5_CF8888.ZC_2018_05'), bytearray(b'min5_CF8888.ZC_2018_06'), bytearray(b'min5_CF8888.ZC_2018_07'), bytearray(b'min5_CS8888.DC_2017_01'), bytearray(b'min5_CS8888.DC_2017_02'), bytearray(b'min5_CS8888.DC_2017_03'), bytearray(b'min5_CS8888.DC_2017_04'), bytearray(b'min5_CS8888.DC_2017_05'), bytearray(b'min5_CS8888.DC_2017_06'), bytearray(b'min5_CS8888.DC_2017_07'), bytearray(b'min5_CS8888.DC_2017_08'), bytearray(b'min5_CS8888.DC_2017_09'), bytearray(b'min5_CS8888.DC_2017_10'), bytearray(b'min5_CS8888.DC_2017_11'), bytearray(b'min5_CS8888.DC_2017_12'), bytearray(b'min5_CS8888.DC_2018_01'), bytearray(b'min5_CS8888.DC_2018_02'), bytearray(b'min5_CS8888.DC_2018_03'), bytearray(b'min5_CS8888.DC_2018_04'), bytearray(b'min5_CS8888.DC_2018_05'), bytearray(b'min5_CS8888.DC_2018_06'), bytearray(b'min5_CS8888.DC_2018_07'), bytearray(b'min5_CU8888.SC_2017_01'), bytearray(b'min5_CU8888.SC_2017_02'), bytearray(b'min5_CU8888.SC_2017_03'), bytearray(b'min5_CU8888.SC_2017_04'), bytearray(b'min5_CU8888.SC_2017_05'), bytearray(b'min5_CU8888.SC_2017_06'), bytearray(b'min5_CU8888.SC_2017_07'), bytearray(b'min5_CU8888.SC_2017_08'), bytearray(b'min5_CU8888.SC_2017_09'), bytearray(b'min5_CU8888.SC_2017_10'), bytearray(b'min5_CU8888.SC_2017_11'), bytearray(b'min5_CU8888.SC_2017_12'), bytearray(b'min5_CU8888.SC_2018_01'), bytearray(b'min5_CU8888.SC_2018_02'), bytearray(b'min5_CU8888.SC_2018_03'), bytearray(b'min5_CU8888.SC_2018_04'), bytearray(b'min5_CU8888.SC_2018_05'), bytearray(b'min5_CU8888.SC_2018_06'), bytearray(b'min5_CU8888.SC_2018_07'), bytearray(b'min5_CY8888.ZC_2017_08'), bytearray(b'min5_CY8888.ZC_2017_09'), bytearray(b'min5_CY8888.ZC_2017_10'), bytearray(b'min5_CY8888.ZC_2017_11'), bytearray(b'min5_CY8888.ZC_2017_12'), bytearray(b'min5_CY8888.ZC_2018_01'), bytearray(b'min5_CY8888.ZC_2018_02'), bytearray(b'min5_CY8888.ZC_2018_03'), bytearray(b'min5_CY8888.ZC_2018_04'), bytearray(b'min5_CY8888.ZC_2018_05'), bytearray(b'min5_CY8888.ZC_2018_06'), bytearray(b'min5_CY8888.ZC_2018_07'), bytearray(b'min5_FG8888.ZC_2017_01'), bytearray(b'min5_FG8888.ZC_2017_02'), bytearray(b'min5_FG8888.ZC_2017_03'), bytearray(b'min5_FG8888.ZC_2017_04'), bytearray(b'min5_FG8888.ZC_2017_05'), bytearray(b'min5_FG8888.ZC_2017_06'), bytearray(b'min5_FG8888.ZC_2017_07'), bytearray(b'min5_FG8888.ZC_2017_08'), bytearray(b'min5_FG8888.ZC_2017_09'), bytearray(b'min5_FG8888.ZC_2017_10'), bytearray(b'min5_FG8888.ZC_2017_11'), bytearray(b'min5_FG8888.ZC_2017_12'), bytearray(b'min5_FG8888.ZC_2018_01'), bytearray(b'min5_FG8888.ZC_2018_02'), bytearray(b'min5_FG8888.ZC_2018_03'), bytearray(b'min5_FG8888.ZC_2018_04'), bytearray(b'min5_FG8888.ZC_2018_05'), bytearray(b'min5_FG8888.ZC_2018_06'), bytearray(b'min5_FG8888.ZC_2018_07'), bytearray(b'min5_HC8888.SC_2017_01'), bytearray(b'min5_HC8888.SC_2017_02'), bytearray(b'min5_HC8888.SC_2017_03'), bytearray(b'min5_HC8888.SC_2017_04'), bytearray(b'min5_HC8888.SC_2017_05'), bytearray(b'min5_HC8888.SC_2017_06'), bytearray(b'min5_HC8888.SC_2017_07'), bytearray(b'min5_HC8888.SC_2017_08'), bytearray(b'min5_HC8888.SC_2017_09'), bytearray(b'min5_HC8888.SC_2017_10'), bytearray(b'min5_HC8888.SC_2017_11'), bytearray(b'min5_HC8888.SC_2017_12'), bytearray(b'min5_HC8888.SC_2018_01'), bytearray(b'min5_HC8888.SC_2018_02'), bytearray(b'min5_HC8888.SC_2018_03'), bytearray(b'min5_HC8888.SC_2018_04'), bytearray(b'min5_HC8888.SC_2018_05'), bytearray(b'min5_HC8888.SC_2018_06'), bytearray(b'min5_HC8888.SC_2018_07'), bytearray(b'min5_I8888.DC_2017_01'), bytearray(b'min5_I8888.DC_2017_02'), bytearray(b'min5_I8888.DC_2017_03'), bytearray(b'min5_I8888.DC_2017_04'), bytearray(b'min5_I8888.DC_2017_05'), bytearray(b'min5_I8888.DC_2017_06'), bytearray(b'min5_I8888.DC_2017_07'), bytearray(b'min5_I8888.DC_2017_08'), bytearray(b'min5_I8888.DC_2017_09'), bytearray(b'min5_I8888.DC_2017_10'), bytearray(b'min5_I8888.DC_2017_11'), bytearray(b'min5_I8888.DC_2017_12'), bytearray(b'min5_I8888.DC_2018_01'), bytearray(b'min5_I8888.DC_2018_02'), bytearray(b'min5_I8888.DC_2018_03'), bytearray(b'min5_I8888.DC_2018_04'), bytearray(b'min5_I8888.DC_2018_05'), bytearray(b'min5_I8888.DC_2018_06'), bytearray(b'min5_I8888.DC_2018_07'), bytearray(b'min5_IC8888.SF_2017_01'), bytearray(b'min5_IC8888.SF_2017_02'), bytearray(b'min5_IC8888.SF_2017_03'), bytearray(b'min5_IC8888.SF_2017_04'), bytearray(b'min5_IC8888.SF_2017_05'), bytearray(b'min5_IC8888.SF_2017_06'), bytearray(b'min5_IC8888.SF_2017_07'), bytearray(b'min5_IC8888.SF_2017_08'), bytearray(b'min5_IC8888.SF_2017_09'), bytearray(b'min5_IC8888.SF_2017_10'), bytearray(b'min5_IC8888.SF_2017_11'), bytearray(b'min5_IC8888.SF_2017_12'), bytearray(b'min5_IC8888.SF_2018_01'), bytearray(b'min5_IC8888.SF_2018_02'), bytearray(b'min5_IC8888.SF_2018_03'), bytearray(b'min5_IC8888.SF_2018_04'), bytearray(b'min5_IC8888.SF_2018_05'), bytearray(b'min5_IC8888.SF_2018_06'), bytearray(b'min5_IC8888.SF_2018_07'), bytearray(b'min5_IF8888.SF_2010_04'), bytearray(b'min5_IF8888.SF_2010_05'), bytearray(b'min5_IF8888.SF_2010_06'), bytearray(b'min5_IF8888.SF_2010_07'), bytearray(b'min5_IF8888.SF_2010_08'), bytearray(b'min5_IF8888.SF_2010_09'), bytearray(b'min5_IF8888.SF_2010_10'), bytearray(b'min5_IF8888.SF_2010_11'), bytearray(b'min5_IF8888.SF_2010_12'), bytearray(b'min5_IF8888.SF_2011_01'), bytearray(b'min5_IF8888.SF_2011_02'), bytearray(b'min5_IF8888.SF_2011_03'), bytearray(b'min5_IF8888.SF_2011_04'), bytearray(b'min5_IF8888.SF_2011_05'), bytearray(b'min5_IF8888.SF_2011_06'), bytearray(b'min5_IF8888.SF_2011_07'), bytearray(b'min5_IF8888.SF_2011_08'), bytearray(b'min5_IF8888.SF_2011_09'), bytearray(b'min5_IF8888.SF_2013_01'), bytearray(b'min5_IF8888.SF_2017_01'), bytearray(b'min5_IF8888.SF_2017_02'), bytearray(b'min5_IF8888.SF_2017_03'), bytearray(b'min5_IF8888.SF_2017_04'), bytearray(b'min5_IF8888.SF_2017_05'), bytearray(b'min5_IF8888.SF_2017_06'), bytearray(b'min5_IF8888.SF_2017_07'), bytearray(b'min5_IF8888.SF_2017_08'), bytearray(b'min5_IF8888.SF_2017_09'), bytearray(b'min5_IF8888.SF_2017_10'), bytearray(b'min5_IF8888.SF_2017_11'), bytearray(b'min5_IF8888.SF_2017_12'), bytearray(b'min5_IF8888.SF_2018_01'), bytearray(b'min5_IF8888.SF_2018_02'), bytearray(b'min5_IF8888.SF_2018_03'), bytearray(b'min5_IF8888.SF_2018_04'), bytearray(b'min5_IF8888.SF_2018_05'), bytearray(b'min5_IF8888.SF_2018_06'), bytearray(b'min5_IF8888.SF_2018_07'), bytearray(b'min5_IH8888.SF_2017_01'), bytearray(b'min5_IH8888.SF_2017_02'), bytearray(b'min5_IH8888.SF_2017_03'), bytearray(b'min5_IH8888.SF_2017_04'), bytearray(b'min5_IH8888.SF_2017_05'), bytearray(b'min5_IH8888.SF_2017_06'), bytearray(b'min5_IH8888.SF_2017_07'), bytearray(b'min5_IH8888.SF_2017_08'), bytearray(b'min5_IH8888.SF_2017_09'), bytearray(b'min5_IH8888.SF_2017_10'), bytearray(b'min5_IH8888.SF_2017_11'), bytearray(b'min5_IH8888.SF_2017_12'), bytearray(b'min5_IH8888.SF_2018_01'), bytearray(b'min5_IH8888.SF_2018_02'), bytearray(b'min5_IH8888.SF_2018_03'), bytearray(b'min5_IH8888.SF_2018_04'), bytearray(b'min5_IH8888.SF_2018_05'), bytearray(b'min5_IH8888.SF_2018_06'), bytearray(b'min5_IH8888.SF_2018_07'), bytearray(b'min5_J8888.DC_2017_01'), bytearray(b'min5_J8888.DC_2017_02'), bytearray(b'min5_J8888.DC_2017_03'), bytearray(b'min5_J8888.DC_2017_04'), bytearray(b'min5_J8888.DC_2017_05'), bytearray(b'min5_J8888.DC_2017_06'), bytearray(b'min5_J8888.DC_2017_07'), bytearray(b'min5_J8888.DC_2017_08'), bytearray(b'min5_J8888.DC_2017_09'), bytearray(b'min5_J8888.DC_2017_10'), bytearray(b'min5_J8888.DC_2017_11'), bytearray(b'min5_J8888.DC_2017_12'), bytearray(b'min5_J8888.DC_2018_01'), bytearray(b'min5_J8888.DC_2018_02'), bytearray(b'min5_J8888.DC_2018_03'), bytearray(b'min5_J8888.DC_2018_04'), bytearray(b'min5_J8888.DC_2018_05'), bytearray(b'min5_J8888.DC_2018_06'), bytearray(b'min5_J8888.DC_2018_07'), bytearray(b'min5_JD8888.DC_2017_01'), bytearray(b'min5_JD8888.DC_2017_02'), bytearray(b'min5_JD8888.DC_2017_03'), bytearray(b'min5_JD8888.DC_2017_04'), bytearray(b'min5_JD8888.DC_2017_05'), bytearray(b'min5_JD8888.DC_2017_06'), bytearray(b'min5_JD8888.DC_2017_07'), bytearray(b'min5_JD8888.DC_2017_08'), bytearray(b'min5_JD8888.DC_2017_09'), bytearray(b'min5_JD8888.DC_2017_10'), bytearray(b'min5_JD8888.DC_2017_11'), bytearray(b'min5_JD8888.DC_2017_12'), bytearray(b'min5_JD8888.DC_2018_01'), bytearray(b'min5_JD8888.DC_2018_02'), bytearray(b'min5_JD8888.DC_2018_03'), bytearray(b'min5_JD8888.DC_2018_04'), bytearray(b'min5_JD8888.DC_2018_05'), bytearray(b'min5_JD8888.DC_2018_06'), bytearray(b'min5_JD8888.DC_2018_07'), bytearray(b'min5_JM8888.DC_2017_01'), bytearray(b'min5_JM8888.DC_2017_02'), bytearray(b'min5_JM8888.DC_2017_03'), bytearray(b'min5_JM8888.DC_2017_04'), bytearray(b'min5_JM8888.DC_2017_05'), bytearray(b'min5_JM8888.DC_2017_06'), bytearray(b'min5_JM8888.DC_2017_07'), bytearray(b'min5_JM8888.DC_2017_08'), bytearray(b'min5_JM8888.DC_2017_09'), bytearray(b'min5_JM8888.DC_2017_10'), bytearray(b'min5_JM8888.DC_2017_11'), bytearray(b'min5_JM8888.DC_2017_12'), bytearray(b'min5_JM8888.DC_2018_01'), bytearray(b'min5_JM8888.DC_2018_02'), bytearray(b'min5_JM8888.DC_2018_03'), bytearray(b'min5_JM8888.DC_2018_04'), bytearray(b'min5_JM8888.DC_2018_05'), bytearray(b'min5_JM8888.DC_2018_06'), bytearray(b'min5_JM8888.DC_2018_07'), bytearray(b'min5_L8888.DC_2017_01'), bytearray(b'min5_L8888.DC_2017_02'), bytearray(b'min5_L8888.DC_2017_03'), bytearray(b'min5_L8888.DC_2017_04'), bytearray(b'min5_L8888.DC_2017_05'), bytearray(b'min5_L8888.DC_2017_06'), bytearray(b'min5_L8888.DC_2017_07'), bytearray(b'min5_L8888.DC_2017_08'), bytearray(b'min5_L8888.DC_2017_09'), bytearray(b'min5_L8888.DC_2017_10'), bytearray(b'min5_L8888.DC_2017_11'), bytearray(b'min5_L8888.DC_2017_12'), bytearray(b'min5_L8888.DC_2018_01'), bytearray(b'min5_L8888.DC_2018_02'), bytearray(b'min5_L8888.DC_2018_03'), bytearray(b'min5_L8888.DC_2018_04'), bytearray(b'min5_L8888.DC_2018_05'), bytearray(b'min5_L8888.DC_2018_06'), bytearray(b'min5_L8888.DC_2018_07'), bytearray(b'min5_M8888.DC_2017_01'), bytearray(b'min5_M8888.DC_2017_02'), bytearray(b'min5_M8888.DC_2017_03'), bytearray(b'min5_M8888.DC_2017_04'), bytearray(b'min5_M8888.DC_2017_05'), bytearray(b'min5_M8888.DC_2017_06'), bytearray(b'min5_M8888.DC_2017_07'), bytearray(b'min5_M8888.DC_2017_08'), bytearray(b'min5_M8888.DC_2017_09'), bytearray(b'min5_M8888.DC_2017_10'), bytearray(b'min5_M8888.DC_2017_11'), bytearray(b'min5_M8888.DC_2017_12'), bytearray(b'min5_M8888.DC_2018_01'), bytearray(b'min5_M8888.DC_2018_02'), bytearray(b'min5_M8888.DC_2018_03'), bytearray(b'min5_M8888.DC_2018_04'), bytearray(b'min5_M8888.DC_2018_05'), bytearray(b'min5_M8888.DC_2018_06'), bytearray(b'min5_M8888.DC_2018_07'), bytearray(b'min5_MA8888.ZC_2017_01'), bytearray(b'min5_MA8888.ZC_2017_02'), bytearray(b'min5_MA8888.ZC_2017_03'), bytearray(b'min5_MA8888.ZC_2017_04'), bytearray(b'min5_MA8888.ZC_2017_05'), bytearray(b'min5_MA8888.ZC_2017_06'), bytearray(b'min5_MA8888.ZC_2017_07'), bytearray(b'min5_MA8888.ZC_2017_08'), bytearray(b'min5_MA8888.ZC_2017_09'), bytearray(b'min5_MA8888.ZC_2017_10'), bytearray(b'min5_MA8888.ZC_2017_11'), bytearray(b'min5_MA8888.ZC_2017_12'), bytearray(b'min5_MA8888.ZC_2018_01'), bytearray(b'min5_MA8888.ZC_2018_02'), bytearray(b'min5_MA8888.ZC_2018_03'), bytearray(b'min5_MA8888.ZC_2018_04'), bytearray(b'min5_MA8888.ZC_2018_05'), bytearray(b'min5_MA8888.ZC_2018_06'), bytearray(b'min5_MA8888.ZC_2018_07'), bytearray(b'min5_MHI888.EX_2018_01'), bytearray(b'min5_MHI888.EX_2018_02'), bytearray(b'min5_MHI888.EX_2018_03'), bytearray(b'min5_MHI888.EX_2018_04'), bytearray(b'min5_MHI888.EX_2018_05'), bytearray(b'min5_MHI888.EX_2018_06'), bytearray(b'min5_MHI888.EX_2018_07'), bytearray(b'min5_NI8888.SC_2017_01'), bytearray(b'min5_NI8888.SC_2017_02'), bytearray(b'min5_NI8888.SC_2017_03'), bytearray(b'min5_NI8888.SC_2017_04'), bytearray(b'min5_NI8888.SC_2017_05'), bytearray(b'min5_NI8888.SC_2017_06'), bytearray(b'min5_NI8888.SC_2017_07'), bytearray(b'min5_NI8888.SC_2017_08'), bytearray(b'min5_NI8888.SC_2017_09'), bytearray(b'min5_NI8888.SC_2017_10'), bytearray(b'min5_NI8888.SC_2017_11'), bytearray(b'min5_NI8888.SC_2017_12'), bytearray(b'min5_NI8888.SC_2018_01'), bytearray(b'min5_NI8888.SC_2018_02'), bytearray(b'min5_NI8888.SC_2018_03'), bytearray(b'min5_NI8888.SC_2018_04'), bytearray(b'min5_NI8888.SC_2018_05'), bytearray(b'min5_NI8888.SC_2018_06'), bytearray(b'min5_NI8888.SC_2018_07'), bytearray(b'min5_OI8888.ZC_2017_01'), bytearray(b'min5_OI8888.ZC_2017_02'), bytearray(b'min5_OI8888.ZC_2017_03'), bytearray(b'min5_OI8888.ZC_2017_04'), bytearray(b'min5_OI8888.ZC_2017_05'), bytearray(b'min5_OI8888.ZC_2017_06'), bytearray(b'min5_OI8888.ZC_2017_07'), bytearray(b'min5_OI8888.ZC_2017_08'), bytearray(b'min5_OI8888.ZC_2017_09'), bytearray(b'min5_OI8888.ZC_2017_10'), bytearray(b'min5_OI8888.ZC_2017_11'), bytearray(b'min5_OI8888.ZC_2017_12'), bytearray(b'min5_OI8888.ZC_2018_01'), bytearray(b'min5_OI8888.ZC_2018_02'), bytearray(b'min5_OI8888.ZC_2018_03'), bytearray(b'min5_OI8888.ZC_2018_04'), bytearray(b'min5_OI8888.ZC_2018_05'), bytearray(b'min5_OI8888.ZC_2018_06'), bytearray(b'min5_OI8888.ZC_2018_07'), bytearray(b'min5_P8888.DC_2017_01'), bytearray(b'min5_P8888.DC_2017_02'), bytearray(b'min5_P8888.DC_2017_03'), bytearray(b'min5_P8888.DC_2017_04'), bytearray(b'min5_P8888.DC_2017_05'), bytearray(b'min5_P8888.DC_2017_06'), bytearray(b'min5_P8888.DC_2017_07'), bytearray(b'min5_P8888.DC_2017_08'), bytearray(b'min5_P8888.DC_2017_09'), bytearray(b'min5_P8888.DC_2017_10'), bytearray(b'min5_P8888.DC_2017_11'), bytearray(b'min5_P8888.DC_2017_12'), bytearray(b'min5_P8888.DC_2018_01'), bytearray(b'min5_P8888.DC_2018_02'), bytearray(b'min5_P8888.DC_2018_03'), bytearray(b'min5_P8888.DC_2018_04'), bytearray(b'min5_P8888.DC_2018_05'), bytearray(b'min5_P8888.DC_2018_06'), bytearray(b'min5_P8888.DC_2018_07'), bytearray(b'min5_PB8888.SC_2017_01'), bytearray(b'min5_PB8888.SC_2017_02'), bytearray(b'min5_PB8888.SC_2017_03'), bytearray(b'min5_PB8888.SC_2017_04'), bytearray(b'min5_PB8888.SC_2017_05'), bytearray(b'min5_PB8888.SC_2017_06'), bytearray(b'min5_PB8888.SC_2017_07'), bytearray(b'min5_PB8888.SC_2017_08'), bytearray(b'min5_PB8888.SC_2017_09'), bytearray(b'min5_PB8888.SC_2017_10'), bytearray(b'min5_PB8888.SC_2017_11'), bytearray(b'min5_PB8888.SC_2017_12'), bytearray(b'min5_PB8888.SC_2018_01'), bytearray(b'min5_PB8888.SC_2018_02'), bytearray(b'min5_PB8888.SC_2018_03'), bytearray(b'min5_PB8888.SC_2018_04'), bytearray(b'min5_PB8888.SC_2018_05'), bytearray(b'min5_PB8888.SC_2018_06'), bytearray(b'min5_PB8888.SC_2018_07'), bytearray(b'min5_PP8888.DC_2017_01'), bytearray(b'min5_PP8888.DC_2017_02'), bytearray(b'min5_PP8888.DC_2017_03'), bytearray(b'min5_PP8888.DC_2017_04'), bytearray(b'min5_PP8888.DC_2017_05'), bytearray(b'min5_PP8888.DC_2017_06'), bytearray(b'min5_PP8888.DC_2017_07'), bytearray(b'min5_PP8888.DC_2017_08'), bytearray(b'min5_PP8888.DC_2017_09'), bytearray(b'min5_PP8888.DC_2017_10'), bytearray(b'min5_PP8888.DC_2017_11'), bytearray(b'min5_PP8888.DC_2017_12'), bytearray(b'min5_PP8888.DC_2018_01'), bytearray(b'min5_PP8888.DC_2018_02'), bytearray(b'min5_PP8888.DC_2018_03'), bytearray(b'min5_PP8888.DC_2018_04'), bytearray(b'min5_PP8888.DC_2018_05'), bytearray(b'min5_PP8888.DC_2018_06'), bytearray(b'min5_PP8888.DC_2018_07'), bytearray(b'min5_RB8888.SC_2017_01'), bytearray(b'min5_RB8888.SC_2017_02'), bytearray(b'min5_RB8888.SC_2017_03'), bytearray(b'min5_RB8888.SC_2017_04'), bytearray(b'min5_RB8888.SC_2017_05'), bytearray(b'min5_RB8888.SC_2017_06'), bytearray(b'min5_RB8888.SC_2017_07'), bytearray(b'min5_RB8888.SC_2017_08'), bytearray(b'min5_RB8888.SC_2017_09'), bytearray(b'min5_RB8888.SC_2017_10'), bytearray(b'min5_RB8888.SC_2017_11'), bytearray(b'min5_RB8888.SC_2017_12'), bytearray(b'min5_RB8888.SC_2018_01'), bytearray(b'min5_RB8888.SC_2018_02'), bytearray(b'min5_RB8888.SC_2018_03'), bytearray(b'min5_RB8888.SC_2018_04'), bytearray(b'min5_RB8888.SC_2018_05'), bytearray(b'min5_RB8888.SC_2018_06'), bytearray(b'min5_RB8888.SC_2018_07'), bytearray(b'min5_RM8888.ZC_2017_01'), bytearray(b'min5_RM8888.ZC_2017_02'), bytearray(b'min5_RM8888.ZC_2017_03'), bytearray(b'min5_RM8888.ZC_2017_04'), bytearray(b'min5_RM8888.ZC_2017_05'), bytearray(b'min5_RM8888.ZC_2017_06'), bytearray(b'min5_RM8888.ZC_2017_07'), bytearray(b'min5_RM8888.ZC_2017_08'), bytearray(b'min5_RM8888.ZC_2017_09'), bytearray(b'min5_RM8888.ZC_2017_10'), bytearray(b'min5_RM8888.ZC_2017_11'), bytearray(b'min5_RM8888.ZC_2017_12'), bytearray(b'min5_RM8888.ZC_2018_01'), bytearray(b'min5_RM8888.ZC_2018_02'), bytearray(b'min5_RM8888.ZC_2018_03'), bytearray(b'min5_RM8888.ZC_2018_04'), bytearray(b'min5_RM8888.ZC_2018_05'), bytearray(b'min5_RM8888.ZC_2018_06'), bytearray(b'min5_RM8888.ZC_2018_07'), bytearray(b'min5_RU8888.SC_2017_01'), bytearray(b'min5_RU8888.SC_2017_02'), bytearray(b'min5_RU8888.SC_2017_03'), bytearray(b'min5_RU8888.SC_2017_04'), bytearray(b'min5_RU8888.SC_2017_05'), bytearray(b'min5_RU8888.SC_2017_06'), bytearray(b'min5_RU8888.SC_2017_07'), bytearray(b'min5_RU8888.SC_2017_08'), bytearray(b'min5_RU8888.SC_2017_09'), bytearray(b'min5_RU8888.SC_2017_10'), bytearray(b'min5_RU8888.SC_2017_11'), bytearray(b'min5_RU8888.SC_2017_12'), bytearray(b'min5_RU8888.SC_2018_01'), bytearray(b'min5_RU8888.SC_2018_02'), bytearray(b'min5_RU8888.SC_2018_03'), bytearray(b'min5_RU8888.SC_2018_04'), bytearray(b'min5_RU8888.SC_2018_05'), bytearray(b'min5_RU8888.SC_2018_06'), bytearray(b'min5_RU8888.SC_2018_07'), bytearray(b'min5_SC8888.SC_2018_03'), bytearray(b'min5_SC8888.SC_2018_04'), bytearray(b'min5_SC8888.SC_2018_05'), bytearray(b'min5_SC8888.SC_2018_06'), bytearray(b'min5_SC8888.SC_2018_07'), bytearray(b'min5_SN8888.SC_2017_01'), bytearray(b'min5_SN8888.SC_2017_02'), bytearray(b'min5_SN8888.SC_2017_03'), bytearray(b'min5_SN8888.SC_2017_04'), bytearray(b'min5_SN8888.SC_2017_05'), bytearray(b'min5_SN8888.SC_2017_06'), bytearray(b'min5_SN8888.SC_2017_07'), bytearray(b'min5_SN8888.SC_2017_08'), bytearray(b'min5_SN8888.SC_2017_09'), bytearray(b'min5_SN8888.SC_2017_10'), bytearray(b'min5_SN8888.SC_2017_11'), bytearray(b'min5_SN8888.SC_2017_12'), bytearray(b'min5_SN8888.SC_2018_01'), bytearray(b'min5_SN8888.SC_2018_02'), bytearray(b'min5_SN8888.SC_2018_03'), bytearray(b'min5_SN8888.SC_2018_04'), bytearray(b'min5_SN8888.SC_2018_05'), bytearray(b'min5_SN8888.SC_2018_06'), bytearray(b'min5_SN8888.SC_2018_07'), bytearray(b'min5_SR8888.ZC_2017_01'), bytearray(b'min5_SR8888.ZC_2017_02'), bytearray(b'min5_SR8888.ZC_2017_03'), bytearray(b'min5_SR8888.ZC_2017_04'), bytearray(b'min5_SR8888.ZC_2017_05'), bytearray(b'min5_SR8888.ZC_2017_06'), bytearray(b'min5_SR8888.ZC_2017_07'), bytearray(b'min5_SR8888.ZC_2017_08'), bytearray(b'min5_SR8888.ZC_2017_09'), bytearray(b'min5_SR8888.ZC_2017_10'), bytearray(b'min5_SR8888.ZC_2017_11'), bytearray(b'min5_SR8888.ZC_2017_12'), bytearray(b'min5_SR8888.ZC_2018_01'), bytearray(b'min5_SR8888.ZC_2018_02'), bytearray(b'min5_SR8888.ZC_2018_03'), bytearray(b'min5_SR8888.ZC_2018_04'), bytearray(b'min5_SR8888.ZC_2018_05'), bytearray(b'min5_SR8888.ZC_2018_06'), bytearray(b'min5_SR8888.ZC_2018_07'), bytearray(b'min5_TA8888.ZC_2017_01'), bytearray(b'min5_TA8888.ZC_2017_02'), bytearray(b'min5_TA8888.ZC_2017_03'), bytearray(b'min5_TA8888.ZC_2017_04'), bytearray(b'min5_TA8888.ZC_2017_05'), bytearray(b'min5_TA8888.ZC_2017_06'), bytearray(b'min5_TA8888.ZC_2017_07'), bytearray(b'min5_TA8888.ZC_2017_08'), bytearray(b'min5_TA8888.ZC_2017_09'), bytearray(b'min5_TA8888.ZC_2017_10'), bytearray(b'min5_TA8888.ZC_2017_11'), bytearray(b'min5_TA8888.ZC_2017_12'), bytearray(b'min5_TA8888.ZC_2018_01'), bytearray(b'min5_TA8888.ZC_2018_02'), bytearray(b'min5_TA8888.ZC_2018_03'), bytearray(b'min5_TA8888.ZC_2018_04'), bytearray(b'min5_TA8888.ZC_2018_05'), bytearray(b'min5_TA8888.ZC_2018_06'), bytearray(b'min5_TA8888.ZC_2018_07'), bytearray(b'min5_V8888.DC_2017_01'), bytearray(b'min5_V8888.DC_2017_02'), bytearray(b'min5_V8888.DC_2017_03'), bytearray(b'min5_V8888.DC_2017_04'), bytearray(b'min5_V8888.DC_2017_05'), bytearray(b'min5_V8888.DC_2017_06'), bytearray(b'min5_V8888.DC_2017_07'), bytearray(b'min5_V8888.DC_2017_08'), bytearray(b'min5_V8888.DC_2017_09'), bytearray(b'min5_V8888.DC_2017_10'), bytearray(b'min5_V8888.DC_2017_11'), bytearray(b'min5_V8888.DC_2017_12'), bytearray(b'min5_V8888.DC_2018_01'), bytearray(b'min5_V8888.DC_2018_02'), bytearray(b'min5_V8888.DC_2018_03'), bytearray(b'min5_V8888.DC_2018_04'), bytearray(b'min5_V8888.DC_2018_05'), bytearray(b'min5_V8888.DC_2018_06'), bytearray(b'min5_V8888.DC_2018_07'), bytearray(b'min5_WFI999.B$_2018_03'), bytearray(b'min5_WFI999.B$_2018_04'), bytearray(b'min5_WFI999.B$_2018_05'), bytearray(b'min5_WFI999.B$_2018_06'), bytearray(b'min5_WFI999.B$_2018_07'), bytearray(b'min5_WFI999.EX_2018_04'), bytearray(b'min5_WFI999.EX_2018_05'), bytearray(b'min5_WFI999.EX_2018_06'), bytearray(b'min5_WFI999.EX_2018_07'), bytearray(b'min5_Y8888.DC_2017_01'), bytearray(b'min5_Y8888.DC_2017_02'), bytearray(b'min5_Y8888.DC_2017_03'), bytearray(b'min5_Y8888.DC_2017_04'), bytearray(b'min5_Y8888.DC_2017_05'), bytearray(b'min5_Y8888.DC_2017_06'), bytearray(b'min5_Y8888.DC_2017_07'), bytearray(b'min5_Y8888.DC_2017_08'), bytearray(b'min5_Y8888.DC_2017_09'), bytearray(b'min5_Y8888.DC_2017_10'), bytearray(b'min5_Y8888.DC_2017_11'), bytearray(b'min5_Y8888.DC_2017_12'), bytearray(b'min5_Y8888.DC_2018_01'), bytearray(b'min5_Y8888.DC_2018_02'), bytearray(b'min5_Y8888.DC_2018_03'), bytearray(b'min5_Y8888.DC_2018_04'), bytearray(b'min5_Y8888.DC_2018_05'), bytearray(b'min5_Y8888.DC_2018_06'), bytearray(b'min5_Y8888.DC_2018_07'), bytearray(b'min5_ZC8888.ZC_2017_01'), bytearray(b'min5_ZC8888.ZC_2017_02'), bytearray(b'min5_ZC8888.ZC_2017_03'), bytearray(b'min5_ZC8888.ZC_2017_04'), bytearray(b'min5_ZC8888.ZC_2017_05'), bytearray(b'min5_ZC8888.ZC_2017_06'), bytearray(b'min5_ZC8888.ZC_2017_07'), bytearray(b'min5_ZC8888.ZC_2017_08'), bytearray(b'min5_ZC8888.ZC_2017_09'), bytearray(b'min5_ZC8888.ZC_2017_10'), bytearray(b'min5_ZC8888.ZC_2017_11'), bytearray(b'min5_ZC8888.ZC_2017_12'), bytearray(b'min5_ZC8888.ZC_2018_01'), bytearray(b'min5_ZC8888.ZC_2018_02'), bytearray(b'min5_ZC8888.ZC_2018_03'), bytearray(b'min5_ZC8888.ZC_2018_04'), bytearray(b'min5_ZC8888.ZC_2018_05'), bytearray(b'min5_ZC8888.ZC_2018_06'), bytearray(b'min5_ZC8888.ZC_2018_07'), bytearray(b'min5_ZN8888.SC_2017_01'), bytearray(b'min5_ZN8888.SC_2017_02'), bytearray(b'min5_ZN8888.SC_2017_03'), bytearray(b'min5_ZN8888.SC_2017_04'), bytearray(b'min5_ZN8888.SC_2017_05'), bytearray(b'min5_ZN8888.SC_2017_06'), bytearray(b'min5_ZN8888.SC_2017_07'), bytearray(b'min5_ZN8888.SC_2017_08'), bytearray(b'min5_ZN8888.SC_2017_09'), bytearray(b'min5_ZN8888.SC_2017_10'), bytearray(b'min5_ZN8888.SC_2017_11'), bytearray(b'min5_ZN8888.SC_2017_12'), bytearray(b'min5_ZN8888.SC_2018_01'), bytearray(b'min5_ZN8888.SC_2018_02'), bytearray(b'min5_ZN8888.SC_2018_03'), bytearray(b'min5_ZN8888.SC_2018_04'), bytearray(b'min5_ZN8888.SC_2018_05'), bytearray(b'min5_ZN8888.SC_2018_06'), bytearray(b'min5_ZN8888.SC_2018_07')]\n"]}], "source": ["db = initialize()\n", "iter_key_values(db)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 2}