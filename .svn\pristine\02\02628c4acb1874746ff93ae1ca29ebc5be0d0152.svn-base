import pandas as pd
import numpy as np
from pyqlab.const import SF_FUT_CODES, MAIN_FUT_CODES


class Pipeline:

    def __init__(self, data_path, start_year, end_year, block_size=15):
        self.data_path = data_path  # d:/RoboQuant2/store/barenc/sf
        self.start_year = start_year
        self.end_year = end_year
        self.block_size = block_size
        self.pos_size = 68
        self.code_size = len(SF_FUT_CODES + MAIN_FUT_CODES)
        self.is_sf = False
        self.fut_codes_dict = {c: i for i, c in enumerate(sorted(SF_FUT_CODES + MAIN_FUT_CODES))}
        
    @staticmethod
    def get_vocab():
        bar_set = []
        for i in range(-12, 13):
            for j in range(-12, 13):
                for k in range(0, 8):
                    for l in range(0, 8):
                        bar_set.append(f'{i}|{j}|{k}|{l}')
        bar_set = sorted(bar_set)
        return bar_set
    
    def _load_data(self):
        df = pd.DataFrame()
        for y in range(self.start_year, self.end_year + 1):
            df2 = pd.read_csv(f'{self.data_path}/min5_{y}.csv', header=None)
            df = pd.concat([df, df2], axis=0, ignore_index=True)
        df.columns = ['symbol', 'date', 'position', 'change', 'entity', 'upline', 'downline']
        if "IF" in df['symbol'].values:
            self.is_sf = True

        df['code_encoded'] = df['symbol'].apply(lambda x: self.fut_codes_dict[x])
        df.drop(columns=['symbol'], inplace=True)
        # move code_encoded to the first column
        cols = df.columns.tolist()
        cols = cols[-1:] + cols[:-1]
        df = df[cols]
        df = df.sort_values(by=['code_encoded', 'date', 'position']).reset_index(drop=True)
        self.pos_size = 68 # len(df['position'].unique())
        return df

    def _prepare_data(self):
        # 将列change的值限定在-10到10之间
        df=self._load_data()
        df.loc[df['change'] < -12, 'change'] = -12
        df.loc[df['change'] > 12, 'change'] = 12
        df.loc[df['entity'] < -12, 'entity'] = -12
        df.loc[df['entity'] > 12, 'entity'] = 12
        df.loc[df['upline'] > 7, 'upline'] = 7
        df.loc[df['downline'] > 7, 'downline'] = 7

        # 将3,4,5,6列的值转换为bar_set中的索引
        bar_set = self.get_vocab()
        df['bar'] = df['change'].astype(str) + '|' + df['entity'].astype(str) + '|' + df['upline'].astype(str) + '|' + df['downline'].astype(str)
        df['bar'] = df['bar'].apply(lambda x: bar_set.index(x))
        df['bar'] = df['bar'].astype(int)
        return df
    
    def get_data(self):
        df = self._prepare_data()
        print('============================')
        print(f'pos_size: {self.pos_size}, code_size: {self.code_size}, bar_size: {len(self.get_vocab())}')
        print(f'df shape: {df.shape}')
        print('============================')
        # dataframe group by symbol to ndarray
        df = df.groupby('code_encoded')
        data = []
        for _, group in df:
            for i in range(0, len(group) - self.block_size):
                data.append(group.iloc[i:i+self.block_size+1, :].values)
        data = np.array(data)
        return data