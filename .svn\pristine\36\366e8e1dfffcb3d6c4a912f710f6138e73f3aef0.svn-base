{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### MOdel testing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### CV2DR"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# test\n", "print(\"=== Testing ===\")\n", "test_dataloader = data_module.test_dataloader()\n", "trainer = Trainer()\n", "trainer.test(model, test_dataloaders=test_dataloader)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Model eval check"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import pandas as pd\n", "import numpy as np\n", "import json"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["with open('d:\\\\RoboQuant\\\\rpt\\\\model_test_data.json') as f:\n", "    md_data = json.load(f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for md in md_data:\n", "    # 加载模型\n", "    model = torch.jit.load(f\"d:\\\\RoboQuant\\\\model\\\\{md['model']}.model\")\n", "    model.eval()\n", "    print(md['encoding'])\n", "    encoding = torch.tensor([md['encoding']], dtype=torch.int32)\n", "    input_tensor = torch.tensor(md['data'])\n", "    if '05' in md['model']:\n", "        channel = 5\n", "    elif '10' in md['model']:\n", "        channel = 10\n", "    elif '15' in md['model']:\n", "        channel = 15\n", "    else:\n", "        continue\n", "    input_tensor = input_tensor.reshape(1, channel, len(md['data'])//channel)\n", "    # print(encoding.shape, input_tensor.shape)\n", "    output_tensor = model(encoding, input_tensor)\n", "    print(output_tensor, md['predict'])\n"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["with open('e:/lab/RoboQuant/bin/x64/Release/rpt/model_test_data.json') as f:\n", "    md_data = json.load(f)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/plain": ["560"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["len(md_data[0]['feat'])"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["feat = md_data[0]['feat'][-11:]\n", "data = md_data[0]['data'][-11:]\n", "means = md_data[0]['means'][-11:]\n", "stds = md_data[0]['stds'][-11:]"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1.6589628458023071, 0.0, -0.6534249186515808, 0.2763267755508423, 1.0, 0.16439345479011536, -0.7624899744987488, -1.0, -0.02448284812271595, -0.12306361645460129, 1.0]\n", "[1.9540044069290161, -4.955992221832275, -0.12699022889137268, 0.2434171885251999, 0.4251939356327057, -1.3482558727264404, -1.1690670251846313, -0.5492770671844482, -0.0646616667509079, -0.1042919009923935, 0.7405258417129517]\n", "[1.370863914489746, 15.722155570983887, -0.04414869099855423, -0.038297999650239944, -0.05515841394662857, 0.26343804597854614, 0.35119858384132385, 0.16802211105823517, 0.2408970147371292, 0.016221705824136734, -0.04293831065297127]\n", "[0.14744026958942413, 3.1723527908325195, 4.7978196144104, 1.2925331592559814, 2.481593132019043, 0.07346127182245255, 0.9526302218437195, 2.126471757888794, 4.104129791259766, 1.3355334997177124, 1.4083753824234009]\n"]}], "source": ["print(feat)\n", "print(data)\n", "print(means)\n", "print(stds)"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ 1.95400437 -4.95599216 -0.12699023  0.24341718  0.42519396 -1.34825587\n", " -1.169067   -0.54927704 -0.06466166 -0.1042919   0.7405258 ]\n"]}], "source": ["print((np.array(feat) - np.array(means)) / np.array(stds))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Model dump check"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rpt_path = 'd:/RoboQuant2/rpt'\n", "json_file = 'model_test_202306172311.json'\n", "with open(f'{rpt_path}/{json_file}') as file:\n", "    # Load the JSON data\n", "    data = json.load(file)\n", "models = data['models']\n", "data.pop('models')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dfs = pd.DataFrame()\n", "for code in data.keys():\n", "    df = pd.DataFrame(data[code], columns=['change'] + models)\n", "    df [\"change\"] = df[\"change\"].shift(-1)\n", "    df.dropna(inplace=True)\n", "    df = df.loc[df['change'] != 0.0, :]\n", "    df.insert(0, 'code', code)\n", "    dfs = pd.concat([dfs, df], axis=0)\n", "# 删除loc.columns[2:]列所有列元素都为0的列\n", "# dfs = dfs.loc[:, dfs[dfs.columns[2:]].sum(axis=0) > 0.0]\n", "# # 删除所有行元素都为0的行\n", "dfs = dfs.loc[dfs[dfs.columns[2:]].sum(axis=1) > 0.0, :]\n", "dfs.reset_index(drop=True, inplace=True)\n", "print(dfs.shape)\n", "dfs.to_csv(f'{rpt_path}/{json_file}.csv', index=False)\n", "dfs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dfs.describe(percentiles=[0.75, 0.95, 0.98, 0.99])"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}